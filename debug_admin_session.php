<?php
// Debug admin session and authentication
session_start();

echo "<h2>🔍 Debug Admin Session & Authentication</h2>";

// Check current session
echo "<h3>1. Current Session Data</h3>";
if (!empty($_SESSION)) {
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>✅ Session exists</h4>";
    echo "<pre>" . print_r($_SESSION, true) . "</pre>";
    
    if (isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in']) {
        echo "<p style='color: green; font-weight: bold; font-size: 18px;'>✅ Admin is logged in!</p>";
    } else {
        echo "<p style='color: red; font-weight: bold; font-size: 18px;'>❌ Admin is NOT logged in!</p>";
    }
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p style='color: red; font-weight: bold;'>❌ No session data found</p>";
    echo "</div>";
}

// Test database connection
echo "<h3>2. Database Connection Test</h3>";
try {
    $pdo = new PDO('mysql:host=localhost;dbname=game_platform', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<p style='color: green;'>✅ Database connected successfully</p>";
    
    // Check admin user
    $stmt = $pdo->prepare("SELECT * FROM admins WHERE username = 'admin' AND status = 1");
    $stmt->execute();
    $admin = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($admin) {
        echo "<p style='color: green;'>✅ Admin user found in database</p>";
        echo "<p>Admin ID: " . $admin['id'] . "</p>";
        echo "<p>Username: " . $admin['username'] . "</p>";
        echo "<p>Full Name: " . $admin['full_name'] . "</p>";
        echo "<p>Status: " . $admin['status'] . "</p>";
    } else {
        echo "<p style='color: red;'>❌ Admin user not found in database</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Database connection failed: " . $e->getMessage() . "</p>";
}

// Test login process
echo "<h3>3. Test Login Process</h3>";
if ($_POST && isset($_POST['test_login'])) {
    $username = 'admin';
    $password = '123456';
    
    echo "<p>Testing login with: $username / $password</p>";
    
    try {
        $stmt = $pdo->prepare("SELECT * FROM admins WHERE username = ? AND status = 1");
        $stmt->execute([$username]);
        
        if ($stmt->rowCount() == 1) {
            $admin = $stmt->fetch(PDO::FETCH_ASSOC);
            echo "<p style='color: green;'>✅ Admin found</p>";
            
            if (password_verify($password, $admin['password'])) {
                echo "<p style='color: green;'>✅ Password verified</p>";
                
                // Set session like CodeIgniter does
                $_SESSION['admin_logged_in'] = TRUE;
                $_SESSION['admin_id'] = $admin['id'];
                $_SESSION['admin_username'] = $admin['username'];
                $_SESSION['admin_name'] = $admin['full_name'];
                
                echo "<p style='color: green; font-weight: bold; font-size: 18px;'>🎉 LOGIN SUCCESSFUL!</p>";
                echo "<p>Session data set:</p>";
                echo "<pre>" . print_r($_SESSION, true) . "</pre>";
                
                echo "<p><a href='http://localhost/admin' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Go to Admin Dashboard</a></p>";
                
            } else {
                echo "<p style='color: red;'>❌ Password verification failed</p>";
            }
        } else {
            echo "<p style='color: red;'>❌ Admin not found</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Login test failed: " . $e->getMessage() . "</p>";
    }
} else {
    echo "<form method='post'>";
    echo "<input type='hidden' name='test_login' value='1'>";
    echo "<button type='submit' style='background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;'>Test Login Process</button>";
    echo "</form>";
}

// Check CodeIgniter session configuration
echo "<h3>4. CodeIgniter Session Configuration</h3>";
if (file_exists('application/config/config.php')) {
    $config_content = file_get_contents('application/config/config.php');
    
    // Extract session configuration
    if (preg_match("/\\\$config\['sess_driver'\]\s*=\s*'([^']+)'/", $config_content, $matches)) {
        echo "<p>Session Driver: " . $matches[1] . "</p>";
    }
    
    if (preg_match("/\\\$config\['sess_cookie_name'\]\s*=\s*'([^']+)'/", $config_content, $matches)) {
        echo "<p>Session Cookie Name: " . $matches[1] . "</p>";
    }
    
    if (preg_match("/\\\$config\['sess_expiration'\]\s*=\s*(\d+)/", $config_content, $matches)) {
        echo "<p>Session Expiration: " . $matches[1] . " seconds</p>";
    }
    
    if (preg_match("/\\\$config\['sess_save_path'\]\s*=\s*([^;]+);/", $config_content, $matches)) {
        echo "<p>Session Save Path: " . trim($matches[1]) . "</p>";
    }
    
    echo "<p style='color: green;'>✅ CodeIgniter config file found</p>";
} else {
    echo "<p style='color: red;'>❌ CodeIgniter config file not found</p>";
}

// Check session files
echo "<h3>5. Session Files Check</h3>";
$session_path = 'application/cache/sessions';
if (is_dir($session_path)) {
    $files = scandir($session_path);
    $session_files = array_filter($files, function($file) {
        return $file !== '.' && $file !== '..' && strpos($file, 'ci_session') === 0;
    });
    
    echo "<p>Session directory exists: $session_path</p>";
    echo "<p>Session files found: " . count($session_files) . "</p>";
    
    if (count($session_files) > 0) {
        echo "<p style='color: green;'>✅ Session files exist</p>";
        echo "<p>Latest session files:</p>";
        echo "<ul>";
        foreach (array_slice($session_files, -5) as $file) {
            echo "<li>$file</li>";
        }
        echo "</ul>";
    } else {
        echo "<p style='color: orange;'>⚠️ No session files found</p>";
    }
} else {
    echo "<p style='color: red;'>❌ Session directory not found: $session_path</p>";
}

echo "<hr>";
echo "<h3>6. Quick Actions</h3>";
echo "<p><a href='http://localhost/admin/login' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>Admin Login</a></p>";
echo "<p><a href='http://localhost/admin' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>Admin Dashboard</a></p>";
echo "<p><a href='debug_admin_session.php' style='background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>Refresh This Page</a></p>";

if (isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in']) {
    echo "<p><a href='?logout=1' style='background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Clear Session (Logout)</a></p>";
}

// Handle logout
if (isset($_GET['logout'])) {
    session_destroy();
    echo "<script>window.location.reload();</script>";
}
?>
