<?php
// Test admin login functionality
echo "<h2>Testing Admin Login System</h2>";

// Test database connection
try {
    $pdo = new PDO('mysql:host=localhost;dbname=game_platform', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<p style='color: green;'>✓ Database connection: SUCCESS</p>";
    
    // Check if admins table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'admins'");
    if ($stmt->rowCount() > 0) {
        echo "<p style='color: green;'>✓ Admins table exists</p>";
        
        // Check if admin user exists
        $stmt = $pdo->prepare("SELECT * FROM admins WHERE username = ?");
        $stmt->execute(['admin']);
        $admin = $stmt->fetch();
        
        if ($admin) {
            echo "<p style='color: green;'>✓ Admin user found</p>";
            echo "<p>Admin ID: " . $admin['id'] . "</p>";
            echo "<p>Admin username: " . $admin['username'] . "</p>";
            echo "<p>Admin email: " . $admin['email'] . "</p>";
            echo "<p>Admin status: " . ($admin['status'] ? 'Active' : 'Inactive') . "</p>";
            
            // Test password verification
            $test_password = 'password'; // Default password from hash
            if (password_verify($test_password, $admin['password'])) {
                echo "<p style='color: green;'>✓ Password verification works with 'password'</p>";
            } else {
                echo "<p style='color: red;'>✗ Password verification failed with 'password'</p>";
                
                // <NAME_EMAIL> and 123456 (from login form)
                $test_password2 = '123456';
                if (password_verify($test_password2, $admin['password'])) {
                    echo "<p style='color: green;'>✓ Password verification works with '123456'</p>";
                } else {
                    echo "<p style='color: red;'>✗ Password verification failed with '123456'</p>";
                    echo "<p>Current password hash: " . $admin['password'] . "</p>";
                }
            }
        } else {
            echo "<p style='color: red;'>✗ Admin user not found</p>";
            
            // Create admin user
            echo "<p>Creating admin user...</p>";
            $password_hash = password_hash('123456', PASSWORD_DEFAULT);
            $stmt = $pdo->prepare("INSERT INTO admins (username, password, email, full_name, status) VALUES (?, ?, ?, ?, ?)");
            $result = $stmt->execute(['admin', $password_hash, '<EMAIL>', 'Administrador Principal', 1]);
            
            if ($result) {
                echo "<p style='color: green;'>✓ Admin user created successfully</p>";
            } else {
                echo "<p style='color: red;'>✗ Failed to create admin user</p>";
            }
        }
        
        // Show all admins
        echo "<h3>All Admin Users:</h3>";
        $stmt = $pdo->query("SELECT * FROM admins");
        $admins = $stmt->fetchAll();
        
        if ($admins) {
            echo "<table border='1' style='border-collapse: collapse;'>";
            echo "<tr><th>ID</th><th>Username</th><th>Email</th><th>Full Name</th><th>Status</th><th>Created At</th></tr>";
            foreach ($admins as $admin) {
                echo "<tr>";
                echo "<td>" . $admin['id'] . "</td>";
                echo "<td>" . $admin['username'] . "</td>";
                echo "<td>" . $admin['email'] . "</td>";
                echo "<td>" . $admin['full_name'] . "</td>";
                echo "<td>" . ($admin['status'] ? 'Active' : 'Inactive') . "</td>";
                echo "<td>" . $admin['created_at'] . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        
    } else {
        echo "<p style='color: red;'>✗ Admins table does not exist</p>";
        
        // Create admins table
        echo "<p>Creating admins table...</p>";
        $sql = "CREATE TABLE IF NOT EXISTS admins (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(50) UNIQUE NOT NULL,
            password VARCHAR(255) NOT NULL,
            email VARCHAR(100) UNIQUE NOT NULL,
            full_name VARCHAR(100) NOT NULL,
            status TINYINT(1) DEFAULT 1,
            last_login TIMESTAMP NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )";
        
        if ($pdo->exec($sql) !== false) {
            echo "<p style='color: green;'>✓ Admins table created successfully</p>";
            
            // Insert default admin
            $password_hash = password_hash('123456', PASSWORD_DEFAULT);
            $stmt = $pdo->prepare("INSERT INTO admins (username, password, email, full_name) VALUES (?, ?, ?, ?)");
            $result = $stmt->execute(['admin', $password_hash, '<EMAIL>', 'Administrador Principal']);
            
            if ($result) {
                echo "<p style='color: green;'>✓ Default admin user created</p>";
            } else {
                echo "<p style='color: red;'>✗ Failed to create default admin user</p>";
            }
        } else {
            echo "<p style='color: red;'>✗ Failed to create admins table</p>";
        }
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Database error: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<h3>Test Login Form</h3>";
echo "<form method='post' action='test_login_process.php'>";
echo "<p>Username: <input type='text' name='username' value='admin' /></p>";
echo "<p>Password: <input type='password' name='password' value='123456' /></p>";
echo "<p><input type='submit' value='Test Login' /></p>";
echo "</form>";
?>
