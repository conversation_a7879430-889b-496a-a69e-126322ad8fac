<?php
// Debug admin login process
echo "<h2>Debug Admin Login Process</h2>";

// Start session
session_start();

// Check if form was submitted
if ($_POST) {
    echo "<h3>Form Submitted</h3>";
    echo "<p>Username: " . ($_POST['username'] ?? 'Not set') . "</p>";
    echo "<p>Password: " . (isset($_POST['password']) ? '[PROVIDED]' : 'Not set') . "</p>";
    
    try {
        $pdo = new PDO('mysql:host=localhost;dbname=game_platform', 'root', '');
        echo "<p style='color: green;'>✓ Database connection successful</p>";
        
        $username = $_POST['username'] ?? '';
        $password = $_POST['password'] ?? '';
        
        // Get admin user
        $stmt = $pdo->prepare("SELECT * FROM admins WHERE username = ? AND status = 1");
        $stmt->execute([$username]);
        $admin = $stmt->fetch();
        
        if ($admin) {
            echo "<p style='color: green;'>✓ Admin user found</p>";
            
            if (password_verify($password, $admin['password'])) {
                echo "<p style='color: green; font-weight: bold;'>✓ Password verified successfully</p>";
                
                // Set session
                $_SESSION['admin_logged_in'] = true;
                $_SESSION['admin_id'] = $admin['id'];
                $_SESSION['admin_username'] = $admin['username'];
                $_SESSION['admin_name'] = $admin['full_name'];
                
                echo "<p style='color: green; font-weight: bold;'>✓ Session set successfully</p>";
                echo "<p>Session data:</p>";
                echo "<pre>" . print_r($_SESSION, true) . "</pre>";
                
                // Update last login
                $stmt = $pdo->prepare("UPDATE admins SET last_login = NOW() WHERE id = ?");
                $stmt->execute([$admin['id']]);
                
                echo "<p style='color: green;'>✓ Last login updated</p>";
                echo "<p style='color: blue; font-weight: bold; font-size: 18px;'>LOGIN SUCCESSFUL! You should be redirected to admin dashboard.</p>";
                echo "<p><a href='admin' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Go to Admin Dashboard</a></p>";
                
            } else {
                echo "<p style='color: red; font-weight: bold;'>✗ Password verification failed</p>";
            }
        } else {
            echo "<p style='color: red; font-weight: bold;'>✗ Admin user not found or inactive</p>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>✗ Error: " . $e->getMessage() . "</p>";
    }
} else {
    echo "<h3>Login Form</h3>";
    echo "<form method='post'>";
    echo "<p><label>Username: <input type='text' name='username' value='admin' required></label></p>";
    echo "<p><label>Password: <input type='password' name='password' value='123456' required></label></p>";
    echo "<p><input type='submit' value='Test Login' style='background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;'></p>";
    echo "</form>";
}

echo "<hr>";
echo "<h3>Current Session</h3>";
if (isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in']) {
    echo "<p style='color: green; font-weight: bold;'>✓ Admin is logged in</p>";
    echo "<pre>" . print_r($_SESSION, true) . "</pre>";
    echo "<p><a href='admin' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Go to Admin Dashboard</a></p>";
} else {
    echo "<p style='color: red;'>✗ Admin is not logged in</p>";
}

echo "<hr>";
echo "<h3>Links</h3>";
echo "<p><a href='admin/login'>CodeIgniter Admin Login</a></p>";
echo "<p><a href='check_admin_user.php'>Check Admin User</a></p>";
?>
