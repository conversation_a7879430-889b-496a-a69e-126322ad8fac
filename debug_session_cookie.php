<?php
// Debug session cookie issues
echo "<h2>🍪 Session Cookie Debug</h2>";

// Start session to get current session ID
session_start();

echo "<h3>1. Current Session Information</h3>";
echo "<p><strong>Session ID:</strong> " . session_id() . "</p>";
echo "<p><strong>Session Name:</strong> " . session_name() . "</p>";

// Check cookies
echo "<h3>2. Current Cookies</h3>";
if (!empty($_COOKIE)) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th><PERSON>ie Name</th><th>Value</th></tr>";
    foreach ($_COOKIE as $name => $value) {
        echo "<tr><td><strong>$name</strong></td><td>" . htmlspecialchars($value) . "</td></tr>";
    }
    echo "</table>";
} else {
    echo "<p style='color: red;'>❌ No cookies found!</p>";
}

// Check CodeIgniter session files
echo "<h3>3. CodeIgniter Session Files</h3>";
$session_path = 'application/cache/sessions';

if (is_dir($session_path)) {
    $files = scandir($session_path);
    $session_files = array_filter($files, function($file) {
        return $file !== '.' && $file !== '..' && strpos($file, 'ci_session') === 0;
    });
    
    echo "<p>Found " . count($session_files) . " session files</p>";
    
    // Check if current session ID matches any file
    $current_session_id = session_id();
    $ci_cookie = $_COOKIE['ci_session'] ?? null;
    
    echo "<p><strong>PHP Session ID:</strong> $current_session_id</p>";
    echo "<p><strong>CI Session Cookie:</strong> " . ($ci_cookie ? $ci_cookie : 'Not found') . "</p>";
    
    if ($ci_cookie) {
        $ci_session_file = $session_path . '/ci_session' . $ci_cookie;
        echo "<p><strong>Expected CI Session File:</strong> $ci_session_file</p>";
        
        if (file_exists($ci_session_file)) {
            echo "<p style='color: green;'>✅ CI Session file exists!</p>";
            $content = file_get_contents($ci_session_file);
            echo "<p><strong>Content:</strong></p>";
            echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 5px;'>" . htmlspecialchars($content) . "</pre>";
            
            // Check if admin data is in the file
            if (strpos($content, 'admin_logged_in') !== false) {
                echo "<p style='color: green;'>✅ Admin login data found in session file!</p>";
            } else {
                echo "<p style='color: red;'>❌ Admin login data NOT found in session file</p>";
            }
        } else {
            echo "<p style='color: red;'>❌ CI Session file does not exist!</p>";
        }
    }
    
    // Show all session files with admin data
    echo "<h4>Session Files with Admin Data:</h4>";
    $admin_sessions = [];
    
    foreach ($session_files as $file) {
        $file_path = $session_path . '/' . $file;
        $content = file_get_contents($file_path);
        
        if (strpos($content, 'admin_logged_in') !== false) {
            $admin_sessions[] = [
                'file' => $file,
                'content' => $content,
                'modified' => filemtime($file_path)
            ];
        }
    }
    
    if (!empty($admin_sessions)) {
        echo "<p style='color: green;'>Found " . count($admin_sessions) . " session files with admin data:</p>";
        
        // Sort by modification time (newest first)
        usort($admin_sessions, function($a, $b) {
            return $b['modified'] - $a['modified'];
        });
        
        foreach ($admin_sessions as $session) {
            echo "<div style='background: #e7f3ff; padding: 10px; margin: 10px 0; border-radius: 5px;'>";
            echo "<p><strong>File:</strong> " . $session['file'] . "</p>";
            echo "<p><strong>Modified:</strong> " . date('Y-m-d H:i:s', $session['modified']) . "</p>";
            echo "<p><strong>Content:</strong></p>";
            echo "<pre style='background: #f8f9fa; padding: 5px; border-radius: 3px; font-size: 12px;'>" . htmlspecialchars($session['content']) . "</pre>";
            echo "</div>";
        }
    } else {
        echo "<p style='color: red;'>❌ No session files with admin data found</p>";
    }
    
} else {
    echo "<p style='color: red;'>❌ Session directory not found</p>";
}

// Test setting a CodeIgniter-compatible session
echo "<h3>4. Test Setting CI Session</h3>";

if (isset($_POST['set_ci_session'])) {
    // Get the latest admin session file
    if (!empty($admin_sessions)) {
        $latest_session = $admin_sessions[0];
        $session_id = str_replace('ci_session', '', $latest_session['file']);
        
        // Set the CI session cookie
        setcookie('ci_session', $session_id, time() + 7200, '/', '', false, true);
        
        echo "<p style='color: green;'>✅ CI session cookie set to: $session_id</p>";
        echo "<p>Refresh the page to see the effect.</p>";
        
        // Also set in PHP session for immediate effect
        $_COOKIE['ci_session'] = $session_id;
    } else {
        echo "<p style='color: red;'>❌ No admin session found to set</p>";
    }
}

if (!empty($admin_sessions)) {
    echo "<form method='post'>";
    echo "<button type='submit' name='set_ci_session' style='background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px;'>Set CI Session Cookie</button>";
    echo "</form>";
}

// Test admin dashboard access
echo "<h3>5. Test Admin Dashboard Access</h3>";
echo "<p><a href='http://localhost/admin' style='background: #28a745; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-size: 18px;'>Test Admin Dashboard</a></p>";

// Clear all sessions
echo "<h3>6. Clear Sessions</h3>";
if (isset($_GET['clear'])) {
    // Clear PHP session
    session_destroy();
    
    // Clear CI session cookie
    setcookie('ci_session', '', time() - 3600, '/');
    
    // Clear CI session files
    if (is_dir($session_path)) {
        $files = glob($session_path . '/ci_session*');
        foreach ($files as $file) {
            unlink($file);
        }
    }
    
    echo "<script>window.location.href = 'debug_session_cookie.php';</script>";
}

echo "<p><a href='?clear=1' style='background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Clear All Sessions</a></p>";

echo "<hr>";
echo "<h3>🔗 Quick Links</h3>";
echo "<p>";
echo "<a href='http://localhost/admin/login' style='margin-right: 10px;'>Admin Login</a> | ";
echo "<a href='http://localhost/admin' style='margin-right: 10px;'>Admin Dashboard</a> | ";
echo "<a href='debug_session_cookie.php' style='margin-right: 10px;'>Refresh</a>";
echo "</p>";
?>
