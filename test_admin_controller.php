<?php
// Test admin controller functionality
echo "<h2>Testing Admin Controller Functionality</h2>";

// Simulate CodeIgniter environment
define('BASEPATH', true);

// Include CodeIgniter files
require_once 'system/core/Common.php';
require_once 'application/config/config.php';
require_once 'application/config/database.php';

// Test database connection using CodeIgniter style
try {
    // Manual database connection test
    $pdo = new PDO('mysql:host=localhost;dbname=game_platform', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<p style='color: green;'>✓ Database connection successful</p>";
    
    // Test the exact query from Admin_model
    echo "<h3>Testing Admin_model verify_login logic</h3>";
    
    $username = 'admin';
    $password = '123456';
    
    echo "<p>Testing with username: <strong>$username</strong></p>";
    echo "<p>Testing with password: <strong>$password</strong></p>";
    
    // Simulate the exact query from Admin_model->verify_login()
    $stmt = $pdo->prepare("SELECT * FROM admins WHERE username = ? AND status = 1");
    $stmt->execute([$username]);
    $admin = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($admin) {
        echo "<p style='color: green;'>✓ Admin user found in database</p>";
        echo "<p>Admin details:</p>";
        echo "<ul>";
        echo "<li>ID: " . $admin['id'] . "</li>";
        echo "<li>Username: " . $admin['username'] . "</li>";
        echo "<li>Email: " . $admin['email'] . "</li>";
        echo "<li>Status: " . $admin['status'] . "</li>";
        echo "</ul>";
        
        // Test password verification
        if (password_verify($password, $admin['password'])) {
            echo "<p style='color: green; font-weight: bold; font-size: 18px;'>✓ PASSWORD VERIFICATION SUCCESSFUL!</p>";
            echo "<p style='color: green; font-weight: bold; font-size: 20px;'>🎉 LOGIN LOGIC IS WORKING CORRECTLY!</p>";
            
            // Simulate session data that would be set
            echo "<h3>Session data that would be set:</h3>";
            echo "<ul>";
            echo "<li>admin_logged_in: TRUE</li>";
            echo "<li>admin_id: " . $admin['id'] . "</li>";
            echo "<li>admin_username: " . $admin['username'] . "</li>";
            echo "<li>admin_name: " . $admin['full_name'] . "</li>";
            echo "</ul>";
            
        } else {
            echo "<p style='color: red;'>✗ Password verification failed</p>";
            echo "<p>Stored hash: " . $admin['password'] . "</p>";
            echo "<p>Testing password: " . $password . "</p>";
        }
    } else {
        echo "<p style='color: red;'>✗ Admin user not found or inactive</p>";
        
        // Show all admins for debugging
        echo "<h3>All admins in database:</h3>";
        $stmt = $pdo->query("SELECT * FROM admins");
        $admins = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if ($admins) {
            foreach ($admins as $admin) {
                echo "<p>Username: " . $admin['username'] . ", Status: " . $admin['status'] . "</p>";
            }
        } else {
            echo "<p>No admins found in database</p>";
        }
    }
    
    // Test form submission simulation
    echo "<h3>Simulating Form Submission</h3>";
    
    // Simulate $_POST data
    $_POST['username'] = 'admin';
    $_POST['password'] = '123456';
    
    echo "<p>Simulating POST data:</p>";
    echo "<ul>";
    echo "<li>username: " . $_POST['username'] . "</li>";
    echo "<li>password: " . $_POST['password'] . "</li>";
    echo "</ul>";
    
    // Simulate the do_login method logic
    $username = $_POST['username'];
    $password = $_POST['password'];
    
    $stmt = $pdo->prepare("SELECT * FROM admins WHERE username = ? AND status = 1");
    $stmt->execute([$username]);
    $admin = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($admin && password_verify($password, $admin['password'])) {
        echo "<p style='color: green; font-weight: bold; font-size: 18px;'>✓ FORM SUBMISSION WOULD BE SUCCESSFUL!</p>";
        echo "<p style='color: green;'>User would be redirected to admin dashboard</p>";
    } else {
        echo "<p style='color: red; font-weight: bold;'>✗ FORM SUBMISSION WOULD FAIL</p>";
        echo "<p style='color: red;'>User would be redirected back to login with error</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Error: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<h3>Conclusion</h3>";
echo "<p>If all tests above show green checkmarks, the login should work.</p>";
echo "<p>If there are any red X marks, those indicate the issues that need to be fixed.</p>";

echo "<hr>";
echo "<h3>Test the actual login</h3>";
echo "<p><a href='admin/login' target='_blank' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Go to Admin Login Page</a></p>";
echo "<p>Use: <strong>admin</strong> / <strong>123456</strong></p>";
?>
