<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Admin extends CI_Controller {

    public function __construct() {
        parent::__construct();
        $this->load->model('Admin_model');
        $this->load->model('Agent_model');
        $this->load->model('Game_model');
        $this->load->model('User_model');
        $this->load->library('session');
        $this->load->helper('url');
        $this->load->helper('form');
        
        // Check if admin is logged in
        if (!$this->session->userdata('admin_logged_in') && $this->router->method != 'login' && $this->router->method != 'do_login') {
            redirect('admin/login');
        }
    }

    public function index() {
        $data['title'] = 'Dashboard Administrativo';
        $data['total_agents'] = $this->Agent_model->count_agents();
        $data['total_users'] = $this->User_model->count_users();
        $data['total_games'] = $this->Game_model->count_games();
        $data['total_transactions'] = $this->Admin_model->count_transactions();
        $data['recent_transactions'] = $this->Admin_model->get_recent_transactions(10);
        $data['top_games'] = $this->Admin_model->get_top_games(5);
        
        $this->load->view('admin/header', $data);
        $this->load->view('admin/dashboard', $data);
        $this->load->view('admin/footer');
    }

    public function login() {
        if ($this->session->userdata('admin_logged_in')) {
            redirect('admin');
        }
        
        $data['title'] = 'Login Administrativo';
        $this->load->view('admin/login', $data);
    }

    public function do_login() {
        $username = $this->input->post('username');
        $password = $this->input->post('password');
        
        $admin = $this->Admin_model->verify_login($username, $password);
        
        if ($admin) {
            $this->session->set_userdata([
                'admin_logged_in' => TRUE,
                'admin_id' => $admin['id'],
                'admin_username' => $admin['username'],
                'admin_name' => $admin['full_name']
            ]);
            
            $this->Admin_model->update_last_login($admin['id']);
            redirect('admin');
        } else {
            $this->session->set_flashdata('error', 'Usuário ou senha inválidos');
            redirect('admin/login');
        }
    }

    public function logout() {
        $this->session->unset_userdata(['admin_logged_in', 'admin_id', 'admin_username', 'admin_name']);
        $this->session->set_flashdata('success', 'Logout realizado com sucesso');
        redirect('admin/login');
    }

    // Agents Management
    public function agents() {
        $data['title'] = 'Gerenciar Agentes';
        $data['agents'] = $this->Agent_model->get_all_agents();
        
        $this->load->view('admin/header', $data);
        $this->load->view('admin/agents', $data);
        $this->load->view('admin/footer');
    }

    public function create_agent() {
        if ($this->input->method() == 'post') {
            $agent_data = [
                'agentCode' => $this->input->post('agentCode'),
                'token' => bin2hex(random_bytes(32)),
                'agentName' => $this->input->post('agent_name'),
                'email' => $this->input->post('email'),
                'password' => password_hash($this->input->post('password'), PASSWORD_DEFAULT),
                'balance' => $this->input->post('balance'),
                'currency' => $this->input->post('currency'),
                'agentType' => $this->input->post('type'),
                'rtpgeral' => $this->input->post('rtpgeral'),
                'status' => 1,
                'secretKey' => bin2hex(random_bytes(16)),
                'siteEndPoint' => base_url(),
                'ipAddress' => $this->input->ip_address(),
                'parentPath' => '.',
                'zeroSetting' => 'default',
                'lang' => 'pt',
                'createdAt' => date('Y-m-d H:i:s'),
                'updatedAt' => date('Y-m-d H:i:s')
            ];
            
            if ($this->Agent_model->create_agent($agent_data)) {
                $this->session->set_flashdata('success', 'Agente criado com sucesso');
            } else {
                $this->session->set_flashdata('error', 'Erro ao criar agente');
            }
            
            redirect('admin/agents');
        }
        
        $data['title'] = 'Criar Agente';
        $this->load->view('admin/header', $data);
        $this->load->view('admin/create_agent', $data);
        $this->load->view('admin/footer');
    }

    public function edit_agent($id) {
        $data['agent'] = $this->Agent_model->get_agent_by_id($id);
        
        if (!$data['agent']) {
            show_404();
        }
        
        if ($this->input->method() == 'post') {
            $agent_data = [
                'agentName' => $this->input->post('agent_name'),
                'email' => $this->input->post('email'),
                'balance' => $this->input->post('balance'),
                'currency' => $this->input->post('currency'),
                'agentType' => $this->input->post('type'),
                'rtpgeral' => $this->input->post('rtpgeral'),
                'status' => $this->input->post('status'),
                'updatedAt' => date('Y-m-d H:i:s')
            ];
            
            if ($this->input->post('password')) {
                $agent_data['password'] = password_hash($this->input->post('password'), PASSWORD_DEFAULT);
            }
            
            if ($this->Agent_model->update_agent($id, $agent_data)) {
                $this->session->set_flashdata('success', 'Agente atualizado com sucesso');
            } else {
                $this->session->set_flashdata('error', 'Erro ao atualizar agente');
            }
            
            redirect('admin/agents');
        }
        
        $data['title'] = 'Editar Agente';
        $this->load->view('admin/header', $data);
        $this->load->view('admin/edit_agent', $data);
        $this->load->view('admin/footer');
    }

    // Games Management
    public function games() {
        $data['title'] = 'Gerenciar Jogos';
        $data['games'] = $this->Game_model->get_all_games();
        $data['providers'] = $this->Game_model->get_providers();
        
        $this->load->view('admin/header', $data);
        $this->load->view('admin/games', $data);
        $this->load->view('admin/footer');
    }

    public function edit_game($id) {
        $data['game'] = $this->Game_model->get_game_by_id($id);
        $data['providers'] = $this->Game_model->get_providers();
        
        if (!$data['game']) {
            show_404();
        }
        
        if ($this->input->method() == 'post') {
            $game_data = [
                'game_name' => $this->input->post('game_name'),
                'provider' => $this->input->post('provider'),
                'rtp' => $this->input->post('rtp'),
                'min_bet' => $this->input->post('min_bet'),
                'max_bet' => $this->input->post('max_bet'),
                'lines' => $this->input->post('lines'),
                'status' => $this->input->post('status')
            ];
            
            if ($this->Game_model->update_game($id, $game_data)) {
                $this->session->set_flashdata('success', 'Jogo atualizado com sucesso');
            } else {
                $this->session->set_flashdata('error', 'Erro ao atualizar jogo');
            }
            
            redirect('admin/games');
        }
        
        $data['title'] = 'Editar Jogo';
        $this->load->view('admin/header', $data);
        $this->load->view('admin/edit_game', $data);
        $this->load->view('admin/footer');
    }

    // Users Management
    public function users() {
        $data['title'] = 'Gerenciar Usuários';
        $data['users'] = $this->User_model->get_all_users_with_agents();
        
        $this->load->view('admin/header', $data);
        $this->load->view('admin/users', $data);
        $this->load->view('admin/footer');
    }

    // Reports
    public function reports() {
        $data['title'] = 'Relatórios';
        $data['daily_stats'] = $this->Admin_model->get_daily_stats();
        $data['monthly_stats'] = $this->Admin_model->get_monthly_stats();
        $data['agent_stats'] = $this->Admin_model->get_agent_stats();
        
        $this->load->view('admin/header', $data);
        $this->load->view('admin/reports', $data);
        $this->load->view('admin/footer');
    }

    // API Documentation
    public function api_docs() {
        $data['title'] = 'Documentação da API';
        
        $this->load->view('admin/header', $data);
        $this->load->view('admin/api_docs', $data);
        $this->load->view('admin/footer');
    }

    // System Settings
    public function settings() {
        if ($this->input->method() == 'post') {
            $settings = $this->input->post();
            
            foreach ($settings as $key => $value) {
                $this->Admin_model->update_setting($key, $value);
            }
            
            $this->session->set_flashdata('success', 'Configurações atualizadas com sucesso');
            redirect('admin/settings');
        }
        
        $data['title'] = 'Configurações do Sistema';
        $data['settings'] = $this->Admin_model->get_all_settings();
        
        $this->load->view('admin/header', $data);
        $this->load->view('admin/settings', $data);
        $this->load->view('admin/footer');
    }

    // RTP Management
    public function rtp_management() {
        $data['title'] = 'Gerenciar RTP';
        $data['agent_configs'] = $this->Admin_model->get_agent_game_configs();
        $data['agents'] = $this->Agent_model->get_all_agents();
        $data['games'] = $this->Game_model->get_all_games();
        
        $this->load->view('admin/header', $data);
        $this->load->view('admin/rtp_management', $data);
        $this->load->view('admin/footer');
    }

    public function update_agent_rtp() {
        $agent_code = $this->input->post('agent_code');
        $game_code = $this->input->post('game_code');
        $custom_rtp = $this->input->post('custom_rtp');
        $custom_min_bet = $this->input->post('custom_min_bet');
        $custom_max_bet = $this->input->post('custom_max_bet');
        
        $config_data = [
            'agentCode' => $agent_code,
            'game_code' => $game_code,
            'custom_rtp' => $custom_rtp,
            'custom_min_bet' => $custom_min_bet,
            'custom_max_bet' => $custom_max_bet
        ];
        
        if ($this->Admin_model->update_agent_game_config($config_data)) {
            echo json_encode(['success' => true, 'message' => 'RTP atualizado com sucesso']);
        } else {
            echo json_encode(['success' => false, 'message' => 'Erro ao atualizar RTP']);
        }
    }
}
