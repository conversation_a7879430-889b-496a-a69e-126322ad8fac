{"resources": [{"type": "GameObject", "id": "66afefc1090205e48bfbc3347fe2edbb", "data": {"root": [{"name": "ru_desktop", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 0}, "children": [{"fileID": 2275, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, {"fileID": 2276, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, {"fileID": 2277, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, {"fileID": 2278, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, {"fileID": 2279, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, {"fileID": 2280, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, {"fileID": 2281, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, {"fileID": 2282, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, {"fileID": 2283, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, {"fileID": 2284, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, {"fileID": 2285, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, {"fileID": 2286, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, {"fileID": 2287, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, {"fileID": 2288, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, {"fileID": 2289, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, {"fileID": 2290, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, {"fileID": 2291, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, {"fileID": 2292, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, {"fileID": 2293, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, {"fileID": 2294, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, {"fileID": 2295, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, {"fileID": 2296, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, {"fileID": 2297, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, {"fileID": 2298, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, {"fileID": 2299, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, {"fileID": 2300, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, {"fileID": 2301, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, {"fileID": 2302, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, {"fileID": 2303, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, {"fileID": 2304, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, {"fileID": 2305, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, {"fileID": 2306, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, {"fileID": 2307, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, {"fileID": 2308, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, {"fileID": 2309, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, {"fileID": 2310, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, {"fileID": 2311, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, {"fileID": 2312, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, {"fileID": 2313, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, {"fileID": 2314, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, {"fileID": 2315, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, {"fileID": 2316, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, {"fileID": 2317, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, {"fileID": 2318, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, {"fileID": 2319, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, {"fileID": 2320, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, {"fileID": 2321, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, {"fileID": 2322, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, {"fileID": 2323, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, {"fileID": 2324, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, {"fileID": 2325, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, {"fileID": 2326, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, {"fileID": 2327, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, {"fileID": 2328, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, {"fileID": 2329, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, {"fileID": 2330, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, {"fileID": 2331, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, {"fileID": 2332, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, {"fileID": 2333, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, {"fileID": 2334, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, {"fileID": 2335, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, {"fileID": 2336, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, {"fileID": 2337, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, {"fileID": 2338, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, {"fileID": 2339, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, {"fileID": 2340, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, {"fileID": 2341, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, {"fileID": 2342, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, {"fileID": 2343, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, {"fileID": 2344, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, {"fileID": 2345, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, {"fileID": 2346, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, {"fileID": 2347, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, {"fileID": 2348, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, {"fileID": 2349, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, {"fileID": 2350, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, {"fileID": 2351, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, {"fileID": 2352, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, {"fileID": 2353, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, {"fileID": 2354, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, {"fileID": 2355, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, {"fileID": 2356, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, {"fileID": 2357, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, {"fileID": 2358, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, {"fileID": 2359, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, {"fileID": 2360, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, {"fileID": 2361, "guid": "66afefc1090205e48bfbc3347fe2edbb"}], "s": "0"}, "fileID": 2362}, {"componentType": "ModificationsManager", "enabled": true, "serializableData": {"root": {"fileID": 0}, "EditMode": false, "Atlases": [], "Transforms": [{"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page1/Title/PaytableTitleLabel1", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page1/Rules/AllSymbolsPayLabel", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page1/ScatterHolder/SymbolScatter/Sprite", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0.52, "y": 0.52, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page1/ScatterHolder/DescriptionHolder/Label1", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -24, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page1/ScatterHolder/DescriptionHolder/Label2", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -35, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page1/WildHolder/SymbolScatter/Sprite", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0.25, "y": 0.25, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page1/WildHolder/DescriptionHolder/Label1", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -12.5, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page1/WildHolder/DescriptionHolder/Label2", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -50, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page1/WildHolder/DescriptionHolder/Label3", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -87.5, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/MoneySymbolHolder/TitleHolder/Title", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 10, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/MoneySymbolHolder/DescriptionHolder/MoneySymbolHolder/Sprite", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0.7, "y": 0.7, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/MoneySymbolHolder/DescriptionHolder/MoneySymbolRules/Label3", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FreeSpinsHolder1/TitleHolder/Title", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/RuleHolder1/Rule1", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 16, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder1/LabelHolder1/Label1", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 27, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder1/LabelHolder2/Label2", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 27, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder1/LabelHolder3/Label3", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 27, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/RuleHolder2/Rule2", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 38, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder2/LabelHolder1/Label1", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 38, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder2/LabelHolder2/Label2", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 32, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/RuleHolder3/Rule3", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 38, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder1/Label1", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 22, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder2/Label2", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder3/Label3", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -13, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder4/Label4", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -13, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder5/Label5", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -13, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder1/Rule1", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 6, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder2/Rule2", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 6.9, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder3/Rule3", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder4/Rule4", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -16, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder5/Rule5", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -8, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder6/Rule6", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -10, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder7/Rule7", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -14, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder8/Rule8", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -26, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/FreeSpinsHolder2/SpecialReelsHolder/SpecialReels", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -34, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/MaxWin/TitleHolder/Title", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/MaxWin/RuleHolder/Label1", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/CAT/TitleHolder/Title1New", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 13, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/CAT/RuleHolder1/Rule1", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/CAT/RuleHolder2/Rule2", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/CAT/RuleHolder3/Rule3", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page4/Title/PaytableTitleLabel", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page4/Volatility/VolatilityDescription", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -30, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page4/RulesTop/AllSymbolsPay", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 50, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page4/RulesTop/AllWinsMultiplied", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 21, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page4/RulesTop/AllValuesExpressed", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -45, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page4/RulesTop/OnlyTheHighestWin", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -65, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page4/RulesTop/WhenWinningOnMultiplePaylines", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -95, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page4/Lines/Sprite", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page4/RulesBottom/SpaceAndEnter", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 92, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page4/RulesBottom/RTP", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page4/RulesBottom/MalfunctionLabel", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -97, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page4/MinMaxHolder", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -185, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/MoneySymbolHolder/DescriptionHolder/PossibleValues/Label", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -12, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/MaxWin/RuleHolder/HolderLabelJackpot/Label1New", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}], "Labels": [{"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 2363, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "extraPayloads": [], "path": "Game/GamePivot/FSWONWindow/content/FreespinsWonWindow/LabelsAndSprites/Catches_label", "oldContent": "CATCHES", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 2364, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "extraPayloads": [], "path": "Game/GamePivot/FreeSpinsPurchase/BuyButtons/Options/Option_0/OptVisual/Texts/BoughtText/LandscapeText/label1", "oldContent": "ACTIVE", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 2365, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "extraPayloads": [], "path": "Game/GamePivot/FSWONWindow/PressAnywhere_Label/label", "oldContent": "Press anywhere to continue", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 2366, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "extraPayloads": [], "path": "Game/GamePivot/FSExtraWindow/content/PressAnywhere_Label/label", "oldContent": "Press anywhere to continue", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 2367, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "extraPayloads": [], "path": "Game/GamePivot/FreeSpinsPurchase/FSPurchaseWindow/Content/AnimatedPivot/Texts/BuyText/label", "oldContent": "BUY FREE SPINS", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 2368, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "extraPayloads": [], "path": "Game/GamePivot/FsFeature/content/Feature_trail/Holder/fisherman_highlight/ScalerX10/stretcher_fs/spins", "oldContent": "spins", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 2369, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "extraPayloads": [], "path": "Game/Background/PaytableOnScreen/Portrait/Message1/labelMsg1", "oldContent": "AL<PERSON> SYMBOLS PAY FROM LEFT TO RIGHT. BONUS PAYS ON ANY POSITION.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 2370, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "extraPayloads": [], "path": "Game/GamePivot/FsFeature/content/Feature_trail/Holder/fisherman_highlight/ScalerX3/stretcher_fs/free", "oldContent": "free", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 2371, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "extraPayloads": [], "path": "Game/GamePivot/Reels/ThePivot/BonusMessages/Fisherman/Labels/uilabel", "oldContent": "MORE FISHERMEN!", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 2372, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "extraPayloads": [], "path": "Game/GamePivot/FreeSpinsPurchase/BuyButtons/Options/Option_0/OptVisual/Texts/BoughtText/LandscapeText/label0", "oldContent": "FEATURE", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 2373, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "extraPayloads": [], "path": "Game/GamePivot/FreeSpinsPurchase/BuyButtons/Options/Option_0/OptVisual/Texts/BuyText/PortraitText/label0", "oldContent": "BUY FREE SPINS", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 2374, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "extraPayloads": [], "path": "Game/GamePivot/FsFeature/content/Feature_trail/Holder/fisherman_highlight/ScalerX2/stretcher_fs/free", "oldContent": "free", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 2375, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "extraPayloads": [], "path": "Game/GamePivot/FSResultWindow/content/SignPivot/FreespinsCongratsWindow/Labels/Congrats_label", "oldContent": "CONGRATULATIONS!", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 2376, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "extraPayloads": [], "path": "Game/GamePivot/FSExtraWindow/content/SignPivot/FreespinsExtraWindow/Labels/Congrats_label", "oldContent": "CONGRATULATIONS!", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 2377, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "extraPayloads": [], "path": "Game/GamePivot/FSExtraWindow/content/SignPivot/FreespinsExtraWindow/Labels/FreeSpins_label ", "oldContent": "FREE SPINS", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 2378, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "extraPayloads": [], "path": "Game/GamePivot/FSExtraWindow/content/SignPivot/FreespinsExtraWindow/Labels/AreNow_label", "oldContent": "ARE NOW", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 2379, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "extraPayloads": [], "path": "Game/GamePivot/FreeSpinsPurchase/BuyButtons/Options/Option_0/OptVisual/Texts/BuyText/LandscapeTest/label0", "oldContent": "BUY FREE SPINS", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 2380, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "extraPayloads": [], "path": "Game/GamePivot/Reels/ThePivot/BonusMessages/PlusFS/Labels/uilabel", "oldContent": "EXTRA FREE SPINS!", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 2381, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "extraPayloads": [], "path": "Game/GamePivot/FsFeature/content/Feature_trail/Holder/fisherman_highlight/ScalerX10/stretcher_fs/free", "oldContent": "free", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 2382, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "extraPayloads": [], "path": "Game/GamePivot/FSWONWindow/content/FreespinsWonWindow/LabelsAndSprites/Congratulations_label", "oldContent": "CONGRATULATIONS!", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 2383, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "extraPayloads": [], "path": "Game/GamePivot/Reels/ThePivot/BonusMessages/Fishes/Labels/uilabel", "oldContent": "MORE FISH!", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 2384, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "extraPayloads": [], "path": "Game/GamePivot/FreeSpinsPurchase/BuyButtons/Options/Option_0/OptVisual/Texts/BoughtText/PortraitText/label0", "oldContent": "FEATURE ACTIVE", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 2385, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "extraPayloads": [], "path": "Game/GamePivot/FSExtraWindow/content/SignPivot/FreespinsExtraWindow/Labels/Extra_label", "oldContent": "EXTRA", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 2386, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "extraPayloads": [], "path": "Game/GamePivot/FSResultWindow/content/SignPivot/FreespinsCongratsWindow/Labels/YouWon_label", "oldContent": "YOU HAVE WON", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 2387, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "extraPayloads": [], "path": "Game/GamePivot/FsFeature/content/Feature_trail/Holder/fisherman_highlight/ScalerX3/stretcher_fs/spins", "oldContent": "spins", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 2388, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "extraPayloads": [], "path": "Game/Background/PaytableOnScreen/Landscape/Message1/labelMsg1", "oldContent": "AL<PERSON> SYMBOLS PAY FROM LEFT TO RIGHT. BONUS PAYS ON ANY POSITION.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 2389, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "extraPayloads": [], "path": "Game/GamePivot/FsFeature/content/Feature_trail/Holder/fisherman_highlight/ScalerX2/stretcher_fs/spins", "oldContent": "spins", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 2390, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "extraPayloads": [], "path": "Game/GamePivot/Reels/ThePivot/BonusMessages/Level2/Labels/uilabel", "oldContent": "START FROM LEVEL 2!", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 2391, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "extraPayloads": [], "path": "Game/GamePivot/FSWONWindow/content/FreespinsWonWindow/LabelsAndSprites/FreeSpins_label", "oldContent": "FREE SPINS!", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 2392, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "extraPayloads": [], "path": "Game/GamePivot/FSResultWindow/content/PressAnywhere_Label/label", "oldContent": "Press anywhere to continue", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 2393, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "extraPayloads": [], "path": "Game/GamePivot/Reels/ThePivot/BonusMessages/Hooks/Labels/uilabel", "oldContent": "MORE HOOKS AND EXPLOSIONS!", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 2394, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/CAT/RuleHolder2/Rule2", "oldContent": "When buying the FREE SPINS round, on the triggering spin 3, 4 or 5 SCATTERS can hit randomly.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 2395, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder4/Label4", "oldContent": "- START FROM LEVEL 2 - The round starts from level 2 in the progressive feature.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 2396, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder5/Label5", "oldContent": "- +2 SPINS - The subsequent round starts with 2 more free spins from the beginning and 2 more spins are added to every retrigger.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 2397, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/RuleHolder1/Rule1", "oldContent": "Hit 3 or more SCATTER symbols to trigger the FREE SPINS feature.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 2398, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/RulesTop/AllValuesExpressed", "oldContent": "All values are expressed as actual wins in coins.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 2399, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/Volatility/VolatilityDescription", "oldContent": "High volatility games pay out less often on average but the chance to hit big wins in a short time span is higher", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 2400, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/MoneySymbolHolder/DescriptionHolder/MoneySymbolRules/Label3", "oldContent": "The fish paying symbols are also MONEY symbols. At every spin, the fish take a random money value which can be won during the FREE SPINS feature.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 2401, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/RulesBottom/RTP/TheoreticalRTP/Label", "oldContent": "The theoretical RTP of this game is {0}%", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 2402, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder8/Rule8", "oldContent": "Also randomly, when there are fisherman symbols on the screen but no fish, at the end of a free spin, a bazooka animation can appear and change all the symbols from the screen, except for fisherman symbols to something else.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 2403, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "extraPayloads": [], "path": "Paytable/Pages/Page1/Rules/AllSymbolsPayLabel", "oldContent": "All symbols pay from left to right on adjacent reels starting from the leftmost reel.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 2404, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder5/Rule5", "oldContent": "After the fourth level, the feature cannot be retriggered anymore.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 2405, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/FreeSpinsHolder2/SpecialReelsHolder/SpecialReels", "oldContent": "Special reels are in play during the feature.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 2406, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/CAT/RuleHolder1/Rule1", "oldContent": "The FREE SPINS round can be instantly triggered from the base game by buying it for 100x current total bet.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 2407, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/RuleHolder2/Rule2", "oldContent": "In the base game whenever 2 SCATTER symbols hit without a third, there is a chance for another one to be brought onto the screen by a random feature:", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 2408, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder1/Label1", "oldContent": "- MORE FISH - More fish symbols are present on the reel strips during the subsequent free spins round", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 2409, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder2/Rule2", "oldContent": "All the WILD symbols that hit during the feature are collected until the end of the round.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 2410, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/RulesTop/AllSymbolsPay", "oldContent": "All symbols pay from left to right on selected paylines.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 2411, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder3/Label3", "oldContent": "- MORE DYNAMITES, HOOKS AND BAZOOKAS - During the round, the chance to hit dynamite, hook or bazooka spin feature is increased.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 2412, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/RulesBottom/SpaceAndEnter", "oldContent": "SPACE and ENTER buttons on the keyboard can be used to start and stop the spin.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 2413, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder7/Rule7", "oldContent": "Randomly, when there are fish symbols on the screen but no fisherman, at the end of a free spin, a hook will appear pulling a random reel up to bring fisherman symbols onto the screen.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 2414, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder1/Rule1", "oldContent": "During the FREE SPINS feature each WILD symbol also collects all the values from MONEY symbols on the screen.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 2415, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/MaxWin/TitleHolder/Title", "oldContent": "MAX WIN", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 2416, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder1/LabelHolder2/Label2", "oldContent": "4x SCATTER awards 15 free spins", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 2417, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/MinMaxHolder/MaxBet/MaximumText", "oldContent": "MAXIMUM BET:", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 2418, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FreeSpinsHolder1/TitleHolder/Title", "oldContent": "FREE SPINS", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 2419, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/MoneySymbolHolder/TitleHolder/Title", "oldContent": "MONEY SYMBOL", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 2420, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "extraPayloads": [], "path": "Paytable/Pages/Page1/WildHolder/DescriptionHolder/Label1", "oldContent": "This is the WILD symbol.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 2421, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "extraPayloads": [], "path": "Paytable/Pages/Page1/Title/PaytableTitleLabel1", "oldContent": "GAME RULES", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 2422, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/CAT/TitleHolder/Title1New", "oldContent": "BUY FREE SPINS", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 2423, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/MinMaxHolder/MinBet/MinimumText", "oldContent": "MINIMUM BET:", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 2424, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/RulesBottom/MalfunctionLabel", "oldContent": "Malfunction voids all pays and plays.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 2425, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "extraPayloads": [], "path": "Paytable/Pages/Page1/ScatterHolder/DescriptionHolder/Label2", "oldContent": "It appears on all reels.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 2426, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder1/LabelHolder1/Label1", "oldContent": "5x SCATTER awards 20 free spins", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 2427, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/RulesTop/AllWinsMultiplied", "oldContent": "All wins are multiplied by bet per line.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 2428, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "extraPayloads": [], "path": "Paytable/Pages/Page1/ScatterHolder/DescriptionHolder/Label1", "oldContent": "This is the SCATTER symbol.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 2429, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/RulesTop/OnlyTheHighestWin", "oldContent": "Only the highest win is paid per line.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 2430, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder1/LabelHolder3/Label3", "oldContent": "3x SCATTER awards 10 free spins", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 2431, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder2/LabelHolder1/Label1", "oldContent": "- Randomly, if the SCATTERS on the screen can move down one position without leaving the reel area, a respin is triggered where the reels with SCATTERS move one position down and the reels without SCATTERS respin.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 2432, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder4/Rule4", "oldContent": "The retriggered spins are played after the previous batch of free spins ends. The multiplier applies to the retriggered spins.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 2433, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/RulesTop/WhenWinningOnMultiplePaylines", "oldContent": "When winning on multiple paylines, all wins are added to the total win.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 2434, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/Title/PaytableTitleLabel", "oldContent": "GAME RULES", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 2435, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/MaxWin/RuleHolder/Label1", "oldContent": "The maximum win amount is limited to {0}x bet. If the total win of a FREE SPINS ROUND reaches {1}x the round immediately ends, win is awarded and all remaining free spins are forfeited", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 2436, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/RulesBottom/RTP/TheoreticalRTPBONUS/Label", "oldContent": "The RTP of the game when using \"BUY FREE SPINS\" is {0}%", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 2437, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/Volatility/VolatilityMeter/LabelHolder/VolatilityLabel", "oldContent": "VOLATILITY", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 2438, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder6/Rule6", "oldContent": "Randomly, when there are fisherman symbols on the screen but no fish, at the end of a free spin, fish MONEY symbols can appear in random positions via the dynamite spin feature.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 2439, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder2/LabelHolder2/Label2", "oldContent": "- Randomly, a hook can pull one of the reels up to reveal another SCATTER.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 2440, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/RuleHolder3/Rule3", "oldContent": "Before the round starts, 0 to 5 modifiers that apply to the subsequent round are randomly selected:", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 2441, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "extraPayloads": [], "path": "Paytable/Pages/Page1/WildHolder/DescriptionHolder/Label2", "oldContent": "It appears on all reels during the FREE SPINS round.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 2442, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "extraPayloads": [], "path": "Paytable/Pages/Page1/WildHolder/DescriptionHolder/Label3", "oldContent": "Substitutes for all symbols except SCATTER.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 2443, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder3/Rule3", "oldContent": "Every 4th WILD symbol collected retriggers the feature, awards 10 more free spins and the multiplier for MONEY symbol collection increases to 2x for the second level, 3x for the third level and 10x for the fourth level.  ", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 2444, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder2/Label2", "oldContent": "- MORE FISHERMAN - More WILD symbols are present on the reel strips during the subsequent free spins round", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 2445, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "extraPayloads": [], "path": "IntroScreen/content/Labels_Holder_landscape/Label_Holder_bigger_1/Label_2 (1)", "oldContent": "BIG FREE SPINS MODIFIERS!", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 2446, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "extraPayloads": [], "path": "IntroScreen/content/Labels_Holder_landscape/Label_Holder_bigger_1/Label_1", "oldContent": "GO FISHIN' FOR", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 2447, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "extraPayloads": [], "path": "IntroScreen/content/IntroButtons/ButtonSkipIntro/content/TextHolder/Label_1", "oldContent": "DON'T SHOW NEXT TIME", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 2448, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/MoneySymbolHolder/DescriptionHolder/PossibleValues/Label", "oldContent": "Possible values are: 2x, 5x, 10x, 15x, 20x, 25x, 50x, 100x, 200x, 500x, 1666x, 2500x or 5000x total bet.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 2449, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/MaxWin/RuleHolder/HolderLabelJackpot/Label1New", "oldContent": "The maximum win amount is limited to {0}x bet except Jack<PERSON>. If the total win of a FREE SPINS round reaches {1}x bet the round immediately ends, win is awarded and all remaining free spins are forfeited.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}], "Spines": [], "revisionNumber": 0}, "fileID": 2450}], "fileID": 2451}, {"name": "Game/GamePivot/FSWONWindow/content/FreespinsWonWindow/LabelsAndSprites/Catches_label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 2362, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "children": [], "psr": "d"}, "fileID": 2275}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "fb2f9c6dd4b03f04582bf095d79ab93a"}, "_text": "ЛОВИТ", "fontSize": 50, "width": 338, "height": 110, "overflow": 0}, "fileID": 2363}], "fileID": 2452}, {"name": "Game/GamePivot/FreeSpinsPurchase/BuyButtons/Options/Option_0/OptVisual/Texts/BoughtText/LandscapeText/label1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 2362, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "children": [], "psr": "d"}, "fileID": 2276}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "fb2f9c6dd4b03f04582bf095d79ab93a"}, "_text": "АКТИВНА", "fontSize": 40, "width": 170, "height": 26, "overflow": 0}, "fileID": 2364}], "fileID": 2453}, {"name": "Game/GamePivot/FSWONWindow/PressAnywhere_Label/label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 2362, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "children": [], "psr": "d"}, "fileID": 2277}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "fb2f9c6dd4b03f04582bf095d79ab93a"}, "_text": "Нажмите в любом месте, чтобы продолжить", "fontSize": 40, "width": 1340, "height": 65, "overflow": 0}, "fileID": 2365}], "fileID": 2454}, {"name": "Game/GamePivot/FSExtraWindow/content/PressAnywhere_Label/label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 2362, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "children": [], "psr": "d"}, "fileID": 2278}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "fb2f9c6dd4b03f04582bf095d79ab93a"}, "_text": "Нажмите в любом месте, чтобы продолжить", "fontSize": 40, "width": 1340, "height": 65, "overflow": 0}, "fileID": 2366}], "fileID": 2455}, {"name": "Game/GamePivot/FreeSpinsPurchase/FSPurchaseWindow/Content/AnimatedPivot/Texts/BuyText/label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 2362, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "children": [], "psr": "d"}, "fileID": 2279}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "fb2f9c6dd4b03f04582bf095d79ab93a"}, "_text": "КУПИТЬ БЕСПЛАТНЫЕ СПИНЫ", "fontSize": 69, "width": 826, "height": 69, "overflow": 0}, "fileID": 2367}], "fileID": 2456}, {"name": "Game/GamePivot/FsFeature/content/Feature_trail/Holder/fisherman_highlight/ScalerX10/stretcher_fs/spins", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 2362, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "children": [], "psr": "d"}, "fileID": 2280}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "fb2f9c6dd4b03f04582bf095d79ab93a"}, "_text": "спины", "fontSize": 30, "width": 64, "height": 33, "overflow": 0}, "fileID": 2368}], "fileID": 2457}, {"name": "Game/Background/PaytableOnScreen/Portrait/Message1/labelMsg1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 2362, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "children": [], "psr": "d"}, "fileID": 2281}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "fb2f9c6dd4b03f04582bf095d79ab93a"}, "_text": "ВСЕ СИМВОЛЫ ПЛАТЯТ СЛЕВА НАПРАВО. СИМВОЛ BONUS ПЛАТИТ В ЛЮБОЙ ПОЗИЦИИ.", "fontSize": 40, "width": 1756, "height": 40, "alignment": 2}, "fileID": 2369}], "fileID": 2458}, {"name": "Game/GamePivot/FsFeature/content/Feature_trail/Holder/fisherman_highlight/ScalerX3/stretcher_fs/free", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 2362, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "children": [], "psr": "d"}, "fileID": 2282}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "fb2f9c6dd4b03f04582bf095d79ab93a"}, "_text": "бесплатные", "fontSize": 30, "width": 60, "height": 33, "overflow": 0}, "fileID": 2370}], "fileID": 2459}, {"name": "Game/GamePivot/Reels/ThePivot/BonusMessages/Fisherman/Labels/uilabel", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 2362, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "children": [], "psr": "d"}, "fileID": 2283}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "fb2f9c6dd4b03f04582bf095d79ab93a"}, "_text": "БОЛЬШЕ РЫБАКОВ!", "fontSize": 256, "width": 1000, "height": 450, "overflow": 0}, "fileID": 2371}], "fileID": 2460}, {"name": "Game/GamePivot/FreeSpinsPurchase/BuyButtons/Options/Option_0/OptVisual/Texts/BoughtText/LandscapeText/label0", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 2362, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "children": [], "psr": "d"}, "fileID": 2284}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "fb2f9c6dd4b03f04582bf095d79ab93a"}, "_text": "ФУНКЦИЯ", "fontSize": 40, "width": 170, "height": 27, "overflow": 0}, "fileID": 2372}], "fileID": 2461}, {"name": "Game/GamePivot/FreeSpinsPurchase/BuyButtons/Options/Option_0/OptVisual/Texts/BuyText/PortraitText/label0", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 2362, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "children": [], "psr": "d"}, "fileID": 2285}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "fb2f9c6dd4b03f04582bf095d79ab93a"}, "_text": "КУПИТЬ БЕСПЛАТНЫЕ СПИНЫ", "fontSize": 26, "width": 270, "height": 70, "overflow": 0}, "fileID": 2373}], "fileID": 2462}, {"name": "Game/GamePivot/FsFeature/content/Feature_trail/Holder/fisherman_highlight/ScalerX2/stretcher_fs/free", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 2362, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "children": [], "psr": "d"}, "fileID": 2286}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "fb2f9c6dd4b03f04582bf095d79ab93a"}, "_text": "бесплатные", "fontSize": 30, "width": 60, "height": 33, "overflow": 0}, "fileID": 2374}], "fileID": 2463}, {"name": "Game/GamePivot/FSResultWindow/content/SignPivot/FreespinsCongratsWindow/Labels/Congrats_label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 2362, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "children": [], "psr": "d"}, "fileID": 2287}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "fb2f9c6dd4b03f04582bf095d79ab93a"}, "_text": "ПОЗДРАВЛЯЕМ!", "fontSize": 169, "width": 653, "height": 169, "overflow": 0}, "fileID": 2375}], "fileID": 2464}, {"name": "Game/GamePivot/FSExtraWindow/content/SignPivot/FreespinsExtraWindow/Labels/Congrats_label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 2362, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "children": [], "psr": "d"}, "fileID": 2288}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "fb2f9c6dd4b03f04582bf095d79ab93a"}, "_text": "ПОЗДРАВЛЯЕМ!", "fontSize": 169, "width": 629, "height": 169, "overflow": 0}, "fileID": 2376}], "fileID": 2465}, {"name": "Game/GamePivot/FSExtraWindow/content/SignPivot/FreespinsExtraWindow/Labels/FreeSpins_label ", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 2362, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "children": [], "psr": "d"}, "fileID": 2289}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "fb2f9c6dd4b03f04582bf095d79ab93a"}, "_text": "БЕСПЛАТНЫХ СПИНОВ", "fontSize": 75, "width": 547, "height": 71, "overflow": 0}, "fileID": 2377}], "fileID": 2466}, {"name": "Game/GamePivot/FSExtraWindow/content/SignPivot/FreespinsExtraWindow/Labels/AreNow_label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 2362, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "children": [], "psr": "d"}, "fileID": 2290}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "fb2f9c6dd4b03f04582bf095d79ab93a"}, "_text": "СЕЙЧАС", "fontSize": 70, "width": 500, "height": 169, "overflow": 0}, "fileID": 2378}], "fileID": 2467}, {"name": "Game/GamePivot/FreeSpinsPurchase/BuyButtons/Options/Option_0/OptVisual/Texts/BuyText/LandscapeTest/label0", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 2362, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "children": [], "psr": "d"}, "fileID": 2291}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "fb2f9c6dd4b03f04582bf095d79ab93a"}, "_text": "КУПИТЬ БЕСПЛАТНЫЕ СПИНЫ", "fontSize": 26, "width": 203, "height": 44, "overflow": 0}, "fileID": 2379}], "fileID": 2468}, {"name": "Game/GamePivot/Reels/ThePivot/BonusMessages/PlusFS/Labels/uilabel", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 2362, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "children": [], "psr": "d"}, "fileID": 2292}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "fb2f9c6dd4b03f04582bf095d79ab93a"}, "_text": "ЭКСТРА БЕСПЛАТНЫХ СПИНОВ", "fontSize": 256, "width": 1200, "height": 450, "overflow": 0}, "fileID": 2380}], "fileID": 2469}, {"name": "Game/GamePivot/FsFeature/content/Feature_trail/Holder/fisherman_highlight/ScalerX10/stretcher_fs/free", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 2362, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "children": [], "psr": "d"}, "fileID": 2293}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "fb2f9c6dd4b03f04582bf095d79ab93a"}, "_text": "бесплатные", "fontSize": 30, "width": 60, "height": 33, "overflow": 0}, "fileID": 2381}], "fileID": 2470}, {"name": "Game/GamePivot/FSWONWindow/content/FreespinsWonWindow/LabelsAndSprites/Congratulations_label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 2362, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "children": [], "psr": "d"}, "fileID": 2294}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "fb2f9c6dd4b03f04582bf095d79ab93a"}, "_text": "ПОЗДРАВЛЯЕМ!", "fontSize": 169, "width": 625, "height": 169, "overflow": 0}, "fileID": 2382}], "fileID": 2471}, {"name": "Game/GamePivot/Reels/ThePivot/BonusMessages/Fishes/Labels/uilabel", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 2362, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "children": [], "psr": "d"}, "fileID": 2295}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "fb2f9c6dd4b03f04582bf095d79ab93a"}, "_text": "БОЛЬШЕ РЫБЫ!", "fontSize": 256, "width": 1200, "height": 450, "overflow": 0}, "fileID": 2383}], "fileID": 2472}, {"name": "Game/GamePivot/FreeSpinsPurchase/BuyButtons/Options/Option_0/OptVisual/Texts/BoughtText/PortraitText/label0", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 2362, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "children": [], "psr": "d"}, "fileID": 2296}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "fb2f9c6dd4b03f04582bf095d79ab93a"}, "_text": "ФУНКЦИЯ АКТИВНА", "fontSize": 60, "width": 164, "height": 208, "overflow": 0}, "fileID": 2384}], "fileID": 2473}, {"name": "Game/GamePivot/FSExtraWindow/content/SignPivot/FreespinsExtraWindow/Labels/Extra_label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 2362, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "children": [], "psr": "d"}, "fileID": 2297}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "fb2f9c6dd4b03f04582bf095d79ab93a"}, "_text": "ЭКСТРА", "fontSize": 75, "width": 253, "height": 169, "overflow": 0}, "fileID": 2385}], "fileID": 2474}, {"name": "Game/GamePivot/FSResultWindow/content/SignPivot/FreespinsCongratsWindow/Labels/YouWon_label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 2362, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "children": [], "psr": "d"}, "fileID": 2298}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "fb2f9c6dd4b03f04582bf095d79ab93a"}, "_text": "ВЫ ВЫИГРАЛИ", "fontSize": 110, "width": 1000, "height": 169, "overflow": 0}, "fileID": 2386}], "fileID": 2475}, {"name": "Game/GamePivot/FsFeature/content/Feature_trail/Holder/fisherman_highlight/ScalerX3/stretcher_fs/spins", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 2362, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "children": [], "psr": "d"}, "fileID": 2299}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "fb2f9c6dd4b03f04582bf095d79ab93a"}, "_text": "спины", "fontSize": 30, "width": 64, "height": 33, "overflow": 0}, "fileID": 2387}], "fileID": 2476}, {"name": "Game/Background/PaytableOnScreen/Landscape/Message1/labelMsg1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 2362, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "children": [], "psr": "d"}, "fileID": 2300}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "fb2f9c6dd4b03f04582bf095d79ab93a"}, "_text": "ВСЕ СИМВОЛЫ ПЛАТЯТ СЛЕВА НАПРАВО. СИМВОЛ BONUS ПЛАТИТ В ЛЮБОЙ ПОЗИЦИИ.", "fontSize": 30, "width": 1322, "height": 30, "alignment": 2}, "fileID": 2388}], "fileID": 2477}, {"name": "Game/GamePivot/FsFeature/content/Feature_trail/Holder/fisherman_highlight/ScalerX2/stretcher_fs/spins", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 2362, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "children": [], "psr": "d"}, "fileID": 2301}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "fb2f9c6dd4b03f04582bf095d79ab93a"}, "_text": "спины", "fontSize": 30, "width": 64, "height": 33, "overflow": 0}, "fileID": 2389}], "fileID": 2478}, {"name": "Game/GamePivot/Reels/ThePivot/BonusMessages/Level2/Labels/uilabel", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 2362, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "children": [], "psr": "d"}, "fileID": 2302}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "fb2f9c6dd4b03f04582bf095d79ab93a"}, "_text": "НАЧАЛО С УРОВНЯ 2!", "fontSize": 256, "width": 1280, "height": 450, "overflow": 0}, "fileID": 2390}], "fileID": 2479}, {"name": "Game/GamePivot/FSWONWindow/content/FreespinsWonWindow/LabelsAndSprites/FreeSpins_label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 2362, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "children": [], "psr": "d"}, "fileID": 2303}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "fb2f9c6dd4b03f04582bf095d79ab93a"}, "_text": "БЕСПЛАТНЫХ СПИНОВ", "fontSize": 110, "width": 752, "height": 99, "overflow": 0}, "fileID": 2391}], "fileID": 2480}, {"name": "Game/GamePivot/FSResultWindow/content/PressAnywhere_Label/label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 2362, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "children": [], "psr": "d"}, "fileID": 2304}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "fb2f9c6dd4b03f04582bf095d79ab93a"}, "_text": "Нажмите в любом месте, чтобы продолжить", "fontSize": 40, "width": 1340, "height": 65, "overflow": 0}, "fileID": 2392}], "fileID": 2481}, {"name": "Game/GamePivot/Reels/ThePivot/BonusMessages/Hooks/Labels/uilabel", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 2362, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "children": [], "psr": "d"}, "fileID": 2305}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "fb2f9c6dd4b03f04582bf095d79ab93a"}, "_text": "БОЛЬШЕ КРЮЧКОВ И ВЗРЫВОВ!", "fontSize": 256, "width": 1000, "height": 450, "overflow": 0}, "fileID": 2393}], "fileID": 2482}, {"name": "Paytable/Pages/Page3/CAT/RuleHolder2/Rule2", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 2362, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "children": [], "psr": "d"}, "fileID": 2306}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "fb2f9c6dd4b03f04582bf095d79ab93a"}, "_text": "При покупке раунда БЕСПЛАТНЫХ СПИНОВ в запускающем спине произвольно может выпасть 3, 4 или 5 символов SCATTER.", "fontSize": 25, "anchorY": 0, "width": 1400, "height": 75, "overflow": 0, "spacingY": 5}, "fileID": 2394}], "fileID": 2483}, {"name": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder4/Label4", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 2362, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "children": [], "psr": "d"}, "fileID": 2307}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "fb2f9c6dd4b03f04582bf095d79ab93a"}, "_text": "НАЧАЛО С УРОВНЯ 2 – раунд начинается с уровня 2 прогрессивной функции.", "fontSize": 25, "anchorX": 0, "width": 1400, "height": 100, "overflow": 0}, "fileID": 2395}], "fileID": 2484}, {"name": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder5/Label5", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 2362, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "children": [], "psr": "d"}, "fileID": 2308}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "fb2f9c6dd4b03f04582bf095d79ab93a"}, "_text": "- +2 СПИНА – следующий раунд начнется с 2 дополнительными бесплатными спинами, которые будут присуждены с самого начала. Также игрок получит еще 2 спина за каждый перезапуск.", "fontSize": 25, "anchorX": 0, "width": 1347, "height": 100, "overflow": 0}, "fileID": 2396}], "fileID": 2485}, {"name": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/RuleHolder1/Rule1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 2362, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "children": [], "psr": "d"}, "fileID": 2309}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "fb2f9c6dd4b03f04582bf095d79ab93a"}, "_text": "Выбейте 3 или более символа SCATTER, чтобы активировать функцию БЕСПЛАТНЫХ СПИНОВ.", "fontSize": 25, "width": 1400, "height": 100, "overflow": 0}, "fileID": 2397}], "fileID": 2486}, {"name": "Paytable/Pages/Page4/RulesTop/AllValuesExpressed", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 2362, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "children": [], "psr": "d"}, "fileID": 2310}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "fb2f9c6dd4b03f04582bf095d79ab93a"}, "_text": "Bсе значения выражены как реальные выигрыши в монетах.", "fontSize": 25, "width": 1333, "height": 160, "overflow": 0}, "fileID": 2398}], "fileID": 2487}, {"name": "Paytable/Pages/Page4/Volatility/VolatilityDescription", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 2362, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "children": [], "psr": "d"}, "fileID": 2311}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "fb2f9c6dd4b03f04582bf095d79ab93a"}, "_text": "Игры с высокой волатильностью в среднем платят реже, но шанс получить большие выигрыши за короткий промежуток времени - выше.", "fontSize": 25, "anchorY": 0, "width": 1368, "height": 93, "overflow": 0}, "fileID": 2399}], "fileID": 2488}, {"name": "Paytable/Pages/Page2/MoneySymbolHolder/DescriptionHolder/MoneySymbolRules/Label3", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 2362, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "children": [], "psr": "d"}, "fileID": 2312}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "fb2f9c6dd4b03f04582bf095d79ab93a"}, "_text": "Оплачиваемые символы рыбы также являются ДЕНЕЖНЫМИ символами. При каждом спине рыба принимает случайное денежное значение, которое можно выиграть во время функции БЕСПЛАТНЫХ СПИНОВ.", "fontSize": 25, "anchorX": 0, "width": 980, "height": 100, "overflow": 0}, "fileID": 2400}], "fileID": 2489}, {"name": "Paytable/Pages/Page4/RulesBottom/RTP/TheoreticalRTP/Label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 2362, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "children": [], "psr": "d"}, "fileID": 2313}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "fb2f9c6dd4b03f04582bf095d79ab93a"}, "_text": "Теоретический процент возврата игроку в этой игре составляет  {0}%", "fontSize": 25, "anchorY": 1, "width": 1200, "height": 75, "overflow": 0}, "fileID": 2401}], "fileID": 2490}, {"name": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder8/Rule8", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 2362, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "children": [], "psr": "d"}, "fileID": 2314}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "fb2f9c6dd4b03f04582bf095d79ab93a"}, "_text": "Также случайным образом, когда на экране отображаются символы рыбака без рыбы, в конце бесплатного спина может произойти анимация с базукой, в результате которой все символы на экране, кроме рыбака, будут изменены.", "fontSize": 25, "width": 1400, "height": 100, "overflow": 0}, "fileID": 2402}], "fileID": 2491}, {"name": "Paytable/Pages/Page1/Rules/AllSymbolsPayLabel", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 2362, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "children": [], "psr": "d"}, "fileID": 2315}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "fb2f9c6dd4b03f04582bf095d79ab93a"}, "_text": "Все символы оплачиваются слева направо на смежных барабанах, начиная с крайнего левого барабана.", "fontSize": 25, "width": 1000, "height": 60, "overflow": 0}, "fileID": 2403}], "fileID": 2492}, {"name": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder5/Rule5", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 2362, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "children": [], "psr": "d"}, "fileID": 2316}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "fb2f9c6dd4b03f04582bf095d79ab93a"}, "_text": "После четвертого запуска уровня функция не может быть перезапущена.", "fontSize": 25, "width": 1400, "height": 100, "overflow": 0}, "fileID": 2404}], "fileID": 2493}, {"name": "Paytable/Pages/Page3/FreeSpinsHolder2/SpecialReelsHolder/SpecialReels", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 2362, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "children": [], "psr": "d"}, "fileID": 2317}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "fb2f9c6dd4b03f04582bf095d79ab93a"}, "_text": "Специальные барабаны участвуют в игре.", "fontSize": 25, "width": 1400, "height": 100, "overflow": 0}, "fileID": 2405}], "fileID": 2494}, {"name": "Paytable/Pages/Page3/CAT/RuleHolder1/Rule1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 2362, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "children": [], "psr": "d"}, "fileID": 2318}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "fb2f9c6dd4b03f04582bf095d79ab93a"}, "_text": "РАУНД БЕСПЛАТНЫХ СПИНОВ можно мгновенно запустить из основной игры, купив его за 100х от суммы текущей общей ставки. ", "fontSize": 25, "anchorY": 1, "width": 1400, "height": 100, "overflow": 0}, "fileID": 2406}], "fileID": 2495}, {"name": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/RuleHolder2/Rule2", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 2362, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "children": [], "psr": "d"}, "fileID": 2319}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "fb2f9c6dd4b03f04582bf095d79ab93a"}, "_text": "Каждый раз, когда 2 символа SCATTER выпадают без третьего символа в течение основной игры, существует вероятность того, что другой символ появится на экране в результате действия произвольной функции:", "fontSize": 25, "width": 1400, "height": 100, "overflow": 0}, "fileID": 2407}], "fileID": 2496}, {"name": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder1/Label1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 2362, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "children": [], "psr": "d"}, "fileID": 2320}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "fb2f9c6dd4b03f04582bf095d79ab93a"}, "_text": "- БОЛЬШЕ РЫБЫ – в течение следующего раунда бесплатных спинов на барабанах будет находиться больше символов рыбы.", "fontSize": 25, "anchorX": 0, "width": 1400, "height": 100, "overflow": 0}, "fileID": 2408}], "fileID": 2497}, {"name": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder2/Rule2", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 2362, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "children": [], "psr": "d"}, "fileID": 2321}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "fb2f9c6dd4b03f04582bf095d79ab93a"}, "_text": "Все символы WILD, выпавшие во время этой функции, собираются до конца раунда.", "fontSize": 25, "width": 1400, "height": 100, "overflow": 0}, "fileID": 2409}], "fileID": 2498}, {"name": "Paytable/Pages/Page4/RulesTop/AllSymbolsPay", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 2362, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "children": [], "psr": "d"}, "fileID": 2322}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "fb2f9c6dd4b03f04582bf095d79ab93a"}, "_text": "Все символы платят слева направо на выбранных платежных линиях.", "fontSize": 25, "width": 1194, "height": 160, "overflow": 0, "spacingY": 5}, "fileID": 2410}], "fileID": 2499}, {"name": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder3/Label3", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 2362, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "children": [], "psr": "d"}, "fileID": 2323}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "fb2f9c6dd4b03f04582bf095d79ab93a"}, "_text": "- БОЛЬШЕ ВЗРЫВЧАТКИ, КРЮЧКОВ И БАЗУК – в течение раунда увеличивается вероятность выпадения функции спина со взрывчаткой, крючком или базукой.", "fontSize": 25, "anchorX": 0, "width": 1359, "height": 100, "overflow": 0}, "fileID": 2411}], "fileID": 2500}, {"name": "Paytable/Pages/Page4/RulesBottom/SpaceAndEnter", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 2362, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "children": [], "psr": "d"}, "fileID": 2324}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "fb2f9c6dd4b03f04582bf095d79ab93a"}, "_text": "Клавиши ПРОБЕЛ и ВВОД на клавиатуре можно использовать для начала и остановки спина.", "fontSize": 25, "width": 1400, "height": 60, "overflow": 0}, "fileID": 2412}], "fileID": 2501}, {"name": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder7/Rule7", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 2362, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "children": [], "psr": "d"}, "fileID": 2325}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "fb2f9c6dd4b03f04582bf095d79ab93a"}, "_text": "Случайным образом, когда на экране отображается только символы рыбы без рыбака, в конце бесплатного спина появится крючок, который потянет произвольный барабан вверх и принесет на экран символы рыбака.", "fontSize": 25, "width": 1400, "height": 100, "overflow": 0}, "fileID": 2413}], "fileID": 2502}, {"name": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder1/Rule1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 2362, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "children": [], "psr": "d"}, "fileID": 2326}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "fb2f9c6dd4b03f04582bf095d79ab93a"}, "_text": "В режиме БЕСПЛАТНЫХ СПИНОВ каждый символ WILD также собирает все значения из ДЕНЕЖНЫХ символов на экране.", "fontSize": 25, "width": 1180, "height": 100, "overflow": 0}, "fileID": 2414}], "fileID": 2503}, {"name": "Paytable/Pages/Page3/MaxWin/TitleHolder/Title", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 2362, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "children": [], "psr": "d"}, "fileID": 2327}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "fb2f9c6dd4b03f04582bf095d79ab93a"}, "_text": "МАКСИМАЛЬНЫЙ ВЫИГРЫШ", "fontSize": 35, "width": 1100, "height": 100, "overflow": 0}, "fileID": 2415}], "fileID": 2504}, {"name": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder1/LabelHolder2/Label2", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 2362, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "children": [], "psr": "d"}, "fileID": 2328}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "fb2f9c6dd4b03f04582bf095d79ab93a"}, "_text": "4x символа SCATTER дают 15 бесплатных спинов.", "fontSize": 25, "width": 1400, "height": 75, "overflow": 0}, "fileID": 2416}], "fileID": 2505}, {"name": "Paytable/Pages/Page4/MinMaxHolder/MaxBet/MaximumText", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 2362, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "children": [], "psr": "d"}, "fileID": 2329}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "fb2f9c6dd4b03f04582bf095d79ab93a"}, "_text": "МАКСИМАЛЬНАЯ СТАВКА:", "fontSize": 25, "anchorX": 0, "width": 322, "height": 26}, "fileID": 2417}], "fileID": 2506}, {"name": "Paytable/Pages/Page2/FreeSpinsHolder1/TitleHolder/Title", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 2362, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "children": [], "psr": "d"}, "fileID": 2330}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "fb2f9c6dd4b03f04582bf095d79ab93a"}, "_text": "БЕСПЛАТНЫЕ СПИНЫ", "fontSize": 35, "width": 1150, "height": 100, "overflow": 0}, "fileID": 2418}], "fileID": 2507}, {"name": "Paytable/Pages/Page2/MoneySymbolHolder/TitleHolder/Title", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 2362, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "children": [], "psr": "d"}, "fileID": 2331}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "fb2f9c6dd4b03f04582bf095d79ab93a"}, "_text": "ДЕНЕЖНЫЙ СИМВОЛ", "fontSize": 35, "width": 1150, "height": 70, "overflow": 0}, "fileID": 2419}], "fileID": 2508}, {"name": "Paytable/Pages/Page1/WildHolder/DescriptionHolder/Label1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 2362, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "children": [], "psr": "d"}, "fileID": 2332}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "fb2f9c6dd4b03f04582bf095d79ab93a"}, "_text": "Это символ WILD.", "fontSize": 25, "anchorX": 0, "anchorY": 1, "width": 363, "height": 75, "overflow": 0}, "fileID": 2420}], "fileID": 2509}, {"name": "Paytable/Pages/Page1/Title/PaytableTitleLabel1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 2362, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "children": [], "psr": "d"}, "fileID": 2333}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "fb2f9c6dd4b03f04582bf095d79ab93a"}, "_text": "ПРАВИЛА ИГРЫ", "fontSize": 35, "width": 1150, "height": 100, "overflow": 0}, "fileID": 2421}], "fileID": 2510}, {"name": "Paytable/Pages/Page3/CAT/TitleHolder/Title1New", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 2362, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "children": [], "psr": "d"}, "fileID": 2334}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "fb2f9c6dd4b03f04582bf095d79ab93a"}, "_text": "ПОКУПКА БЕСПЛАТНЫХ СПИНОВ", "fontSize": 35, "width": 1400, "height": 70, "overflow": 0}, "fileID": 2422}], "fileID": 2511}, {"name": "Paytable/Pages/Page4/MinMaxHolder/MinBet/MinimumText", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 2362, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "children": [], "psr": "d"}, "fileID": 2335}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "fb2f9c6dd4b03f04582bf095d79ab93a"}, "_text": "МИНИМАЛЬНАЯ СТАВКА:", "fontSize": 25, "anchorX": 0, "width": 312, "height": 26}, "fileID": 2423}], "fileID": 2512}, {"name": "Paytable/Pages/Page4/RulesBottom/MalfunctionLabel", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 2362, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "children": [], "psr": "d"}, "fileID": 2336}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "fb2f9c6dd4b03f04582bf095d79ab93a"}, "_text": "Неисправность аннулирует все выплаты и игры.", "fontSize": 25, "width": 888, "height": 60, "overflow": 0}, "fileID": 2424}], "fileID": 2513}, {"name": "Paytable/Pages/Page1/ScatterHolder/DescriptionHolder/Label2", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 2362, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "children": [], "psr": "d"}, "fileID": 2337}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "fb2f9c6dd4b03f04582bf095d79ab93a"}, "_text": "Он появляется на всех барабанах.", "fontSize": 25, "anchorX": 0, "anchorY": 0, "width": 357, "height": 75, "overflow": 0}, "fileID": 2425}], "fileID": 2514}, {"name": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder1/LabelHolder1/Label1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 2362, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "children": [], "psr": "d"}, "fileID": 2338}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "fb2f9c6dd4b03f04582bf095d79ab93a"}, "_text": "5x символов SCATTER дают 20 бесплатных спинов.", "fontSize": 25, "width": 1400, "height": 75, "overflow": 0}, "fileID": 2426}], "fileID": 2515}, {"name": "Paytable/Pages/Page4/RulesTop/AllWinsMultiplied", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 2362, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "children": [], "psr": "d"}, "fileID": 2339}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "fb2f9c6dd4b03f04582bf095d79ab93a"}, "_text": "Все выигрыши умножаются на ставку по линии.", "fontSize": 25, "width": 1194, "height": 160, "overflow": 0, "spacingY": 5}, "fileID": 2427}], "fileID": 2516}, {"name": "Paytable/Pages/Page1/ScatterHolder/DescriptionHolder/Label1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 2362, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "children": [], "psr": "d"}, "fileID": 2340}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "fb2f9c6dd4b03f04582bf095d79ab93a"}, "_text": "Этот символ - SCATTER. ", "fontSize": 25, "anchorX": 0, "anchorY": 1, "width": 357, "height": 75, "overflow": 0}, "fileID": 2428}], "fileID": 2517}, {"name": "Paytable/Pages/Page4/RulesTop/OnlyTheHighestWin", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 2362, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "children": [], "psr": "d"}, "fileID": 2341}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "fb2f9c6dd4b03f04582bf095d79ab93a"}, "_text": "Только самый большой выигрыш выплачивается по линии.", "fontSize": 25, "anchorY": 0, "width": 1400, "height": 50, "overflow": 0}, "fileID": 2429}], "fileID": 2518}, {"name": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder1/LabelHolder3/Label3", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 2362, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "children": [], "psr": "d"}, "fileID": 2342}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "fb2f9c6dd4b03f04582bf095d79ab93a"}, "_text": "3x символа SCATTER дают 10 бесплатных спинов.", "fontSize": 25, "width": 1400, "height": 75, "overflow": 0}, "fileID": 2430}], "fileID": 2519}, {"name": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder2/LabelHolder1/Label1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 2362, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "children": [], "psr": "d"}, "fileID": 2343}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "fb2f9c6dd4b03f04582bf095d79ab93a"}, "_text": "- Если символы SCATTER на экране могут сместиться вниз на одну позицию, не покидая область барабанов, запускается повторный спин, в течение которого барабаны с символами SCATTER сдвигаются на одну позицию вниз, а барабаны, не содержащие символов SCATTER, вращаются повторно.", "fontSize": 25, "anchorX": 0, "width": 1344, "height": 125, "overflow": 0}, "fileID": 2431}], "fileID": 2520}, {"name": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder4/Rule4", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 2362, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "children": [], "psr": "d"}, "fileID": 2344}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "fb2f9c6dd4b03f04582bf095d79ab93a"}, "_text": "Повторно запущенные спины разыгрываются после завершения предыдущей партии бесплатных спинов. Множитель применяется к повторно запущенным спинам.", "fontSize": 25, "width": 1400, "height": 125, "overflow": 0}, "fileID": 2432}], "fileID": 2521}, {"name": "Paytable/Pages/Page4/RulesTop/WhenWinningOnMultiplePaylines", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 2362, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "children": [], "psr": "d"}, "fileID": 2345}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "fb2f9c6dd4b03f04582bf095d79ab93a"}, "_text": "При выигрыше на нескольких линиях, все выигрыши добавляются к общему выигрышу.", "fontSize": 25, "anchorY": 0, "width": 1400, "height": 50, "overflow": 0}, "fileID": 2433}], "fileID": 2522}, {"name": "Paytable/Pages/Page4/Title/PaytableTitleLabel", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 2362, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "children": [], "psr": "d"}, "fileID": 2346}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "fb2f9c6dd4b03f04582bf095d79ab93a"}, "_text": "ПРАВИЛА ИГРЫ", "fontSize": 30, "width": 1150, "height": 100, "overflow": 0}, "fileID": 2434}], "fileID": 2523}, {"name": "Paytable/Pages/Page3/MaxWin/RuleHolder/Label1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 2362, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "children": [], "psr": "d"}, "fileID": 2347}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "fb2f9c6dd4b03f04582bf095d79ab93a"}, "_text": "Максимальная сумма выигрыша ограничена ​​{0}x от суммы ставки. Если общий выигрыш за РАУНД БЕСПЛАТНЫХ СПИНОВ достигнет {1}x от суммы ставки, раунд немедленно заканчивается, выигрыш присуждается, а все оставшиеся бесплатные спины аннулируются.", "fontSize": 25, "anchorY": 0, "width": 1400, "height": 100, "overflow": 0}, "fileID": 2435}], "fileID": 2524}, {"name": "Paytable/Pages/Page4/RulesBottom/RTP/TheoreticalRTPBONUS/Label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 2362, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "children": [], "psr": "d"}, "fileID": 2348}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "fb2f9c6dd4b03f04582bf095d79ab93a"}, "_text": "Процент выплат игрокам в этой игре при использовании функции «КУПИТЬ БЕСПЛАТНЫЕ СПИНЫ» составляет {0}%", "fontSize": 25, "anchorY": 0, "width": 1200, "height": 75, "overflow": 0}, "fileID": 2436}], "fileID": 2525}, {"name": "Paytable/Pages/Page4/Volatility/VolatilityMeter/LabelHolder/VolatilityLabel", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 2362, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "children": [], "psr": "d"}, "fileID": 2349}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "fb2f9c6dd4b03f04582bf095d79ab93a"}, "_text": "ВОЛАТИЛЬНОСТЬ", "fontSize": 22, "anchorX": 0, "width": 194, "height": 22}, "fileID": 2437}], "fileID": 2526}, {"name": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder6/Rule6", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 2362, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "children": [], "psr": "d"}, "fileID": 2350}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "fb2f9c6dd4b03f04582bf095d79ab93a"}, "_text": "Случайным образом, когда на экране отображается только символы рыбака без рыбы, в конце бесплатного спина в результате функции взрывного спина в произвольных позициях могут появиться ДЕНЕЖНЫЕ символы с рыбой.", "fontSize": 25, "width": 1400, "height": 100, "overflow": 0}, "fileID": 2438}], "fileID": 2527}, {"name": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder2/LabelHolder2/Label2", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 2362, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "children": [], "psr": "d"}, "fileID": 2351}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "fb2f9c6dd4b03f04582bf095d79ab93a"}, "_text": "- Случайно крючок может потянуть один из барабанов вверх и открыть еще один символ SCATTER.", "fontSize": 25, "anchorX": 0, "width": 1321, "height": 75, "overflow": 0}, "fileID": 2439}], "fileID": 2528}, {"name": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/RuleHolder3/Rule3", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 2362, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "children": [], "psr": "d"}, "fileID": 2352}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "fb2f9c6dd4b03f04582bf095d79ab93a"}, "_text": "Перед началом раунда произвольно выбирается от 0 до 5 модификаторов, применяемых к следующему раунду:", "fontSize": 25, "width": 1235, "height": 84, "overflow": 0}, "fileID": 2440}], "fileID": 2529}, {"name": "Paytable/Pages/Page1/WildHolder/DescriptionHolder/Label2", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 2362, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "children": [], "psr": "d"}, "fileID": 2353}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "fb2f9c6dd4b03f04582bf095d79ab93a"}, "_text": "Он появляется на всех барабанах во время раунда БЕСПЛАТНЫХ СПИНОВ.", "fontSize": 25, "anchorX": 0, "width": 363, "height": 75, "overflow": 0}, "fileID": 2441}], "fileID": 2530}, {"name": "Paytable/Pages/Page1/WildHolder/DescriptionHolder/Label3", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 2362, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "children": [], "psr": "d"}, "fileID": 2354}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "fb2f9c6dd4b03f04582bf095d79ab93a"}, "_text": "Заменяет все символы, кроме символов SCATTER.", "fontSize": 25, "anchorX": 0, "anchorY": 0, "width": 363, "height": 75, "overflow": 0}, "fileID": 2442}], "fileID": 2531}, {"name": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder3/Rule3", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 2362, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "children": [], "psr": "d"}, "fileID": 2355}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "fb2f9c6dd4b03f04582bf095d79ab93a"}, "_text": "Каждый 4-й собранный символ WILD перезапускает функцию, а также присуждает еще 10 бесплатных спинов и множитель для сбора ДЕНЕЖНЫХ символов увеличивается в 2х раза для второго уровня, в 3х раза для третьего уровня и в 10х раз для четвертого уровня.", "fontSize": 25, "width": 1327, "height": 150, "overflow": 0}, "fileID": 2443}], "fileID": 2532}, {"name": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder2/Label2", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 2362, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "children": [], "psr": "d"}, "fileID": 2356}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "fb2f9c6dd4b03f04582bf095d79ab93a"}, "_text": "- БОЛЬШЕ РЫБАКОВ – в течение следующего раунда бесплатных спинов на барабанах будет находиться больше символов WILD.", "fontSize": 25, "anchorX": 0, "width": 1400, "height": 100, "overflow": 0}, "fileID": 2444}], "fileID": 2533}, {"name": "IntroScreen/content/Labels_Holder_landscape/Label_Holder_bigger_1/Label_2 (1)", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 2362, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "children": [], "psr": "d"}, "fileID": 2357}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "fb2f9c6dd4b03f04582bf095d79ab93a"}, "_text": "БОЛЬШИЕ МОДИФИКАТОРЫ  БЕСПЛАТНЫХ СПИНОВ!", "fontSize": 60, "width": 1000, "height": 80, "overflow": 0}, "fileID": 2445}], "fileID": 2534}, {"name": "IntroScreen/content/Labels_Holder_landscape/Label_Holder_bigger_1/Label_1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 2362, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "children": [], "psr": "d"}, "fileID": 2358}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "fb2f9c6dd4b03f04582bf095d79ab93a"}, "_text": "ОТПРАВЛЯЙТЕСЬ НА РЫБАЛКУ И ПОЙМАЙТЕ", "fontSize": 60, "width": 1000, "height": 80, "overflow": 0}, "fileID": 2446}], "fileID": 2535}, {"name": "IntroScreen/content/IntroButtons/ButtonSkipIntro/content/TextHolder/Label_1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 2362, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "children": [], "psr": "d"}, "fileID": 2359}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "fb2f9c6dd4b03f04582bf095d79ab93a"}, "_text": "НЕ ПОКАЗЫВАТЬ В СЛЕДУЮЩИЙ РАЗ", "fontSize": 30, "anchorX": 0, "width": 556, "height": 30}, "fileID": 2447}], "fileID": 2536}, {"name": "Paytable/Pages/Page2/MoneySymbolHolder/DescriptionHolder/PossibleValues/Label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 2362, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "children": [], "psr": "d"}, "fileID": 2360}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "fb2f9c6dd4b03f04582bf095d79ab93a"}, "_text": "Возможные значения:2x, 5x, 10x, 15x, 20x, 25x, 50x, 100x, 200x, 500x, 1666x, 2500x или 5000x от суммы основной ставки.", "fontSize": 25, "anchorX": 0, "width": 973, "height": 100, "overflow": 0}, "fileID": 2448}], "fileID": 2537}, {"name": "Paytable/Pages/Page3/MaxWin/RuleHolder/HolderLabelJackpot/Label1New", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 2362, "guid": "66afefc1090205e48bfbc3347fe2edbb"}, "children": [], "psr": "d"}, "fileID": 2361}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "fb2f9c6dd4b03f04582bf095d79ab93a"}, "_text": "Максимальная сумма выигрыша ограничена {0}x от суммы ставки, за исключением Джекпота. Если общий выигрыш в раунде БЕСПЛАТНЫХ ВРАЩЕНИЙ достигнет {1}x от суммы ставки, раунд немедленно заканчивается, выигрыш присуждается, а все оставшиеся бесплатные спины аннулируются.", "fontSize": 25, "anchorY": 0, "width": 1400, "height": 100, "overflow": 0}, "fileID": 2449}], "fileID": 2538}]}}, {"type": "Font", "id": "fb2f9c6dd4b03f04582bf095d79ab93a", "data": {"fontName": "ffb2f9c6dd4b03f04582bf095d79ab9", "path": "@font-face{font-family:'ffb2f9c6dd4b03f04582bf095d79ab9';src:url('data:application/x-font-woff;base64,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********************************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') format('woff')}"}}]}