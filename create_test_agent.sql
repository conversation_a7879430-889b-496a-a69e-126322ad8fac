USE game_platform;

-- Inserir agente de teste baseado na estrutura real
INSERT INTO agents (
    agentCode,
    agentName,
    password,
    token,
    balance,
    currency,
    agentType,
    rtpgeral,
    status,
    email,
    secretKey,
    siteEndPoint,
    ipAddress,
    parentPath,
    zeroSetting,
    lang,
    createdAt,
    updatedAt
) VALUES (
    'AGENT001',
    'Agente Teste',
    '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi',
    'test_token_123456789',
    1000.00,
    'BRL',
    1,
    95,
    1,
    '<EMAIL>',
    'secret123',
    'http://localhost',
    '127.0.0.1',
    '.',
    'default',
    'pt',
    NOW(),
    NOW()
) ON DUPLICATE KEY UPDATE
    agentName = 'Agente Teste',
    email = '<EMAIL>',
    password = '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi';

-- Verificar se foi inserido
SELECT agentCode, agentName, email, status FROM agents WHERE agentCode = 'AGENT001';
