<?php
// Test mod_rewrite functionality
echo "<h2>🔍 Mod_Rewrite Test</h2>";

// Test 1: Check if mod_rewrite is enabled
echo "<h3>1. Apache Modules Check</h3>";

if (function_exists('apache_get_modules')) {
    $modules = apache_get_modules();
    if (in_array('mod_rewrite', $modules)) {
        echo "<p style='color: green;'>✅ mod_rewrite is enabled</p>";
    } else {
        echo "<p style='color: red;'>❌ mod_rewrite is NOT enabled</p>";
    }
    
    echo "<p><strong>Loaded Apache modules:</strong></p>";
    echo "<ul>";
    foreach ($modules as $module) {
        echo "<li>$module</li>";
    }
    echo "</ul>";
} else {
    echo "<p style='color: orange;'>⚠️ Cannot check Apache modules (function not available)</p>";
}

// Test 2: Check .htaccess file
echo "<h3>2. .htaccess File Check</h3>";

if (file_exists('.htaccess')) {
    echo "<p style='color: green;'>✅ .htaccess file exists</p>";
    
    $htaccess_content = file_get_contents('.htaccess');
    echo "<p><strong>.htaccess content:</strong></p>";
    echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 5px;'>" . htmlspecialchars($htaccess_content) . "</pre>";
    
    if (strpos($htaccess_content, 'RewriteEngine On') !== false) {
        echo "<p style='color: green;'>✅ RewriteEngine is enabled in .htaccess</p>";
    } else {
        echo "<p style='color: red;'>❌ RewriteEngine is NOT enabled in .htaccess</p>";
    }
    
    if (strpos($htaccess_content, 'index.php') !== false) {
        echo "<p style='color: green;'>✅ Rewrite rules point to index.php</p>";
    } else {
        echo "<p style='color: red;'>❌ Rewrite rules do NOT point to index.php</p>";
    }
} else {
    echo "<p style='color: red;'>❌ .htaccess file does NOT exist</p>";
}

// Test 3: Check server variables
echo "<h3>3. Server Variables</h3>";

echo "<p><strong>REQUEST_URI:</strong> " . ($_SERVER['REQUEST_URI'] ?? 'Not set') . "</p>";
echo "<p><strong>SCRIPT_NAME:</strong> " . ($_SERVER['SCRIPT_NAME'] ?? 'Not set') . "</p>";
echo "<p><strong>PATH_INFO:</strong> " . ($_SERVER['PATH_INFO'] ?? 'Not set') . "</p>";
echo "<p><strong>QUERY_STRING:</strong> " . ($_SERVER['QUERY_STRING'] ?? 'Not set') . "</p>";
echo "<p><strong>HTTP_HOST:</strong> " . ($_SERVER['HTTP_HOST'] ?? 'Not set') . "</p>";
echo "<p><strong>SERVER_NAME:</strong> " . ($_SERVER['SERVER_NAME'] ?? 'Not set') . "</p>";

// Test 4: Test rewrite functionality
echo "<h3>4. Rewrite Test</h3>";

echo "<p>Current URL: " . (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http') . "://" . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'] . "</p>";

// Test URLs
echo "<p><strong>Test these URLs:</strong></p>";
echo "<ul>";
echo "<li><a href='http://localhost/admin' target='_blank'>http://localhost/admin</a> (should work with rewrite)</li>";
echo "<li><a href='http://localhost/index.php/admin' target='_blank'>http://localhost/index.php/admin</a> (should always work)</li>";
echo "<li><a href='http://localhost/admin/login' target='_blank'>http://localhost/admin/login</a> (should work with rewrite)</li>";
echo "<li><a href='http://localhost/index.php/admin/login' target='_blank'>http://localhost/index.php/admin/login</a> (should always work)</li>";
echo "</ul>";

// Test 5: Check if CodeIgniter is handling the request
echo "<h3>5. CodeIgniter Request Handling</h3>";

if (isset($_SERVER['REQUEST_URI'])) {
    $request_uri = $_SERVER['REQUEST_URI'];
    echo "<p><strong>Current request URI:</strong> $request_uri</p>";
    
    if (strpos($request_uri, 'index.php') !== false) {
        echo "<p style='color: green;'>✅ Request includes index.php (direct access)</p>";
    } else {
        echo "<p style='color: orange;'>⚠️ Request does NOT include index.php (relies on rewrite)</p>";
    }
    
    if (strpos($request_uri, 'admin') !== false) {
        echo "<p style='color: green;'>✅ Request is for admin section</p>";
    } else {
        echo "<p style='color: red;'>❌ Request is NOT for admin section</p>";
    }
}

// Test 6: Manual rewrite test
echo "<h3>6. Manual Rewrite Test</h3>";

echo "<p>If mod_rewrite is working, these should be equivalent:</p>";
echo "<ul>";
echo "<li>http://localhost/admin → http://localhost/index.php/admin</li>";
echo "<li>http://localhost/admin/login → http://localhost/index.php/admin/login</li>";
echo "</ul>";

echo "<p><strong>Test by clicking:</strong></p>";
echo "<p><a href='http://localhost/admin' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>Test /admin</a>";
echo "<a href='http://localhost/index.php/admin' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Test /index.php/admin</a></p>";

// Test 7: Check if there are any conflicting files
echo "<h3>7. Conflicting Files Check</h3>";

$potential_conflicts = ['admin.php', 'admin.html', 'admin/index.php', 'admin/index.html'];

foreach ($potential_conflicts as $file) {
    if (file_exists($file)) {
        echo "<p style='color: red;'>❌ Conflicting file found: $file</p>";
    } else {
        echo "<p style='color: green;'>✅ No conflict: $file does not exist</p>";
    }
}

echo "<hr>";
echo "<p style='color: #6c757d; font-size: 14px;'>This test helps identify mod_rewrite issues that might prevent clean URLs from working.</p>";
?>
