
},
{
"type":"GameObject",
"id":"1d40a48ed68d87942b431bdb48a772c4",
"data" : 
{
"root" : 
[
{
"name":"Sym04",
"activeSelf":true,
"layer":0,
"components" : 
[
{
"componentType":"Transform",
"enabled":true,
"serializableData" : 
{
"parent" : 
{
"fileID":0
},
"children" : 
[
],
"psr":"d"
},
"fileID":6116
},
{
"componentType":"UIAtlas",
"enabled":true,
"serializableData" : 
{
"textureContent" : 
{
"fileID":2800000,
"guid":"b2e70ce262bb51a4cbe6ae910b9eac44"
},
"spriteList" : 
{
"s_static_rod" : 
{
"width":317,
"height":227,
"paddingLeft":18,
"paddingTop":47,
"paddingRight":17,
"paddingBottom":47
}
}
},
"fileID":1750
}
],
"fileID":6117
}
]
}
},
{
"type":"GameObject",
"id":"8b6cd8747f289f64ba9ee93ade17467b",
"data" : 
{
"root" : 
[
{
"name":"BBBlizzard_Rod_Material",
"activeSelf":true,
"layer":0,
"components" : 
[
{
"componentType":"Transform",
"enabled":true,
"serializableData" : 
{
"parent" : 
{
"fileID":0
},
"children" : 
[
],
"psr":"d"
},
"fileID":6118
},
{
"componentType":"UIAtlas",
"enabled":true,
"serializableData" : 
{
"textureContent" : 
{
"fileID":2800000,
"guid":"85c4256c088628e45b4d8551b2bd0385"
},
"spriteList" : 
{
"s_ICE" : 
{
"rotate":1,
"y":166,
"width":159,
"height":499
},
"s_ICE_ADD" : 
{
"x":245,
"y":183,
"width":249,
"height":79
},
"s_ICE_FG" : 
{
"x":495,
"y":222,
"width":97,
"height":43
},
"s_Reel_Black" : 
{
"x":1729,
"y":43,
"width":148,
"height":154
},
"s_Reel_Glow" : 
{
"x":160,
"y":175,
"width":84,
"height":87
},
"s_reel_handleGlow" : 
{
"rotate":1,
"x":495,
"y":183,
"width":40,
"height":38
},
"s_reel_handleLongGlow" : 
{
"x":1573,
"y":11,
"width":39,
"height":44,
"paddingRight":1
},
"s_Reel1" : 
{
"rotate":1,
"x":1504,
"y":243,
"width":155,
"height":204
},
"s_Reel10" : 
{
"rotate":1,
"x":756,
"y":9,
"width":190,
"height":192
},
"s_Reel11" : 
{
"rotate":1,
"x":393,
"y":11,
"width":206,
"height":171
},
"s_Reel12" : 
{
"x":1266,
"y":3,
"width":150,
"height":218
},
"s_Reel13" : 
{
"x":1052,
"y":212,
"width":149,
"height":226
},
"s_Reel14" : 
{
"x":738,
"y":433,
"width":149,
"height":232
},
"s_Reel15" : 
{
"x":588,
"y":431,
"width":149,
"height":234
},
"s_Reel16" : 
{
"x":438,
"y":430,
"width":149,
"height":235
},
"s_Reel17" : 
{
"x":596,
"y":197,
"width":149,
"height":233
},
"s_Reel18" : 
{
"x":902,
"y":206,
"width":149,
"height":228
},
"s_Reel19" : 
{
"x":1356,
"y":448,
"width":153,
"height":217
},
"s_Reel2" : 
{
"rotate":1,
"x":1417,
"y":33,
"width":155,
"height":206
},
"s_Reel20" : 
{
"x":947,
"width":162,
"height":205
},
"s_Reel21" : 
{
"rotate":1,
"width":181,
"height":165
},
"s_Reel22" : 
{
"rotate":1,
"x":438,
"y":266,
"width":157,
"height":163
},
"s_Reel23" : 
{
"x":1816,
"y":510,
"width":149,
"height":155
},
"s_Reel24" : 
{
"x":1666,
"y":510,
"width":149,
"height":155
},
"s_Reel25" : 
{
"x":1816,
"y":354,
"width":149,
"height":155
},
"s_Reel26" : 
{
"x":1816,
"y":198,
"width":149,
"height":155
},
"s_Reel27" : 
{
"rotate":1,
"x":1660,
"y":292,
"width":155,
"height":170
},
"s_Reel28" : 
{
"rotate":1,
"x":1573,
"y":56,
"width":155,
"height":186
},
"s_Reel29" : 
{
"rotate":1,
"x":600,
"width":155,
"height":196
},
"s_Reel3" : 
{
"rotate":1,
"x":1110,
"width":155,
"height":211
},
"s_Reel30" : 
{
"rotate":1,
"x":1510,
"y":463,
"width":155,
"height":202
},
"s_Reel4" : 
{
"rotate":1,
"x":1202,
"y":222,
"width":155,
"height":219
},
"s_Reel5" : 
{
"rotate":1,
"x":1044,
"y":439,
"width":155,
"height":226
},
"s_Reel6" : 
{
"rotate":1,
"x":746,
"y":202,
"width":155,
"height":230
},
"s_Reel7" : 
{
"rotate":1,
"x":888,
"y":435,
"width":155,
"height":230
},
"s_Reel8" : 
{
"rotate":1,
"x":1200,
"y":442,
"width":155,
"height":223
},
"s_Reel9" : 
{
"x":182,
"y":2,
"width":210,
"height":172
},
"s_Rod" : 
{
"rotate":1,
"x":160,
"y":263,
"width":277,
"height":402
},
"s_Rod_Glow" : 
{
"rotate":1,
"x":1358,
"y":240,
"width":145,
"height":207,
"paddingTop":1
}
}
},
"fileID":1754
}
],
"fileID":6119
}
]
}
},
{
"type":"GameObject",
"id":"4e23776563be07846b6df7758fbf5824",
"data" : 
{
"root" : 
[
{
"name":"Sym05_float",
"activeSelf":true,
"layer":0,
"components" : 
[
{
"componentType":"Transform",
"enabled":true,
"serializableData" : 
{
"parent" : 
{
"fileID":0
},
"children" : 
[
],
"psr":"d"
},
"fileID":6120
},
{
"componentType":"UIAtlas",
"enabled":true,
"serializableData" : 
{
"textureContent" : 
{
"fileID":2800000,
"guid":"85a58bec9c4fab6488f3502398071355"
},
"spriteList" : 
{
"s_Float" : 
{
"width":414,
"height":353,
"paddingLeft":20,
"paddingTop":40,
"paddingRight":18,
"paddingBottom":52
}
}
},
"fileID":1771
}
],
"fileID":6121
}
]
}
},
{
"type":"GameObject",
"id":"5d52080e446e42c4586e77131a03e8d0",
"data" : 
{
"root" : 
[
{
"name":"Float_NEW_Float_NEW",
"activeSelf":true,
"layer":0,
"components" : 
[
{
"componentType":"Transform",
"enabled":true,
"serializableData" : 
{
"parent" : 
{
"fileID":0
},
"children" : 
[
],
"psr":"d"
},
"fileID":6122
},
{
"componentType":"UIAtlas",
"enabled":true,
"serializableData" : 
{
"textureContent" : 
{
"fileID":2800000,
"guid":"4bafc59ab7f4621478e9fde0cd42035b"
},
"spriteList" : 
{
"s_Float" : 
{
"x":1,
"y":294,
"width":744,
"height":873
},
"s_Float_Dark" : 
{
"x":1,
"y":1169,
"width":749,
"height":878
},
"s_Float_Glow" : 
{
"x":752,
"y":1552,
"width":431,
"height":495
},
"s_glow_blue" : 
{
"x":1,
"y":172,
"width":123,
"height":120
},
"s_horizon_line" : 
{
"rotate":1,
"x":1963,
"y":1171,
"width":80,
"height":364
},
"s_ripple01" : 
{
"x":1185,
"y":1699,
"width":510,
"height":348
},
"s_ripple02" : 
{
"x":752,
"y":855,
"width":510,
"height":345
},
"s_ripple03" : 
{
"x":747,
"y":160,
"width":510,
"height":344
},
"s_ripple04" : 
{
"rotate":1,
"x":1259,
"y":163,
"width":345,
"height":510
},
"s_ripple05" : 
{
"rotate":1,
"x":1612,
"y":513,
"width":345,
"height":510
},
"s_ripple06" : 
{
"rotate":1,
"x":1606,
"y":1,
"width":345,
"height":510
},
"s_ripple09" : 
{
"rotate":1,
"x":1614,
"y":1025,
"width":347,
"height":510
},
"s_ripple10" : 
{
"x":752,
"y":1202,
"width":510,
"height":348
},
"s_ripple11" : 
{
"rotate":1,
"x":1697,
"y":1537,
"width":348,
"height":510
},
"s_ripple12" : 
{
"rotate":1,
"x":1264,
"y":1187,
"width":348,
"height":510
},
"s_ripple13" : 
{
"x":747,
"y":506,
"width":510,
"height":347
},
"s_ripple14" : 
{
"rotate":1,
"x":1264,
"y":675,
"width":346,
"height":510
}
}
},
"fileID":1775
}
],
"fileID":6123
}
]
}
},
{
"type":"GameObject",
"id":"b179780a4689b7c4bb17a34e0bf5eb5e",
"data" : 
{
"root" : 
[
{
"name":"Float_NEW_Float_NEW2",
"activeSelf":true,
"layer":0,
"components" : 
[
{
"componentType":"Transform",
"enabled":true,
"serializableData" : 
{
"parent" : 
{
"fileID":0
},
"children" : 
[
],
"psr":"d"
},
"fileID":6124
},
{
"componentType":"UIAtlas",
"enabled":true,
"serializableData" : 
{
"textureContent" : 
{
"fileID":2800000,
"guid":"ceb18df3b607c444294922588261779b"
},
"spriteList" : 
{
"s_ripple07" : 
{
"x":1,
"y":348,
"width":510,
"height":345
},
"s_ripple08" : 
{
"x":1,
"y":1,
"width":510,
"height":345
},
"s_ripple15" : 
{
"x":513,
"y":348,
"width":510,
"height":345
},
"s_ripple16" : 
{
"x":513,
"y":1,
"width":510,
"height":345
},
"s_ripple17" : 
{
"x":1025,
"y":350,
"width":510,
"height":343
},
"s_ripple18" : 
{
"x":1537,
"y":350,
"width":510,
"height":343
},
"s_ripple19" : 
{
"x":1025,
"y":5,
"width":510,
"height":343
}
}
},
"fileID":1776
}
],
"fileID":6125
}
]
}
},
{
"type":"GameObject",
"id":"261bc7660444d7147a8bda66dcb2e317",
"data" : 
{
"root" : 
[
{
"name":"Sym06",
"activeSelf":true,
"layer":0,
"components" : 
[
{
"componentType":"Transform",
"enabled":true,
"serializableData" : 
{
"parent" : 
{
"fileID":0
},
"children" : 
[
],
"psr":"d"
},
"fileID":6126
},
{
"componentType":"UIAtlas",
"enabled":true,
"serializableData" : 
{
"textureContent" : 
{
"fileID":2800000,
"guid":"5d3e8d6f7db75d442841fdaa3f1a48f1"
},
"spriteList" : 
{
"s_static_box" : 
{
"width":266,
"height":253,
"paddingLeft":35,
"paddingTop":62,
"paddingRight":19,
"paddingBottom":44
}
}
},
"fileID":1795
}
],
"fileID":6127
}
]
}
},
{
"type":"GameObject",
"id":"31069d61830ff2d43bc22c751c87320b",
"data" : 
{
"root" : 
[
{
"name":"BBB_Baitbox_Material",
"activeSelf":true,
"layer":0,
"components" : 
[
{
"componentType":"Transform",
"enabled":true,
"serializableData" : 
{
"parent" : 
{
"fileID":0
},
"children" : 
[
],
"psr":"d"
},
"fileID":6128
},
{
"componentType":"UIAtlas",
"enabled":true,
"serializableData" : 
{
"textureContent" : 
{
"fileID":2800000,
"guid":"5d25ddbb0e54c3d42a21fc456a3f16ee"
},
"spriteList" : 
{
"s_bn_box_glow" : 
{
"x":2,
"y":1025,
"width":709,
"height":616
},
"s_bn_box_lines" : 
{
"x":2,
"y":456,
"width":658,
"height":567
},
"s_Box_BG2" : 
{
"x":2,
"y":2,
"width":612,
"height":452
},
"s_Box_Front2" : 
{
"x":1333,
"y":1203,
"width":626,
"height":438
},
"s_glow_blue" : 
{
"x":1691,
"y":11,
"width":246,
"height":239
},
"s_Lid" : 
{
"x":616,
"y":10,
"width":535,
"height":392
},
"s_Lid_Glow" : 
{
"x":713,
"y":1166,
"width":618,
"height":475
},
"s_Lid_Lines" : 
{
"x":1334,
"y":777,
"width":567,
"height":424
},
"s_Mug" : 
{
"rotate":1,
"x":1881,
"y":366,
"width":92,
"height":115
},
"s_Pick" : 
{
"rotate":1,
"x":1903,
"y":864,
"width":77,
"height":167
},
"s_Rails_Back" : 
{
"x":1903,
"y":1033,
"width":73,
"height":168
},
"s_Rails_Front1" : 
{
"x":1884,
"y":622,
"width":78,
"height":153
},
"s_Rails_Front2" : 
{
"x":1884,
"y":483,
"width":70,
"height":137
},
"s_Tray_BG2" : 
{
"x":1210,
"y":252,
"width":536,
"height":245
},
"s_Tray_FG2" : 
{
"x":1153,
"y":37,
"width":536,
"height":213
},
"s_Tray_Glow" : 
{
"x":713,
"y":836,
"width":619,
"height":328
},
"s_Tray_Lines" : 
{
"x":1210,
"y":499,
"width":567,
"height":276
},
"s_Tray_Shadow" : 
{
"x":662,
"y":404,
"width":546,
"height":430
},
"s_Tray_wall1" : 
{
"rotate":1,
"x":1779,
"y":487,
"width":103,
"height":288
},
"s_TrayContents1" : 
{
"rotate":1,
"x":1748,
"y":293,
"width":131,
"height":192
}
}
},
"fileID":1801
}
],
"fileID":6129
}
]
}
},
{
"type":"GameObject",
"id":"58c03a641cab6fb479da04be281c8bbc",
"data" : 
{
"root" : 
[
{
"name":"Fish_S_Material",
"activeSelf":true,
"layer":0,
"components" : 
[
{
"componentType":"Transform",
"enabled":true,
"serializableData" : 
{
"parent" : 
{
"fileID":0
},
"children" : 
[
],
"psr":"d"
},
"fileID":6130
},
{
"componentType":"UIAtlas",
"enabled":true,
"serializableData" : 
{
"textureContent" : 
{
"fileID":2800000,
"guid":"0b058a3b70f846c4fb479f62a83eeebb"
},
"spriteList" : 
{
"s_Background_New3" : 
{
"x":1,
"y":1,
"width":557,
"height":494
},
"s_Bubbles" : 
{
"rotate":1,
"x":560,
"y":16,
"width":82,
"height":33
},
"s_Fin1_S3" : 
{
"x":1006,
"y":88,
"width":38,
"height":25
},
"s_Fin2_S3" : 
{
"rotate":1,
"x":1191,
"y":326,
"width":24,
"height":28
},
"s_Fish_Glow_S" : 
{
"x":1006,
"y":356,
"width":209,
"height":139
},
"s_Fish_S_new3" : 
{
"x":1006,
"y":242,
"width":183,
"height":112
},
"s_Frame_Lines" : 
{
"x":1361,
"y":464,
"width":35,
"height":31
},
"s_IceOverlay3" : 
{
"x":1217,
"y":370,
"width":142,
"height":125
},
"s_IceOverlay4" : 
{
"x":1006,
"y":115,
"width":142,
"height":125
},
"s_Lighting2" : 
{
"x":560,
"y":51,
"width":444,
"height":444
}
}
},
"fileID":1864
}
],
"fileID":6131
}
]
}
},
{
"type":"GameObject",
"id":"e11cba442955fa741b6938af7d051991",
"data" : 
{
"root" : 
[
{
"name":"Fish_M_Material",
"activeSelf":true,
"layer":0,
"components" : 
[
{
"componentType":"Transform",
"enabled":true,
"serializableData" : 
{
"parent" : 
{
"fileID":0
},
"children" : 
[
],
"psr":"d"
},
"fileID":6132
},
{
"componentType":"UIAtlas",
"enabled":true,
"serializableData" : 
{
"textureContent" : 
{
"fileID":2800000,
"guid":"88e3ccbf401ddd54f9abe3c8bddc42c9"
},
"spriteList" : 
{
"s_Background_New3" : 
{
"x":1,
"y":1,
"width":557,
"height":494
},
"s_Bubbles" : 
{
"rotate":1,
"x":560,
"y":16,
"width":82,
"height":33
},
"s_Fin1_M3" : 
{
"x":1006,
"y":84,
"width":79,
"height":52
},
"s_Fin2_M3" : 
{
"x":1339,
"y":321,
"width":65,
"height":47
},
"s_Fish_Glow_M" : 
{
"x":1006,
"y":304,
"width":331,
"height":191
},
"s_Fish_M3" : 
{
"x":1006,
"y":138,
"width":305,
"height":164
},
"s_Frame_Lines" : 
{
"x":1627,
"y":464,
"width":35,
"height":31
},
"s_IceOverlay3" : 
{
"x":1339,
"y":370,
"width":142,
"height":125
},
"s_IceOverlay4" : 
{
"x":1483,
"y":370,
"width":142,
"height":125
},
"s_Lighting2" : 
{
"x":560,
"y":51,
"width":444,
"height":444
}
}
},
"fileID":1868
}
],
"fileID":6133
}
]
}
},
{
"type":"GameObject",
"id":"b1296e0ed0c0b8a4b9f9de305acc18aa",
"data" : 
{
"root" : 
[
{
"name":"Fish_L_Material",
"activeSelf":true,
"layer":0,
"components" : 
[
{
"componentType":"Transform",
"enabled":true,
"serializableData" : 
{
"parent" : 
{
"fileID":0
},
"children" : 
[
],
"psr":"d"
},
"fileID":6134
},
{
"componentType":"UIAtlas",
"enabled":true,
"serializableData" : 
{
"textureContent" : 
{
"fileID":2800000,
"guid":"ca03a10056ae2ac47b0a5d33e818698b"
},
"spriteList" : 
{
"s_Background_New3" : 
{
"x":1,
"y":1,
"width":557,
"height":494
},
"s_Bubbles" : 
{
"rotate":1,
"x":560,
"y":16,
"width":82,
"height":33
},
"s_Fin_L3" : 
{
"x":1836,
"y":439,
"width":92,
"height":56
},
"s_Fin2_L3" : 
{
"x":1663,
"y":309,
"width":61,
"height":59
},
"s_Fish_Body_L3" : 
{
"x":1006,
"y":30,
"width":505,
"height":213
},
"s_Fish_Glow_L" : 
{
"x":1006,
"y":245,
"width":540,
"height":250
},
"s_Fish_Mouth_L3" : 
{
"x":1548,
"y":303,
"width":113,
"height":65
},
"s_Frame_Lines" : 
{
"rotate":1,
"x":1513,
"y":208,
"width":31,
"height":35
},
"s_IceOverlay3" : 
{
"x":1548,
"y":370,
"width":142,
"height":125
},
"s_IceOverlay4" : 
{
"x":1692,
"y":370,
"width":142,
"height":125
},
"s_Lighting2" : 
{
"x":560,
"y":51,
"width":444,
"height":444
}
}
},
"fileID":1872
}
],
"fileID":6135
}
]
}
},
{
"type":"GameObject",
"id":"8eb707d6cb94a34438098c5cf6d1308a",
"data" : 
{
"root" : 
[
{
"name":"Fish_XL_Material",
"activeSelf":true,
"layer":0,
"components" : 
[
{
"componentType":"Transform",
"enabled":true,
"serializableData" : 
{
"parent" : 
{
"fileID":0
},
"children" : 
[
],
"psr":"d"
},
"fileID":6136
},
{
"componentType":"UIAtlas",
"enabled":true,
"serializableData" : 
{
"textureContent" : 
{
"fileID":2800000,
"guid":"080443a950e0a86428d3611df48ba841"
},
"spriteList" : 
{
"s_Background_New3" : 
{
"x":1,
"y":1,
"width":557,
"height":494
},
"s_Bubbles" : 
{
"rotate":1,
"x":560,
"y":3,
"width":82,
"height":33
},
"s_Fin_XL3" : 
{
"rotate":1,
"x":560,
"y":38,
"width":189,
"height":106
},
"s_Fish_Glow_XL" : 
{
"x":560,
"y":146,
"width":545,
"height":349
},
"s_Fish_Mouth_XL3" : 
{
"x":1553,
"y":70,
"width":152,
"height":125
},
"s_Fish_XL3" : 
{
"x":1553,
"y":197,
"width":468,
"height":298
},
"s_Frame_Lines" : 
{
"x":751,
"y":113,
"width":35,
"height":31
},
"s_IceOverlay3" : 
{
"x":1707,
"y":70,
"width":142,
"height":125
},
"s_IceOverlay4" : 
{
"x":1851,
"y":70,
"width":142,
"height":125
},
"s_Lighting2" : 
{
"x":1107,
"y":51,
"width":444,
"height":444
}
}
},
"fileID":1876
}
],
"fileID":6137
}
]
}
},
{
"type":"GameObject",
"id":"0f7f1e643b2efd04aa3ffebd73e340a9",
"data" : 
{
"root" : 
[
{
"name":"Fish_Gold_Material",
"activeSelf":true,
"layer":0,
"components" : 
[
{
"componentType":"Transform",
"enabled":true,
"serializableData" : 
{
"parent" : 
{
"fileID":0
},
"children" : 
[
],
"psr":"d"
},
"fileID":6138
},
{
"componentType":"UIAtlas",
"enabled":true,
"serializableData" : 
{
"textureContent" : 
{
"fileID":2800000,
"guid":"8f8566ff2f11a884e96debf69adb1987"
},
"spriteList" : 
{
"s_Background_Gold" : 
{
"x":1,
"y":15,
"width":557,
"height":494
},
"s_Fish_Fin1_Gold2" : 
{
"x":1590,
"y":806,
"width":117,
"height":90
},
"s_Fish_Fin2_Gold2" : 
{
"x":560,
"y":34,
"width":54,
"height":96
},
"s_Fish_Glow_Gold" : 
{
"x":1,
"y":511,
"width":572,
"height":512
},
"s_Fish_Gold2" : 
{
"x":575,
"y":511,
"width":567,
"height":512
},
"s_Fish_Mouth_Gold2" : 
{
"x":1144,
"y":512,
"width":103,
"height":65
},
"s_flare" : 
{
"x":1734,
"y":952,
"width":123,
"height":71
},
"s_FX_Dot" : 
{
"x":560,
"y":1,
"width":31,
"height":31
},
"s_FX_Ring" : 
{
"x":704,
"y":384,
"width":125,
"height":125
},
"s_GoldFrame" : 
{
"x":560,
"y":259,
"width":142,
"height":250
},
"s_IceOverlay3" : 
{
"x":1590,
"y":898,
"width":142,
"height":125
},
"s_IceOverlay4" : 
{
"x":560,
"y":132,
"width":142,
"height":125
},
"s_Lighting2" : 
{
"x":1144,
"y":579,
"width":444,
"height":444
}
}
},
"fileID":1880
}
],
"fileID":6139
}
]
}
},
{
"type":"GameObject",
"id":"aba731f7314fe4f40ab2fa962a0b3ce7",
"data" : 
{
"root" : 
[
{
"name":"Sym08",
"activeSelf":true,
"layer":0,
"components" : 
[
{
"componentType":"Transform",
"enabled":true,
"serializableData" : 
{
"parent" : 
{
"fileID":0
},
"children" : 
[
],
"psr":"d"
},
"fileID":6140
},
{
"componentType":"UIAtlas",
"enabled":true,
"serializableData" : 
{
"textureContent" : 
{
"fileID":2800000,
"guid":"d130498a46a01494daef2267ceea1644"
},
"spriteList" : 
{
"s_static_A" : 
{
"width":194,
"height":191,
"paddingLeft":219,
"paddingTop":223,
"paddingRight":216,
"paddingBottom":222
}
}
},
"fileID":1936
}
],
"fileID":6141
}
]
}
},
{
"type":"GameObject",
"id":"3e0654ab74e46c94795245ee31e2ad35",
"data" : 
{
"root" : 
[
{
"name":"symbol_A_Material",
"activeSelf":true,
"layer":0,
"components" : 
[
{
"componentType":"Transform",
"enabled":true,
"serializableData" : 
{
"parent" : 
{
"fileID":0
},
"children" : 
[
],
"psr":"d"
},
"fileID":6142
},
{
"componentType":"UIAtlas",
"enabled":true,
"serializableData" : 
{
"textureContent" : 
{
"fileID":2800000,
"guid":"c4c43de564a0e1647bf60f721dbc7902"
},
"spriteList" : 
{
"s_ripple" : 
{
"x":2,
"y":2,
"width":240,
"height":240
},
"s_splash_sparkles" : 
{
"x":244,
"y":40,
"width":200,
"height":202
},
"s_symbol_A" : 
{
"x":446,
"y":53,
"width":192,
"height":189
},
"s_wipe_A" : 
{
"rotate":1,
"x":640,
"y":63,
"width":172,
"height":179
}
}
},
"fileID":1940
}
],
"fileID":6143
}
]
}
},
{
"type":"GameObject",
"id":"55563174d9f11c5489586fddadc9543d",
"data" : 
{
"root" : 
[
{
"name":"Sym09",
"activeSelf":true,
"layer":0,
"components" : 
[
{
"componentType":"Transform",
"enabled":true,
"serializableData" : 
{
"parent" : 
{
"fileID":0
},
"children" : 
[
],
"psr":"d"
},
"fileID":6144
},
{
"componentType":"UIAtlas",
"enabled":true,
"serializableData" : 
{
"textureContent" : 
{
"fileID":2800000,
"guid":"dd87aecd44a76bb4995ae2bf35078e2d"
},
"spriteList" : 
{
"s_static_K" : 
{
"width":167,
"height":193,
"paddingLeft":233,
"paddingTop":222,
"paddingRight":229,
"paddingBottom":221
}
}
},
"fileID":1959
}
],
"fileID":6145
}
]
}
},
{
"type":"GameObject",
"id":"d96095d3f1d794c46a330342976b44a9",
"data" : 
{
"root" : 
[
{
"name":"symbol_K_Material",
"activeSelf":true,
"layer":0,
"components" : 
[
{
"componentType":"Transform",
"enabled":true,
"serializableData" : 
{
"parent" : 
{
"fileID":0
},
"children" : 
[
],
"psr":"d"
},
"fileID":6146
},
{
"componentType":"UIAtlas",
"enabled":true,
"serializableData" : 
{
"textureContent" : 
{
"fileID":2800000,
"guid":"da667d889a55f4b45ad9874b1cbf18a3"
},
"spriteList" : 
{
"s_ripple" : 
{
"x":2,
"y":157,
"width":240,
"height":240
},
"s_splash_sparkles" : 
{
"rotate":1,
"x":244,
"y":197,
"width":202,
"height":200
},
"s_symbol_K" : 
{
"rotate":1,
"x":244,
"y":29,
"width":192,
"height":166
},
"s_wipe_K" : 
{
"rotate":1,
"x":2,
"y":2,
"width":175,
"height":153
}
}
},
"fileID":1965
}
],
"fileID":6147
}
]
}
},
{
"type":"GameObject",
"id":"8bf24ec64aeec5d4d866c1ed1a0f5ea9",
"data" : 
{
"root" : 
[
{
"name":"Sym10",
"activeSelf":true,
"layer":0,
"components" : 
[
{
"componentType":"Transform",
"enabled":true,
"serializableData" : 
{
"parent" : 
{
"fileID":0
},
"children" : 
[
],
"psr":"d"
},
"fileID":6148
},
{
"componentType":"UIAtlas",
"enabled":true,
"serializableData" : 
{
"textureContent" : 
{
"fileID":2800000,
"guid":"c1ea0d05fedf63f4aa148b3a07e803ed"
},
"spriteList" : 
{
"s_static_Q" : 
{
"width":189,
"height":197,
"paddingLeft":222,
"paddingTop":220,
"paddingRight":218,
"paddingBottom":219
}
}
},
"fileID":1984
}
],
"fileID":6149
}
]
}
},
{
"type":"GameObject",
"id":"1722e4ce4303197458bcc7fc3c0932f4",
"data" : 
{
"root" : 
[
{
"name":"symbol_Q_Material",
"activeSelf":true,
"layer":0,
"components" : 
[
{
"componentType":"Transform",
"enabled":true,
"serializableData" : 
{
"parent" : 
{
"fileID":0
},
"children" : 
[
],
"psr":"d"
},
"fileID":6150
},
{
"componentType":"UIAtlas",
"enabled":true,
"serializableData" : 
{
"textureContent" : 
{
"fileID":2800000,
"guid":"026e3ce5fe5d89e49be105441611b38d"
},
"spriteList" : 
{
"s_ripple" : 
{
"x":2,
"y":2,
"width":240,
"height":240
},
"s_splash_sparkles" : 
{
"x":244,
"y":40,
"width":200,
"height":202
},
"s_symbol_Q" : 
{
"x":446,
"y":46,
"width":187,
"height":196
},
"s_wipe_Q" : 
{
"x":635,
"y":63,
"width":175,
"height":179
}
}
},
"fileID":1990
}
],
"fileID":6151
}
]
}
},
{
"type":"GameObject",
"id":"806ce8b45a1ab7e49ae2a14c3ff844ff",
"data" : 
{
"root" : 
[
{
"name":"Sym11",
"activeSelf":true,
"layer":0,
"components" : 
[
{
"componentType":"Transform",
"enabled":true,
"serializableData" : 
{
"parent" : 
{
"fileID":0
},
"children" : 
[
],
"psr":"d"
},
"fileID":6152
},
{
"componentType":"UIAtlas",
"enabled":true,
"serializableData" : 
{
"textureContent" : 
{
"fileID":2800000,
"guid":"42634ae5fa291254b8e409b492096ccf"
},
"spriteList" : 
{
"s_static_J" : 
{
"width":146,
"height":198,
"paddingLeft":243,
"paddingTop":219,
"paddingRight":240,
"paddingBottom":219
}
}
},
"fileID":2009
}
],
"fileID":6153
}
]
}
},
{
"type":"GameObject",
"id":"cb136c1b33435954f9dd943079e4fc8c",
"data" : 
{
"root" : 
[
{
"name":"symbol_J_Material",
"activeSelf":true,
"layer":0,
"components" : 
[
{
"componentType":"Transform",
"enabled":true,
"serializableData" : 
{
"parent" : 
{
"fileID":0
},
"children" : 
[
],
"psr":"d"
},
"fileID":6154
},
{
"componentType":"UIAtlas",
"enabled":true,
"serializableData" : 
{
"textureContent" : 
{
"fileID":2800000,
"guid":"b489b3b2b4b6f9444ba9405b424a8178"
},
"spriteList" : 
{
"s_ripple" : 
{
"x":2,
"y":2,
"width":240,
"height":240
},
"s_splash_sparkles" : 
{
"x":244,
"y":40,
"width":200,
"height":202
},
"s_symbol_J" : 
{
"x":446,
"y":45,
"width":145,
"height":197
},
"s_wipe_J" : 
{
"x":593,
"y":62,
"width":131,
"height":180
}
}
},
"fileID":2015
}
],
"fileID":6155
}
]
}
},
{
"type":"GameObject",
"id":"cd632debf7cca034baaecb758eca6ac4",
"data" : 
{
"root" : 
[
{
"name":"Sym12",
"activeSelf":true,
"layer":0,
"components" : 
[
{
"componentType":"Transform",
"enabled":true,
"serializableData" : 
{
"parent" : 
{
"fileID":0
},
"children" : 
[
],
"psr":"d"
},
"fileID":6156
},
{
"componentType":"UIAtlas",
"enabled":true,
"serializableData" : 
{
"textureContent" : 
{
"fileID":2800000,
"guid":"a65ba0596e15b064f8d368e920df6a38"
},
"spriteList" : 
{
"s_static_10" : 
{
"width":213,
"height":193,
"paddingLeft":210,
"paddingTop":222,
"paddingRight":206,
"paddingBottom":221
}
}
},
"fileID":2034
}
],
"fileID":6157
}
]
}
},
{
"type":"GameObject",
"id":"b0fe395b8a46e3e4bbb24905d8c28ef3",
"data" : 
{
"root" : 
[
{
"name":"symbol_10_Material",
"activeSelf":true,
"layer":0,
"components" : 
[
{
"componentType":"Transform",
"enabled":true,
"serializableData" : 
{
"parent" : 
{
"fileID":0
},
"children" : 
[
],
"psr":"d"
},
"fileID":6158
},
{
"componentType":"UIAtlas",
"enabled":true,
"serializableData" : 
{
"textureContent" : 
{
"fileID":2800000,
"guid":"90c0bb361aba76740ba2a1929c99296d"
},
"spriteList" : 
{
"s_ripple" : 
{
"x":2,
"y":2,
"width":240,
"height":240
},
"s_splash_sparkles" : 
{
"x":244,
"y":40,
"width":200,
"height":202
},
"s_symbol_10" : 
{
"rotate":1,
"x":446,
"y":31,
"width":191,
"height":211
},
"s_wiipe_10" : 
{
"rotate":1,
"x":639,
"y":44,
"width":174,
"height":198
}
}
},
"fileID":2040
}
],
"fileID":6159
}
]
}
},
{
"type":"GameObject",
"id":"5b8a0cd954093d849a4402334268062a",
"data" : 
{
"root" : 
[
{
"name":"clipping_mask",
"activeSelf":true,
"layer":0,
"components" : 
[
{
"componentType":"Transform",
"enabled":true,
"serializableData" : 
{
"parent" : 
{
"fileID":0
},
"children" : 
[
],
"psr":"d"
},
"fileID":6160
},
{
"componentType":"UIAtlas",
"enabled":true,
"serializableData" : 
{
"textureContent" : 
{
"fileID":2800000,
"guid":"0a86deaf255853540b9a2cd282e6ca8a"
},
"spriteList" : 
{
"s_reel_clipping_mask" : 
{
"width":320,
"height":180
}
}
},
"fileID":2424
}
],
"fileID":6161
}
]
}
},
{
"type":"AudioClip",
"id":"914f2e2d37e8f5542b3468339c6d3dbf",
"data" : 
{
"path":"res/914f2e2d37e8f5542b3468339c6d3dbf"
}
},
{
"type":"AudioClip",
"id":"30432456ca58ffa4b80c03e4c45c58f9",
"data" : 
{
"path":"res/30432456ca58ffa4b80c03e4c45c58f9"
}
},
{
"type":"AudioClip",
"id":"79adabfe70510fd48b18a630f51335d0",
"data" : 
{
"path":"res/79adabfe70510fd48b18a630f51335d0"
}
},
{
"type":"GameObject",
"id":"a94547a251cbf954b8d8873e0c6e0a76",
"data" : 
{
"root" : 
[
{
"name":"Whale_Material",
"activeSelf":true,
"layer":0,
"components" : 
[
{
"componentType":"Transform",
"enabled":true,
"serializableData" : 
{
"parent" : 
{
"fileID":0
},
"children" : 
[
],
"psr":"d"
},
"fileID":6162
},
{
"componentType":"UIAtlas",
"enabled":true,
"serializableData" : 
{
"textureContent" : 
{
"fileID":2800000,
"guid":"5a45fea22fd068c499a7ea875dc8559a"
},
"spriteList" : 
{
"s_Breath" : 
{
"x":1386,
"y":2,
"width":247,
"height":245
},
"s_Fish1" : 
{
"x":1658,
"y":223,
"width":147,
"height":126,
"paddingTop":1
},
"s_Fish2" : 
{
"x":1635,
"y":61,
"width":79,
"height":57,
"paddingRight":1
},
"s_Fish3" : 
{
"x":1737,
"y":187,
"width":46,
"height":34,
"paddingLeft":1
},
"s_Frame_Piece" : 
{
"x":736,
"y":32,
"width":101,
"height":100,
"paddingRight":1,
"paddingBottom":1
},
"s_Gradient2" : 
{
"x":839,
"y":150,
"width":16,
"height":86,
"paddingBottom":13
},
"s_Lighting2" : 
{
"x":512,
"y":14,
"width":222,
"height":222
},
"s_Ocean" : 
{
"x":863,
"y":303,
"width":524,
"height":300
},
"s_particle" : 
{
"x":736,
"y":134,
"width":101,
"height":102
},
"s_Snow_Cloud" : 
{
"x":1389,
"y":380,
"width":340,
"height":223
},
"s_splash_sparkles" : 
{
"x":1635,
"y":120,
"width":100,
"height":101
},
"s_Whale_Eye" : 
{
"x":1807,
"y":299,
"width":35,
"height":50
},
"s_Whale_Eye_Lids_BOTTOM" : 
{
"x":1658,
"y":352,
"width":30,
"height":26
},
"s_Whale_Eye_Lids_TOP" : 
{
"rotate":1,
"x":839,
"y":116,
"width":22,
"height":32
},
"s_Whale_Fin" : 
{
"rotate":1,
"x":1731,
"y":351,
"width":114,
"height":252
},
"s_Whale_Fin_Outline" : 
{
"x":1389,
"y":249,
"width":267,
"height":129
},
"s_Whale_Jaw" : 
{
"x":2,
"y":5,
"width":508,
"height":231
},
"s_Whale_Outline_Jaw" : 
{
"x":863,
"y":52,
"width":521,
"height":249,
"paddingBottom":1
},
"s_Whale_Top2" : 
{
"x":2,
"y":238,
"width":859,
"height":365,
"paddingRight":1,
"paddingBottom":1
}
}
},
"fileID":2451
}
],
"fileID":6163
}
]
}
},
{
"type":"GameObject",
"id":"8299fa808f2e4854380fe028d3b14fe1",
"data" : 
{
"root" : 
[
{
"name":"Whale_Symbol_Land_Material",
"activeSelf":true,
"layer":0,
"components" : 
[
{
"componentType":"Transform",
"enabled":true,
"serializableData" : 
{
"parent" : 
{
"fileID":0
},
"children" : 
[
],
"psr":"d"
},
"fileID":6164
},
{
"componentType":"UIAtlas",
"enabled":true,
"serializableData" : 
{
"textureContent" : 
{
"fileID":2800000,
"guid":"8e6a3b28dfc8e8346a43459e6b448d0b"
},
"spriteList" : 
{
"s_Breath" : 
{
"x":376,
"y":46,
"width":247,
"height":245
},
"s_particle" : 
{
"x":625,
"y":88,
"width":202,
"height":203
},
"s_Snow" : 
{
"x":2,
"y":2,
"width":372,
"height":289
}
}
},
"fileID":2465
}
],
"fileID":6165
}
]
}
},
{
"type":"AnimationClip",
"id":"dee016b2230c2324485bb9fceda7902e",
"data" : 
{
"name":"levels_BG_pulse",
"length":1,
"wrapMode":2,
"curves" : 
[
{
"nestedFieldType":"AnimationClipCurveData",
"serializableData" : 
{
"path":"",
"type":"Transform",
"propertyName":"m_LocalScale.x",
"curve" : 
{
"nestedFieldType":"AnimationCurve",
"serializableData" : 
{
"times" : 
[0,0.5,1
],
"values" : 
[1,1.1,1
],
"inTangents" : 
[0.200000047683716,0.200000047683716,-0.200000047683716
],
"outTangents" : 
[0.200000047683716,-0.200000047683716,0
]
}
}
}
},
{
"nestedFieldType":"AnimationClipCurveData",
"serializableData" : 
{
"path":"",
"type":"Transform",
"propertyName":"m_LocalScale.y",
"curve" : 
{
"nestedFieldType":"AnimationCurve",
"serializableData" : 
{
"times" : 
[0,0.5,1
],
"values" : 
[1,1.1,1
],
"inTangents" : 
[0.200000047683716,0.200000047683716,-0.200000047683716
],
"outTangents" : 
[0.200000047683716,-0.200000047683716
]
}
}
}
},
{
"nestedFieldType":"AnimationClipCurveData",
"serializableData" : 
{
"path":"",
"type":"Transform",
"propertyName":"m_LocalScale.z",
"curve" : 
{
"nestedFieldType":"AnimationCurve",
"serializableData" : 
{
"times" : 
[0,1
],
"values" : 
[1
],
"inTangents" : 
[0
],
"outTangents":"copy"
}
}
}
}
],
"events" : 
[
]
}
},
{
"type":"GameObject",
"id":"0c07bfa1d550d0542b12be814b6cf24f",
"data" : 
{
"root" : 
[
{
"name":"placeholder",
"activeSelf":true,
"layer":0,
"components" : 
[
{
"componentType":"Transform",
"enabled":true,
"serializableData" : 
{
"parent" : 
{
"fileID":0
},
"children" : 
[
],
"psr":"d"
},
"fileID":6166
},
{
"componentType":"UIAtlas",
"enabled":true,
"serializableData" : 
{
"textureContent" : 
{
"fileID":2800000,
"guid":"c5f0472c5c32956458bea7040088326a"
},
"spriteList" : 
{
"s_bomb" : 
{
"y":1,
"width":180,
"height":217
},
"s_boom" : 
{
"y":482,
"width":1201,
"height":750
},
"s_fsDONE" : 
{
"x":838,
"y":219,
"width":418,
"height":262
},
"s_fswin_window" : 
{
"y":219,
"width":418,
"height":262
},
"s_fswinExtra" : 
{
"x":419,
"y":219,
"width":418,
"height":262
}
}
},
"fileID":2475
}
],
"fileID":6167
}
]
}
},
{
"type":"GameObject",
"id":"a9e6ae7843ab1ca4ebb5ea9e60422acf",
"data" : 
{
"root" : 
[
{
"name":"movetotargettest",
"activeSelf":true,
"layer":0,
"components" : 
[
{
"componentType":"Transform",
"enabled":true,
"serializableData" : 
{
"parent" : 
{
"fileID":0
},
"children" : 
[
],
"psr":"d"
},
"fileID":6168
},
{
"componentType":"UIAtlas",
"enabled":true,
"serializableData" : 
{
"textureContent" : 
{
"fileID":2800000,
"guid":"90148e4b93ee8654cbbbcd67ce2dc693"
},
"spriteList" : 
{
"s_movetotargettest" : 
{
"width":329,
"height":65
}
}
},
"fileID":2489
}
],
"fileID":6169
}
]
}
},
{
"type":"AudioClip",
"id":"18f7ff9f40126774dabb69867e3e16fa",
"data" : 
{
"path":"res/18f7ff9f40126774dabb69867e3e16fa"
}
},
{
"type":"AudioClip",
"id":"1b2a8e9679ef02545bdf3a2fdda9b7f7",
"data" : 
{
"path":"res/1b2a8e9679ef02545bdf3a2fdda9b7f7"
}
},
{
"type":"AudioClip",
"id":"0b5dfbecdf753f544b6121ea81aa6c8b",
"data" : 
{
"path":"res/0b5dfbecdf753f544b6121ea81aa6c8b"
}
},
{
"type":"AnimationClip",
"id":"5d219f08ba8a628459a7680bf273f05f",
"data" : 
{
"name":"bump",
"length":0.3833333,
"wrapMode":1,
"curves" : 
[
{
"nestedFieldType":"AnimationClipCurveData",
"serializableData" : 
{
"path":"",
"type":"Transform",
"propertyName":"m_LocalScale.x",
"curve" : 
{
"nestedFieldType":"AnimationCurve",
"serializableData" : 
{
"times" : 
[0,0.1333333,0.3833333
],
"values" : 
[1,1.2,1
],
"inTangents" : 
[0
],
"outTangents":"copy"
}
}
}
},
{
"nestedFieldType":"AnimationClipCurveData",
"serializableData" : 
{
"path":"",
"type":"Transform",
"propertyName":"m_LocalScale.y",
"curve" : 
{
"nestedFieldType":"AnimationCurve",
"serializableData" : 
{
"times" : 
[0,0.1333333,0.3833333
],
"values" : 
[1,1.2,1
],
"inTangents" : 
[0
],
"outTangents":"copy"
}
}
}
},
{
"nestedFieldType":"AnimationClipCurveData",
"serializableData" : 
{
"path":"",
"type":"Transform",
"propertyName":"m_LocalScale.z",
"curve" : 
{
"nestedFieldType":"AnimationCurve",
"serializableData" : 
{
"times" : 
[0,0.1333333,0.3833333
],
"values" : 
[0
],
"inTangents" : 
[0
],
"outTangents":"copy"
}
}
}
}
],
"events" : 
[
]
}
},
{
"type":"GameObject",
"id":"3a65c40542e207d41a27ba214b0d4422",
"data" : 
{
"root" : 
[
{
"name":"feature_feature",
"activeSelf":true,
"layer":0,
"components" : 
[
{
"componentType":"Transform",
"enabled":true,
"serializableData" : 
{
"parent" : 
{
"fileID":0
},
"children" : 
[
],
"psr":"d"
},
"fileID":6170
},
{
"componentType":"UIAtlas",
"enabled":true,
"serializableData" : 
{
"textureContent" : 
{
"fileID":2800000,
"guid":"cd0a3144f591ccd49a686b89d2044574"
},
"spriteList" : 
{
"s_Arm" : 
{
"x":1760,
"y":969,
"width":276,
"height":641
},
"s_Arm_RIM" : 
{
"rotate":1,
"x":801,
"y":28,
"width":161,
"height":69
},
"s_arm1" : 
{
"rotate":1,
"x":1465,
"y":924,
"width":142,
"height":231
},
"s_arm1_GLOW" : 
{
"x":228,
"y":3,
"width":139,
"height":94
},
"s_bike_blade_GLOW" : 
{
"x":589,
"y":9,
"width":210,
"height":88
},
"s_bike_body_GLOW" : 
{
"x":1424,
"y":14,
"width":321,
"height":203
},
"s_bike_handles" : 
{
"x":1760,
"y":1612,
"width":285,
"height":435
},
"s_Body" : 
{
"x":1,
"y":99,
"width":924,
"height":1948
},
"s_Body_Glow" : 
{
"x":1094,
"y":358,
"width":369,
"height":797
},
"s_Fish" : 
{
"rotate":1,
"x":1465,
"y":219,
"width":513,
"height":685
},
"s_fisherman_symbol" : 
{
"x":1094,
"y":30,
"width":328,
"height":326
},
"s_Flash" : 
{
"x":1424,
"y":232,
"width":26,
"height":124
},
"s_Head_BG_Glasses" : 
{
"x":1,
"y":1,
"width":225,
"height":96
},
"s_Head_BG_Mouth" : 
{
"x":1760,
"y":927,
"width":131,
"height":40
},
"s_panel_edge" : 
{
"x":1612,
"y":906,
"width":146,
"height":1141
},
"s_panel_width" : 
{
"x":964,
"y":14,
"width":128,
"height":1141
},
"s_particle" : 
{
"rotate":1,
"x":1747,
"y":15,
"width":203,
"height":202
},
"s_Rod_Arm" : 
{
"x":927,
"y":1157,
"width":683,
"height":890
},
"s_Shadow" : 
{
"x":369,
"y":6,
"width":218,
"height":91
}
}
},
"fileID":3159
}
],
"fileID":6171
}
]
}
},
{
"type":"GameObject",
"id":"059e5c62df64b0b4bbfb33cc36f8f923",
"data" : 
{
"root" : 
[
{
"name":"feature_feature2",
"activeSelf":true,
"layer":0,
"components" : 
[
{
"componentType":"Transform",
"enabled":true,
"serializableData" : 
{
"parent" : 
{
"fileID":0
},
"children" : 
[
],
"psr":"d"
},
"fileID":6172
},
{
"componentType":"UIAtlas",
"enabled":true,
"serializableData" : 
{
"textureContent" : 
{
"fileID":2800000,
"guid":"ef3c4155d9864f14398a49c9cf08b2ec"
},
"spriteList" : 
{
"s_Arm_Glow" : 
{
"x":455,
"y":1,
"width":146,
"height":328
},
"s_arm2" : 
{
"x":1406,
"y":229,
"width":169,
"height":133
},
"s_bike_blade" : 
{
"rotate":1,
"x":1258,
"y":556,
"width":129,
"height":377
},
"s_bike_body" : 
{
"x":672,
"y":1200,
"width":601,
"height":361
},
"s_bike_flare" : 
{
"x":672,
"y":930,
"width":577,
"height":268
},
"s_bike_handles_GLOW" : 
{
"x":1239,
"y":199,
"width":165,
"height":240
},
"s_bike_track" : 
{
"rotate":1,
"x":496,
"y":331,
"width":269,
"height":521
},
"s_bike_track_GLOW" : 
{
"rotate":1,
"x":1389,
"y":654,
"width":156,
"height":279
},
"s_Body_RIM" : 
{
"x":1025,
"y":441,
"width":231,
"height":487
},
"s_Breath" : 
{
"x":1,
"y":363,
"width":493,
"height":489
},
"s_coat_armless" : 
{
"x":1043,
"y":189,
"width":194,
"height":250
},
"s_coat_armless_GLOW" : 
{
"x":1438,
"y":79,
"width":120,
"height":148
},
"s_fish_cluster" : 
{
"rotate":1,
"x":767,
"y":384,
"width":256,
"height":544
},
"s_Fish_GLOW" : 
{
"rotate":1,
"x":786,
"y":49,
"width":255,
"height":333
},
"s_fish_gold" : 
{
"rotate":1,
"x":1275,
"y":1167,
"width":344,
"height":394
},
"s_Fish_RIM" : 
{
"x":1389,
"y":524,
"width":171,
"height":128
},
"s_Head_FG" : 
{
"x":1,
"y":854,
"width":669,
"height":707
},
"s_Head_FG_RIM" : 
{
"x":1268,
"y":20,
"width":168,
"height":177
},
"s_Head_GLOW" : 
{
"rotate":1,
"x":1251,
"y":935,
"width":271,
"height":230
},
"s_helmet" : 
{
"rotate":1,
"x":1406,
"y":364,
"width":157,
"height":158
},
"s_helmet_GLOW" : 
{
"x":1258,
"y":451,
"width":102,
"height":103
},
"s_leg" : 
{
"x":603,
"y":3,
"width":181,
"height":326
},
"s_Rod_Arm_Glow" : 
{
"rotate":1,
"x":1,
"y":12,
"width":452,
"height":349
},
"s_Rod_Arm_RIM" : 
{
"rotate":1,
"x":1043,
"y":16,
"width":223,
"height":171
}
}
},
"fileID":3160
}
],
"fileID":6173
}
]
}
},
{
"type":"AudioClip",
"id":"c944a09965f846f4280b745b82849322",
"data" : 
{
"path":"res/c944a09965f846f4280b745b82849322"
}
},
{
"type":"AnimationClip",
"id":"291cdef312a39c949bd723e0b7305da1",
"data" : 
{
"name":"FsWindow_appear",
"length":1,
"wrapMode":0,
"curves" : 
[
{
"nestedFieldType":"AnimationClipCurveData",
"serializableData" : 
{
"path":"",
"type":"Transform",
"propertyName":"m_LocalRotation.x",
"curve" : 
{
"nestedFieldType":"AnimationCurve",
"serializableData" : 
{
"times" : 
{
"timesCount":61,
"increase":0.01666667
},
"values" : 
[0
],
"inTangents" : 
[0
],
"outTangents":"copy"
}
}
}
},
{
"nestedFieldType":"AnimationClipCurveData",
"serializableData" : 
{
"path":"",
"type":"Transform",
"propertyName":"m_LocalRotation.y",
"curve" : 
{
"nestedFieldType":"AnimationCurve",
"serializableData" : 
{
"times" : 
{
"timesCount":61,
"increase":0.01666667
},
"values" : 
[0
],
"inTangents" : 
[0
],
"outTangents":"copy"
}
}
}
},
{
"nestedFieldType":"AnimationClipCurveData",
"serializableData" : 
{
"path":"",
"type":"Transform",
"propertyName":"m_LocalRotation.z",
"curve" : 
{
"nestedFieldType":"AnimationCurve",
"serializableData" : 
{
"times" : 
{
"timesCount":61,
"increase":0.01666667
},
"values" : 
[0.7071069,0.6881577,0.668381,0.6478541,0.6266643,0.6049081,0.5826905,0.5601251,0.5373324,0.5144396,0.4915794,0.4688894,0.4465102,0.4245858,0.4032614,0.3826835,0.3630887,0.3445337,0.326888,0.3100128,0.2937622,0.2779853,0.2625259,0.2472244,0.2319176,0.2164395,0.2004534,0.1838572,0.1668374,0.1495837,0.1322883,0.115145,0.0983496,0.08209816,0.06658727,0.05201311,0.03857118,0.02645605,0.01586111,0.006978641,0,-0.004097146,-0.004910171,-0.003111047,0.0006283079,0.005635913,0.01123966,0.01676744,0.02154731,0.02490763,0.02617696,0.02567971,0.02429261,0.02217262,0.01947668,0.01636177,0.01298493,0.009503221,0.006073754,0.002853652,0
],
"inTangents" : 
[-1.1369526386261,-1.16177725791931,-1.20910573005676,-1.25149893760681,-1.28838133811951,-1.31921422481537,-1.34349179267883,-1.36074352264404,-1.37056398391724,-1.3725893497467,-1.36650693416595,-1.35207664966583,-1.32910799980164,-1.29746508598328,-1.25706839561462,-1.20518136024475,-1.14449143409729,-1.08601832389832,-1.03562867641449,-0.993772804737091,-0.960823655128479,-0.937087953090668,-0.922825336456299,-0.918250799179077,-0.923546373844147,-0.943924009799957,-0.977469027042389,-1.00847840309143,-1.02820205688477,-1.03647494316101,-1.03316187858582,-1.01816082000732,-0.991407334804535,-0.952870845794678,-0.902552485466003,-0.840483427047729,-0.766712427139282,-0.681302905082703,-0.584322929382324,-0.475833654403687,-0.332273900508881,-0.147305279970169,0.0295829772949219,0.166154533624649,0.262409061193466,0.31834089756012,0.333946228027344,0.309229850769043,0.244205743074417,0.138889491558075,0.0231624618172646,-0.0565304048359394,-0.105212822556496,-0.144478172063828,-0.174325525760651,-0.19475269317627,-0.20575675368309,-0.207335472106934,-0.199487254023552,-0.182211250066757,-0.171216249465942
],
"outTangents":"copy"
}
}
}
},
{
"nestedFieldType":"AnimationClipCurveData",
"serializableData" : 
{
"path":"",
"type":"Transform",
"propertyName":"m_LocalRotation.w",
"curve" : 
{
"nestedFieldType":"AnimationCurve",
"serializableData" : 
{
"times" : 
{
"timesCount":61,
"increase":0.01666667
},
"values" : 
[0.7071067,0.7255611,0.7438191,0.7617644,0.7792893,0.7962953,0.8126941,0.8284081,0.8433706,0.8575266,0.8708327,0.8832569,0.8947785,0.9053877,0.9150848,0.9238795,0.9317546,0.9387739,0.9450631,0.9507324,0.9558785,0.9605854,0.9649249,0.9689583,0.9727355,0.976296,0.9797032,0.982953,0.9859844,0.9887491,0.9912113,0.9933487,0.9951519,0.9966242,0.9977806,0.9986464,0.9992558,0.99965,0.9998742,0.9999756,1,0.9999916,0.999988,0.9999952,0.9999998,0.9999841,0.9999368,0.9998594,0.9997678,0.9996898,0.9996573,0.9996702,0.9997049,0.9997541,0.9998103,0.9998661,0.9999157,0.9999548,0.9999816,0.9999959,1
],
"inTangents" : 
[1.10726594924927,1.10137224197388,1.08609795570374,1.06410562992096,1.03592646121979,1.00214493274689,0.963383376598358,0.920292615890503,0.873556017875671,0.823865592479706,0.771909415721893,0.718374311923981,0.663923680782318,0.609188735485077,0.554754197597504,0.500092327594757,0.446832418441772,0.399253606796265,0.358754098415375,0.324462950229645,0.295588135719299,0.271392822265625,0.25118687748909,0.234315782785416,0.220132291316986,0.209033310413361,0.199708178639412,0.188435763120651,0.173883885145187,0.156805515289307,0.137989044189453,0.118219368159771,0.0982654988765717,0.0788605958223343,0.0606644749641418,0.0442564897239208,0.0301087182015181,0.0185519643127918,0.00976861454546452,0.00377297750674188,0.000479221809655428,-0.000361204496584833,0.00010728846245911,0.000355840078555048,-0.000330806069541723,-0.00189006514847279,-0.00374257937073708,-0.00506937969475985,-0.00508904922753572,-0.00331521360203624,-0.000586510286666453,0.00142693659290671,0.00251770252361894,0.00316322152502835,0.00335991708561778,0.00316143338568509,0.00266075390391052,0.00197589583694935,0.0012338173110038,0.000552533427253366,0.000243182832491584
],
"outTangents":"copy"
}
}
}
}
],
"events" : 
[
]
}
},
{
"type":"AnimationClip",
"id":"0e0fa95667be6714c9fe7212b383ffcd",
"data" : 
{
"name":"feature_trail_plank_in",
"length":1,
"wrapMode":0,
"curves" : 
[
{
"nestedFieldType":"AnimationClipCurveData",
"serializableData" : 
{
"path":"",
"type":"Transform",
"propertyName":"m_LocalScale.x",
"curve" : 
{
"nestedFieldType":"AnimationCurve",
"serializableData" : 
{
"times" : 
[0,0.3333333,0.5,0.6666667,0.85,1
],
"values" : 
[1
],
"inTangents" : 
[0
],
"outTangents":"copy"
}
}
}
},
{
"nestedFieldType":"AnimationClipCurveData",
"serializableData" : 
{
"path":"",
"type":"Transform",
"propertyName":"m_LocalScale.y",
"curve" : 
{
"nestedFieldType":"AnimationCurve",
"serializableData" : 
{
"times" : 
[0,0.3333333,0.5,0.6666667,0.85,1
],
"values" : 
[0,1,0.8,1.1,0.9,1
],
"inTangents" : 
[3,0.899999976158142,0.299999892711639,0.35454523563385,-0.212121218442917,0.666666924953461
],
"outTangents":"copy"
}
}
}
},
{
"nestedFieldType":"AnimationClipCurveData",
"serializableData" : 
{
"path":"",
"type":"Transform",
"propertyName":"m_LocalScale.z",
"curve" : 
{
"nestedFieldType":"AnimationCurve",
"serializableData" : 
{
"times" : 
[0,0.3333333,0.5,0.6666667,0.85,1
],
"values" : 
[1
],
"inTangents" : 
[0
],
"outTangents":"copy"
}
}
}
},
{
"nestedFieldType":"AnimationClipCurveData",
"serializableData" : 
{
"path":"",
"type":"Transform",
"propertyName":"m_LocalPosition.x",
"curve" : 
{
"nestedFieldType":"AnimationCurve",
"serializableData" : 
{
"times" : 
[0,0.3333333,0.5,0.6666667,0.85,1
],
"values" : 
[0
],
"inTangents" : 
[0
],
"outTangents":"copy"
}
}
}
},
{
"nestedFieldType":"AnimationClipCurveData",
"serializableData" : 
{
"path":"",
"type":"Transform",
"propertyName":"m_LocalPosition.y",
"curve" : 
{
"nestedFieldType":"AnimationCurve",
"serializableData" : 
{
"times" : 
[0,0.3333333,0.5,0.6666667,0.85,1
],
"values" : 
[80,8,22,1,12,8
],
"inTangents" : 
[-216,-66,-20.9999885559082,-32.9999923706055,16.6666641235352,-26.6666717529297
],
"outTangents":"copy"
}
}
}
},
{
"nestedFieldType":"AnimationClipCurveData",
"serializableData" : 
{
"path":"",
"type":"Transform",
"propertyName":"m_LocalPosition.z",
"curve" : 
{
"nestedFieldType":"AnimationCurve",
"serializableData" : 
{
"times" : 
[0,0.3333333,0.5,0.6666667,0.85,1
],
"values" : 
[0
],
"inTangents" : 
[0
],
"outTangents":"copy"
}
}
}
}
],
"events" : 
[
]
}
},
{
"type":"AnimationClip",
"id":"fd7148e850e315f4faa0f40dc1104d90",
"data" : 
{
"name":"feature_trail_plank_out",
"length":0.5,
"wrapMode":0,
"curves" : 
[
{
"nestedFieldType":"AnimationClipCurveData",
"serializableData" : 
{
"path":"",
"type":"Transform",
"propertyName":"m_LocalScale.x",
"curve" : 
{
"nestedFieldType":"AnimationCurve",
"serializableData" : 
{
"times" : 
[0,0.08333334,0.5
],
"values" : 
[1,1.1,0
],
"inTangents" : 
[1.20000028610229,-0.71999990940094,-2.64000010490417
],
"outTangents":"copy"
}
}
}
},
{
"nestedFieldType":"AnimationClipCurveData",
"serializableData" : 
{
"path":"",
"type":"Transform",
"propertyName":"m_LocalScale.y",
"curve" : 
{
"nestedFieldType":"AnimationCurve",
"serializableData" : 
{
"times" : 
[0,0.08333334,0.5
],
"values" : 
[1,1.1,0
],
"inTangents" : 
[1.20000028610229,-0.71999990940094,-2.64000010490417
],
"outTangents":"copy"
}
}
}
},
{
"nestedFieldType":"AnimationClipCurveData",
"serializableData" : 
{
"path":"",
"type":"Transform",
"propertyName":"m_LocalScale.z",
"curve" : 
{
"nestedFieldType":"AnimationCurve",
"serializableData" : 
{
"times" : 
[0,0.5
],
"values" : 
[1
],
"inTangents" : 
[0
],
"outTangents":"copy"
}
}
}
}
],
"events" : 
[
]
}
},
{
"type":"AnimationClip",
"id":"071169a36ef92ef4a9cd4dc072af4a60",
"data" : 
{
"name":"feature_trail_plank_jiggle",
"length":0.55,
"wrapMode":0,
"curves" : 
[
{
"nestedFieldType":"AnimationClipCurveData",
"serializableData" : 
{
"path":"",
"type":"Transform",
"propertyName":"m_LocalScale.x",
"curve" : 
{
"nestedFieldType":"AnimationCurve",
"serializableData" : 
{
"times" : 
[0,0.25,0.55
],
"values" : 
[1
],
"inTangents" : 
[0
],
"outTangents":"copy"
}
}
}
},
{
"nestedFieldType":"AnimationClipCurveData",
"serializableData" : 
{
"path":"",
"type":"Transform",
"propertyName":"m_LocalScale.y",
"curve" : 
{
"nestedFieldType":"AnimationCurve",
"serializableData" : 
{
"times" : 
[0,0.25,0.55
],
"values" : 
[1,0.98,1
],
"inTangents" : 
[-0.0799999237060547,-0.00666666030883789,0.0666666030883789
],
"outTangents":"copy"
}
}
}
},
{
"nestedFieldType":"AnimationClipCurveData",
"serializableData" : 
{
"path":"",
"type":"Transform",
"propertyName":"m_LocalScale.z",
"curve" : 
{
"nestedFieldType":"AnimationCurve",
"serializableData" : 
{
"times" : 
[0,0.25,0.55
],
"values" : 
[1
],
"inTangents" : 
[0
],
"outTangents":"copy"
}
}
}
},
{
"nestedFieldType":"AnimationClipCurveData",
"serializableData" : 
{
"path":"",
"type":"Transform",
"propertyName":"m_LocalPosition.x",
"curve" : 
{
"nestedFieldType":"AnimationCurve",
"serializableData" : 
{
"times" : 
[0,0.3333333,0.55
],
"values" : 
[0
],
"inTangents" : 
[0
],
"outTangents":"copy"
}
}
}
},
{
"nestedFieldType":"AnimationClipCurveData",
"serializableData" : 
{
"path":"",
"type":"Transform",
"propertyName":"m_LocalPosition.y",
"curve" : 
{
"nestedFieldType":"AnimationCurve",
"serializableData" : 
{
"times" : 
[0,0.25,0.3333333,0.55
],
"values" : 
[8,10,9,8
],
"inTangents" : 
[8,-1.99999952316284,-8.30769157409668,-4.61538457870483
],
"outTangents":"copy"
}
}
}
},
{
"nestedFieldType":"AnimationClipCurveData",
"serializableData" : 
{
"path":"",
"type":"Transform",
"propertyName":"m_LocalPosition.z",
"curve" : 
{
"nestedFieldType":"AnimationCurve",
"serializableData" : 
{
"times" : 
[0,0.3333333,0.55
],
"values" : 
[0
],
"inTangents" : 
[0
],
"outTangents":"copy"
}
}
}
}
],
"events" : 
[
]
}
},
{
"type":"GameObject",
"id":"aab966e3ed9fce14cb44bc112eaae969",
"data" : 
{
"root" : 
[
{
"name":"feature_trail",
"activeSelf":true,
"layer":0,
"components" : 
[
{
"componentType":"Transform",
"enabled":true,
"serializableData" : 
{
"parent" : 
{
"fileID":0
},
"children" : 
[
],
"psr":"d"
},
"fileID":6174
},
{
"componentType":"UIAtlas",
"enabled":true,
"serializableData" : 
{
"textureContent" : 
{
"fileID":2800000,
"guid":"9f369f1228129864b96e824d681938e6"
},
"spriteList" : 
{
"s_feature_trail_fisherman" : 
{
"y":1,
"width":104,
"height":99
},
"s_feature_trail_landscape" : 
{
"y":101,
"width":1482,
"height":295
},
"s_FS_bg" : 
{
"x":105,
"y":1,
"width":103,
"height":99
}
}
},
"fileID":3293
}
],
"fileID":6175
}
]
}
},
{
"type":"AnimationClip",
"id":"267e9322cf2090946b86a5a075851eba",
"data" : 
{
"name":"feature_trail_icon_bump",
"length":0.5,
"wrapMode":0,
"curves" : 
[
{
"nestedFieldType":"AnimationClipCurveData",
"serializableData" : 
{
"path":"",
"type":"Transform",
"propertyName":"m_LocalScale.z",
"curve" : 
{
"nestedFieldType":"AnimationCurve",
"serializableData" : 
{
"times" : 
[0,0.3333333,0.4166667,0.5
],
"values" : 
[1
],
"inTangents" : 
[0
],
"outTangents":"copy"
}
}
}
},
{
"nestedFieldType":"AnimationClipCurveData",
"serializableData" : 
{
"path":"",
"type":"Transform",
"propertyName":"m_LocalScale.y",
"curve" : 
{
"nestedFieldType":"AnimationCurve",
"serializableData" : 
{
"times" : 
[0,0.08333334,0.3333333,0.4166667,0.5
],
"values" : 
[1,1.5,1,1.05,1
],
"inTangents" : 
[0
],
"outTangents":"copy"
}
}
}
},
{
"nestedFieldType":"AnimationClipCurveData",
"serializableData" : 
{
"path":"",
"type":"Transform",
"propertyName":"m_LocalScale.x",
"curve" : 
{
"nestedFieldType":"AnimationCurve",
"serializableData" : 
{
"times" : 
[0,0.08333334,0.3333333,0.4166667,0.5
],
"values" : 
[1,1.5,1,1.05,1
],
"inTangents" : 
[0
],
"outTangents":"copy"
}
}
}
}
],
"events" : 
[
]
}
},
{
"type":"GameObject",
"id":"dc823e41d4e91a74ebc625d0f1c1336d",
"data" : 
{
"root" : 
[
{
"name":"FS_Sign",
"activeSelf":true,
"layer":0,
"components" : 
[
{
"componentType":"Transform",
"enabled":true,
"serializableData" : 
{
"parent" : 
{
"fileID":0
},
"children" : 
[
],
"psr":"d"
},
"fileID":6176
},
{
"componentType":"UIAtlas",
"enabled":true,
"serializableData" : 
{
"textureContent" : 
{
"fileID":2800000,
"guid":"ab28e504aff3f9a4fab2955c2f920234"
},
"spriteList" : 
{
"s_fisherman" : 
{
"x":307,
"y":1,
"width":187,
"height":167
},
"s_fishes" : 
{
"y":31,
"width":306,
"height":137
},
"s_sign" : 
{
"y":169,
"width":996,
"height":900
}
}
},
"fileID":3420
}
],
"fileID":6177
}
]
}
},
{
"type":"GameObject",
"id":"23cec7b8aa369f94c95425298fb75044",
"data" : 
{
"root" : 
[
{
"name":"WinLines",
"activeSelf":true,
"layer":0,
"components" : 
[
{
"componentType":"Transform",
"enabled":true,
"serializableData" : 
{
"parent" : 
{
"fileID":0
},
"children" : 
[
],
"psr":"d"
},
"fileID":6178
},
{
"componentType":"UIAtlas",
"enabled":true,
"serializableData" : 
{
"textureContent" : 
{
"fileID":2800000,
"guid":"141940278ffe5454f802ed853e741d9d"
},
"spriteList" : 
{
"s_line_indicator" : 
{
"x":1411,
"y":550,
"width":43,
"height":44,
"paddingLeft":17,
"paddingTop":17,
"paddingRight":18,
"paddingBottom":17
},
"s_line_indicator_highlight" : 
{
"x":1411,
"y":596,
"width":77,
"height":77,
"paddingRight":1,
"paddingBottom":1
},
"s_winLines_1_2_3" : 
{
"x":1456,
"y":581,
"width":20,
"height":13,
"paddingTop":5,
"paddingBottom":2,
"borderLeft":4,
"borderRight":4
},
"s_winLines_4" : 
{
"width":1409,
"height":335,
"paddingLeft":62,
"paddingTop":7,
"paddingRight":62
},
"s_winLines_5" : 
{
"y":337,
"width":1409,
"height":336,
"paddingLeft":63,
"paddingTop":2,
"paddingRight":61,
"paddingBottom":4
}
}
},
"fileID":3519
}
],
"fileID":6179
}
]
}
},
{
"type":"GameObject",
"id":"eee9bf9d89339264681edb819b174602",
"data" : 
{
"root" : 
[
{
"name":"winlines",
"activeSelf":true,
"layer":0,
"components" : 
[
{
"componentType":"Transform",
"enabled":true,
"serializableData" : 
{
"parent" : 
{
"fileID":0
},
"children" : 
[
],
"psr":"d"
},
"fileID":6180
},
{
"componentType":"UIAtlas",
"enabled":true,
"serializableData" : 
{
"textureContent" : 
{
"fileID":2800000,
"guid":"a9e6313ec53d9d34591822dd522d83fe"
},
"spriteList" : 
{
"s_square" : 
{
"width":64,
"height":64,
"borderLeft":25,
"borderTop":25,
"borderRight":25,
"borderBottom":25
}
}
},
"fileID":3664
}
],
"fileID":6181
}
]
}
},
{
"type":"AudioClip",
"id":"1ff3542452ba95b409655ee602225d21",
"data" : 
{
"path":"res/1ff3542452ba95b409655ee602225d21"
}
},
{
"type":"AudioClip",
"id":"acd1f947ae984834cb4625886d250139",
"data" : 
{
"path":"res/acd1f947ae984834cb4625886d250139"
}
},
{
"type":"AudioClip",
"id":"693d23e2c1766fd4580242798e80c931",
"data" : 
{
"path":"res/693d23e2c1766fd4580242798e80c931"
}
},
{
"type":"AudioClip",
"id":"449b467a62eb8f146958821523282d81",
"data" : 
{
"path":"res/449b467a62eb8f146958821523282d81"
}
},
{
"type":"AudioClip",
"id":"2c5945eef37c8f843989fc1feaa5f774",
"data" : 
{
"path":"res/2c5945eef37c8f843989fc1feaa5f774"
}
},
{
"type":"AudioClip",
"id":"f5d4b2518ca6faa4ebbc7bb0861a40a4",
"data" : 
{
"path":"res/f5d4b2518ca6faa4ebbc7bb0861a40a4"
}
},
{
"type":"AudioClip",
"id":"fbfc643395e96ca48b26afbde4f5c4a4",
"data" : 
{
"path":"res/fbfc643395e96ca48b26afbde4f5c4a4"
}
},
{
"type":"AudioClip",
"id":"8b11b54b721817d4ba95cfacd79af491",
"data" : 
{
"path":"res/8b11b54b721817d4ba95cfacd79af491"
}
},
{
"type":"AudioClip",
"id":"3ba4fb60abfe8854b835a7c91c8dfedd",
"data" : 
{
"path":"res/3ba4fb60abfe8854b835a7c91c8dfedd"
}
},
{
"type":"AudioClip",
"id":"16864169eb4b44349a3ff6a205afbe04",
"data" : 
{
"path":"res/16864169eb4b44349a3ff6a205afbe04"
}
},
{
"type":"AudioClip",
"id":"44b3984dcb1aa3343b723fc06a4327ed",
"data" : 
{
"path":"res/44b3984dcb1aa3343b723fc06a4327ed"
}
},
{
"type":"AudioClip",
"id":"001df54acc624694a94820f46856578b",
"data" : 
{
"path":"res/001df54acc624694a94820f46856578b"
}
},
{
"type":"AudioClip",
"id":"e3fbe7b2888d8284ea7119c18f1d3a97",
"data" : 
{
"path":"res/e3fbe7b2888d8284ea7119c18f1d3a97"
}
},
{
"type":"AudioClip",
"id":"485a71e9f7e626d4894058cd81639f63",
"data" : 
{
"path":"res/485a71e9f7e626d4894058cd81639f63"
}
},
{
"type":"AudioClip",
"id":"0abb07da9b5645a46a69da4ab9385ec4",
"data" : 
{
"path":"res/0abb07da9b5645a46a69da4ab9385ec4"
}
},
{
"type":"AudioClip",
"id":"42e7dee34b4ce9442bbf2c7f71a5b613",
"data" : 
{
"path":"res/42e7dee34b4ce9442bbf2c7f71a5b613"
}
},
{
"type":"AudioClip",
"id":"49584eb84dabc1b4aa6d2b58b10f8062",
"data" : 
{
"path":"res/49584eb84dabc1b4aa6d2b58b10f8062"
}
},
{
"type":"AudioClip",
"id":"15a5662eee5877144961aed23792af6f",
"data" : 
{
"path":"res/15a5662eee5877144961aed23792af6f"
}
},
{
"type":"AudioClip",
"id":"655ce8acebb2d3941a5e686118db0794",
"data" : 
{
"path":"res/655ce8acebb2d3941a5e686118db0794"
}
},
{
"type":"AudioClip",
"id":"41965fe4f5e42d44baf4babc1245047c",
"data" : 
{
"path":"res/41965fe4f5e42d44baf4babc1245047c"
}
},
{
"type":"AudioClip",
"id":"8d33b7e80ab75454598a7d74ea9e7644",
"data" : 
{
"path":"res/8d33b7e80ab75454598a7d74ea9e7644"
}
},
{
"type":"AudioClip",
"id":"8f89a6bf762dfd94ca4af48db13aaba6",
"data" : 
{
"path":"res/8f89a6bf762dfd94ca4af48db13aaba6"
}
},
{
"type":"AudioClip",
"id":"0c57c7c1a2edf3e44b4b9b134b409ef8",
"data" : 
{
"path":"res/0c57c7c1a2edf3e44b4b9b134b409ef8"
}
},
{
"type":"AudioClip",
"id":"7466eae9fa1a8ca43ba573a19bfd04d6",
"data" : 
{
"path":"res/7466eae9fa1a8ca43ba573a19bfd04d6"
}
},
{
"type":"GameObject",
"id":"2edb09565e62f654a9e9ffff9ee16667",
"data" : 
{
"root" : 
[
{
"name":"SymbolThumbnail",
"activeSelf":true,
"layer":0,
"components" : 
[
{
"componentType":"Transform",
"enabled":true,
"serializableData" : 
{
"parent" : 
{
"fileID":0
},
"children" : 
[
],
"psr":"d"
},
"fileID":6182
},
{
"componentType":"UIAtlas",
"enabled":true,
"serializableData" : 
{
"textureContent" : 
{
"fileID":2800000,
"guid":"44fac5ac9d3f68b4ebf2151fc175f613"
},
"spriteList" : 
{
"s_s01" : 
{
"x":209,
"y":145,
"width":73,
"height":80
},
"s_s02" : 
{
"y":120,
"width":77,
"height":76,
"paddingLeft":2,
"paddingRight":1
},
"s_s03" : 
{
"x":162,
"y":5,
"width":80,
"height":67,
"paddingBottom":1
},
"s_s04" : 
{
"x":243,
"y":15,
"width":78,
"height":57,
"paddingLeft":1,
"paddingTop":7,
"paddingRight":1,
"paddingBottom":7
},
"s_s05" : 
{
"y":197,
"width":127,
"height":109,
"paddingLeft":5,
"paddingTop":11,
"paddingRight":4,
"paddingBottom":14
},
"s_s06" : 
{
"x":128,
"y":152,
"width":80,
"height":75,
"paddingTop":1
},
"s_s08" : 
{
"x":128,
"y":228,
"width":80,
"height":78
},
"s_s09" : 
{
"x":283,
"y":145,
"width":70,
"height":80
},
"s_s10" : 
{
"x":209,
"y":226,
"width":78,
"height":80
},
"s_s11" : 
{
"x":288,
"y":226,
"width":62,
"height":80,
"paddingLeft":9,
"paddingRight":9
},
"s_s12" : 
{
"x":78,
"y":79,
"width":79,
"height":72
},
"s_s17" : 
{
"x":158,
"y":73,
"width":80,
"height":71,
"paddingTop":5
},
"s_s18" : 
{
"x":81,
"y":1,
"width":80,
"height":71,
"paddingTop":5
},
"s_s19" : 
{
"y":7,
"width":80,
"height":71,
"paddingTop":5
},
"s_s20" : 
{
"x":239,
"y":73,
"width":80,
"height":71,
"paddingTop":5
}
}
},
"fileID":3903
}
],
"fileID":6183
}
]
}
},
{
"type":"AudioClip",
"id":"6f2f4e604e6bd124b93b04d0d7411c3f",
"data" : 
{
"path":"res/6f2f4e604e6bd124b93b04d0d7411c3f"
}
},
{
"type":"AudioClip",
"id":"ea1d2a8cb55828d47b3b09e6c2139908",
"data" : 
{
"path":"res/ea1d2a8cb55828d47b3b09e6c2139908"
}
},
{
"type":"AudioClip",
"id":"a3758576abda6064982e608fc79b62ed",
"data" : 
{
"path":"res/a3758576abda6064982e608fc79b62ed"
}
},
{
"type":"AudioClip",
"id":"01e889cf7e88c7d4ab8c816d9e4dfc7a",
"data" : 
{
"path":"res/01e889cf7e88c7d4ab8c816d9e4dfc7a"
}
},
{
"type":"AudioClip",
"id":"81c86bb190af8a84f8530996961d6289",
"data" : 
{
"path":"res/81c86bb190af8a84f8530996961d6289"
}
},
{
"type":"AudioClip",
"id":"3c724b00408b6f5408f477b4ad4677b6",
"data" : 
{
"path":"res/3c724b00408b6f5408f477b4ad4677b6"
}
},
{
"type":"AudioClip",
"id":"6acba57afef635a488f82559f1fe72b7",
"data" : 
{
"path":"res/6acba57afef635a488f82559f1fe72b7"
}
},
{
"type":"AudioClip",
"id":"8d964c0823e16eb43b850b24c566820c",
"data" : 
{
"path":"res/8d964c0823e16eb43b850b24c566820c"
}
},
{
"type":"AudioClip",
"id":"66a56af12b59bcc46a0c7237160b3b7c",
"data" : 
{
"path":"res/66a56af12b59bcc46a0c7237160b3b7c"
}
},
{
"type":"AudioClip",
"id":"7521b9165e6d90941967e10ef64d1097",
"data" : 
{
"path":"res/7521b9165e6d90941967e10ef64d1097"
}
},
{
"type":"AudioClip",
"id":"c14fbebc198ecbc46ad45ceb878fd640",
"data" : 
{
"path":"res/c14fbebc198ecbc46ad45ceb878fd640"
}
},
{
"type":"AudioClip",
"id":"4d7f983c70cc819418d9a255910a22d6",
"data" : 
{
"path":"res/4d7f983c70cc819418d9a255910a22d6"
}
},
{
"type":"AudioClip",
"id":"2370e71c68d313442819cc0c6c53a492",
"data" : 
{
"path":"res/2370e71c68d313442819cc0c6c53a492"
}
},
{
"type":"AudioClip",
"id":"8816edc1147c44d43b164b0ed88ccef1",
"data" : 
{
"path":"res/8816edc1147c44d43b164b0ed88ccef1"
}
},
{
"type":"AudioClip",
"id":"f50aa3d9f6b84154ab93bef202ca1878",
"data" : 
{
"path":"res/f50aa3d9f6b84154ab93bef202ca1878"
}
},
{
"type":"AudioClip",
"id":"fb6eb381007028d4391f7f9744dd5492",
"data" : 
{
"path":"res/fb6eb381007028d4391f7f9744dd5492"
}
},
{
"type":"AudioClip",
"id":"525f6943355638f4bbcab361dd60cea6",
"data" : 
{
"path":"res/525f6943355638f4bbcab361dd60cea6"
}
},
{
"type":"AudioClip",
"id":"c3d156f99863c604080c38863d943c75",
"data" : 
{
"path":"res/c3d156f99863c604080c38863d943c75"
}
},
{
"type":"AudioClip",
"id":"61312232c5508064e956d7be06551b42",
"data" : 
{
"path":"res/61312232c5508064e956d7be06551b42"
}
},
{
"type":"AudioClip",
"id":"34588ec2cde1a6147915909564947691",
"data" : 
{
"path":"res/34588ec2cde1a6147915909564947691"
}
},
{
"type":"AudioClip",
"id":"4cddbcfa34d126e449e20076272fcabc",
"data" : 
{
"path":"res/4cddbcfa34d126e449e20076272fcabc"
}
},
{
"type":"AudioClip",
"id":"846bf26383287614e8c50e0a61cca76c",
"data" : 
{
"path":"res/846bf26383287614e8c50e0a61cca76c"
}
},
{
"type":"AudioClip",
"id":"c052eabd34334484fbdf950ac3e7c5cd",
"data" : 
{
"path":"res/c052eabd34334484fbdf950ac3e7c5cd"
}
},
{
"type":"AnimationClip",
"id":"1c194606ec976db4e95ef96e0b7af795",
"data" : 
{
"name":"BetAlphaToTransp",
"length":0.01666667,
"wrapMode":0,
"curves" : 
[
{
"nestedFieldType":"AnimationClipCurveData",
"serializableData" : 
{
"path":"",
"type":"PanelAlphaProxy",
"propertyName":"alpha",
"curve" : 
{
"nestedFieldType":"AnimationCurve",
"serializableData" : 
{
"times" : 
[0,0.01666667
],
"values" : 
[0.48
],
"inTangents" : 
[0
],
"outTangents":"copy"
}
}
}
}
],
"events" : 
[
]
}
},
{
"type":"AnimationClip",
"id":"ef1a1ca1a73e86547836bff12bc27129",
"data" : 
{
"name":"BetAlphaToFull",
"length":0.01666667,
"wrapMode":0,
"curves" : 
[
{
"nestedFieldType":"AnimationClipCurveData",
"serializableData" : 
{
"path":"",
"type":"PanelAlphaProxy",
"propertyName":"alpha",
"curve" : 
{
"nestedFieldType":"AnimationCurve",
"serializableData" : 
{
"times" : 
[0,0.01666667
],
"values" : 
[1
],
"inTangents" : 
[1.79769313486232E+308
],
"outTangents":"copy"
}
}
}
}
],
"events" : 
[
]
}
},
{
"type":"AnimationClip",
"id":"5acb0f9680e9a814cb81f578228ef990",
"data" : 
{
"name":"BetButtonPress",
"length":0.01666667,
"wrapMode":0,
"curves" : 
[
{
"nestedFieldType":"AnimationClipCurveData",
"serializableData" : 
{
"path":"",
"type":"Transform",
"propertyName":"m_LocalScale.x",
"curve" : 
{
"nestedFieldType":"AnimationCurve",
"serializableData" : 
{
"times" : 
[0,0.01666667
],
"values" : 
[0.95
],
"inTangents" : 
[0
],
"outTangents":"copy"
}
}
}
},
{
"nestedFieldType":"AnimationClipCurveData",
"serializableData" : 
{
"path":"",
"type":"Transform",
"propertyName":"m_LocalScale.y",
"curve" : 
{
"nestedFieldType":"AnimationCurve",
"serializableData" : 
{
"times" : 
[0,0.01666667
],
"values" : 
[0.95
],
"inTangents" : 
[0
],
"outTangents":"copy"
}
}
}
},
{
"nestedFieldType":"AnimationClipCurveData",
"serializableData" : 
{
"path":"",
"type":"Transform",
"propertyName":"m_LocalScale.z",
"curve" : 
{
"nestedFieldType":"AnimationCurve",
"serializableData" : 
{
"times" : 
[0,0.01666667
],
"values" : 
[1
],
"inTangents" : 
[0
],
"outTangents":"copy"
}
}
}
}
],
"events" : 
[
]
}
},
{
"type":"AnimationClip",
"id":"e1b29f3599175c3449279e6269c88b2a",
"data" : 
{
"name":"BetButtonClick",
"length":0.3333333,
"wrapMode":0,
"curves" : 
[
{
"nestedFieldType":"AnimationClipCurveData",
"serializableData" : 
{
"path":"",
"type":"Transform",
"propertyName":"m_LocalScale.x",
"curve" : 
{
"nestedFieldType":"AnimationCurve",
"serializableData" : 
{
"times" : 
[0,0.06666667,0.2666667,0.3333333
],
"values" : 
[0.95,1.04,0.98,1
],
"inTangents" : 
[0
],
"outTangents" : 
[1.3499995470047,0
]
}
}
}
},
{
"nestedFieldType":"AnimationClipCurveData",
"serializableData" : 
{
"path":"",
"type":"Transform",
"propertyName":"m_LocalScale.y",
"curve" : 
{
"nestedFieldType":"AnimationCurve",
"serializableData" : 
{
"times" : 
[0,0.1166667,0.2333333,0.3333333
],
"values" : 
[0.95,0.96,1.02,1
],
"inTangents" : 
[0
],
"outTangents" : 
[0.0857142060995102,0
]
}
}
}
},
{
"nestedFieldType":"AnimationClipCurveData",
"serializableData" : 
{
"path":"",
"type":"Transform",
"propertyName":"m_LocalScale.z",
"curve" : 
{
"nestedFieldType":"AnimationCurve",
"serializableData" : 
{
"times" : 
[0,0.3333333
],
"values" : 
[1
],
"inTangents" : 
[0
],
"outTangents":"copy"
}
}
}
}
],
"events" : 
[
]
}
},
{
"type":"AnimationClip",
"id":"5181b3d8cb76267468d190d1cf0c6748",
"data" : 
{
"name":"BetButtonReset",
"length":0.01666667,
"wrapMode":0,
"curves" : 
[
{
"nestedFieldType":"AnimationClipCurveData",
"serializableData" : 
{
"path":"",
"type":"Transform",
"propertyName":"m_LocalScale.x",
"curve" : 
{
"nestedFieldType":"AnimationCurve",
"serializableData" : 
{
"times" : 
[0,0.01666667
],
"values" : 
[1
],
"inTangents" : 
[1.79769313486232E+308
],
"outTangents":"copy"
}
}
}
},
{
"nestedFieldType":"AnimationClipCurveData",
"serializableData" : 
{
"path":"",
"type":"Transform",
"propertyName":"m_LocalScale.y",
"curve" : 
{
"nestedFieldType":"AnimationCurve",
"serializableData" : 
{
"times" : 
[0,0.01666667
],
"values" : 
[1
],
"inTangents" : 
[1.79769313486232E+308
],
"outTangents":"copy"
}
}
}
},
{
"nestedFieldType":"AnimationClipCurveData",
"serializableData" : 
{
"path":"",
"type":"Transform",
"propertyName":"m_LocalScale.z",
"curve" : 
{
"nestedFieldType":"AnimationCurve",
"serializableData" : 
{
"times" : 
[0,0.01666667
],
"values" : 
[1
],
"inTangents" : 
[1.79769313486232E+308
],
"outTangents":"copy"
}
}
}
}
],
"events" : 
[
]
}
},
{
"type":"GameObject",
"id":"96c3f47bf422c2f4a960bc92389df895",
"data" : 
{
"root" : 
[
{
"name":"BetBuy",
"activeSelf":true,
"layer":0,
"components" : 
[
{
"componentType":"Transform",
"enabled":true,
"serializableData" : 
{
"parent" : 
{
"fileID":0
},
"children" : 
[
],
"psr":"d"
},
"fileID":6184
},
{
"componentType":"UIAtlas",
"enabled":true,
"serializableData" : 
{
"textureContent" : 
{
"fileID":2800000,
"guid":"0e427027226969943af4d879a788ded7"
},
"spriteList" : 
{
"s_Arrow" : 
{
"x":691,
"y":581,
"width":56,
"height":42
},
"s_Checkmark" : 
{
"x":691,
"y":539,
"width":48,
"height":41
},
"s_Green" : 
{
"x":691,
"y":624,
"width":86,
"height":46
},
"s_Nope" : 
{
"x":748,
"y":576,
"width":45,
"height":47,
"paddingLeft":1,
"paddingTop":1,
"paddingRight":2,
"paddingBottom":1
},
"s_Purchase_Landscape" : 
{
"width":310,
"height":430
},
"s_Purchase_Portrait" : 
{
"y":431,
"width":690,
"height":286
},
"s_Red" : 
{
"x":691,
"y":671,
"width":86,
"height":46
},
"s_Slider" : 
{
"x":311,
"y":368,
"width":252,
"height":62
}
}
},
"fileID":4079
}
],
"fileID":6185
}
]
}
},
{
"type":"AnimationClip",
"id":"03157f4cce74cc4458f7bde105ae6895",
"data" : 
{
"name":"BetButtonSliderClick",
"length":0.1833333,
"wrapMode":0,
"curves" : 
[
{
"nestedFieldType":"AnimationClipCurveData",
"serializableData" : 
{
"path":"SlideBtn",
"type":"Transform",
"propertyName":"m_LocalPosition.x",
"curve" : 
{
"nestedFieldType":"AnimationCurve",
"serializableData" : 
{
"times" : 
[0,0.1833333
],
"values" : 
[-42,42
],
"inTangents" : 
[0
],
"outTangents":"copy"
}
}
}
},
{
"nestedFieldType":"AnimationClipCurveData",
"serializableData" : 
{
"path":"SlideBtn",
"type":"Transform",
"propertyName":"m_LocalPosition.y",
"curve" : 
{
"nestedFieldType":"AnimationCurve",
"serializableData" : 
{
"times" : 
[0,0.1833333
],
"values" : 
[-1
],
"inTangents" : 
[0
],
"outTangents":"copy"
}
}
}
},
{
"nestedFieldType":"AnimationClipCurveData",
"serializableData" : 
{
"path":"SlideBtn",
"type":"Transform",
"propertyName":"m_LocalPosition.z",
"curve" : 
{
"nestedFieldType":"AnimationCurve",
"serializableData" : 
{
"times" : 
[0,0.1833333
],
"values" : 
[0
],
"inTangents" : 
[0
],
"outTangents":"copy"
}
}
}
}
],
"events" : 
[
]
}
},
{
"type":"AnimationClip",
"id":"e91832de70084a64aab782830778e071",
"data" : 
{
"name":"GenericWindowAppear",
"length":0.5,
"wrapMode":0,
"curves" : 
[
{
"nestedFieldType":"AnimationClipCurveData",
"serializableData" : 
{
"path":"",
"type":"Transform",
"propertyName":"m_LocalScale.x",
"curve" : 
{
"nestedFieldType":"AnimationCurve",
"serializableData" : 
{
"times" : 
[0,0.3,0.5
],
"values" : 
[0.1,1.03,1
],
"inTangents" : 
[0
],
"outTangents" : 
[3.09999966621399,0
]
}
}
}
},
{
"nestedFieldType":"AnimationClipCurveData",
"serializableData" : 
{
"path":"",
"type":"Transform",
"propertyName":"m_LocalScale.y",
"curve" : 
{
"nestedFieldType":"AnimationCurve",
"serializableData" : 
{
"times" : 
[0,0.3,0.5
],
"values" : 
[0.1,1.03,1
],
"inTangents" : 
[0
],
"outTangents" : 
[3.09999966621399,0
]
}
}
}
},
{
"nestedFieldType":"AnimationClipCurveData",
"serializableData" : 
{
"path":"",
"type":"Transform",
"propertyName":"m_LocalScale.z",
"curve" : 
{
"nestedFieldType":"AnimationCurve",
"serializableData" : 
{
"times" : 
[0,0.5
],
"values" : 
[1
],
"inTangents" : 
[0
],
"outTangents":"copy"
}
}
}
}
],
"events" : 
[
]
}
},
{
"type":"AnimationClip",
"id":"484d85a8f84c6074e830da6b1ffd1b30",
"data" : 
{
"name":"panelFade1sec",
"length":1,
"wrapMode":0,
"curves" : 
[
{
"nestedFieldType":"AnimationClipCurveData",
"serializableData" : 
{
"path":"",
"type":"PanelAlphaProxy",
"propertyName":"alpha",
"curve" : 
{
"nestedFieldType":"AnimationCurve",
"serializableData" : 
{
"times" : 
[0,1
],
"values" : 
[0.01,1
],
"inTangents" : 
[0.990000009536743
],
"outTangents":"copy"
}
}
}
}
],
"events" : 
[
]
}
},
{
"type":"GameObject",
"id":"288e4fea6576848419739e18fcd3634d",
"data" : 
{
"root" : 
[
{
"name":"rotating_coin",
"activeSelf":true,
"layer":0,
"components" : 
[
{
"componentType":"Transform",
"enabled":true,
"serializableData" : 
{
"parent" : 
{
"fileID":0
},
"children" : 
[
],
"psr":"d"
},
"fileID":6186
},
{
"componentType":"UIAtlas",
"enabled":true,
"serializableData" : 
{
"textureContent" : 
{
"fileID":2800000,
"guid":"333b61bd4412ed84b8e6b13be59ee192"
},
"spriteList" : 
{
"s_frame00" : 
{
"y":973,
"width":360,
"height":351,
"paddingLeft":61,
"paddingTop":21,
"paddingRight":79,
"paddingBottom":3
},
"s_frame01" : 
{
"x":362,
"y":974,
"width":360,
"height":350,
"paddingLeft":62,
"paddingTop":1,
"paddingRight":78,
"paddingBottom":24
},
"s_frame02" : 
{
"x":368,
"y":131,
"width":366,
"height":275,
"paddingLeft":61,
"paddingTop":24,
"paddingRight":73,
"paddingBottom":76
},
"s_frame03" : 
{
"x":373,
"y":3,
"width":371,
"height":126,
"paddingLeft":60,
"paddingTop":108,
"paddingRight":69,
"paddingBottom":141
},
"s_frame04" : 
{
"x":736,
"y":269,
"width":370,
"height":137,
"paddingLeft":60,
"paddingTop":136,
"paddingRight":70,
"paddingBottom":102
},
"s_frame05" : 
{
"x":724,
"y":408,
"width":364,
"height":281,
"paddingLeft":61,
"paddingTop":72,
"paddingRight":75,
"paddingBottom":22
},
"s_frame06" : 
{
"x":724,
"y":974,
"width":359,
"height":350,
"paddingLeft":62,
"paddingTop":22,
"paddingRight":79,
"paddingBottom":3
},
"s_frame07" : 
{
"x":362,
"y":623,
"width":360,
"height":349,
"paddingLeft":62,
"paddingTop":1,
"paddingRight":78,
"paddingBottom":25
},
"s_frame08" : 
{
"y":346,
"width":366,
"height":275,
"paddingLeft":61,
"paddingTop":23,
"paddingRight":73,
"paddingBottom":77
},
"s_frame09" : 
{
"y":2,
"width":371,
"height":127,
"paddingLeft":60,
"paddingTop":106,
"paddingRight":69,
"paddingBottom":142
},
"s_frame10" : 
{
"x":736,
"y":131,
"width":370,
"height":136,
"paddingLeft":60,
"paddingTop":135,
"paddingRight":70,
"paddingBottom":104
},
"s_frame11" : 
{
"x":724,
"y":691,
"width":364,
"height":281,
"paddingLeft":61,
"paddingTop":71,
"paddingRight":75,
"paddingBottom":23
}
}
},
"fileID":4458
}
],
"fileID":6187
}
]
}
},
{
"type":"AnimationClip",
"id":"1c5d034d0d040014a816457c1579e62a",
"data" : 
{
"name":"Title_in",
"length":0.5,
"wrapMode":0,
"curves" : 
[
{
"nestedFieldType":"AnimationClipCurveData",
"serializableData" : 
{
"path":"",
"type":"Transform",
"propertyName":"m_LocalScale.x",
"curve" : 
{
"nestedFieldType":"AnimationCurve",
"serializableData" : 
{
"times" : 
[0,0.5
],
"values" : 
[0,1.5
],
"inTangents" : 
[3
],
"outTangents":"copy"
}
}
}
},
{
"nestedFieldType":"AnimationClipCurveData",
"serializableData" : 
{
"path":"",
"type":"Transform",
"propertyName":"m_LocalScale.y",
"curve" : 
{
"nestedFieldType":"AnimationCurve",
"serializableData" : 
{
"times" : 
[0,0.5
],
"values" : 
[0,1.5
],
"inTangents" : 
[3
],
"outTangents":"copy"
}
}
}
},
{
"nestedFieldType":"AnimationClipCurveData",
"serializableData" : 
{
"path":"",
"type":"Transform",
"propertyName":"m_LocalScale.z",
"curve" : 
{
"nestedFieldType":"AnimationCurve",
"serializableData" : 
{
"times" : 
[0,0.5
],
"values" : 
[0
],
"inTangents" : 
[0
],
"outTangents":"copy"
}
}
}
},
{
"nestedFieldType":"AnimationClipCurveData",
"serializableData" : 
{
"path":"",
"type":"Transform",
"propertyName":"m_LocalPosition.x",
"curve" : 
{
"nestedFieldType":"AnimationCurve",
"serializableData" : 
{
"times" : 
[0,0.25,0.5
],
"values" : 
[0
],
"inTangents" : 
[0
],
"outTangents":"copy"
}
}
}
},
{
"nestedFieldType":"AnimationClipCurveData",
"serializableData" : 
{
"path":"",
"type":"Transform",
"propertyName":"m_LocalPosition.y",
"curve" : 
{
"nestedFieldType":"AnimationCurve",
"serializableData" : 
{
"times" : 
[0,0.25,0.5
],
"values" : 
[-800,-318,-425
],
"inTangents" : 
[1928,750,-428
],
"outTangents":"copy"
}
}
}
},
{
"nestedFieldType":"AnimationClipCurveData",
"serializableData" : 
{
"path":"",
"type":"Transform",
"propertyName":"m_LocalPosition.z",
"curve" : 
{
"nestedFieldType":"AnimationCurve",
"serializableData" : 
{
"times" : 
[0,0.25,0.5
],
"values" : 
[0
],
"inTangents" : 
[0
],
"outTangents":"copy"
}
}
}
}
],
"events" : 
[
]
}
},
{
"type":"AnimationClip",
"id":"b3fab036ca90a1842a8c784f51691b9d",
"data" : 
{
"name":"Title_out",
"length":0.25,
"wrapMode":0,
"curves" : 
[
{
"nestedFieldType":"AnimationClipCurveData",
"serializableData" : 
{
"path":"",
"type":"Transform",
"propertyName":"m_LocalScale.x",
"curve" : 
{
"nestedFieldType":"AnimationCurve",
"serializableData" : 
{
"times" : 
[0,0.25
],
"values" : 
[1.5,0
],
"inTangents" : 
[-6
],
"outTangents":"copy"
}
}
}
},
{
"nestedFieldType":"AnimationClipCurveData",
"serializableData" : 
{
"path":"",
"type":"Transform",
"propertyName":"m_LocalScale.y",
"curve" : 
{
"nestedFieldType":"AnimationCurve",
"serializableData" : 
{
"times" : 
[0,0.25
],
"values" : 
[1.5,0
],
"inTangents" : 
[-6
],
"outTangents":"copy"
}
}
}
},
{
"nestedFieldType":"AnimationClipCurveData",
"serializableData" : 
{
"path":"",
"type":"Transform",
"propertyName":"m_LocalScale.z",
"curve" : 
{
"nestedFieldType":"AnimationCurve",
"serializableData" : 
{
"times" : 
[0,0.25
],
"values" : 
[0
],
"inTangents" : 
[0
],
"outTangents":"copy"
}
}
}
},
{
"nestedFieldType":"AnimationClipCurveData",
"serializableData" : 
{
"path":"",
"type":"Transform",
"propertyName":"m_LocalPosition.x",
"curve" : 
{
"nestedFieldType":"AnimationCurve",
"serializableData" : 
{
"times" : 
[0,0.25
],
"values" : 
[0
],
"inTangents" : 
[0
],
"outTangents":"copy"
}
}
}
},
{
"nestedFieldType":"AnimationClipCurveData",
"serializableData" : 
{
"path":"",
"type":"Transform",
"propertyName":"m_LocalPosition.y",
"curve" : 
{
"nestedFieldType":"AnimationCurve",
"serializableData" : 
{
"times" : 
[0,0.1333333,0.25
],
"values" : 
[-425,-518,-705
],
"inTangents" : 
[-697.499938964844,-1150.17858886719,-1602.85717773438
],
"outTangents":"copy"
}
}
}
},
{
"nestedFieldType":"AnimationClipCurveData",
"serializableData" : 
{
"path":"",
"type":"Transform",
"propertyName":"m_LocalPosition.z",
"curve" : 
{
"nestedFieldType":"AnimationCurve",
"serializableData" : 
{
"times" : 
[0,0.25
],
"values" : 
[0
],
"inTangents" : 
[0
],
"outTangents":"copy"
}
}
}
}
],
"events" : 
[
]
}
},
{
"type":"GameObject",
"id":"a134ed1617bb6514398ad59c5bba67b0",
"data" : 
{
"root" : 
[
{
"name":"win_message_Material",
"activeSelf":true,
"layer":0,
"components" : 
[
{
"componentType":"Transform",
"enabled":true,
"serializableData" : 
{
"parent" : 
{
"fileID":0
},
"children" : 
[
],
"psr":"d"
},
"fileID":6188
},
{
"componentType":"UIAtlas",
"enabled":true,
"serializableData" : 
{
"textureContent" : 
{
"fileID":2800000,
"guid":"547dcae77960d19498e59a7bc948c9d4"
},
"spriteList" : 
{
"s__all_flakes_256_00" : 
{
"x":2429,
"y":517,
"width":256,
"height":256
},
"s__all_flakes_256_01" : 
{
"x":2687,
"y":517,
"width":256,
"height":256
},
"s__all_flakes_256_02" : 
{
"x":2429,
"y":259,
"width":256,
"height":256
},
"s__all_flakes_256_03" : 
{
"x":2687,
"y":259,
"width":256,
"height":256
},
"s__all_flakes_256_04" : 
{
"x":2429,
"y":1,
"width":256,
"height":256
},
"s__all_flakes_256_05" : 
{
"x":2687,
"y":1,
"width":256,
"height":256
},
"s_BIG" : 
{
"x":1,
"y":7,
"width":2426,
"height":766
},
"s_EPIC" : 
{
"x":1,
"y":2289,
"width":2809,
"height":770
},
"s_MEGA" : 
{
"x":1,
"y":1518,
"width":2750,
"height":769
},
"s_SUPER" : 
{
"x":1,
"y":775,
"width":2837,
"height":741
},
"s_win_blackout" : 
{
"rotate":1,
"x":2753,
"y":1921,
"width":208,
"height":366
}
}
},
"fileID":4588
}
],
"fileID":6189
}
]
}
},
{
"type":"GameObject",
"id":"cb38e6577fedf5c4fbec28ae11fd88a6",
"data" : 
{
"root" : 
[
{
"name":"BigWin_Bass",
"activeSelf":true,
"layer":0,
"components" : 
[
{
"componentType":"Transform",
"enabled":true,
"serializableData" : 
{
"parent" : 
{
"fileID":0
},
"children" : 
[
],
"psr":"d"
},
"fileID":6190
},
{
"componentType":"UIAtlas",
"enabled":true,
"serializableData" : 
{
"textureContent" : 
{
"fileID":2800000,
"guid":"4cff316108d98a74aabbe32f3d103472"
},
"spriteList" : 
{
"s_BIG" : 
{
"y":1,
"width":607,
"height":192
},
"s_EPIC" : 
{
"y":194,
"width":703,
"height":193
},
"s_MEGA" : 
{
"y":388,
"width":764,
"height":193
},
"s_SUPER" : 
{
"y":582,
"width":842,
"height":186
}
}
},
"fileID":4591
}
],
"fileID":6191
}
]
}
},
{
"type":"AnimationClip",
"id":"492b07765ae64174285f007acc643230",
"data" : 
{
"name":"labelPulseAnimCustom",
"length":1,
"wrapMode":0,
"curves" : 
[
{
"nestedFieldType":"AnimationClipCurveData",
"serializableData" : 
{
"path":"",
"type":"Transform",
"propertyName":"m_LocalScale.x",
"curve" : 
{
"nestedFieldType":"AnimationCurve",
"serializableData" : 
{
"times" : 
[0,0.08333334,0.3333333,0.4166667,1
],
"values" : 
[1,1.3,1,1.3,1
],
"inTangents" : 
[0
],
"outTangents":"copy"
}
}
}
},
{
"nestedFieldType":"AnimationClipCurveData",
"serializableData" : 
{
"path":"",
"type":"Transform",
"propertyName":"m_LocalScale.y",
"curve" : 
{
"nestedFieldType":"AnimationCurve",
"serializableData" : 
{
"times" : 
[0,0.08333334,0.3333333,0.4166667,1
],
"values" : 
[1,1.3,1,1.3,1
],
"inTangents" : 
[0
],
"outTangents":"copy"
}
}
}
},
{
"nestedFieldType":"AnimationClipCurveData",
"serializableData" : 
{
"path":"",
"type":"Transform",
"propertyName":"m_LocalScale.z",
"curve" : 
{
"nestedFieldType":"AnimationCurve",
"serializableData" : 
{
"times" : 
[0,0.3333333,1
],
"values" : 
[1
],
"inTangents" : 
[0
],
"outTangents":"copy"
}
}
}
}
],
"events" : 
[
]
}
},
{
"type":"GameObject",
"id":"446f0e4093ef2e04f885b29c726f9200",
"data" : 
{
"root" : 
[
{
"name":"bigbass",
"activeSelf":true,
"layer":0,
"components" : 
[
{
"componentType":"Transform",
"enabled":true,
"serializableData" : 
{
"parent" : 
{
"fileID":0
},
"children" : 
[
],
"psr":"d"
},
"fileID":6192
},
{
"componentType":"UIFont",
"enabled":true,
"serializableData" : 
{
"fontData":"<?xml version=\"1.0\"?><font><info face=\"bigbass26a698ecdbb49594e8a3eee1346c5d25\" size=\"320\" bold=\"0\" italic=\"0\" charset=\"\" unicode=\"1\" stretchH=\"100\" smooth=\"1\" aa=\"4\" padding=\"10,15,15,15\" spacing=\"1,1\" outline=\"0\" /><common lineHeight=\"320\" base=\"236\" scaleW=\"1024\" scaleH=\"1024\" pages=\"1\" packed=\"0\" alphaChnl=\"1\" redChnl=\"0\" greenChnl=\"0\" blueChnl=\"0\" /><pages><page id=\"0\" file=\"bigbass_0.png\" /></pages><chars count=\"23\"><char id=\"36\" x=\"0\" y=\"0\" width=\"137\" height=\"240\" xoffset=\"-6\" yoffset=\"48\" xadvance=\"120\" page=\"0\" chnl=\"15\" /><char id=\"44\" x=\"885\" y=\"208\" width=\"88\" height=\"109\" xoffset=\"-22\" yoffset=\"183\" xadvance=\"60\" page=\"0\" chnl=\"15\" /><char id=\"45\" x=\"173\" y=\"606\" width=\"148\" height=\"42\" xoffset=\"-12\" yoffset=\"137\" xadvance=\"120\" page=\"0\" chnl=\"15\" /><char id=\"46\" x=\"885\" y=\"318\" width=\"73\" height=\"63\" xoffset=\"0\" yoffset=\"192\" xadvance=\"60\" page=\"0\" chnl=\"15\" /><char id=\"48\" x=\"456\" y=\"0\" width=\"137\" height=\"207\" xoffset=\"-6\" yoffset=\"48\" xadvance=\"120\" page=\"0\" chnl=\"15\" /><char id=\"49\" x=\"290\" y=\"213\" width=\"137\" height=\"201\" xoffset=\"-6\" yoffset=\"50\" xadvance=\"120\" page=\"0\" chnl=\"15\" /><char id=\"50\" x=\"146\" y=\"213\" width=\"143\" height=\"203\" xoffset=\"-14\" yoffset=\"48\" xadvance=\"120\" page=\"0\" chnl=\"15\" /><char id=\"51\" x=\"311\" y=\"0\" width=\"144\" height=\"207\" xoffset=\"-10\" yoffset=\"48\" xadvance=\"120\" page=\"0\" chnl=\"15\" /><char id=\"52\" x=\"566\" y=\"208\" width=\"136\" height=\"199\" xoffset=\"-8\" yoffset=\"52\" xadvance=\"120\" page=\"0\" chnl=\"15\" /><char id=\"53\" x=\"0\" y=\"241\" width=\"145\" height=\"203\" xoffset=\"-10\" yoffset=\"52\" xadvance=\"120\" page=\"0\" chnl=\"15\" /><char id=\"54\" x=\"594\" y=\"0\" width=\"137\" height=\"207\" xoffset=\"-4\" yoffset=\"48\" xadvance=\"120\" page=\"0\" chnl=\"15\" /><char id=\"55\" x=\"428\" y=\"208\" width=\"137\" height=\"199\" xoffset=\"-6\" yoffset=\"46\" xadvance=\"120\" page=\"0\" chnl=\"15\" /><char id=\"56\" x=\"732\" y=\"0\" width=\"137\" height=\"207\" xoffset=\"-6\" yoffset=\"48\" xadvance=\"120\" page=\"0\" chnl=\"15\" /><char id=\"57\" x=\"870\" y=\"0\" width=\"137\" height=\"207\" xoffset=\"1\" yoffset=\"48\" xadvance=\"120\" page=\"0\" chnl=\"15\" /><char id=\"120\" x=\"0\" y=\"638\" width=\"172\" height=\"146\" xoffset=\"-24\" yoffset=\"105\" xadvance=\"120\" page=\"0\" chnl=\"15\" /><char id=\"163\" x=\"0\" y=\"445\" width=\"161\" height=\"192\" xoffset=\"-20\" yoffset=\"54\" xadvance=\"120\" page=\"0\" chnl=\"15\" /><char id=\"165\" x=\"352\" y=\"415\" width=\"173\" height=\"188\" xoffset=\"-24\" yoffset=\"55\" xadvance=\"120\" page=\"0\" chnl=\"15\" /><char id=\"3647\" x=\"138\" y=\"0\" width=\"172\" height=\"212\" xoffset=\"-25\" yoffset=\"42\" xadvance=\"120\" page=\"0\" chnl=\"15\" /><char id=\"8361\" x=\"162\" y=\"417\" width=\"189\" height=\"188\" xoffset=\"-4\" yoffset=\"60\" xadvance=\"190\" page=\"0\" chnl=\"15\" /><char id=\"8363\" x=\"856\" y=\"404\" width=\"142\" height=\"167\" xoffset=\"-12\" yoffset=\"40\" xadvance=\"120\" page=\"0\" chnl=\"15\" /><char id=\"8364\" x=\"703\" y=\"208\" width=\"181\" height=\"195\" xoffset=\"-30\" yoffset=\"50\" xadvance=\"120\" page=\"0\" chnl=\"15\" /><char id=\"8369\" x=\"526\" y=\"408\" width=\"171\" height=\"188\" xoffset=\"-13\" yoffset=\"50\" xadvance=\"120\" page=\"0\" chnl=\"15\" /><char id=\"8381\" x=\"698\" y=\"408\" width=\"157\" height=\"188\" xoffset=\"-13\" yoffset=\"48\" xadvance=\"130\" page=\"0\" chnl=\"15\" /></chars></font>",
"texture" : 
{
"fileID":2800000,
"guid":"c76944ddfc45df849a1b7977c48a3516"
}
},
"fileID":4617
}
],
"fileID":6193
}
]
}
},
{
"type":"GameObject",
"id":"6074cebbff97b4346ab60ffa45459672",
"data" : 
{
"root" : 
[
{
"name":"paytable_common",
"activeSelf":true,
"layer":0,
"components" : 
[
{
"componentType":"Transform",
"enabled":true,
"serializableData" : 
{
"parent" : 
{
"fileID":0
},
"children" : 
[
],
"psr":"d"
},
"fileID":6194
},
{
"componentType":"UIAtlas",
"enabled":true,
"serializableData" : 
{
"textureContent" : 
{
"fileID":2800000,
"guid":"fcaf16f4c90e2ba418d358c90f830de0"
},
"spriteList" : 
{
"s_Button_Normal" : 
{
"y":6,
"width":179,
"height":30,
"paddingLeft":8,
"paddingTop":7,
"paddingRight":10,
"paddingBottom":7,
"borderLeft":14,
"borderTop":14,
"borderRight":14,
"borderBottom":14
},
"s_Gamble_Black_Normal" : 
{
"x":117,
"y":38,
"width":65,
"height":65,
"paddingLeft":26,
"paddingTop":26,
"paddingRight":29,
"paddingBottom":27
},
"s_Gamble_Red_Normal" : 
{
"x":117,
"y":105,
"width":65,
"height":65,
"paddingLeft":28,
"paddingTop":26,
"paddingRight":27,
"paddingBottom":27
},
"s_GambleCard_Black" : 
{
"x":164,
"y":261,
"width":50,
"height":73,
"paddingLeft":15,
"paddingTop":19,
"paddingRight":14,
"paddingBottom":15
},
"s_GambleCard_Red" : 
{
"x":164,
"y":186,
"width":50,
"height":73,
"paddingLeft":15,
"paddingTop":19,
"paddingRight":14,
"paddingBottom":15
},
"s_GambleItemBG" : 
{
"y":172,
"width":162,
"height":162,
"paddingLeft":30,
"paddingTop":30,
"paddingRight":26,
"paddingBottom":21
},
"s_half_window_opaque" : 
{
"x":184,
"y":125,
"width":115,
"height":59,
"paddingLeft":7,
"paddingTop":5,
"paddingRight":7,
"borderLeft":40,
"borderTop":40,
"borderRight":40,
"borderBottom":10
},
"s_PaytablePageButton_Close_Normal" : 
{
"x":238,
"y":63,
"width":60,
"height":60,
"paddingLeft":4,
"paddingTop":9,
"paddingRight":5,
"paddingBottom":9
},
"s_PaytablePageButton_Close_Over" : 
{
"x":278,
"y":274,
"width":60,
"height":60,
"paddingLeft":4,
"paddingTop":9,
"paddingRight":5,
"paddingBottom":9
},
"s_PaytablePageButton_Close_Pressed" : 
{
"x":216,
"y":274,
"width":60,
"height":60,
"paddingLeft":3,
"paddingTop":10,
"paddingRight":6,
"paddingBottom":8
},
"s_PaytablePageButton_Left_Normal" : 
{
"x":184,
"y":3,
"width":52,
"height":59,
"paddingLeft":6,
"paddingTop":10,
"paddingRight":11,
"paddingBottom":9
},
"s_PaytablePageButton_Left_Over" : 
{
"x":291,
"y":2,
"width":52,
"height":59,
"paddingLeft":6,
"paddingTop":10,
"paddingRight":11,
"paddingBottom":9
},
"s_PaytablePageButton_Left_Pressed" : 
{
"x":238,
"y":2,
"width":51,
"height":59,
"paddingLeft":6,
"paddingTop":11,
"paddingRight":12,
"paddingBottom":8
},
"s_PaytablePageButton_Right_Normal" : 
{
"x":184,
"y":64,
"width":52,
"height":59,
"paddingLeft":11,
"paddingTop":9,
"paddingRight":6,
"paddingBottom":10
},
"s_PaytablePageButton_Right_Over" : 
{
"x":269,
"y":213,
"width":52,
"height":59,
"paddingLeft":11,
"paddingTop":9,
"paddingRight":6,
"paddingBottom":10
},
"s_PaytablePageButton_Right_Pressed" : 
{
"x":216,
"y":213,
"width":51,
"height":59,
"paddingLeft":11,
"paddingTop":10,
"paddingRight":7,
"paddingBottom":9
},
"s_PaytablePageDot_Normal" : 
{
"x":300,
"y":100,
"width":22,
"height":23,
"paddingLeft":1,
"paddingRight":1,
"paddingBottom":1
},
"s_PaytablePageDot_Over" : 
{
"x":216,
"y":188,
"width":22,
"height":23,
"paddingLeft":1,
"paddingRight":1,
"paddingBottom":1
},
"s_PaytablePageDot_Pressed" : 
{
"x":300,
"y":75,
"width":22,
"height":23,
"paddingLeft":1,
"paddingTop":1,
"paddingRight":1
},
"s_Window" : 
{
"y":53,
"width":115,
"height":117,
"paddingLeft":7,
"paddingTop":5,
"paddingRight":7,
"paddingBottom":7,
"borderLeft":50,
"borderTop":50,
"borderRight":50,
"borderBottom":50
}
}
},
"fileID":4965
}
],
"fileID":6195
}
]
}
},
{
"type":"GameObject",
"id":"78157f02457cb674ca00a5e6f5c585a0",
"data" : 
{
"root" : 
[
{
"name":"fishez",
"activeSelf":true,
"layer":0,
"components" : 
[
{
"componentType":"Transform",
"enabled":true,
"serializableData" : 
{
"parent" : 
{
"fileID":0
},
"children" : 
[
],
"psr":"d"
},
"fileID":6196
},
{
"componentType":"UIAtlas",
"enabled":true,
"serializableData" : 
{
"textureContent" : 
{
"fileID":2800000,
"guid":"3e0fc74334ef6fe42afda0fb0c73a62f"
},
"spriteList" : 
{
"s_fishez" : 
{
"width":308,
"height":163
}
}
},
"fileID":5048
}
],
"fileID":6197
}
]
}
},
{
"type":"GameObject",
"id":"619de73a81981714aa208a5f8c92c931",
"data" : 
{
"root" : 
[
{
"name":"Paylines",
"activeSelf":true,
"layer":0,
"components" : 
[
{
"componentType":"Transform",
"enabled":true,
"serializableData" : 
{
"parent" : 
{
"fileID":0
},
"children" : 
[
],
"psr":"d"
},
"fileID":6198
},
{
"componentType":"UIAtlas",
"enabled":true,
"serializableData" : 
{
"textureContent" : 
{
"fileID":2800000,
"guid":"6755148412f695c46b0067a2517622d6"
},
"spriteList" : 
{
"s_paylines_12_bbb" : 
{
"width":314,
"height":97
}
}
},
"fileID":5386
}
],
"fileID":6199
}
]
}
},
{
"type":"GameObject",
"id":"86cc2edad7d5d61489c32545a1373079",
"data" : 
{
"root" : 
[
{
"name":"GUI_v11",
"activeSelf":true,
"layer":0,
"components" : 
[
{
"componentType":"Transform",
"enabled":true,
"serializableData" : 
{
"parent" : 
{
"fileID":0
},
"children" : 
[
],
"psr":"d"
},
"fileID":6200
},
{
"componentType":"UIAtlas",
"enabled":true,
"serializableData" : 
{
"textureContent" : 
{
"fileID":2800000,
"guid":"b89b2a11d8fdd8d48861dde5e0aaebee"
},
"spriteList" : 
{
"s_autoplay" : 
{
"x":807,
"y":60,
"width":83,
"height":67
},
"s_bar" : 
{
"x":728,
"y":39,
"width":217,
"height":16,
"borderLeft":2,
"borderTop":2,
"borderRight":2,
"borderBottom":2
},
"s_bets" : 
{
"x":1124,
"y":63,
"width":58,
"height":55
},
"s_black_bar" : 
{
"y":188,
"width":1276,
"height":91,
"paddingLeft":220,
"paddingRight":281
},
"s_button" : 
{
"x":1058,
"y":107,
"width":64,
"height":79,
"borderLeft":30,
"borderRight":30
},
"s_button_mid" : 
{
"x":1394,
"y":200,
"width":28,
"height":79,
"borderLeft":12,
"borderRight":12
},
"s_button_side" : 
{
"x":1259,
"y":48,
"width":44,
"height":79,
"borderLeft":20,
"borderRight":20
},
"s_checkbox_0" : 
{
"x":1305,
"y":80,
"width":47,
"height":47
},
"s_checkbox_1" : 
{
"x":1354,
"y":80,
"width":47,
"height":47,
"borderLeft":20,
"borderTop":20,
"borderRight":20,
"borderBottom":20
},
"s_checkbox_icon" : 
{
"x":1280,
"y":5,
"width":30,
"height":24,
"paddingLeft":10,
"paddingTop":12,
"paddingRight":7,
"paddingBottom":11
},
"s_circle" : 
{
"x":975,
"y":53,
"width":67,
"height":67
},
"s_ear" : 
{
"x":1074,
"y":7,
"width":16,
"height":16,
"borderLeft":6,
"borderTop":6,
"borderRight":6,
"borderBottom":6
},
"s_home" : 
{
"x":1380,
"y":7,
"width":23,
"height":21
},
"s_hourglass" : 
{
"x":1278,
"y":129,
"width":114,
"height":150,
"paddingLeft":18,
"paddingRight":18
},
"s_input" : 
{
"x":1095,
"y":13,
"width":48,
"height":48,
"borderLeft":20,
"borderTop":20,
"borderRight":20,
"borderBottom":20
},
"s_link" : 
{
"x":1145,
"y":2,
"width":25,
"height":23
},
"s_lobby" : 
{
"x":1044,
"y":45,
"width":49,
"height":60,
"paddingLeft":5,
"paddingRight":6
},
"s_lupa" : 
{
"x":947,
"y":12,
"width":39,
"height":39,
"paddingLeft":6,
"paddingTop":6,
"paddingRight":5,
"paddingBottom":5
},
"s_minus" : 
{
"x":892,
"y":58,
"width":39,
"height":3,
"borderLeft":3,
"borderRight":3
},
"s_notification_button" : 
{
"x":679,
"y":9,
"width":30,
"height":30,
"borderLeft":13,
"borderTop":13,
"borderRight":13,
"borderBottom":13
},
"s_notification_button_side" : 
{
"x":1095,
"y":75,
"width":20,
"height":30,
"borderLeft":13,
"borderTop":13,
"borderRight":3,
"borderBottom":13
},
"s_outline" : 
{
"x":1124,
"y":120,
"width":66,
"height":66,
"borderLeft":30,
"borderTop":30,
"borderRight":30,
"borderBottom":30
},
"s_paytable" : 
{
"x":1038,
"y":9,
"width":34,
"height":34
},
"s_race_icon_asia" : 
{
"x":892,
"y":63,
"width":81,
"height":64
},
"s_race_icon_asia_big" : 
{
"x":390,
"y":50,
"width":171,
"height":136
},
"s_race_icon_asia_rated" : 
{
"x":975,
"y":122,
"width":81,
"height":64
},
"s_race_icon_no_question" : 
{
"x":645,
"y":41,
"width":81,
"height":86
},
"s_SearchBox" : 
{
"x":728,
"y":57,
"width":77,
"height":70,
"paddingLeft":2,
"paddingTop":5,
"paddingRight":1,
"paddingBottom":5,
"borderLeft":10,
"borderTop":10,
"borderRight":10,
"borderBottom":10
},
"s_settings" : 
{
"x":1145,
"y":27,
"width":34,
"height":34
},
"s_slider" : 
{
"x":1354,
"y":30,
"width":46,
"height":48,
"paddingLeft":4,
"paddingRight":4,
"paddingBottom":8,
"borderLeft":20,
"borderTop":20,
"borderRight":20,
"borderBottom":20
},
"s_slider_bg" : 
{
"x":1038,
"y":2,
"width":6,
"height":5
},
"s_slider_icon" : 
{
"x":1074,
"y":25,
"width":18,
"height":18,
"paddingLeft":18,
"paddingTop":15,
"paddingRight":18,
"paddingBottom":23
},
"s_slider_shadow" : 
{
"x":1184,
"y":10,
"width":54,
"height":56,
"borderLeft":20,
"borderTop":20,
"borderRight":20,
"borderBottom":20
},
"s_slider_stroke" : 
{
"x":988,
"y":3,
"width":48,
"height":48,
"paddingLeft":3,
"paddingRight":3,
"paddingBottom":8,
"borderLeft":20,
"borderTop":20,
"borderRight":20,
"borderBottom":20
},
"s_spin" : 
{
"width":219,
"height":186
},
"s_thumb" : 
{
"x":1044,
"y":110,
"width":10,
"height":10,
"paddingLeft":1,
"paddingTop":1,
"paddingRight":1,
"paddingBottom":1,
"borderLeft":4,
"borderTop":4,
"borderRight":4,
"borderBottom":4
},
"s_toggle" : 
{
"x":1305,
"y":31,
"width":47,
"height":47,
"paddingLeft":7,
"paddingTop":4,
"paddingRight":7,
"paddingBottom":5,
"borderLeft":10,
"borderTop":10,
"borderRight":10,
"borderBottom":10
},
"s_toggle_icon" : 
{
"x":1259,
"y":170,
"width":16,
"height":16,
"paddingLeft":23,
"paddingTop":19,
"paddingRight":22,
"paddingBottom":21
},
"s_toggle_shadow" : 
{
"x":1192,
"y":68,
"width":58,
"height":56,
"paddingRight":3,
"borderLeft":25,
"borderRight":25
},
"s_tournament" : 
{
"x":1192,
"y":126,
"width":65,
"height":60
},
"s_tournament_big" : 
{
"x":221,
"y":31,
"width":167,
"height":155
},
"s_window" : 
{
"x":645,
"y":7,
"width":32,
"height":32,
"borderLeft":15,
"borderTop":15,
"borderRight":15,
"borderBottom":15
},
"s_window_half" : 
{
"x":1312,
"y":13,
"width":32,
"height":16,
"borderLeft":15,
"borderTop":13,
"borderRight":15,
"borderBottom":1
},
"s_window_half_gradient" : 
{
"x":1346,
"y":12,
"width":32,
"height":16,
"borderLeft":15,
"borderTop":13,
"borderRight":15,
"borderBottom":1
},
"s_window_quarter" : 
{
"x":1240,
"y":50,
"width":16,
"height":16,
"borderLeft":13,
"borderTop":13,
"borderRight":1,
"borderBottom":1
},
"s_window_quarter_gradient" : 
{
"x":1259,
"y":152,
"width":16,
"height":16,
"borderLeft":13,
"borderTop":13,
"borderRight":1,
"borderBottom":1
},
"s_x" : 
{
"x":1240,
"y":4,
"width":38,
"height":42,
"paddingLeft":22,
"paddingTop":25,
"paddingRight":20,
"paddingBottom":22
},
"s_x_bg" : 
{
"x":563,
"y":38,
"width":80,
"height":89,
"borderLeft":3,
"borderTop":18,
"borderRight":18,
"borderBottom":3
}
}
},
"fileID":5476
}
],
"fileID":6201
}
]
}
},
{
"type":"GameObject",
"id":"b3d6b8c253a678647ba4d16892763cd1",
"data" : 
{
"root" : 
[
{
"name":"GUI_v11_desktop",
"activeSelf":true,
"layer":0,
"components" : 
[
{
"componentType":"Transform",
"enabled":true,
"serializableData" : 
{
"parent" : 
{
"fileID":0
},
"children" : 
[
],
"psr":"d"
},
"fileID":6202
},
{
"componentType":"UIAtlas",
"enabled":true,
"serializableData" : 
{
"textureContent" : 
{
"fileID":2800000,
"guid":"8fae07bf89d7cf64880ada5aae6f71ce"
},
"spriteList" : 
{
"s_ananas" : 
{
"x":835,
"y":443,
"width":48,
"height":49,
"paddingRight":133,
"paddingBottom":129
},
"s_ananas_circle" : 
{
"x":514,
"y":88,
"width":166,
"height":166,
"paddingLeft":15,
"paddingTop":12
},
"s_bar" : 
{
"y":256,
"width":888,
"height":69
},
"s_Bubble" : 
{
"x":560,
"y":327,
"width":325,
"height":114,
"paddingTop":29,
"borderLeft":30,
"borderTop":30,
"borderRight":30,
"borderBottom":60
},
"s_Button_Normal" : 
{
"x":656,
"y":45,
"width":42,
"height":41,
"paddingLeft":2,
"paddingTop":2,
"paddingRight":2,
"paddingBottom":3,
"borderLeft":20,
"borderTop":20,
"borderRight":20,
"borderBottom":20
},
"s_Button_Normal_Black" : 
{
"x":514,
"y":45,
"width":42,
"height":41,
"paddingLeft":2,
"paddingTop":2,
"paddingRight":2,
"paddingBottom":3,
"borderLeft":20,
"borderTop":20,
"borderRight":20,
"borderBottom":20
},
"s_circle" : 
{
"x":700,
"y":19,
"width":69,
"height":69
},
"s_collider" : 
{
"x":852,
"y":112,
"width":2,
"height":2,
"paddingRight":6,
"paddingBottom":6
},
"s_DynamicCollider" : 
{
"x":852,
"y":98,
"width":2,
"height":2,
"paddingRight":6,
"paddingBottom":6
},
"s_Gamble_Black_Normal" : 
{
"x":768,
"y":443,
"width":65,
"height":65,
"paddingLeft":26,
"paddingTop":26,
"paddingRight":29,
"paddingBottom":27
},
"s_Gamble_Red_Normal" : 
{
"x":771,
"y":23,
"width":65,
"height":65,
"paddingLeft":28,
"paddingTop":26,
"paddingRight":27,
"paddingBottom":27
},
"s_GambleCard_Black" : 
{
"x":606,
"y":17,
"width":46,
"height":69,
"paddingLeft":16,
"paddingTop":19,
"paddingRight":17,
"paddingBottom":19
},
"s_GambleCard_Red" : 
{
"x":558,
"y":17,
"width":46,
"height":69,
"paddingLeft":16,
"paddingTop":19,
"paddingRight":17,
"paddingBottom":19
},
"s_GambleItemBG" : 
{
"x":682,
"y":90,
"width":162,
"height":164,
"paddingLeft":30,
"paddingTop":28,
"paddingRight":26,
"paddingBottom":21
},
"s_gradient" : 
{
"x":838,
"y":20,
"width":16,
"height":16,
"borderLeft":2,
"borderRight":2
},
"s_HelpButton_Normal" : 
{
"x":668,
"y":13,
"width":30,
"height":30,
"paddingLeft":7,
"paddingTop":7,
"paddingRight":7,
"paddingBottom":7
},
"s_lobby_thumb" : 
{
"y":327,
"width":331,
"height":160,
"paddingLeft":4,
"paddingTop":5,
"paddingRight":4,
"paddingBottom":15
},
"s_PragmaticPlay" : 
{
"x":692,
"y":519,
"width":166,
"height":17,
"paddingRight":19
},
"s_sound" : 
{
"x":838,
"y":56,
"width":32,
"height":28
},
"s_SoundButton_Loading" : 
{
"x":692,
"y":443,
"width":74,
"height":74,
"paddingLeft":13,
"paddingTop":13,
"paddingRight":13,
"paddingBottom":13
},
"s_SoundButton_Normal_4slider" : 
{
"x":514,
"y":13,
"width":30,
"height":30,
"paddingLeft":1,
"paddingTop":1,
"paddingRight":1,
"paddingBottom":1
},
"s_spin_hover" : 
{
"x":257,
"width":255,
"height":254,
"paddingTop":1
},
"s_spin_normal" : 
{
"width":255,
"height":254,
"paddingTop":1
},
"s_spin_pressed" : 
{
"x":333,
"y":327,
"width":225,
"height":225
},
"s_Spin_Stop_Icon" : 
{
"x":602,
"y":443,
"width":88,
"height":88,
"paddingLeft":56,
"paddingTop":56,
"paddingRight":56,
"paddingBottom":56
},
"s_WhiteSquare" : 
{
"x":838,
"y":38,
"width":16,
"height":16,
"borderLeft":4,
"borderTop":4,
"borderRight":4,
"borderBottom":4
},
"s_Window" : 
{
"x":560,
"y":443,
"width":40,
"height":108,
"borderLeft":18,
"borderTop":50,
"borderRight":18,
"borderBottom":54
}
}
},
"fileID":5520
}
],
"fileID":6203
}
]
}
},
{
"type":"GameObject",
"id":"c20b62941ae71194d835c0e89b6e3712",
"data" : 
{
"root" : 
[
{
"name":"Paytable_RK",
"activeSelf":true,
"layer":0,
"components" : 
[
{
"componentType":"Transform",
"enabled":true,
"serializableData" : 
{
"parent" : 
{
"fileID":0
},
"children" : 
[
],
"psr":"d"
},
"fileID":6204
},
{
"componentType":"UIAtlas",
"enabled":true,
"serializableData" : 
{
"textureContent" : 
{
"fileID":2800000,
"guid":"96b922c92544cf144b970eff42453e27"
},
"spriteList" : 
{
"s_PaytablePageButton_Close_Normal" : 
{
"x":71,
"y":96,
"width":69,
"height":70,
"paddingTop":4,
"paddingBottom":4
},
"s_PaytablePageButton_Close_Over" : 
{
"x":142,
"y":11,
"width":69,
"height":78
},
"s_PaytablePageButton_Close_Pressed" : 
{
"y":180,
"width":60,
"height":61,
"paddingLeft":3,
"paddingTop":9,
"paddingRight":6,
"paddingBottom":8
},
"s_PaytablePageButton_Left_Normal" : 
{
"y":100,
"width":69,
"height":78
},
"s_PaytablePageButton_Left_Over" : 
{
"x":71,
"y":168,
"width":69,
"height":73,
"paddingTop":3,
"paddingBottom":2
},
"s_PaytablePageButton_Left_Pressed" : 
{
"x":142,
"y":91,
"width":68,
"height":70,
"paddingLeft":1,
"paddingTop":4,
"paddingBottom":4
},
"s_PaytablePageButton_Right_Normal" : 
{
"x":142,
"y":163,
"width":69,
"height":78
},
"s_PaytablePageButton_Right_Over" : 
{
"x":71,
"y":24,
"width":69,
"height":70,
"paddingTop":4,
"paddingBottom":4
},
"s_PaytablePageButton_Right_Pressed" : 
{
"y":27,
"width":69,
"height":71,
"paddingTop":4,
"paddingBottom":3
},
"s_PaytablePageDot_Normal" : 
{
"y":2,
"width":22,
"height":23,
"paddingLeft":1,
"paddingRight":1,
"paddingBottom":1
},
"s_PaytablePageDot_Over" : 
{
"x":24,
"y":2,
"width":22,
"height":23,
"paddingLeft":1,
"paddingRight":1,
"paddingBottom":1
},
"s_PaytablePageDot_Pressed" : 
{
"x":212,
"y":104,
"width":22,
"height":23,
"paddingLeft":1,
"paddingTop":1,
"paddingRight":1
},
"s_window" : 
{
"x":212,
"y":129,
"width":32,
"height":32,
"borderLeft":15,
"borderTop":8,
"borderRight":15,
"borderBottom":8
}
}
},
"fileID":5751
}
],
"fileID":6205
}
]
}
},
{
"type":"AudioClip",
"id":"eb676f1125280a44ea444439633afbe0",
"data" : 
{
"path":"res/eb676f1125280a44ea444439633afbe0"
}
},
{
"type":"AudioClip",
"id":"5708981d09ef4b24ab2724b13dced1c3",
"data" : 
{
"path":"res/5708981d09ef4b24ab2724b13dced1c3"
}
}
]}