/* Game Platform Admin Theme */
/* Modern purple/blue gradient theme for admin panel */

:root {
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --light-bg: #f8f9fa;
    --white: #ffffff;
    --shadow-light: 0 5px 15px rgba(102,126,234,0.1);
    --shadow-medium: 0 10px 30px rgba(102,126,234,0.15);
    --shadow-heavy: 0 20px 40px rgba(102,126,234,0.2);
    --border-radius: 1rem;
    --border-radius-lg: 1.5rem;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Enhanced animations */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInScale {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

/* Page animations */
.main-content > * {
    animation: slideInUp 0.6s ease-out;
}

.card {
    animation: fadeInScale 0.5s ease-out;
}

/* Enhanced stats cards */
.stats-card {
    position: relative;
    overflow: hidden;
    transition: var(--transition);
}

.stats-card::after {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 50%);
    animation: float 8s ease-in-out infinite;
    pointer-events: none;
}

.stats-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: var(--shadow-heavy);
}

/* Enhanced navigation */
.sidebar .nav-link {
    position: relative;
    overflow: hidden;
}

.sidebar .nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.sidebar .nav-link:hover::before {
    left: 100%;
}

/* Enhanced form elements */
.form-control:focus,
.form-select:focus {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

/* Enhanced buttons */
.btn {
    position: relative;
    overflow: hidden;
    transition: var(--transition);
}

.btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255,255,255,0.2);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
}

.btn:hover::before {
    width: 300px;
    height: 300px;
}

.btn:hover {
    transform: translateY(-3px);
}

/* Enhanced table styles */
.table-responsive {
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-light);
}

.table tbody tr {
    transition: var(--transition);
}

.table tbody tr:hover {
    background: linear-gradient(90deg, rgba(102,126,234,0.05), rgba(118,75,162,0.05));
    transform: translateX(8px);
}

/* Enhanced modals */
.modal.fade .modal-dialog {
    transform: scale(0.8) translateY(-50px);
    transition: var(--transition);
}

.modal.show .modal-dialog {
    transform: scale(1) translateY(0);
}

/* Enhanced alerts */
.alert {
    border-left: 4px solid;
    animation: slideInUp 0.5s ease-out;
}

.alert-success {
    border-left-color: #28a745;
    background: linear-gradient(90deg, rgba(40,167,69,0.1), rgba(40,167,69,0.05));
}

.alert-danger {
    border-left-color: #dc3545;
    background: linear-gradient(90deg, rgba(220,53,69,0.1), rgba(220,53,69,0.05));
}

.alert-warning {
    border-left-color: #ffc107;
    background: linear-gradient(90deg, rgba(255,193,7,0.1), rgba(255,193,7,0.05));
}

.alert-info {
    border-left-color: #17a2b8;
    background: linear-gradient(90deg, rgba(23,162,184,0.1), rgba(23,162,184,0.05));
}

/* Enhanced badges */
.badge {
    position: relative;
    overflow: hidden;
}

.badge::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.5s;
}

.badge:hover::before {
    left: 100%;
}

/* Loading states */
.btn:disabled {
    position: relative;
}

.btn:disabled::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive improvements */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
        transition: var(--transition);
    }
    
    .sidebar.show {
        transform: translateX(0);
    }
    
    .main-content {
        margin-left: 0 !important;
    }
    
    .stats-card {
        margin-bottom: 1rem;
    }
    
    .btn-group {
        flex-direction: column;
    }
    
    .btn-group .btn {
        margin: 2px 0;
        border-radius: var(--border-radius) !important;
    }
}

/* Dark mode support (future enhancement) */
@media (prefers-color-scheme: dark) {
    :root {
        --light-bg: #1a1a1a;
        --white: #2d2d2d;
    }
}

/* Print styles */
@media print {
    .sidebar,
    .btn,
    .modal {
        display: none !important;
    }
    
    .main-content {
        margin: 0 !important;
        padding: 0 !important;
    }
    
    .card {
        box-shadow: none !important;
        border: 1px solid #ddd !important;
    }
}
