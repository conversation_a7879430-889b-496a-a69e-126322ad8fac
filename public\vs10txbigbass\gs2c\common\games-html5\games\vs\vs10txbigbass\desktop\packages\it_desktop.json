{"resources": [{"type": "GameObject", "id": "94b4cc74e863dd443bd5ec8b5fb17dbb", "data": {"root": [{"name": "it_desktop", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 0}, "children": [{"fileID": 120568, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, {"fileID": 120569, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, {"fileID": 120570, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, {"fileID": 120571, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, {"fileID": 120572, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, {"fileID": 120573, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, {"fileID": 120574, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, {"fileID": 120575, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, {"fileID": 120576, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, {"fileID": 120577, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, {"fileID": 120578, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, {"fileID": 120579, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, {"fileID": 120580, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, {"fileID": 120581, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, {"fileID": 120582, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, {"fileID": 120583, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, {"fileID": 120584, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, {"fileID": 120585, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, {"fileID": 120586, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, {"fileID": 120587, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, {"fileID": 120588, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, {"fileID": 120589, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, {"fileID": 120590, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, {"fileID": 120591, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, {"fileID": 120592, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, {"fileID": 120593, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, {"fileID": 120594, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, {"fileID": 120595, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, {"fileID": 120596, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, {"fileID": 120597, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, {"fileID": 120598, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, {"fileID": 120599, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, {"fileID": 120600, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, {"fileID": 120601, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, {"fileID": 120602, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, {"fileID": 120603, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, {"fileID": 120604, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, {"fileID": 120605, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, {"fileID": 120606, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, {"fileID": 120607, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, {"fileID": 120608, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, {"fileID": 120609, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, {"fileID": 120610, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, {"fileID": 120611, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, {"fileID": 120612, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, {"fileID": 120613, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, {"fileID": 120614, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, {"fileID": 120615, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, {"fileID": 120616, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, {"fileID": 120617, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, {"fileID": 120618, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, {"fileID": 120619, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, {"fileID": 120620, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, {"fileID": 120621, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, {"fileID": 120622, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, {"fileID": 120623, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, {"fileID": 120624, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, {"fileID": 120625, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, {"fileID": 120626, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, {"fileID": 120627, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, {"fileID": 120628, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, {"fileID": 120629, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, {"fileID": 120630, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, {"fileID": 120631, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, {"fileID": 120632, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, {"fileID": 120633, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, {"fileID": 120634, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, {"fileID": 120635, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, {"fileID": 120636, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, {"fileID": 120637, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, {"fileID": 120638, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, {"fileID": 120639, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, {"fileID": 120640, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, {"fileID": 120641, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, {"fileID": 120642, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, {"fileID": 120643, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, {"fileID": 120644, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, {"fileID": 120645, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, {"fileID": 120646, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, {"fileID": 120647, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, {"fileID": 120648, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, {"fileID": 120649, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, {"fileID": 120650, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, {"fileID": 120651, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, {"fileID": 120652, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, {"fileID": 120653, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, {"fileID": 120654, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}], "s": "0"}, "fileID": 120655}, {"componentType": "ModificationsManager", "enabled": true, "serializableData": {"root": {"fileID": 0}, "EditMode": false, "Atlases": [], "Transforms": [{"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page1/Title/PaytableTitleLabel1", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page1/Rules/AllSymbolsPayLabel", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page1/ScatterHolder/SymbolScatter/Sprite", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0.52, "y": 0.52, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page1/ScatterHolder/DescriptionHolder/Label1", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -24, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page1/ScatterHolder/DescriptionHolder/Label2", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -35, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page1/WildHolder/SymbolScatter/Sprite", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0.25, "y": 0.25, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page1/WildHolder/DescriptionHolder/Label1", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -12.5, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page1/WildHolder/DescriptionHolder/Label2", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -50, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page1/WildHolder/DescriptionHolder/Label3", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -87.5, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/MoneySymbolHolder/TitleHolder/Title", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/MoneySymbolHolder/DescriptionHolder/MoneySymbolHolder/Sprite", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0.7, "y": 0.7, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/MoneySymbolHolder/DescriptionHolder/MoneySymbolRules/Label3", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FreeSpinsHolder1/TitleHolder/Title", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/RuleHolder1/Rule1", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder1/LabelHolder1/Label1", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder1/LabelHolder2/Label2", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder1/LabelHolder3/Label3", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/RuleHolder2/Rule2", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder2/LabelHolder1/Label1", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder2/LabelHolder2/Label2", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -8, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/RuleHolder3/Rule3", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder1/Label1", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder2/Label2", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder3/Label3", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder4/Label4", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder5/Label5", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder1/Rule1", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": -29, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder2/Rule2", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 10.3, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder3/Rule3", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 14, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder4/Rule4", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder5/Rule5", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder6/Rule6", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder7/Rule7", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder8/Rule8", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -8, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/FreeSpinsHolder2/SpecialReelsHolder/SpecialReels", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -12, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/MaxWin/TitleHolder/Title", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/MaxWin/RuleHolder/Label1", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/CAT/TitleHolder/Title1New", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 8, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/CAT/RuleHolder1/Rule1", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -12, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/CAT/RuleHolder2/Rule2", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -12, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/CAT/RuleHolder3/Rule3", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -12, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page4/Title/PaytableTitleLabel", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page4/Volatility/VolatilityDescription", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -30, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page4/RulesTop/AllSymbolsPay", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 44, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page4/RulesTop/AllWinsMultiplied", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 15, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page4/RulesTop/AllValuesExpressed", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -45, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page4/RulesTop/OnlyTheHighestWin", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -65, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page4/RulesTop/WhenWinningOnMultiplePaylines", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -95, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page4/Lines/Sprite", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page4/RulesBottom/SpaceAndEnter", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 92, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page4/RulesBottom/RTP", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page4/RulesBottom/MalfunctionLabel", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -97, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page4/MinMaxHolder", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -185, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/MaxWin/RuleHolder/HolderLabelJackpot/Label1New", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}], "Labels": [{"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 120656, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "extraPayloads": [], "path": "Game/GamePivot/FSWONWindow/content/FreespinsWonWindow/LabelsAndSprites/Catches_label", "oldContent": "CATCHES", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 120657, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "extraPayloads": [], "path": "Game/GamePivot/FreeSpinsPurchase/BuyButtons/Options/Option_0/OptVisual/Texts/BoughtText/LandscapeText/label1", "oldContent": "ACTIVE", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 120658, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "extraPayloads": [], "path": "Game/GamePivot/FSWONWindow/PressAnywhere_Label/label", "oldContent": "Press anywhere to continue", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 120659, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "extraPayloads": [], "path": "Game/GamePivot/FSExtraWindow/content/PressAnywhere_Label/label", "oldContent": "Press anywhere to continue...", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 120660, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "extraPayloads": [], "path": "Game/GamePivot/FreeSpinsPurchase/FSPurchaseWindow/Content/AnimatedPivot/Texts/BuyText/label", "oldContent": "BUY FREE SPINS", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 120661, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "extraPayloads": [], "path": "Game/GamePivot/FsFeature/content/Feature_trail/Holder/fisherman_highlight/ScalerX10/stretcher_fs/spins", "oldContent": "spins", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 120662, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "extraPayloads": [], "path": "Game/Background/PaytableOnScreen/Portrait/Message1/labelMsg1", "oldContent": "AL<PERSON> SYMBOLS PAY FROM LEFT TO RIGHT. BONUS PAYS ON ANY POSITION.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 120663, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "extraPayloads": [], "path": "Game/GamePivot/FsFeature/content/Feature_trail/Holder/fisherman_highlight/ScalerX3/stretcher_fs/free", "oldContent": "free", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 120664, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "extraPayloads": [], "path": "Game/GamePivot/Reels/ThePivot/BonusMessages/Fisherman/Labels/uilabel", "oldContent": "MORE FISHERMEN!", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 120665, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "extraPayloads": [], "path": "Game/GamePivot/FreeSpinsPurchase/BuyButtons/Options/Option_0/OptVisual/Texts/BoughtText/LandscapeText/label0", "oldContent": "FEATURE", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 120666, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "extraPayloads": [], "path": "Game/GamePivot/FreeSpinsPurchase/BuyButtons/Options/Option_0/OptVisual/Texts/BuyText/PortraitText/label0", "oldContent": "BUY FREE SPINS", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 120667, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "extraPayloads": [], "path": "Game/GamePivot/FsFeature/content/Feature_trail/Holder/fisherman_highlight/ScalerX2/stretcher_fs/free", "oldContent": "free", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 120668, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "extraPayloads": [], "path": "Game/GamePivot/FSResultWindow/content/SignPivot/FreespinsCongratsWindow/Labels/Congrats_label", "oldContent": "CONGRATULATIONS!", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 120669, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "extraPayloads": [], "path": "Game/GamePivot/FSExtraWindow/content/SignPivot/FreespinsExtraWindow/Labels/Congrats_label", "oldContent": "CONGRATULATIONS!", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 120670, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "extraPayloads": [], "path": "Game/GamePivot/FSExtraWindow/content/SignPivot/FreespinsExtraWindow/Labels/FreeSpins_label ", "oldContent": "FREESPINS", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 120671, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "extraPayloads": [], "path": "Game/GamePivot/FSExtraWindow/content/SignPivot/FreespinsExtraWindow/Labels/AreNow_label", "oldContent": "ARE NOW", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 120672, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "extraPayloads": [], "path": "Game/GamePivot/FreeSpinsPurchase/BuyButtons/Options/Option_0/OptVisual/Texts/BuyText/LandscapeTest/label0", "oldContent": "BUY FREE SPINS", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 120673, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "extraPayloads": [], "path": "Game/GamePivot/Reels/ThePivot/BonusMessages/PlusFS/Labels/uilabel", "oldContent": "EXTRA FREE SPINS!", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 120674, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "extraPayloads": [], "path": "Game/GamePivot/FsFeature/content/Feature_trail/Holder/fisherman_highlight/ScalerX10/stretcher_fs/free", "oldContent": "free", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 120675, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "extraPayloads": [], "path": "Game/GamePivot/FSWONWindow/content/FreespinsWonWindow/LabelsAndSprites/Congratulations_label", "oldContent": "CONGRATULATIONS!", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 120676, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "extraPayloads": [], "path": "Game/GamePivot/Reels/ThePivot/BonusMessages/Fishes/Labels/uilabel", "oldContent": "MORE FISH!", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 120677, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "extraPayloads": [], "path": "Game/GamePivot/FreeSpinsPurchase/BuyButtons/Options/Option_0/OptVisual/Texts/BoughtText/PortraitText/label0", "oldContent": "FEATURE ACTIVE", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 120678, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "extraPayloads": [], "path": "Game/GamePivot/FSExtraWindow/content/SignPivot/FreespinsExtraWindow/Labels/Extra_label", "oldContent": "THE NEXT", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 120679, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "extraPayloads": [], "path": "Game/GamePivot/FSResultWindow/content/SignPivot/FreespinsCongratsWindow/Labels/YouWon_label", "oldContent": "YOU HAVE WON", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 120680, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "extraPayloads": [], "path": "Game/GamePivot/FsFeature/content/Feature_trail/Holder/fisherman_highlight/ScalerX3/stretcher_fs/spins", "oldContent": "spins", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 120681, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "extraPayloads": [], "path": "Game/Background/PaytableOnScreen/Landscape/Message1/labelMsg1", "oldContent": "AL<PERSON> SYMBOLS PAY FROM LEFT TO RIGHT. BONUS PAYS ON ANY POSITION.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 120682, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "extraPayloads": [], "path": "Game/GamePivot/FsFeature/content/Feature_trail/Holder/fisherman_highlight/ScalerX2/stretcher_fs/spins", "oldContent": "spins", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 120683, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "extraPayloads": [], "path": "Game/GamePivot/Reels/ThePivot/BonusMessages/Level2/Labels/uilabel", "oldContent": "START FROM LEVEL 2!", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 120684, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "extraPayloads": [], "path": "Game/GamePivot/FSWONWindow/content/FreespinsWonWindow/LabelsAndSprites/FreeSpins_label", "oldContent": "FREE SPINS!", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 120685, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "extraPayloads": [], "path": "Game/GamePivot/FSResultWindow/content/PressAnywhere_Label/label", "oldContent": "Press anywhere to continue...", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 120686, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "extraPayloads": [], "path": "Game/GamePivot/Reels/ThePivot/BonusMessages/Hooks/Labels/uilabel", "oldContent": "MORE HOOKS AND EXPLOSIONS!", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 120687, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/CAT/RuleHolder2/Rule2", "oldContent": "When buying the FREE SPINS round, on the triggering spin 3, 4 or 5 SCATTERS can hit randomly.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 120688, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder4/Label4", "oldContent": "- START FROM LEVEL 2 - The round starts from level 2 in the progressive feature.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 120689, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder5/Label5", "oldContent": "- +2 SPINS - The subsequent round starts with 2 more free spins from the beginning and 2 more spins are added to every retrigger.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 120690, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/RuleHolder1/Rule1", "oldContent": "Hit 3 or more SCATTER symbols to trigger the FREE SPINS feature.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 120691, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/RulesTop/AllValuesExpressed", "oldContent": "All values are expressed as actual wins in coins.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 120692, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/Volatility/VolatilityDescription", "oldContent": "High volatility games pay out less often on average but the chance to hit big wins in a short time span is higher", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 120693, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/MoneySymbolHolder/DescriptionHolder/MoneySymbolRules/Label3", "oldContent": "The fish paying symbols are also MONEY symbols. At every spin, the fish take a random money value which can be won during the FREE SPINS feature.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 120694, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/RulesBottom/RTP/TheoreticalRTP/Label", "oldContent": "The theoretical RTP of this game is {0}%", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 120695, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder8/Rule8", "oldContent": "Also randomly, when there are fisherman symbols on the screen but no fish, at the end of a free spin, a bazooka animation can appear and change all the symbols from the screen, except for fisherman symbols to something else.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 120696, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "extraPayloads": [], "path": "Paytable/Pages/Page1/Rules/AllSymbolsPayLabel", "oldContent": "All symbols pay from left to right on adjacent reels starting from the leftmost reel.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 120697, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder5/Rule5", "oldContent": "After the fourth level, the feature cannot be retriggered anymore.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 120698, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/FreeSpinsHolder2/SpecialReelsHolder/SpecialReels", "oldContent": "Special reels are in play during the feature.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 120699, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/CAT/RuleHolder1/Rule1", "oldContent": "The FREE SPINS round can be instantly triggered from the base game by buying it for 100x current total bet.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 120700, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/RuleHolder2/Rule2", "oldContent": "In the base game whenever 2 SCATTER symbols hit without a third, there is a chance for another one to be brought onto the screen by a random feature:", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 120701, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder1/Label1", "oldContent": "- MORE FISH - More fish symbols are present on the reel strips during the subsequent free spins round", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 120702, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder2/Rule2", "oldContent": "All the WILD symbols that hit during the feature are collected until the end of the round.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 120703, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/RulesTop/AllSymbolsPay", "oldContent": "All symbols pay from left to right on selected paylines.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 120704, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder3/Label3", "oldContent": "- MORE DYNAMITES, HOOKS AND BAZOOKAS - During the round, the chance to hit dynamite, hook or bazooka spin feature is increased.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 120705, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/RulesBottom/SpaceAndEnter", "oldContent": "SPACE and ENTER buttons on the keyboard can be used to start and stop the spin.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 120706, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder7/Rule7", "oldContent": "Randomly, when there are fish symbols on the screen but no fisherman, at the end of a free spin, a hook will appear pulling a random reel up to bring fisherman symbols onto the screen.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 120707, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder1/Rule1", "oldContent": "During the FREE SPINS feature each WILD symbol also collects all the values from MONEY symbols on the screen.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 120708, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/MaxWin/TitleHolder/Title", "oldContent": "MAX WIN", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 120709, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder1/LabelHolder2/Label2", "oldContent": "4x SCATTER awards 15 free spins", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 120710, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/MinMaxHolder/MaxBet/MaximumText", "oldContent": "MAXIMUM BET:", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 120711, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FreeSpinsHolder1/TitleHolder/Title", "oldContent": "FREE SPINS", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 120712, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/MoneySymbolHolder/TitleHolder/Title", "oldContent": "MONEY SYMBOL", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 120713, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "extraPayloads": [], "path": "Paytable/Pages/Page1/WildHolder/DescriptionHolder/Label1", "oldContent": "This is the WILD symbol.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 120714, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "extraPayloads": [], "path": "Paytable/Pages/Page1/Title/PaytableTitleLabel1", "oldContent": "GAME RULES", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 120715, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/CAT/TitleHolder/Title1New", "oldContent": "BUY FREE SPINS", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 120716, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/MinMaxHolder/MinBet/MinimumText", "oldContent": "MINIMUM BET:", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 120717, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/RulesBottom/MalfunctionLabel", "oldContent": "Malfunction voids all pays and plays.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 120718, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "extraPayloads": [], "path": "Paytable/Pages/Page1/ScatterHolder/DescriptionHolder/Label2", "oldContent": "It appears on all reels.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 120719, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder1/LabelHolder1/Label1", "oldContent": "5x SCATTER awards 20 free spins", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 120720, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/RulesTop/AllWinsMultiplied", "oldContent": "All wins are multiplied by bet per line.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 120721, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "extraPayloads": [], "path": "Paytable/Pages/Page1/ScatterHolder/DescriptionHolder/Label1", "oldContent": "This is the SCATTER symbol.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 120722, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/RulesTop/OnlyTheHighestWin", "oldContent": "Only the highest win is paid per line.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 120723, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder1/LabelHolder3/Label3", "oldContent": "3x SCATTER awards 10 free spins", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 120724, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder2/LabelHolder1/Label1", "oldContent": "- Randomly, if the SCATTERS on the screen can move down one position without leaving the reel area, a respin is triggered where the reels with SCATTERS move one position down and the reels without SCATTERS respin.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 120725, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder4/Rule4", "oldContent": "The retriggered spins are played after the previous batch of free spins ends. The multiplier applies to the retriggered spins.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 120726, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/RulesTop/WhenWinningOnMultiplePaylines", "oldContent": "When winning on multiple paylines, all wins are added to the total win.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 120727, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/Title/PaytableTitleLabel", "oldContent": "GAME RULES", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 120728, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/MaxWin/RuleHolder/Label1", "oldContent": "The maximum win amount is limited to {0}x bet. If the total win of a FREE SPINS ROUND reaches {1}x the round immediately ends, win is awarded and all remaining free spins are forfeited", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 120729, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/RulesBottom/RTP/TheoreticalRTPBONUS/Label", "oldContent": "The RTP of the game when using \"BUY FREE SPINS\" is {0}%", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 120730, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/Volatility/VolatilityMeter/LabelHolder/VolatilityLabel", "oldContent": "VOLATILITY", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 120731, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder6/Rule6", "oldContent": "Randomly, when there are fisherman symbols on the screen but no fish, at the end of a free spin, fish MONEY symbols can appear in random positions via the dynamite spin feature.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 120732, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder2/LabelHolder2/Label2", "oldContent": "- Randomly, a hook can pull one of the reels up to reveal another SCATTER.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 120733, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/RuleHolder3/Rule3", "oldContent": "Before the round starts, 0 to 5 modifiers that apply to the subsequent round are randomly selected:", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 120734, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "extraPayloads": [], "path": "Paytable/Pages/Page1/WildHolder/DescriptionHolder/Label2", "oldContent": "It appears on all reels during the FREE SPINS round.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 120735, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "extraPayloads": [], "path": "Paytable/Pages/Page1/WildHolder/DescriptionHolder/Label3", "oldContent": "Substitutes for all symbols except SCATTER.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 120736, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder3/Rule3", "oldContent": "Every 4th WILD symbol collected retriggers the feature, awards 10 more free spins and the multiplier for MONEY symbol collection increases to 2x for the second level, 3x for the third level and 10x for the fourth level.  ", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 120737, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder2/Label2", "oldContent": "- MORE FISHERMAN - More WILD symbols are present on the reel strips during the subsequent free spins round", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 120738, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "extraPayloads": [], "path": "IntroScreen/content/Labels_Holder_landscape/Label_Holder_bigger_1/Label_2 (1)", "oldContent": "BIG FREE SPINS MODIFIERS!", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 120739, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "extraPayloads": [], "path": "IntroScreen/content/Labels_Holder_landscape/Label_Holder_bigger_1/Label_1", "oldContent": "GO FISHIN' FOR", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 120740, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "extraPayloads": [], "path": "IntroScreen/content/IntroButtons/ButtonSkipIntro/content/TextHolder/Label_1", "oldContent": "DON'T SHOW NEXT TIME", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 120741, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/MoneySymbolHolder/DescriptionHolder/PossibleValues/Label", "oldContent": "Possible values are: 2x, 5x, 10x, 15x, 20x, 25x, 50x, 100x, 200x, 500x, 1666x, 2500x or 5000x total bet.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 120742, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/MaxWin/RuleHolder/HolderLabelJackpot/Label1New", "oldContent": "The maximum win amount is limited to {0}x bet except Jack<PERSON>. If the total win of a FREE SPINS round reaches {1}x bet the round immediately ends, win is awarded and all remaining free spins are forfeited.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}], "Spines": [], "revisionNumber": 0}, "fileID": 120743}], "fileID": 120744}, {"name": "Game/GamePivot/FSWONWindow/content/FreespinsWonWindow/LabelsAndSprites/Catches_label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 120655, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "children": [], "psr": "d"}, "fileID": 120568}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "ea83dc65b54d7264bbf2980bc50f4c20"}, "_text": "CATTURA", "fontSize": 50, "width": 338, "height": 110, "overflow": 0}, "fileID": 120656}], "fileID": 120745}, {"name": "Game/GamePivot/FreeSpinsPurchase/BuyButtons/Options/Option_0/OptVisual/Texts/BoughtText/LandscapeText/label1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 120655, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "children": [], "psr": "d"}, "fileID": 120569}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "0852da972dc74354eb12b17314a0a600"}, "_text": "ATTIVA", "fontSize": 40, "width": 170, "height": 80, "overflow": 0}, "fileID": 120657}], "fileID": 120746}, {"name": "Game/GamePivot/FSWONWindow/PressAnywhere_Label/label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 120655, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "children": [], "psr": "d"}, "fileID": 120570}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "ea83dc65b54d7264bbf2980bc50f4c20"}, "_text": "Premi ovunque per continuare...", "fontSize": 40, "width": 1340, "height": 65, "overflow": 0}, "fileID": 120658}], "fileID": 120747}, {"name": "Game/GamePivot/FSExtraWindow/content/PressAnywhere_Label/label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 120655, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "children": [], "psr": "d"}, "fileID": 120571}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "ea83dc65b54d7264bbf2980bc50f4c20"}, "_text": "Premi ovunque per continuare...", "fontSize": 40, "width": 1340, "height": 65, "overflow": 0}, "fileID": 120659}], "fileID": 120748}, {"name": "Game/GamePivot/FreeSpinsPurchase/FSPurchaseWindow/Content/AnimatedPivot/Texts/BuyText/label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 120655, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "children": [], "psr": "d"}, "fileID": 120572}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "ea83dc65b54d7264bbf2980bc50f4c20"}, "_text": "COMPRA GIRI GRATIS", "fontSize": 69, "width": 826, "height": 69, "overflow": 0}, "fileID": 120660}], "fileID": 120749}, {"name": "Game/GamePivot/FsFeature/content/Feature_trail/Holder/fisherman_highlight/ScalerX10/stretcher_fs/spins", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 120655, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "children": [], "psr": "d"}, "fileID": 120573}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "62bdaee7365cf924a8738f1d5fb39297"}, "_text": "giri", "fontSize": 30, "width": 64, "height": 33, "overflow": 0}, "fileID": 120661}], "fileID": 120750}, {"name": "Game/Background/PaytableOnScreen/Portrait/Message1/labelMsg1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 120655, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "children": [], "psr": "d"}, "fileID": 120574}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "9eb9df9b69dba9b49a344c3cd66fa06a"}, "_text": "TUTTI I SIMBOLI PAGANO DA SINISTRA A DESTRA. IL BONUS PAGA IN OGNI POSIZIONE", "fontSize": 40, "width": 1460, "height": 40, "alignment": 2}, "fileID": 120662}], "fileID": 120751}, {"name": "Game/GamePivot/FsFeature/content/Feature_trail/Holder/fisherman_highlight/ScalerX3/stretcher_fs/free", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 120655, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "children": [], "psr": "d"}, "fileID": 120575}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "62bdaee7365cf924a8738f1d5fb39297"}, "_text": "gratis", "fontSize": 30, "width": 60, "height": 33, "overflow": 0}, "fileID": 120663}], "fileID": 120752}, {"name": "Game/GamePivot/Reels/ThePivot/BonusMessages/Fisherman/Labels/uilabel", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 120655, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "children": [], "psr": "d"}, "fileID": 120576}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "ea83dc65b54d7264bbf2980bc50f4c20"}, "_text": "PIÙ PESCATORI!", "fontSize": 256, "width": 1000, "height": 450, "overflow": 0}, "fileID": 120664}], "fileID": 120753}, {"name": "Game/GamePivot/FreeSpinsPurchase/BuyButtons/Options/Option_0/OptVisual/Texts/BoughtText/LandscapeText/label0", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 120655, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "children": [], "psr": "d"}, "fileID": 120577}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "0852da972dc74354eb12b17314a0a600"}, "_text": "FUNZIONE", "fontSize": 40, "width": 170, "height": 80, "overflow": 0}, "fileID": 120665}], "fileID": 120754}, {"name": "Game/GamePivot/FreeSpinsPurchase/BuyButtons/Options/Option_0/OptVisual/Texts/BuyText/PortraitText/label0", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 120655, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "children": [], "psr": "d"}, "fileID": 120578}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "ea83dc65b54d7264bbf2980bc50f4c20"}, "_text": "COMPRA GIRI GRATIS", "fontSize": 26, "width": 270, "height": 70, "overflow": 0}, "fileID": 120666}], "fileID": 120755}, {"name": "Game/GamePivot/FsFeature/content/Feature_trail/Holder/fisherman_highlight/ScalerX2/stretcher_fs/free", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 120655, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "children": [], "psr": "d"}, "fileID": 120579}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "62bdaee7365cf924a8738f1d5fb39297"}, "_text": "gratis", "fontSize": 30, "width": 60, "height": 33, "overflow": 0}, "fileID": 120667}], "fileID": 120756}, {"name": "Game/GamePivot/FSResultWindow/content/SignPivot/FreespinsCongratsWindow/Labels/Congrats_label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 120655, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "children": [], "psr": "d"}, "fileID": 120580}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "ea83dc65b54d7264bbf2980bc50f4c20"}, "_text": "CONGRATULAZIONI!", "fontSize": 169, "width": 1000, "height": 169, "overflow": 0}, "fileID": 120668}], "fileID": 120757}, {"name": "Game/GamePivot/FSExtraWindow/content/SignPivot/FreespinsExtraWindow/Labels/Congrats_label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 120655, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "children": [], "psr": "d"}, "fileID": 120581}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "ea83dc65b54d7264bbf2980bc50f4c20"}, "_text": "CONGRATULAZIONI!", "fontSize": 169, "width": 1000, "height": 169, "overflow": 0}, "fileID": 120669}], "fileID": 120758}, {"name": "Game/GamePivot/FSExtraWindow/content/SignPivot/FreespinsExtraWindow/Labels/FreeSpins_label ", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 120655, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "children": [], "psr": "d"}, "fileID": 120582}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "ea83dc65b54d7264bbf2980bc50f4c20"}, "_text": "GIRI GRATIS", "fontSize": 75, "width": 519, "height": 125, "overflow": 0}, "fileID": 120670}], "fileID": 120759}, {"name": "Game/GamePivot/FSExtraWindow/content/SignPivot/FreespinsExtraWindow/Labels/AreNow_label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 120655, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "children": [], "psr": "d"}, "fileID": 120583}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "ea83dc65b54d7264bbf2980bc50f4c20"}, "_text": "ORA SONO", "fontSize": 70, "width": 500, "height": 169, "overflow": 0}, "fileID": 120671}], "fileID": 120760}, {"name": "Game/GamePivot/FreeSpinsPurchase/BuyButtons/Options/Option_0/OptVisual/Texts/BuyText/LandscapeTest/label0", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 120655, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "children": [], "psr": "d"}, "fileID": 120584}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "ea83dc65b54d7264bbf2980bc50f4c20"}, "_text": "COMPRA GIRI GRATIS", "fontSize": 26, "width": 318, "height": 18, "overflow": 0}, "fileID": 120672}], "fileID": 120761}, {"name": "Game/GamePivot/Reels/ThePivot/BonusMessages/PlusFS/Labels/uilabel", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 120655, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "children": [], "psr": "d"}, "fileID": 120585}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "ea83dc65b54d7264bbf2980bc50f4c20"}, "_text": "EXTRA GIRI GRATIS", "fontSize": 256, "width": 1200, "height": 450, "overflow": 0}, "fileID": 120673}], "fileID": 120762}, {"name": "Game/GamePivot/FsFeature/content/Feature_trail/Holder/fisherman_highlight/ScalerX10/stretcher_fs/free", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 120655, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "children": [], "psr": "d"}, "fileID": 120586}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "62bdaee7365cf924a8738f1d5fb39297"}, "_text": "gratis", "fontSize": 30, "width": 60, "height": 33, "overflow": 0}, "fileID": 120674}], "fileID": 120763}, {"name": "Game/GamePivot/FSWONWindow/content/FreespinsWonWindow/LabelsAndSprites/Congratulations_label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 120655, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "children": [], "psr": "d"}, "fileID": 120587}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "ea83dc65b54d7264bbf2980bc50f4c20"}, "_text": "CONGRATULAZIONI!", "fontSize": 169, "width": 1000, "height": 169, "overflow": 0}, "fileID": 120675}], "fileID": 120764}, {"name": "Game/GamePivot/Reels/ThePivot/BonusMessages/Fishes/Labels/uilabel", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 120655, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "children": [], "psr": "d"}, "fileID": 120588}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "ea83dc65b54d7264bbf2980bc50f4c20"}, "_text": "PIÙ PESCI!", "fontSize": 256, "width": 1200, "height": 450, "overflow": 0}, "fileID": 120676}], "fileID": 120765}, {"name": "Game/GamePivot/FreeSpinsPurchase/BuyButtons/Options/Option_0/OptVisual/Texts/BoughtText/PortraitText/label0", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 120655, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "children": [], "psr": "d"}, "fileID": 120589}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "0852da972dc74354eb12b17314a0a600"}, "_text": "FUNZIONE ATTIVA", "fontSize": 60, "width": 164, "height": 208, "overflow": 0}, "fileID": 120677}], "fileID": 120766}, {"name": "Game/GamePivot/FSExtraWindow/content/SignPivot/FreespinsExtraWindow/Labels/Extra_label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 120655, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "children": [], "psr": "d"}, "fileID": 120590}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "ea83dc65b54d7264bbf2980bc50f4c20"}, "_text": "EXTRA", "fontSize": 75, "width": 283, "height": 169, "overflow": 0}, "fileID": 120678}], "fileID": 120767}, {"name": "Game/GamePivot/FSResultWindow/content/SignPivot/FreespinsCongratsWindow/Labels/YouWon_label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 120655, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "children": [], "psr": "d"}, "fileID": 120591}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "ea83dc65b54d7264bbf2980bc50f4c20"}, "_text": "HAI VINTO", "fontSize": 110, "width": 1000, "height": 169, "overflow": 0}, "fileID": 120679}], "fileID": 120768}, {"name": "Game/GamePivot/FsFeature/content/Feature_trail/Holder/fisherman_highlight/ScalerX3/stretcher_fs/spins", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 120655, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "children": [], "psr": "d"}, "fileID": 120592}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "62bdaee7365cf924a8738f1d5fb39297"}, "_text": "giri", "fontSize": 30, "width": 64, "height": 33, "overflow": 0}, "fileID": 120680}], "fileID": 120769}, {"name": "Game/Background/PaytableOnScreen/Landscape/Message1/labelMsg1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 120655, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "children": [], "psr": "d"}, "fileID": 120593}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "9eb9df9b69dba9b49a344c3cd66fa06a"}, "_text": "TUTTI I SIMBOLI PAGANO DA SINISTRA A DESTRA. IL BONUS PAGA IN OGNI POSIZIONE", "fontSize": 30, "width": 1098, "height": 30, "alignment": 2}, "fileID": 120681}], "fileID": 120770}, {"name": "Game/GamePivot/FsFeature/content/Feature_trail/Holder/fisherman_highlight/ScalerX2/stretcher_fs/spins", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 120655, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "children": [], "psr": "d"}, "fileID": 120594}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "62bdaee7365cf924a8738f1d5fb39297"}, "_text": "giri", "fontSize": 30, "width": 64, "height": 33, "overflow": 0}, "fileID": 120682}], "fileID": 120771}, {"name": "Game/GamePivot/Reels/ThePivot/BonusMessages/Level2/Labels/uilabel", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 120655, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "children": [], "psr": "d"}, "fileID": 120595}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "ea83dc65b54d7264bbf2980bc50f4c20"}, "_text": "INIZIA DAL LIVELLO 2!", "fontSize": 256, "width": 1280, "height": 450, "overflow": 0}, "fileID": 120683}], "fileID": 120772}, {"name": "Game/GamePivot/FSWONWindow/content/FreespinsWonWindow/LabelsAndSprites/FreeSpins_label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 120655, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "children": [], "psr": "d"}, "fileID": 120596}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "ea83dc65b54d7264bbf2980bc50f4c20"}, "_text": "GIRI GRATIS", "fontSize": 110, "width": 1000, "height": 150, "overflow": 0}, "fileID": 120684}], "fileID": 120773}, {"name": "Game/GamePivot/FSResultWindow/content/PressAnywhere_Label/label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 120655, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "children": [], "psr": "d"}, "fileID": 120597}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "ea83dc65b54d7264bbf2980bc50f4c20"}, "_text": "Premi ovunque per continuare...", "fontSize": 40, "width": 1340, "height": 65, "overflow": 0}, "fileID": 120685}], "fileID": 120774}, {"name": "Game/GamePivot/Reels/ThePivot/BonusMessages/Hooks/Labels/uilabel", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 120655, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "children": [], "psr": "d"}, "fileID": 120598}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "ea83dc65b54d7264bbf2980bc50f4c20"}, "_text": "PIÙ AMI ED ESPLOSIONI!", "fontSize": 256, "width": 1000, "height": 450, "overflow": 0}, "fileID": 120686}], "fileID": 120775}, {"name": "Paytable/Pages/Page3/CAT/RuleHolder2/Rule2", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 120655, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "children": [], "psr": "d"}, "fileID": 120599}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "0c524605bf904a44488e740873704409"}, "_text": "Quando acquisti il round GIRI GRATIS, nel giro attivante possono comparire 3, 4 o 5 SCATTER casualmente.", "fontSize": 25, "anchorY": 0, "width": 1400, "height": 75, "overflow": 0, "spacingY": 5}, "fileID": 120687}], "fileID": 120776}, {"name": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder4/Label4", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 120655, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "children": [], "psr": "d"}, "fileID": 120600}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "0c524605bf904a44488e740873704409"}, "_text": "INIZI DAL LIVELLO 2 - Il round inizia dal livello 2 nella funzione progressiva.", "fontSize": 25, "anchorX": 0, "width": 1400, "height": 100, "overflow": 0}, "fileID": 120688}], "fileID": 120777}, {"name": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder5/Label5", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 120655, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "children": [], "psr": "d"}, "fileID": 120601}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "0c524605bf904a44488e740873704409"}, "_text": "- +2 GIRI - Il seguente round inizia con altri 2 giri gratis dall'inizio e altri 2 giri vengono aggiunti a ogni riattivazione.", "fontSize": 25, "anchorX": 0, "width": 1368, "height": 100, "overflow": 0}, "fileID": 120689}], "fileID": 120778}, {"name": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/RuleHolder1/Rule1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 120655, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "children": [], "psr": "d"}, "fileID": 120602}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "0c524605bf904a44488e740873704409"}, "_text": "Trova 3 o più simboli SCATTER per attivare la funzione GIRI GRATIS.", "fontSize": 25, "width": 1400, "height": 100, "overflow": 0}, "fileID": 120690}], "fileID": 120779}, {"name": "Paytable/Pages/Page4/RulesTop/AllValuesExpressed", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 120655, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "children": [], "psr": "d"}, "fileID": 120603}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "0c524605bf904a44488e740873704409"}, "_text": "Tutti i valori sono espressi come vincite reali in monete.", "fontSize": 25, "width": 1333, "height": 160, "overflow": 0}, "fileID": 120691}], "fileID": 120780}, {"name": "Paytable/Pages/Page4/Volatility/VolatilityDescription", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 120655, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "children": [], "psr": "d"}, "fileID": 120604}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "0c524605bf904a44488e740873704409"}, "_text": "I giochi a volatilità alta pagano meno spesso in media ma la possibilità di vincere grosse somme in brevi lassi di tempo è maggiore.", "fontSize": 25, "anchorY": 0, "width": 1368, "height": 93, "overflow": 0}, "fileID": 120692}], "fileID": 120781}, {"name": "Paytable/Pages/Page2/MoneySymbolHolder/DescriptionHolder/MoneySymbolRules/Label3", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 120655, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "children": [], "psr": "d"}, "fileID": 120605}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "0c524605bf904a44488e740873704409"}, "_text": "I simboli pesce sono anche simboli DENARO. A ogni spin, il pesce assume un valore di denaro casuale che può essere vinto durante la funzione GIRI GRATIS.", "fontSize": 25, "anchorX": 0, "width": 1008, "height": 100, "overflow": 0}, "fileID": 120693}], "fileID": 120782}, {"name": "Paytable/Pages/Page4/RulesBottom/RTP/TheoreticalRTP/Label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 120655, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "children": [], "psr": "d"}, "fileID": 120606}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "0c524605bf904a44488e740873704409"}, "_text": "L'RTP teorico di questa partita è {0}%", "fontSize": 25, "anchorY": 1, "width": 1200, "height": 75, "overflow": 0}, "fileID": 120694}], "fileID": 120783}, {"name": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder8/Rule8", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 120655, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "children": [], "psr": "d"}, "fileID": 120607}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "0c524605bf904a44488e740873704409"}, "_text": "<PERSON><PERSON><PERSON>, sempre casualmente, quando ci sono simboli pescatore a pescatore ma nessun pesce, al termine di un giro gratis, può comparire l'animazione di un bazooka e cambiare tutti i simboli a schermo, tranne i perscatori, in altri simboli.", "fontSize": 25, "width": 1269, "height": 100, "overflow": 0}, "fileID": 120695}], "fileID": 120784}, {"name": "Paytable/Pages/Page1/Rules/AllSymbolsPayLabel", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 120655, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "children": [], "psr": "d"}, "fileID": 120608}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "0c524605bf904a44488e740873704409"}, "_text": "<PERSON>tti i simboli pagano da sinistra a destra su rulli adiacenti iniziando dal rullo più a sinistra.", "fontSize": 25, "width": 1105, "height": 60, "overflow": 0}, "fileID": 120696}], "fileID": 120785}, {"name": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder5/Rule5", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 120655, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "children": [], "psr": "d"}, "fileID": 120609}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "0c524605bf904a44488e740873704409"}, "_text": "Dopo il quarto livello, la funzione non può più essere riattivata.", "fontSize": 25, "width": 1400, "height": 100, "overflow": 0}, "fileID": 120697}], "fileID": 120786}, {"name": "Paytable/Pages/Page3/FreeSpinsHolder2/SpecialReelsHolder/SpecialReels", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 120655, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "children": [], "psr": "d"}, "fileID": 120610}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "0c524605bf904a44488e740873704409"}, "_text": "Sono in gioco rulli speciali durante la funzione.", "fontSize": 25, "width": 1400, "height": 100, "overflow": 0}, "fileID": 120698}], "fileID": 120787}, {"name": "Paytable/Pages/Page3/CAT/RuleHolder1/Rule1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 120655, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "children": [], "psr": "d"}, "fileID": 120611}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "0c524605bf904a44488e740873704409"}, "_text": "La FUNZIONE GIRI GRATIS può essere attivato immediatamente dal gioco base acquistandolo per 100x la puntata totale attuale.", "fontSize": 25, "anchorY": 1, "width": 1400, "height": 100, "overflow": 0}, "fileID": 120699}], "fileID": 120788}, {"name": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/RuleHolder2/Rule2", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 120655, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "children": [], "psr": "d"}, "fileID": 120612}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "0c524605bf904a44488e740873704409"}, "_text": "Nel gioco base, ogni volta che compaiono 2 simboli SCATTER senza un terzo, c'è una possibilita che ne arrivi un altro a schermo tramite una funzione casuale:", "fontSize": 25, "width": 1169, "height": 100, "overflow": 0}, "fileID": 120700}], "fileID": 120789}, {"name": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder1/Label1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 120655, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "children": [], "psr": "d"}, "fileID": 120613}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "0c524605bf904a44488e740873704409"}, "_text": "- PIÙ PESCI - <PERSON><PERSON> simboli pesce sono presenti sui rulli durante il seguente round giri gratis", "fontSize": 25, "anchorX": 0, "width": 1400, "height": 100, "overflow": 0}, "fileID": 120701}], "fileID": 120790}, {"name": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder2/Rule2", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 120655, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "children": [], "psr": "d"}, "fileID": 120614}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "0c524605bf904a44488e740873704409"}, "_text": "Tutti i simboli WILD che compaiono durante la funzione vengono raccolti fino alla fine del giro.", "fontSize": 25, "width": 1400, "height": 100, "overflow": 0}, "fileID": 120702}], "fileID": 120791}, {"name": "Paytable/Pages/Page4/RulesTop/AllSymbolsPay", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 120655, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "children": [], "psr": "d"}, "fileID": 120615}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "0c524605bf904a44488e740873704409"}, "_text": "<PERSON><PERSON> i simboli pagano da sinistra a destra sulle linee di pagamento scelte.", "fontSize": 25, "width": 1194, "height": 160, "overflow": 0, "spacingY": 5}, "fileID": 120703}], "fileID": 120792}, {"name": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder3/Label3", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 120655, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "children": [], "psr": "d"}, "fileID": 120616}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "0c524605bf904a44488e740873704409"}, "_text": "- PIÙ DINAMITI, AMI E BAZOOKA - Durante il round, la possibilità di trovare la funzione giro dinamite, amo o bazooka è aumentata.", "fontSize": 25, "anchorX": 0, "width": 1359, "height": 100, "overflow": 0}, "fileID": 120704}], "fileID": 120793}, {"name": "Paytable/Pages/Page4/RulesBottom/SpaceAndEnter", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 120655, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "children": [], "psr": "d"}, "fileID": 120617}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "0c524605bf904a44488e740873704409"}, "_text": "I tasti SPAZIO e INVIO sulla tastiera possono essere usati per far partire o fermare il rullo.", "fontSize": 25, "width": 1400, "height": 60, "overflow": 0}, "fileID": 120705}], "fileID": 120794}, {"name": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder7/Rule7", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 120655, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "children": [], "psr": "d"}, "fileID": 120618}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "0c524605bf904a44488e740873704409"}, "_text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, quando sono presenti simboli pesce a schermo ma nessun pescatore, al termine di un giro gratis compare un amo che tira su un rullo a caso per portare i simboli pescatore nello schermo.", "fontSize": 25, "width": 1384, "height": 100, "overflow": 0}, "fileID": 120706}], "fileID": 120795}, {"name": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder1/Rule1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 120655, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "children": [], "psr": "d"}, "fileID": 120619}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "0c524605bf904a44488e740873704409"}, "_text": "Durante la funzione GIRI GRATIS ogni simbolo WILD raccoglie anche i valori dei simboli DENARO a schermo.", "fontSize": 25, "width": 1314, "height": 100, "overflow": 0}, "fileID": 120707}], "fileID": 120796}, {"name": "Paytable/Pages/Page3/MaxWin/TitleHolder/Title", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 120655, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "children": [], "psr": "d"}, "fileID": 120620}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "0c524605bf904a44488e740873704409"}, "_text": "VINCITA MASSIMA", "fontSize": 35, "width": 1100, "height": 100, "overflow": 0}, "fileID": 120708}], "fileID": 120797}, {"name": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder1/LabelHolder2/Label2", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 120655, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "children": [], "psr": "d"}, "fileID": 120621}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "0c524605bf904a44488e740873704409"}, "_text": "4x simboli SCATTER premiano con 15 giri gratis.", "fontSize": 25, "width": 1400, "height": 75, "overflow": 0}, "fileID": 120709}], "fileID": 120798}, {"name": "Paytable/Pages/Page4/MinMaxHolder/MaxBet/MaximumText", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 120655, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "children": [], "psr": "d"}, "fileID": 120622}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "0c524605bf904a44488e740873704409"}, "_text": "PUNTATA MASSIMA:", "fontSize": 25, "anchorX": 0, "width": 242, "height": 26}, "fileID": 120710}], "fileID": 120799}, {"name": "Paytable/Pages/Page2/FreeSpinsHolder1/TitleHolder/Title", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 120655, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "children": [], "psr": "d"}, "fileID": 120623}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "0c524605bf904a44488e740873704409"}, "_text": "GIRI GRATIS", "fontSize": 35, "width": 1150, "height": 100, "overflow": 0}, "fileID": 120711}], "fileID": 120800}, {"name": "Paytable/Pages/Page2/MoneySymbolHolder/TitleHolder/Title", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 120655, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "children": [], "psr": "d"}, "fileID": 120624}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "0c524605bf904a44488e740873704409"}, "_text": "SIMBOLO DENARO", "fontSize": 35, "width": 1150, "height": 70, "overflow": 0}, "fileID": 120712}], "fileID": 120801}, {"name": "Paytable/Pages/Page1/WildHolder/DescriptionHolder/Label1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 120655, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "children": [], "psr": "d"}, "fileID": 120625}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "0c524605bf904a44488e740873704409"}, "_text": "Questo è il simbolo WILD.", "fontSize": 25, "anchorX": 0, "anchorY": 1, "width": 363, "height": 75, "overflow": 0}, "fileID": 120713}], "fileID": 120802}, {"name": "Paytable/Pages/Page1/Title/PaytableTitleLabel1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 120655, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "children": [], "psr": "d"}, "fileID": 120626}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "0c524605bf904a44488e740873704409"}, "_text": "REGOLE DEL GIOCO", "fontSize": 35, "width": 1150, "height": 100, "overflow": 0}, "fileID": 120714}], "fileID": 120803}, {"name": "Paytable/Pages/Page3/CAT/TitleHolder/Title1New", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 120655, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "children": [], "psr": "d"}, "fileID": 120627}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "0c524605bf904a44488e740873704409"}, "_text": "COMPRA GIRI GRATIS", "fontSize": 35, "width": 1400, "height": 70, "overflow": 0}, "fileID": 120715}], "fileID": 120804}, {"name": "Paytable/Pages/Page4/MinMaxHolder/MinBet/MinimumText", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 120655, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "children": [], "psr": "d"}, "fileID": 120628}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "0c524605bf904a44488e740873704409"}, "_text": "PUNTATA MINIMA:", "fontSize": 25, "anchorX": 0, "width": 226, "height": 26}, "fileID": 120716}], "fileID": 120805}, {"name": "Paytable/Pages/Page4/RulesBottom/MalfunctionLabel", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 120655, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "children": [], "psr": "d"}, "fileID": 120629}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "0c524605bf904a44488e740873704409"}, "_text": "I pagamenti e i giochi sono cancellati dal malfunzionamento.", "fontSize": 25, "width": 888, "height": 60, "overflow": 0}, "fileID": 120717}], "fileID": 120806}, {"name": "Paytable/Pages/Page1/ScatterHolder/DescriptionHolder/Label2", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 120655, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "children": [], "psr": "d"}, "fileID": 120630}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "0c524605bf904a44488e740873704409"}, "_text": "Compare su tutti i rulli.", "fontSize": 25, "anchorX": 0, "anchorY": 0, "width": 357, "height": 75, "overflow": 0}, "fileID": 120718}], "fileID": 120807}, {"name": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder1/LabelHolder1/Label1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 120655, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "children": [], "psr": "d"}, "fileID": 120631}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "0c524605bf904a44488e740873704409"}, "_text": "5x simboli SCATTER premiano con 20 giri gratis.", "fontSize": 25, "width": 1400, "height": 75, "overflow": 0}, "fileID": 120719}], "fileID": 120808}, {"name": "Paytable/Pages/Page4/RulesTop/AllWinsMultiplied", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 120655, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "children": [], "psr": "d"}, "fileID": 120632}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "0c524605bf904a44488e740873704409"}, "_text": "Le vincite si moltiplicano per la puntata per linea.", "fontSize": 25, "width": 1194, "height": 160, "overflow": 0, "spacingY": 5}, "fileID": 120720}], "fileID": 120809}, {"name": "Paytable/Pages/Page1/ScatterHolder/DescriptionHolder/Label1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 120655, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "children": [], "psr": "d"}, "fileID": 120633}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "0c524605bf904a44488e740873704409"}, "_text": "Questo è il simbolo SCATTER. ", "fontSize": 25, "anchorX": 0, "anchorY": 1, "width": 422, "height": 75, "overflow": 0}, "fileID": 120721}], "fileID": 120810}, {"name": "Paytable/Pages/Page4/RulesTop/OnlyTheHighestWin", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 120655, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "children": [], "psr": "d"}, "fileID": 120634}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "0c524605bf904a44488e740873704409"}, "_text": "Viene pagata solo la vincita più alta per linea.", "fontSize": 25, "anchorY": 0, "width": 1400, "height": 50, "overflow": 0}, "fileID": 120722}], "fileID": 120811}, {"name": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder1/LabelHolder3/Label3", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 120655, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "children": [], "psr": "d"}, "fileID": 120635}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "0c524605bf904a44488e740873704409"}, "_text": "3x simboli SCATTER premiano con 10 giri gratis.", "fontSize": 25, "width": 1400, "height": 75, "overflow": 0}, "fileID": 120723}], "fileID": 120812}, {"name": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder2/LabelHolder1/Label1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 120655, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "children": [], "psr": "d"}, "fileID": 120636}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "0c524605bf904a44488e740873704409"}, "_text": "- Casualmente, se gli SCATTER a schermo possono muoversi di una posizione verso il basso senza lasciare l'area dei rulli, si attiva un respin dove i rulli con SCATTER si spostano di una posizione verso il basso e i rulli senza SCATTER girano di nuovo.", "fontSize": 25, "anchorX": 0, "width": 1400, "height": 125, "overflow": 0}, "fileID": 120724}], "fileID": 120813}, {"name": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder4/Rule4", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 120655, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "children": [], "psr": "d"}, "fileID": 120637}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "0c524605bf904a44488e740873704409"}, "_text": "Gli spin riattivati vengono giocati dopo al termine del gruppo precedente di giri gratis. Il moltiplicatore si applica agli spin riattivati.", "fontSize": 25, "width": 920, "height": 125, "overflow": 0}, "fileID": 120725}], "fileID": 120814}, {"name": "Paytable/Pages/Page4/RulesTop/WhenWinningOnMultiplePaylines", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 120655, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "children": [], "psr": "d"}, "fileID": 120638}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "0c524605bf904a44488e740873704409"}, "_text": "Quando si vince su più linee, tutte le vincite vengono aggiunte alla vincita totale.", "fontSize": 25, "anchorY": 0, "width": 1400, "height": 50, "overflow": 0}, "fileID": 120726}], "fileID": 120815}, {"name": "Paytable/Pages/Page4/Title/PaytableTitleLabel", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 120655, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "children": [], "psr": "d"}, "fileID": 120639}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "0c524605bf904a44488e740873704409"}, "_text": "REGOLE DEL GIOCO", "fontSize": 30, "width": 1150, "height": 100, "overflow": 0}, "fileID": 120727}], "fileID": 120816}, {"name": "Paytable/Pages/Page3/MaxWin/RuleHolder/Label1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 120655, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "children": [], "psr": "d"}, "fileID": 120640}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "0c524605bf904a44488e740873704409"}, "_text": "La vincita massima è limitata a {0}x la puntata. Se la vincita totale di una FUNZIONE GIRI GRATIS raggiunge {1}x la funzione termina immediatamente, la vincita viene consegnata e tutti i giri gratis rimanenti vengono annullati", "fontSize": 25, "anchorY": 0, "width": 1400, "height": 100, "overflow": 0}, "fileID": 120728}], "fileID": 120817}, {"name": "Paytable/Pages/Page4/RulesBottom/RTP/TheoreticalRTPBONUS/Label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 120655, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "children": [], "psr": "d"}, "fileID": 120641}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "0c524605bf904a44488e740873704409"}, "_text": "L'RTP (ritorno al giocatore) del gioco usando \"COMPRA GIRI GRATIS\" è {0}%", "fontSize": 25, "anchorY": 0, "width": 1200, "height": 75, "overflow": 0}, "fileID": 120729}], "fileID": 120818}, {"name": "Paytable/Pages/Page4/Volatility/VolatilityMeter/LabelHolder/VolatilityLabel", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 120655, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "children": [], "psr": "d"}, "fileID": 120642}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "9eb9df9b69dba9b49a344c3cd66fa06a"}, "_text": "VOLATILITÀ", "fontSize": 22, "anchorX": 0, "width": 106, "height": 22}, "fileID": 120730}], "fileID": 120819}, {"name": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder6/Rule6", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 120655, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "children": [], "psr": "d"}, "fileID": 120643}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "0c524605bf904a44488e740873704409"}, "_text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, quando sono presenti simboli pescatore a schermo ma nessun pesce, al termine di un giro gratis possono comparire dei simboli pesce DENARO in posizioni casuale tramite la funzione giro dinamite.", "fontSize": 25, "width": 1400, "height": 100, "overflow": 0}, "fileID": 120731}], "fileID": 120820}, {"name": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder2/LabelHolder2/Label2", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 120655, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "children": [], "psr": "d"}, "fileID": 120644}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "0c524605bf904a44488e740873704409"}, "_text": "- Casualmente, un amo può tirare su uno dei rulli per rivelare un altro SCATTER.", "fontSize": 25, "anchorX": 0, "width": 1400, "height": 75, "overflow": 0}, "fileID": 120732}], "fileID": 120821}, {"name": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/RuleHolder3/Rule3", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 120655, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "children": [], "psr": "d"}, "fileID": 120645}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "0c524605bf904a44488e740873704409"}, "_text": "Prima dell'inizio del round, vengono selezionati da 0 a 5 modificatori che si applicano al round seguente:", "fontSize": 25, "width": 1400, "height": 100, "overflow": 0}, "fileID": 120733}], "fileID": 120822}, {"name": "Paytable/Pages/Page1/WildHolder/DescriptionHolder/Label2", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 120655, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "children": [], "psr": "d"}, "fileID": 120646}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "0c524605bf904a44488e740873704409"}, "_text": "Compare su tutti i rulli durante la funzione GIRI GRATIS.", "fontSize": 25, "anchorX": 0, "width": 363, "height": 75, "overflow": 0}, "fileID": 120734}], "fileID": 120823}, {"name": "Paytable/Pages/Page1/WildHolder/DescriptionHolder/Label3", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 120655, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "children": [], "psr": "d"}, "fileID": 120647}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "0c524605bf904a44488e740873704409"}, "_text": "Sostituisce tutti i simboli tranne gli SCATTER.", "fontSize": 25, "anchorX": 0, "anchorY": 0, "width": 363, "height": 75, "overflow": 0}, "fileID": 120735}], "fileID": 120824}, {"name": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder3/Rule3", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 120655, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "children": [], "psr": "d"}, "fileID": 120648}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "0c524605bf904a44488e740873704409"}, "_text": "Ogni 4 simboli WILD raccolti riattivano la funzione, premiano con 10 giri gratis aggiuntivi e il moltiplicatore per la raccolta dei simboli DENARO aumenta a 2x per il secondo livello, 3x per il terzo livello e 10x per il quarto livello.", "fontSize": 25, "width": 1400, "height": 150, "overflow": 0}, "fileID": 120736}], "fileID": 120825}, {"name": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder2/Label2", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 120655, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "children": [], "psr": "d"}, "fileID": 120649}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "0c524605bf904a44488e740873704409"}, "_text": "- PIÙ PESCATORI - Più simboli WILD sono presenti sui rulli durante il seguente round giri gratis", "fontSize": 25, "anchorX": 0, "width": 1400, "height": 100, "overflow": 0}, "fileID": 120737}], "fileID": 120826}, {"name": "IntroScreen/content/Labels_Holder_landscape/Label_Holder_bigger_1/Label_2 (1)", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 120655, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "children": [], "psr": "d"}, "fileID": 120650}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "62bdaee7365cf924a8738f1d5fb39297"}, "_text": "GRANDI MODIFICATORI DI GIRI GRATIS!", "fontSize": 60, "width": 1000, "height": 80, "overflow": 0}, "fileID": 120738}], "fileID": 120827}, {"name": "IntroScreen/content/Labels_Holder_landscape/Label_Holder_bigger_1/Label_1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 120655, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "children": [], "psr": "d"}, "fileID": 120651}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "62bdaee7365cf924a8738f1d5fb39297"}, "_text": "VAI A PESCA DI", "fontSize": 60, "width": 1000, "height": 80, "overflow": 0}, "fileID": 120739}], "fileID": 120828}, {"name": "IntroScreen/content/IntroButtons/ButtonSkipIntro/content/TextHolder/Label_1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 120655, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "children": [], "psr": "d"}, "fileID": 120652}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "0c524605bf904a44488e740873704409"}, "_text": "NON MOSTRARE PIÙ", "fontSize": 30, "anchorX": 0, "width": 298, "height": 30}, "fileID": 120740}], "fileID": 120829}, {"name": "Paytable/Pages/Page2/MoneySymbolHolder/DescriptionHolder/PossibleValues/Label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 120655, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "children": [], "psr": "d"}, "fileID": 120653}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "0c524605bf904a44488e740873704409"}, "_text": "Valori possibili: 2x, 5x, 10x, 15x, 20x, 25x, 50x, 100x, 200x, 500x, 1666x, 2500x o 5000x la puntata totale.", "fontSize": 25, "anchorX": 0, "width": 986, "height": 100, "overflow": 0}, "fileID": 120741}], "fileID": 120830}, {"name": "Paytable/Pages/Page3/MaxWin/RuleHolder/HolderLabelJackpot/Label1New", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 120655, "guid": "94b4cc74e863dd443bd5ec8b5fb17dbb"}, "children": [], "psr": "d"}, "fileID": 120654}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "0c524605bf904a44488e740873704409"}, "_text": "L'importo massimo di vincita è limitato a {0}x la puntata tranne per il Jackpot. Se la vincita totale di una funzione dei GIRI GRATIS raggiunge {1}x la puntata, la funzione termina subito, la vincita viene assegnata e tutti i giri gratis vengono rimossi.", "fontSize": 25, "anchorY": 0, "width": 1400, "height": 100, "overflow": 0}, "fileID": 120742}], "fileID": 120831}]}}, {"type": "Font", "id": "ea83dc65b54d7264bbf2980bc50f4c20", "data": {"fontName": "fea83dc65b54d7264bbf2980bc50f4c", "path": "@font-face{font-family:'fea83dc65b54d7264bbf2980bc50f4c';src:url('data:application/x-font-woff;base64,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') format('woff')}"}}, {"type": "Font", "id": "0852da972dc74354eb12b17314a0a600", "data": {"fontName": "f0852da972dc74354eb12b17314a0a6", "path": "@font-face{font-family:'f0852da972dc74354eb12b17314a0a6';src:url('data:application/x-font-woff;base64,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') format('woff')}"}}, {"type": "Font", "id": "62bdaee7365cf924a8738f1d5fb39297", "data": {"fontName": "f62bdaee7365cf924a8738f1d5fb392", "path": "@font-face{font-family:'f62bdaee7365cf924a8738f1d5fb392';src:url('data:application/x-font-woff;base64,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') format('woff')}"}}, {"type": "Font", "id": "9eb9df9b69dba9b49a344c3cd66fa06a", "data": {"fontName": "f9eb9df9b69dba9b49a344c3cd66fa0", "path": "@font-face{font-family:'f9eb9df9b69dba9b49a344c3cd66fa0';src:url('data:application/x-font-woff;base64,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') format('woff')}"}}, {"type": "Font", "id": "0c524605bf904a44488e740873704409", "data": {"fontName": "f0c524605bf904a44488e7408737044", "path": "@font-face{font-family:'f0c524605bf904a44488e7408737044';src:url('data:application/x-font-woff;base64,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') format('woff')}"}}]}