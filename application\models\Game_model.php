<?php 

class Game_model extends CI_Model {

    public function game_launch($data){

        $this->db->where('game_code', $data['game_code']);
        $game = $this->db->get('games')->result();

        $this->db->where('userCode', $data['user_code']);
        $this->db->where('agentCode', $data['agent_code']);
        $user = $this->db->get('users')->result();

        $this->db->where('user', $user[0]->aasUserCode);
        $this->db->delete('game_session');

        if (!empty($game)) {
            $gameURL = base_url().'play?game='.$data['game_code'].'&user='. $user[0]->aasUserCode  .'&lang=pt&cur=R$';
            $data = array(
                'status' => 1, 'message' => 'GAME_URL', 'launch_url' => $gameURL
            );
            return $data;
        } else {
            return false;
        }

    }

    // New methods for admin panel
    public function get_game_by_code($game_code) {
        $this->db->where('game_code', $game_code);
        $query = $this->db->get('games');
        return $query->row_array();
    }

    public function get_game_by_id($id) {
        $this->db->where('id', $id);
        $query = $this->db->get('games');
        return $query->row_array();
    }

    public function get_all_games() {
        $this->db->select('g.*, p.name as provider_name');
        $this->db->from('games g');
        $this->db->join('providers p', 'g.provider = p.code', 'left');
        $this->db->order_by('g.game_name', 'ASC');
        $query = $this->db->get();
        return $query->result_array();
    }

    public function get_active_games() {
        $this->db->select('g.*, p.name as provider_name');
        $this->db->from('games g');
        $this->db->join('providers p', 'g.provider = p.code', 'left');
        $this->db->where('g.status', 1);
        $this->db->order_by('g.game_name', 'ASC');
        $query = $this->db->get();
        return $query->result_array();
    }

    public function count_games() {
        return $this->db->count_all('games');
    }

    public function create_game($game_data) {
        return $this->db->insert('games', $game_data);
    }

    public function update_game($id, $game_data) {
        $this->db->where('id', $id);
        return $this->db->update('games', $game_data);
    }

    public function update_game_by_code($game_code, $game_data) {
        $this->db->where('game_code', $game_code);
        return $this->db->update('games', $game_data);
    }

    public function get_providers() {
        $this->db->order_by('name', 'ASC');
        $query = $this->db->get('providers');
        return $query->result_array();
    }

    public function get_provider_by_code($code) {
        $this->db->where('code', $code);
        $query = $this->db->get('providers');
        return $query->row_array();
    }

    public function create_provider($provider_data) {
        return $this->db->insert('providers', $provider_data);
    }

    public function update_provider($id, $provider_data) {
        $this->db->where('id', $id);
        return $this->db->update('providers', $provider_data);
    }

    public function get_games_by_provider($provider_code) {
        $this->db->where('provider', $provider_code);
        $this->db->order_by('game_name', 'ASC');
        $query = $this->db->get('games');
        return $query->result_array();
    }

    public function get_game_stats($game_code) {
        $this->db->select('COUNT(*) as total_plays, SUM(bet_money) as total_bet, SUM(win_money) as total_win, AVG(bet_money) as avg_bet');
        $this->db->from('transaction_history');
        $this->db->where('game_code', $game_code);
        $query = $this->db->get();
        return $query->row_array();
    }

    public function get_popular_games($limit = 10) {
        $this->db->select('g.game_code, g.game_name, g.banner, COUNT(th.id) as play_count, SUM(th.bet_money) as total_bet');
        $this->db->from('games g');
        $this->db->join('transaction_history th', 'g.game_code = th.game_code');
        $this->db->where('g.status', 1);
        $this->db->group_by('g.game_code');
        $this->db->order_by('play_count', 'DESC');
        $this->db->limit($limit);
        $query = $this->db->get();
        return $query->result_array();
    }

    public function get_agent_game_config($agentCode, $game_code) {
        $this->db->where('agentCode', $agentCode);
        $this->db->where('game_code', $game_code);
        $query = $this->db->get('agent_game_config');
        return $query->row_array();
    }

    public function get_game_config_for_agent($agentCode, $game_code) {
        // First get the base game configuration
        $game = $this->get_game_by_code($game_code);

        if (!$game) {
            return false;
        }

        // Check if agent has custom configuration
        $agent_config = $this->get_agent_game_config($agentCode, $game_code);

        if ($agent_config) {
            // Override with agent-specific settings
            if ($agent_config['custom_rtp']) {
                $game['rtp'] = $agent_config['custom_rtp'];
            }
            if ($agent_config['custom_min_bet']) {
                $game['min_bet'] = $agent_config['custom_min_bet'];
            }
            if ($agent_config['custom_max_bet']) {
                $game['max_bet'] = $agent_config['custom_max_bet'];
            }
        }

        return $game;
    }
}