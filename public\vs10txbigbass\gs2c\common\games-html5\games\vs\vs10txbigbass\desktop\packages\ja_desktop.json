{"resources": [{"type": "GameObject", "id": "ad2121a9ed6a85d47a07ff1194ef7bda", "data": {"root": [{"name": "ja_desktop", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 0}, "children": [{"fileID": 4228, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, {"fileID": 4229, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, {"fileID": 4230, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, {"fileID": 4231, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, {"fileID": 4232, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, {"fileID": 4233, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, {"fileID": 4234, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, {"fileID": 4235, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, {"fileID": 4236, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, {"fileID": 4237, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, {"fileID": 4238, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, {"fileID": 4239, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, {"fileID": 4240, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, {"fileID": 4241, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, {"fileID": 4242, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, {"fileID": 4243, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, {"fileID": 4244, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, {"fileID": 4245, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, {"fileID": 4246, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, {"fileID": 4247, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, {"fileID": 4248, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, {"fileID": 4249, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, {"fileID": 4250, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, {"fileID": 4251, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, {"fileID": 4252, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, {"fileID": 4253, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, {"fileID": 4254, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, {"fileID": 4255, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, {"fileID": 4256, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, {"fileID": 4257, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, {"fileID": 4258, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, {"fileID": 4259, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, {"fileID": 4260, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, {"fileID": 4261, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, {"fileID": 4262, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, {"fileID": 4263, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, {"fileID": 4264, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, {"fileID": 4265, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, {"fileID": 4266, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, {"fileID": 4267, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, {"fileID": 4268, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, {"fileID": 4269, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, {"fileID": 4270, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, {"fileID": 4271, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, {"fileID": 4272, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, {"fileID": 4273, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, {"fileID": 4274, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, {"fileID": 4275, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, {"fileID": 4276, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, {"fileID": 4277, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, {"fileID": 4278, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, {"fileID": 4279, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, {"fileID": 4280, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, {"fileID": 4281, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, {"fileID": 4282, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, {"fileID": 4283, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, {"fileID": 4284, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, {"fileID": 4285, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, {"fileID": 4286, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, {"fileID": 4287, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, {"fileID": 4288, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, {"fileID": 4289, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, {"fileID": 4290, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, {"fileID": 4291, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, {"fileID": 4292, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, {"fileID": 4293, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, {"fileID": 4294, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, {"fileID": 4295, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, {"fileID": 4296, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, {"fileID": 4297, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, {"fileID": 4298, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, {"fileID": 4299, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, {"fileID": 4300, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, {"fileID": 4301, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, {"fileID": 4302, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, {"fileID": 4303, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, {"fileID": 4304, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, {"fileID": 4305, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, {"fileID": 4306, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, {"fileID": 4307, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, {"fileID": 4308, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, {"fileID": 4309, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, {"fileID": 4310, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, {"fileID": 4311, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, {"fileID": 4312, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, {"fileID": 4313, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, {"fileID": 4314, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}], "s": "0"}, "fileID": 4315}, {"componentType": "ModificationsManager", "enabled": true, "serializableData": {"root": {"fileID": 0}, "EditMode": false, "Atlases": [], "Transforms": [{"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page1/Title/PaytableTitleLabel1", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page1/Rules/AllSymbolsPayLabel", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page1/ScatterHolder/SymbolScatter/Sprite", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0.52, "y": 0.52, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page1/ScatterHolder/DescriptionHolder/Label1", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -24, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page1/ScatterHolder/DescriptionHolder/Label2", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -35, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page1/WildHolder/SymbolScatter/Sprite", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0.25, "y": 0.25, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page1/WildHolder/DescriptionHolder/Label1", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -12.5, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page1/WildHolder/DescriptionHolder/Label2", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -50, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page1/WildHolder/DescriptionHolder/Label3", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -87.5, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/MoneySymbolHolder/TitleHolder/Title", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 17, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/MoneySymbolHolder/DescriptionHolder/MoneySymbolHolder/Sprite", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0.7, "y": 0.7, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/MoneySymbolHolder/DescriptionHolder/MoneySymbolRules/Label3", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 4, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FreeSpinsHolder1/TitleHolder/Title", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/RuleHolder1/Rule1", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder1/LabelHolder1/Label1", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder1/LabelHolder2/Label2", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder1/LabelHolder3/Label3", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/RuleHolder2/Rule2", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 4, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder2/LabelHolder1/Label1", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder2/LabelHolder2/Label2", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -13, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/RuleHolder3/Rule3", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder1/Label1", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder2/Label2", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder3/Label3", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder4/Label4", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder5/Label5", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder1/Rule1", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 25, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder2/Rule2", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 33.7, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder3/Rule3", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 25, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder4/Rule4", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder5/Rule5", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 7, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder6/Rule6", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -3, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder7/Rule7", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -21, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder8/Rule8", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -41, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/FreeSpinsHolder2/SpecialReelsHolder/SpecialReels", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -53, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/MaxWin/TitleHolder/Title", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -20, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/MaxWin/RuleHolder/Label1", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -34, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/CAT/TitleHolder/Title1New", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/CAT/RuleHolder1/Rule1", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/CAT/RuleHolder2/Rule2", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -13, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/CAT/RuleHolder3/Rule3", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page4/Title/PaytableTitleLabel", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page4/Volatility/VolatilityDescription", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -30, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page4/RulesTop/AllSymbolsPay", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 32, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page4/RulesTop/AllWinsMultiplied", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -4, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page4/RulesTop/AllValuesExpressed", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -45, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page4/RulesTop/OnlyTheHighestWin", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -65, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page4/RulesTop/WhenWinningOnMultiplePaylines", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -95, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page4/Lines/Sprite", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page4/RulesBottom/SpaceAndEnter", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 92, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page4/RulesBottom/RTP", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page4/RulesBottom/MalfunctionLabel", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -97, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page4/MinMaxHolder", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -185, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/MaxWin/RuleHolder/HolderLabelJackpot/Label1New", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -16, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}], "Labels": [{"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 4316, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "extraPayloads": [], "path": "Game/GamePivot/FSWONWindow/content/FreespinsWonWindow/LabelsAndSprites/Catches_label", "oldContent": "CATCHES", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 4317, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "extraPayloads": [], "path": "Game/GamePivot/FreeSpinsPurchase/BuyButtons/Options/Option_0/OptVisual/Texts/BoughtText/LandscapeText/label1", "oldContent": "ACTIVE", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 4318, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "extraPayloads": [], "path": "Game/GamePivot/FSWONWindow/PressAnywhere_Label/label", "oldContent": "Press anywhere to continue", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 4319, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "extraPayloads": [], "path": "Game/GamePivot/FSExtraWindow/content/PressAnywhere_Label/label", "oldContent": "Press anywhere to continue...", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 4320, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "extraPayloads": [], "path": "Game/GamePivot/FreeSpinsPurchase/FSPurchaseWindow/Content/AnimatedPivot/Texts/BuyText/label", "oldContent": "BUY FREE SPINS", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 4321, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "extraPayloads": [], "path": "Game/GamePivot/FsFeature/content/Feature_trail/Holder/fisherman_highlight/ScalerX10/stretcher_fs/spins", "oldContent": "spins", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 4322, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "extraPayloads": [], "path": "Game/Background/PaytableOnScreen/Portrait/Message1/labelMsg1", "oldContent": "AL<PERSON> SYMBOLS PAY FROM LEFT TO RIGHT. BONUS PAYS ON ANY POSITION.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 4323, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "extraPayloads": [], "path": "Game/GamePivot/FsFeature/content/Feature_trail/Holder/fisherman_highlight/ScalerX3/stretcher_fs/free", "oldContent": "free", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 4324, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "extraPayloads": [], "path": "Game/GamePivot/Reels/ThePivot/BonusMessages/Fisherman/Labels/uilabel", "oldContent": "MORE FISHERMEN!", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 4325, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "extraPayloads": [], "path": "Game/GamePivot/FreeSpinsPurchase/BuyButtons/Options/Option_0/OptVisual/Texts/BoughtText/LandscapeText/label0", "oldContent": "FEATURE", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 4326, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "extraPayloads": [], "path": "Game/GamePivot/FreeSpinsPurchase/BuyButtons/Options/Option_0/OptVisual/Texts/BuyText/PortraitText/label0", "oldContent": "BUY FREE SPINS", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 4327, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "extraPayloads": [], "path": "Game/GamePivot/FsFeature/content/Feature_trail/Holder/fisherman_highlight/ScalerX2/stretcher_fs/free", "oldContent": "free", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 4328, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "extraPayloads": [], "path": "Game/GamePivot/FSResultWindow/content/SignPivot/FreespinsCongratsWindow/Labels/Congrats_label", "oldContent": "CONGRATULATIONS!", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 4329, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "extraPayloads": [], "path": "Game/GamePivot/FSExtraWindow/content/SignPivot/FreespinsExtraWindow/Labels/Congrats_label", "oldContent": "CONGRATULATIONS!", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 4330, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "extraPayloads": [], "path": "Game/GamePivot/FSExtraWindow/content/SignPivot/FreespinsExtraWindow/Labels/FreeSpins_label ", "oldContent": "FREESPINS", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 4331, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "extraPayloads": [], "path": "Game/GamePivot/FSExtraWindow/content/SignPivot/FreespinsExtraWindow/Labels/AreNow_label", "oldContent": "ARE NOW", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 4332, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "extraPayloads": [], "path": "Game/GamePivot/FreeSpinsPurchase/BuyButtons/Options/Option_0/OptVisual/Texts/BuyText/LandscapeTest/label0", "oldContent": "BUY FREE SPINS", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 4333, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "extraPayloads": [], "path": "Game/GamePivot/Reels/ThePivot/BonusMessages/PlusFS/Labels/uilabel", "oldContent": "EXTRA FREE SPINS!", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 4334, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "extraPayloads": [], "path": "Game/GamePivot/FsFeature/content/Feature_trail/Holder/fisherman_highlight/ScalerX10/stretcher_fs/free", "oldContent": "free", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 4335, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "extraPayloads": [], "path": "Game/GamePivot/FSWONWindow/content/FreespinsWonWindow/LabelsAndSprites/Congratulations_label", "oldContent": "CONGRATULATIONS!", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 4336, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "extraPayloads": [], "path": "Game/GamePivot/Reels/ThePivot/BonusMessages/Fishes/Labels/uilabel", "oldContent": "MORE FISH!", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 4337, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "extraPayloads": [], "path": "Game/GamePivot/FreeSpinsPurchase/BuyButtons/Options/Option_0/OptVisual/Texts/BoughtText/PortraitText/label0", "oldContent": "FEATURE ACTIVE", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 4338, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "extraPayloads": [], "path": "Game/GamePivot/FSExtraWindow/content/SignPivot/FreespinsExtraWindow/Labels/Extra_label", "oldContent": "THE NEXT", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 4339, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "extraPayloads": [], "path": "Game/GamePivot/FSResultWindow/content/SignPivot/FreespinsCongratsWindow/Labels/YouWon_label", "oldContent": "YOU HAVE WON", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 4340, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "extraPayloads": [], "path": "Game/GamePivot/FsFeature/content/Feature_trail/Holder/fisherman_highlight/ScalerX3/stretcher_fs/spins", "oldContent": "spins", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 4341, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "extraPayloads": [], "path": "Game/Background/PaytableOnScreen/Landscape/Message1/labelMsg1", "oldContent": "AL<PERSON> SYMBOLS PAY FROM LEFT TO RIGHT. BONUS PAYS ON ANY POSITION.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 4342, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "extraPayloads": [], "path": "Game/GamePivot/FsFeature/content/Feature_trail/Holder/fisherman_highlight/ScalerX2/stretcher_fs/spins", "oldContent": "spins", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 4343, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "extraPayloads": [], "path": "Game/GamePivot/Reels/ThePivot/BonusMessages/Level2/Labels/uilabel", "oldContent": "START FROM LEVEL 2!", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 4344, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "extraPayloads": [], "path": "Game/GamePivot/FSWONWindow/content/FreespinsWonWindow/LabelsAndSprites/FreeSpins_label", "oldContent": "FREE SPINS!", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 4345, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "extraPayloads": [], "path": "Game/GamePivot/FSResultWindow/content/PressAnywhere_Label/label", "oldContent": "Press anywhere to continue...", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 4346, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "extraPayloads": [], "path": "Game/GamePivot/Reels/ThePivot/BonusMessages/Hooks/Labels/uilabel", "oldContent": "MORE HOOKS AND EXPLOSIONS!", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 4347, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/CAT/RuleHolder2/Rule2", "oldContent": "When buying the FREE SPINS round, on the triggering spin 3, 4 or 5 SCATTERS can hit randomly.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 4348, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder4/Label4", "oldContent": "- START FROM LEVEL 2 - The round starts from level 2 in the progressive feature.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 4349, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder5/Label5", "oldContent": "- +2 SPINS - The subsequent round starts with 2 more free spins from the beginning and 2 more spins are added to every retrigger.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 4350, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/RuleHolder1/Rule1", "oldContent": "Hit 3 or more SCATTER symbols to trigger the FREE SPINS feature.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 4351, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/RulesTop/AllValuesExpressed", "oldContent": "All values are expressed as actual wins in coins.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 4352, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/Volatility/VolatilityDescription", "oldContent": "High volatility games pay out less often on average but the chance to hit big wins in a short time span is higher", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 4353, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/MoneySymbolHolder/DescriptionHolder/MoneySymbolRules/Label3", "oldContent": "The fish paying symbols are also MONEY symbols. At every spin, the fish take a random money value which can be won during the FREE SPINS feature.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 4354, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/RulesBottom/RTP/TheoreticalRTP/Label", "oldContent": "The theoretical RTP of this game is {0}%", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 4355, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder8/Rule8", "oldContent": "Also randomly, when there are fisherman symbols on the screen but no fish, at the end of a free spin, a bazooka animation can appear and change all the symbols from the screen, except for fisherman symbols to something else.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 4356, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "extraPayloads": [], "path": "Paytable/Pages/Page1/Rules/AllSymbolsPayLabel", "oldContent": "All symbols pay from left to right on adjacent reels starting from the leftmost reel.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 4357, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder5/Rule5", "oldContent": "After the fourth level, the feature cannot be retriggered anymore.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 4358, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/FreeSpinsHolder2/SpecialReelsHolder/SpecialReels", "oldContent": "Special reels are in play during the feature.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 4359, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/CAT/RuleHolder1/Rule1", "oldContent": "The FREE SPINS round can be instantly triggered from the base game by buying it for 100x current total bet.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 4360, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/RuleHolder2/Rule2", "oldContent": "In the base game whenever 2 SCATTER symbols hit without a third, there is a chance for another one to be brought onto the screen by a random feature:", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 4361, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder1/Label1", "oldContent": "- MORE FISH - More fish symbols are present on the reel strips during the subsequent free spins round", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 4362, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder2/Rule2", "oldContent": "All the WILD symbols that hit during the feature are collected until the end of the round.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 4363, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/RulesTop/AllSymbolsPay", "oldContent": "All symbols pay from left to right on selected paylines.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 4364, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder3/Label3", "oldContent": "- MORE DYNAMITES, HOOKS AND BAZOOKAS - During the round, the chance to hit dynamite, hook or bazooka spin feature is increased.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 4365, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/RulesBottom/SpaceAndEnter", "oldContent": "SPACE and ENTER buttons on the keyboard can be used to start and stop the spin.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 4366, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder7/Rule7", "oldContent": "Randomly, when there are fish symbols on the screen but no fisherman, at the end of a free spin, a hook will appear pulling a random reel up to bring fisherman symbols onto the screen.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 4367, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder1/Rule1", "oldContent": "During the FREE SPINS feature each WILD symbol also collects all the values from MONEY symbols on the screen.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 4368, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/MaxWin/TitleHolder/Title", "oldContent": "MAX WIN", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 4369, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder1/LabelHolder2/Label2", "oldContent": "4x SCATTER awards 15 free spins", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 4370, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/MinMaxHolder/MaxBet/MaximumText", "oldContent": "MAXIMUM BET:", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 4371, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FreeSpinsHolder1/TitleHolder/Title", "oldContent": "FREE SPINS", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 4372, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/MoneySymbolHolder/TitleHolder/Title", "oldContent": "MONEY SYMBOL", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 4373, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "extraPayloads": [], "path": "Paytable/Pages/Page1/WildHolder/DescriptionHolder/Label1", "oldContent": "This is the WILD symbol.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 4374, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "extraPayloads": [], "path": "Paytable/Pages/Page1/Title/PaytableTitleLabel1", "oldContent": "GAME RULES", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 4375, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/CAT/TitleHolder/Title1New", "oldContent": "BUY FREE SPINS", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 4376, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/MinMaxHolder/MinBet/MinimumText", "oldContent": "MINIMUM BET:", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 4377, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/RulesBottom/MalfunctionLabel", "oldContent": "Malfunction voids all pays and plays.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 4378, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "extraPayloads": [], "path": "Paytable/Pages/Page1/ScatterHolder/DescriptionHolder/Label2", "oldContent": "It appears on all reels.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 4379, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder1/LabelHolder1/Label1", "oldContent": "5x SCATTER awards 20 free spins", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 4380, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/RulesTop/AllWinsMultiplied", "oldContent": "All wins are multiplied by bet per line.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 4381, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "extraPayloads": [], "path": "Paytable/Pages/Page1/ScatterHolder/DescriptionHolder/Label1", "oldContent": "This is the SCATTER symbol.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 4382, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/RulesTop/OnlyTheHighestWin", "oldContent": "Only the highest win is paid per line.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 4383, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder1/LabelHolder3/Label3", "oldContent": "3x SCATTER awards 10 free spins", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 4384, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder2/LabelHolder1/Label1", "oldContent": "- Randomly, if the SCATTERS on the screen can move down one position without leaving the reel area, a respin is triggered where the reels with SCATTERS move one position down and the reels without SCATTERS respin.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 4385, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder4/Rule4", "oldContent": "The retriggered spins are played after the previous batch of free spins ends. The multiplier applies to the retriggered spins.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 4386, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/RulesTop/WhenWinningOnMultiplePaylines", "oldContent": "When winning on multiple paylines, all wins are added to the total win.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 4387, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/Title/PaytableTitleLabel", "oldContent": "GAME RULES", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 4388, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/MaxWin/RuleHolder/Label1", "oldContent": "The maximum win amount is limited to {0}x bet. If the total win of a FREE SPINS ROUND reaches {1}x the round immediately ends, win is awarded and all remaining free spins are forfeited", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 4389, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/RulesBottom/RTP/TheoreticalRTPBONUS/Label", "oldContent": "The RTP of the game when using \"BUY FREE SPINS\" is {0}%", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 4390, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/Volatility/VolatilityMeter/LabelHolder/VolatilityLabel", "oldContent": "VOLATILITY", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 4391, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder6/Rule6", "oldContent": "Randomly, when there are fisherman symbols on the screen but no fish, at the end of a free spin, fish MONEY symbols can appear in random positions via the dynamite spin feature.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 4392, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder2/LabelHolder2/Label2", "oldContent": "- Randomly, a hook can pull one of the reels up to reveal another SCATTER.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 4393, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/RuleHolder3/Rule3", "oldContent": "Before the round starts, 0 to 5 modifiers that apply to the subsequent round are randomly selected:", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 4394, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "extraPayloads": [], "path": "Paytable/Pages/Page1/WildHolder/DescriptionHolder/Label2", "oldContent": "It appears on all reels during the FREE SPINS round.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 4395, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "extraPayloads": [], "path": "Paytable/Pages/Page1/WildHolder/DescriptionHolder/Label3", "oldContent": "Substitutes for all symbols except SCATTER.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 4396, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder3/Rule3", "oldContent": "Every 4th WILD symbol collected retriggers the feature, awards 10 more free spins and the multiplier for MONEY symbol collection increases to 2x for the second level, 3x for the third level and 10x for the fourth level.  ", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 4397, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder2/Label2", "oldContent": "- MORE FISHERMAN - More WILD symbols are present on the reel strips during the subsequent free spins round", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 4398, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "extraPayloads": [], "path": "IntroScreen/content/Labels_Holder_landscape/Label_Holder_bigger_1/Label_2 (1)", "oldContent": "BIG FREE SPINS MODIFIERS!", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 4399, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "extraPayloads": [], "path": "IntroScreen/content/Labels_Holder_landscape/Label_Holder_bigger_1/Label_1", "oldContent": "GO FISHIN' FOR", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 4400, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "extraPayloads": [], "path": "IntroScreen/content/IntroButtons/ButtonSkipIntro/content/TextHolder/Label_1", "oldContent": "DON'T SHOW NEXT TIME", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 4401, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/MoneySymbolHolder/DescriptionHolder/PossibleValues/Label", "oldContent": "Possible values are: 2x, 5x, 10x, 15x, 20x, 25x, 50x, 100x, 200x, 500x, 1666x, 2500x or 5000x total bet.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 4402, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/MaxWin/RuleHolder/HolderLabelJackpot/Label1New", "oldContent": "The maximum win amount is limited to {0}x bet except Jack<PERSON>. If the total win of a FREE SPINS round reaches {1}x bet the round immediately ends, win is awarded and all remaining free spins are forfeited.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}], "Spines": [], "revisionNumber": 0}, "fileID": 4403}], "fileID": 4404}, {"name": "Game/GamePivot/FSWONWindow/content/FreespinsWonWindow/LabelsAndSprites/Catches_label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 4315, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "children": [], "psr": "d"}, "fileID": 4228}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "582c51ba2c2681f4da96c2d74c9f81b9"}, "_text": "は次を捕獲", "fontSize": 50, "width": 338, "height": 110, "overflow": 0}, "fileID": 4316}], "fileID": 4405}, {"name": "Game/GamePivot/FreeSpinsPurchase/BuyButtons/Options/Option_0/OptVisual/Texts/BoughtText/LandscapeText/label1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 4315, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "children": [], "psr": "d"}, "fileID": 4229}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "582c51ba2c2681f4da96c2d74c9f81b9"}, "_text": "有効", "fontSize": 40, "width": 170, "height": 80, "overflow": 0}, "fileID": 4317}], "fileID": 4406}, {"name": "Game/GamePivot/FSWONWindow/PressAnywhere_Label/label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 4315, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "children": [], "psr": "d"}, "fileID": 4230}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "582c51ba2c2681f4da96c2d74c9f81b9"}, "_text": "任意の場所を押して続行…", "fontSize": 40, "width": 1340, "height": 65, "overflow": 0}, "fileID": 4318}], "fileID": 4407}, {"name": "Game/GamePivot/FSExtraWindow/content/PressAnywhere_Label/label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 4315, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "children": [], "psr": "d"}, "fileID": 4231}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "582c51ba2c2681f4da96c2d74c9f81b9"}, "_text": "任意の場所を押して続行…", "fontSize": 40, "width": 1340, "height": 65, "overflow": 0}, "fileID": 4319}], "fileID": 4408}, {"name": "Game/GamePivot/FreeSpinsPurchase/FSPurchaseWindow/Content/AnimatedPivot/Texts/BuyText/label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 4315, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "children": [], "psr": "d"}, "fileID": 4232}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "582c51ba2c2681f4da96c2d74c9f81b9"}, "_text": "フリースピンを購入", "fontSize": 69, "width": 826, "height": 69, "overflow": 0}, "fileID": 4320}], "fileID": 4409}, {"name": "Game/GamePivot/FsFeature/content/Feature_trail/Holder/fisherman_highlight/ScalerX10/stretcher_fs/spins", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 4315, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "children": [], "psr": "d"}, "fileID": 4233}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "582c51ba2c2681f4da96c2d74c9f81b9"}, "_text": "スピン", "fontSize": 30, "width": 64, "height": 33, "overflow": 0}, "fileID": 4321}], "fileID": 4410}, {"name": "Game/Background/PaytableOnScreen/Portrait/Message1/labelMsg1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 4315, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "children": [], "psr": "d"}, "fileID": 4234}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "582c51ba2c2681f4da96c2d74c9f81b9"}, "_text": "シンボルの支払いは左から右へとなります。BONUSはどの位置でも配当を行います。", "fontSize": 40, "width": 1542, "height": 40, "alignment": 2}, "fileID": 4322}], "fileID": 4411}, {"name": "Game/GamePivot/FsFeature/content/Feature_trail/Holder/fisherman_highlight/ScalerX3/stretcher_fs/free", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 4315, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "children": [], "psr": "d"}, "fileID": 4235}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "582c51ba2c2681f4da96c2d74c9f81b9"}, "_text": "フリー", "fontSize": 30, "width": 60, "height": 33, "overflow": 0}, "fileID": 4323}], "fileID": 4412}, {"name": "Game/GamePivot/Reels/ThePivot/BonusMessages/Fisherman/Labels/uilabel", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 4315, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "children": [], "psr": "d"}, "fileID": 4236}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "582c51ba2c2681f4da96c2d74c9f81b9"}, "_text": "より多くの漁師", "fontSize": 256, "width": 1000, "height": 450, "overflow": 0}, "fileID": 4324}], "fileID": 4413}, {"name": "Game/GamePivot/FreeSpinsPurchase/BuyButtons/Options/Option_0/OptVisual/Texts/BoughtText/LandscapeText/label0", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 4315, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "children": [], "psr": "d"}, "fileID": 4237}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "582c51ba2c2681f4da96c2d74c9f81b9"}, "_text": "機能", "fontSize": 40, "width": 170, "height": 80, "overflow": 0}, "fileID": 4325}], "fileID": 4414}, {"name": "Game/GamePivot/FreeSpinsPurchase/BuyButtons/Options/Option_0/OptVisual/Texts/BuyText/PortraitText/label0", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 4315, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "children": [], "psr": "d"}, "fileID": 4238}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "582c51ba2c2681f4da96c2d74c9f81b9"}, "_text": "フリースピンを購入", "fontSize": 26, "width": 270, "height": 70, "overflow": 0}, "fileID": 4326}], "fileID": 4415}, {"name": "Game/GamePivot/FsFeature/content/Feature_trail/Holder/fisherman_highlight/ScalerX2/stretcher_fs/free", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 4315, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "children": [], "psr": "d"}, "fileID": 4239}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "582c51ba2c2681f4da96c2d74c9f81b9"}, "_text": "フリー", "fontSize": 30, "width": 60, "height": 33, "overflow": 0}, "fileID": 4327}], "fileID": 4416}, {"name": "Game/GamePivot/FSResultWindow/content/SignPivot/FreespinsCongratsWindow/Labels/Congrats_label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 4315, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "children": [], "psr": "d"}, "fileID": 4240}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "582c51ba2c2681f4da96c2d74c9f81b9"}, "_text": "おめでとうございます!", "fontSize": 169, "width": 713, "height": 169, "overflow": 0}, "fileID": 4328}], "fileID": 4417}, {"name": "Game/GamePivot/FSExtraWindow/content/SignPivot/FreespinsExtraWindow/Labels/Congrats_label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 4315, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "children": [], "psr": "d"}, "fileID": 4241}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "582c51ba2c2681f4da96c2d74c9f81b9"}, "_text": "おめでとうございます!", "fontSize": 169, "width": 731, "height": 169, "overflow": 0}, "fileID": 4329}], "fileID": 4418}, {"name": "Game/GamePivot/FSExtraWindow/content/SignPivot/FreespinsExtraWindow/Labels/FreeSpins_label ", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 4315, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "children": [], "psr": "d"}, "fileID": 4242}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "582c51ba2c2681f4da96c2d74c9f81b9"}, "_text": "フリースピン", "fontSize": 75, "width": 1000, "height": 169, "overflow": 0}, "fileID": 4330}], "fileID": 4419}, {"name": "Game/GamePivot/FSExtraWindow/content/SignPivot/FreespinsExtraWindow/Labels/AreNow_label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 4315, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "children": [], "psr": "d"}, "fileID": 4243}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "582c51ba2c2681f4da96c2d74c9f81b9"}, "_text": "は今", "fontSize": 70, "width": 500, "height": 169, "overflow": 0}, "fileID": 4331}], "fileID": 4420}, {"name": "Game/GamePivot/FreeSpinsPurchase/BuyButtons/Options/Option_0/OptVisual/Texts/BuyText/LandscapeTest/label0", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 4315, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "children": [], "psr": "d"}, "fileID": 4244}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "582c51ba2c2681f4da96c2d74c9f81b9"}, "_text": "フリースピンを購入", "fontSize": 26, "width": 270, "height": 70, "overflow": 0}, "fileID": 4332}], "fileID": 4421}, {"name": "Game/GamePivot/Reels/ThePivot/BonusMessages/PlusFS/Labels/uilabel", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 4315, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "children": [], "psr": "d"}, "fileID": 4245}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "582c51ba2c2681f4da96c2d74c9f81b9"}, "_text": "追加 フリースピン", "fontSize": 256, "width": 1200, "height": 450, "overflow": 0}, "fileID": 4333}], "fileID": 4422}, {"name": "Game/GamePivot/FsFeature/content/Feature_trail/Holder/fisherman_highlight/ScalerX10/stretcher_fs/free", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 4315, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "children": [], "psr": "d"}, "fileID": 4246}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "582c51ba2c2681f4da96c2d74c9f81b9"}, "_text": "フリー", "fontSize": 30, "width": 60, "height": 33, "overflow": 0}, "fileID": 4334}], "fileID": 4423}, {"name": "Game/GamePivot/FSWONWindow/content/FreespinsWonWindow/LabelsAndSprites/Congratulations_label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 4315, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "children": [], "psr": "d"}, "fileID": 4247}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "582c51ba2c2681f4da96c2d74c9f81b9"}, "_text": "おめでとうございます!", "fontSize": 169, "width": 803, "height": 169, "overflow": 0}, "fileID": 4335}], "fileID": 4424}, {"name": "Game/GamePivot/Reels/ThePivot/BonusMessages/Fishes/Labels/uilabel", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 4315, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "children": [], "psr": "d"}, "fileID": 4248}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "582c51ba2c2681f4da96c2d74c9f81b9"}, "_text": "より多くの魚！", "fontSize": 256, "width": 1200, "height": 450, "overflow": 0}, "fileID": 4336}], "fileID": 4425}, {"name": "Game/GamePivot/FreeSpinsPurchase/BuyButtons/Options/Option_0/OptVisual/Texts/BoughtText/PortraitText/label0", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 4315, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "children": [], "psr": "d"}, "fileID": 4249}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "582c51ba2c2681f4da96c2d74c9f81b9"}, "_text": "機能 有効", "fontSize": 60, "width": 164, "height": 208, "overflow": 0}, "fileID": 4337}], "fileID": 4426}, {"name": "Game/GamePivot/FSExtraWindow/content/SignPivot/FreespinsExtraWindow/Labels/Extra_label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 4315, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "children": [], "psr": "d"}, "fileID": 4250}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "582c51ba2c2681f4da96c2d74c9f81b9"}, "_text": "追加", "fontSize": 75, "width": 1000, "height": 169, "overflow": 0}, "fileID": 4338}], "fileID": 4427}, {"name": "Game/GamePivot/FSResultWindow/content/SignPivot/FreespinsCongratsWindow/Labels/YouWon_label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 4315, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "children": [], "psr": "d"}, "fileID": 4251}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "582c51ba2c2681f4da96c2d74c9f81b9"}, "_text": "獲得しました", "fontSize": 110, "width": 1000, "height": 169, "overflow": 0}, "fileID": 4339}], "fileID": 4428}, {"name": "Game/GamePivot/FsFeature/content/Feature_trail/Holder/fisherman_highlight/ScalerX3/stretcher_fs/spins", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 4315, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "children": [], "psr": "d"}, "fileID": 4252}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "582c51ba2c2681f4da96c2d74c9f81b9"}, "_text": "スピン", "fontSize": 30, "width": 64, "height": 33, "overflow": 0}, "fileID": 4340}], "fileID": 4429}, {"name": "Game/Background/PaytableOnScreen/Landscape/Message1/labelMsg1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 4315, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "children": [], "psr": "d"}, "fileID": 4253}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "582c51ba2c2681f4da96c2d74c9f81b9"}, "_text": "シンボルの支払いは左から右へとなります。BONUSはどの位置でも配当を行います。", "fontSize": 30, "width": 1158, "height": 30, "alignment": 2}, "fileID": 4341}], "fileID": 4430}, {"name": "Game/GamePivot/FsFeature/content/Feature_trail/Holder/fisherman_highlight/ScalerX2/stretcher_fs/spins", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 4315, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "children": [], "psr": "d"}, "fileID": 4254}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "582c51ba2c2681f4da96c2d74c9f81b9"}, "_text": "スピン", "fontSize": 30, "width": 64, "height": 33, "overflow": 0}, "fileID": 4342}], "fileID": 4431}, {"name": "Game/GamePivot/Reels/ThePivot/BonusMessages/Level2/Labels/uilabel", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 4315, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "children": [], "psr": "d"}, "fileID": 4255}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "582c51ba2c2681f4da96c2d74c9f81b9"}, "_text": "レベル2から開始します！", "fontSize": 256, "width": 1280, "height": 450, "overflow": 0}, "fileID": 4343}], "fileID": 4432}, {"name": "Game/GamePivot/FSWONWindow/content/FreespinsWonWindow/LabelsAndSprites/FreeSpins_label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 4315, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "children": [], "psr": "d"}, "fileID": 4256}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "582c51ba2c2681f4da96c2d74c9f81b9"}, "_text": "フリースピン", "fontSize": 110, "width": 1000, "height": 150, "overflow": 0}, "fileID": 4344}], "fileID": 4433}, {"name": "Game/GamePivot/FSResultWindow/content/PressAnywhere_Label/label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 4315, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "children": [], "psr": "d"}, "fileID": 4257}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "582c51ba2c2681f4da96c2d74c9f81b9"}, "_text": "任意の場所を押して続行…", "fontSize": 40, "width": 1340, "height": 65, "overflow": 0}, "fileID": 4345}], "fileID": 4434}, {"name": "Game/GamePivot/Reels/ThePivot/BonusMessages/Hooks/Labels/uilabel", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 4315, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "children": [], "psr": "d"}, "fileID": 4258}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "582c51ba2c2681f4da96c2d74c9f81b9"}, "_text": "より多くのフックと爆発！", "fontSize": 256, "width": 1000, "height": 450, "overflow": 0}, "fileID": 4346}], "fileID": 4435}, {"name": "Paytable/Pages/Page3/CAT/RuleHolder2/Rule2", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 4315, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "children": [], "psr": "d"}, "fileID": 4259}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "582c51ba2c2681f4da96c2d74c9f81b9"}, "_text": "フリースピンラウンドを購入するときに、誘発したスピンで、3、4、\nまたは5個のSCATTERシンボルがランダムに現れる場合があります。", "fontSize": 25, "anchorY": 0, "width": 1400, "height": 75, "overflow": 0, "spacingY": 5}, "fileID": 4347}], "fileID": 4436}, {"name": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder4/Label4", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 4315, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "children": [], "psr": "d"}, "fileID": 4260}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "582c51ba2c2681f4da96c2d74c9f81b9"}, "_text": "レベル2から開始 - プログレッシブ機能のレベル2からラウンドが開始されます。", "fontSize": 25, "anchorX": 0, "width": 1400, "height": 100, "overflow": 0}, "fileID": 4348}], "fileID": 4437}, {"name": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder5/Label5", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 4315, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "children": [], "psr": "d"}, "fileID": 4261}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "582c51ba2c2681f4da96c2d74c9f81b9"}, "_text": "- +2スピン - 次のラウンドは、最初から追加2回のフリースピンで始まり、再誘発のたびにさらに2回のスピンが追加されます。", "fontSize": 25, "anchorX": 0, "width": 1347, "height": 100, "overflow": 0}, "fileID": 4349}], "fileID": 4438}, {"name": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/RuleHolder1/Rule1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 4315, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "children": [], "psr": "d"}, "fileID": 4262}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "582c51ba2c2681f4da96c2d74c9f81b9"}, "_text": "3個以上のSCATTERシンボルがそろうと、フリースピン機能を誘発します。", "fontSize": 25, "width": 1400, "height": 100, "overflow": 0}, "fileID": 4350}], "fileID": 4439}, {"name": "Paytable/Pages/Page4/RulesTop/AllValuesExpressed", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 4315, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "children": [], "psr": "d"}, "fileID": 4263}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "582c51ba2c2681f4da96c2d74c9f81b9"}, "_text": "全ての値は、実際にコインとして勝利に含まれます。", "fontSize": 25, "width": 1333, "height": 160, "overflow": 0}, "fileID": 4351}], "fileID": 4440}, {"name": "Paytable/Pages/Page4/Volatility/VolatilityDescription", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 4315, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "children": [], "psr": "d"}, "fileID": 4264}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "582c51ba2c2681f4da96c2d74c9f81b9"}, "_text": "ボラティリティ が高いゲームは、平均すると配当回数は少ない一方で、短期間で大きく勝利するチャンスが高くなります。", "fontSize": 25, "anchorY": 0, "width": 1368, "height": 93, "overflow": 0}, "fileID": 4352}], "fileID": 4441}, {"name": "Paytable/Pages/Page2/MoneySymbolHolder/DescriptionHolder/MoneySymbolRules/Label3", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 4315, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "children": [], "psr": "d"}, "fileID": 4265}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "582c51ba2c2681f4da96c2d74c9f81b9"}, "_text": "魚の配当シンボルもマネーシンボルです。\nすべてのスピンで、魚は任意のマネーの値を取ります。\nそれはフリースピン機能中に獲得できます。", "fontSize": 25, "anchorX": 0, "width": 1166, "height": 100, "overflow": 0}, "fileID": 4353}], "fileID": 4442}, {"name": "Paytable/Pages/Page4/RulesBottom/RTP/TheoreticalRTP/Label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 4315, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "children": [], "psr": "d"}, "fileID": 4266}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "582c51ba2c2681f4da96c2d74c9f81b9"}, "_text": "このゲームの理論上のRTPは  {0}%", "fontSize": 25, "anchorY": 1, "width": 1200, "height": 75, "overflow": 0}, "fileID": 4354}], "fileID": 4443}, {"name": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder8/Rule8", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 4315, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "children": [], "psr": "d"}, "fileID": 4267}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "582c51ba2c2681f4da96c2d74c9f81b9"}, "_text": "また、ランダムに、画面に漁師のシンボルがあり、魚がいない場合、\nフリースピンの終了時に、バズーカ砲のアニメーションが表示され、\n漁師のシンボルを除くすべてのシンボルが画面から別のものに変更されます。", "fontSize": 25, "width": 1400, "height": 100, "overflow": 0}, "fileID": 4355}], "fileID": 4444}, {"name": "Paytable/Pages/Page1/Rules/AllSymbolsPayLabel", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 4315, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "children": [], "psr": "d"}, "fileID": 4268}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "582c51ba2c2681f4da96c2d74c9f81b9"}, "_text": "シンボルはすべて、一番左のリールから始まる隣接するリールで、左から右に配当を行います。", "fontSize": 25, "width": 1000, "height": 60, "overflow": 0}, "fileID": 4356}], "fileID": 4445}, {"name": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder5/Rule5", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 4315, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "children": [], "psr": "d"}, "fileID": 4269}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "582c51ba2c2681f4da96c2d74c9f81b9"}, "_text": "レベル4の後、その機能はそれ以上再誘発できません。", "fontSize": 25, "width": 1400, "height": 100, "overflow": 0}, "fileID": 4357}], "fileID": 4446}, {"name": "Paytable/Pages/Page3/FreeSpinsHolder2/SpecialReelsHolder/SpecialReels", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 4315, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "children": [], "psr": "d"}, "fileID": 4270}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "582c51ba2c2681f4da96c2d74c9f81b9"}, "_text": "特別リールがこの機能中に作動します。", "fontSize": 25, "width": 1400, "height": 100, "overflow": 0}, "fileID": 4358}], "fileID": 4447}, {"name": "Paytable/Pages/Page3/CAT/RuleHolder1/Rule1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 4315, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "children": [], "psr": "d"}, "fileID": 4271}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "582c51ba2c2681f4da96c2d74c9f81b9"}, "_text": "フリースピンラウンドは、ベースゲームから現在のベッ ト合計の100xを購入することで、即座に誘発することができます。", "fontSize": 25, "anchorY": 1, "width": 1400, "height": 100, "overflow": 0}, "fileID": 4359}], "fileID": 4448}, {"name": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/RuleHolder2/Rule2", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 4315, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "children": [], "psr": "d"}, "fileID": 4272}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "582c51ba2c2681f4da96c2d74c9f81b9"}, "_text": "通常ゲームでは、2個のSCATTERシンボルが3個目なしで現れるたびに、\nランダムな機能によってもうひとつのSCATTERシンボルが画面に表示される可能性があります。", "fontSize": 25, "width": 1169, "height": 100, "overflow": 0}, "fileID": 4360}], "fileID": 4449}, {"name": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder1/Label1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 4315, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "children": [], "psr": "d"}, "fileID": 4273}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "582c51ba2c2681f4da96c2d74c9f81b9"}, "_text": "-より多くの漁 - その後のフリースピンラウンド中に、より多くの魚シンボルがリールストリップに現れます", "fontSize": 25, "anchorX": 0, "width": 1400, "height": 100, "overflow": 0}, "fileID": 4361}], "fileID": 4450}, {"name": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder2/Rule2", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 4315, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "children": [], "psr": "d"}, "fileID": 4274}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "582c51ba2c2681f4da96c2d74c9f81b9"}, "_text": "機能中に現れたすべてのWILDシンボルは、ラウンド終了するまで集められます。", "fontSize": 25, "width": 1400, "height": 100, "overflow": 0}, "fileID": 4362}], "fileID": 4451}, {"name": "Paytable/Pages/Page4/RulesTop/AllSymbolsPay", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 4315, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "children": [], "psr": "d"}, "fileID": 4275}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "582c51ba2c2681f4da96c2d74c9f81b9"}, "_text": "選択したペイラインで、全てのシンボルの支払いは左から右へとなります。", "fontSize": 25, "width": 1194, "height": 160, "overflow": 0, "spacingY": 5}, "fileID": 4363}], "fileID": 4452}, {"name": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder3/Label3", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 4315, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "children": [], "psr": "d"}, "fileID": 4276}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "582c51ba2c2681f4da96c2d74c9f81b9"}, "_text": "- より多くのダイナマイト、フック、バズーカ砲 - ラウンド中、ダイナマイト、フック、またはバズーカ砲のスピン機能をヒットする可能性が高くなります。", "fontSize": 25, "anchorX": 0, "width": 1359, "height": 100, "overflow": 0}, "fileID": 4364}], "fileID": 4453}, {"name": "Paytable/Pages/Page4/RulesBottom/SpaceAndEnter", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 4315, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "children": [], "psr": "d"}, "fileID": 4277}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "582c51ba2c2681f4da96c2d74c9f81b9"}, "_text": "キーボードのスペースおよびエンターボタンを使用して、スピンを開始したり停止したりできます。", "fontSize": 25, "width": 1400, "height": 60, "overflow": 0}, "fileID": 4365}], "fileID": 4454}, {"name": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder7/Rule7", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 4315, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "children": [], "psr": "d"}, "fileID": 4278}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "582c51ba2c2681f4da96c2d74c9f81b9"}, "_text": "ランダムに、画面に魚のシンボルがあるが漁師がいない場合、\nフリースピンの終了時にフックが表示され、\nランダムなリールを引き上げて漁師のシンボルを画面に表示します。", "fontSize": 25, "width": 1384, "height": 100, "overflow": 0}, "fileID": 4366}], "fileID": 4455}, {"name": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder1/Rule1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 4315, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "children": [], "psr": "d"}, "fileID": 4279}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "582c51ba2c2681f4da96c2d74c9f81b9"}, "_text": "フリースピン機能中、各WILDシンボルシンボルは画面上のマネーシンボルからすべての値も集めます。", "fontSize": 25, "width": 1400, "height": 100, "overflow": 0}, "fileID": 4367}], "fileID": 4456}, {"name": "Paytable/Pages/Page3/MaxWin/TitleHolder/Title", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 4315, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "children": [], "psr": "d"}, "fileID": 4280}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "582c51ba2c2681f4da96c2d74c9f81b9"}, "_text": "最大配当", "fontSize": 35, "width": 1100, "height": 100, "overflow": 0}, "fileID": 4368}], "fileID": 4457}, {"name": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder1/LabelHolder2/Label2", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 4315, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "children": [], "psr": "d"}, "fileID": 4281}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "582c51ba2c2681f4da96c2d74c9f81b9"}, "_text": "4x SCATTERシンボルは15回のフリースピンを授与します。", "fontSize": 25, "width": 1400, "height": 75, "overflow": 0}, "fileID": 4369}], "fileID": 4458}, {"name": "Paytable/Pages/Page4/MinMaxHolder/MaxBet/MaximumText", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 4315, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "children": [], "psr": "d"}, "fileID": 4282}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "582c51ba2c2681f4da96c2d74c9f81b9"}, "_text": "最大ベット：", "fontSize": 25, "anchorX": 0, "width": 150, "height": 26}, "fileID": 4370}], "fileID": 4459}, {"name": "Paytable/Pages/Page2/FreeSpinsHolder1/TitleHolder/Title", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 4315, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "children": [], "psr": "d"}, "fileID": 4283}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "582c51ba2c2681f4da96c2d74c9f81b9"}, "_text": "フリースピン", "fontSize": 35, "width": 1150, "height": 100, "overflow": 0}, "fileID": 4371}], "fileID": 4460}, {"name": "Paytable/Pages/Page2/MoneySymbolHolder/TitleHolder/Title", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 4315, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "children": [], "psr": "d"}, "fileID": 4284}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "582c51ba2c2681f4da96c2d74c9f81b9"}, "_text": "マネーシンボル", "fontSize": 35, "width": 1150, "height": 70, "overflow": 0}, "fileID": 4372}], "fileID": 4461}, {"name": "Paytable/Pages/Page1/WildHolder/DescriptionHolder/Label1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 4315, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "children": [], "psr": "d"}, "fileID": 4285}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "582c51ba2c2681f4da96c2d74c9f81b9"}, "_text": "これはWILDシンボルです。", "fontSize": 25, "anchorX": 0, "anchorY": 1, "width": 363, "height": 75, "overflow": 0}, "fileID": 4373}], "fileID": 4462}, {"name": "Paytable/Pages/Page1/Title/PaytableTitleLabel1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 4315, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "children": [], "psr": "d"}, "fileID": 4286}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "582c51ba2c2681f4da96c2d74c9f81b9"}, "_text": "ゲームルール", "fontSize": 35, "width": 1150, "height": 100, "overflow": 0}, "fileID": 4374}], "fileID": 4463}, {"name": "Paytable/Pages/Page3/CAT/TitleHolder/Title1New", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 4315, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "children": [], "psr": "d"}, "fileID": 4287}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "582c51ba2c2681f4da96c2d74c9f81b9"}, "_text": "フリースピンを購入", "fontSize": 35, "width": 1400, "height": 70, "overflow": 0}, "fileID": 4375}], "fileID": 4464}, {"name": "Paytable/Pages/Page4/MinMaxHolder/MinBet/MinimumText", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 4315, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "children": [], "psr": "d"}, "fileID": 4288}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "582c51ba2c2681f4da96c2d74c9f81b9"}, "_text": "最小ベット：", "fontSize": 25, "anchorX": 0, "width": 150, "height": 26}, "fileID": 4376}], "fileID": 4465}, {"name": "Paytable/Pages/Page4/RulesBottom/MalfunctionLabel", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 4315, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "children": [], "psr": "d"}, "fileID": 4289}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "582c51ba2c2681f4da96c2d74c9f81b9"}, "_text": "誤作動が起きると、配当とプレイはすべて無効になります。", "fontSize": 25, "width": 888, "height": 60, "overflow": 0}, "fileID": 4377}], "fileID": 4466}, {"name": "Paytable/Pages/Page1/ScatterHolder/DescriptionHolder/Label2", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 4315, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "children": [], "psr": "d"}, "fileID": 4290}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "582c51ba2c2681f4da96c2d74c9f81b9"}, "_text": "これは全リールに現れます。", "fontSize": 25, "anchorX": 0, "anchorY": 0, "width": 357, "height": 75, "overflow": 0}, "fileID": 4378}], "fileID": 4467}, {"name": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder1/LabelHolder1/Label1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 4315, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "children": [], "psr": "d"}, "fileID": 4291}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "582c51ba2c2681f4da96c2d74c9f81b9"}, "_text": "5x SCATTERシンボルは20回のフリースピンを授与します。", "fontSize": 25, "width": 1400, "height": 75, "overflow": 0}, "fileID": 4379}], "fileID": 4468}, {"name": "Paytable/Pages/Page4/RulesTop/AllWinsMultiplied", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 4315, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "children": [], "psr": "d"}, "fileID": 4292}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "582c51ba2c2681f4da96c2d74c9f81b9"}, "_text": "全ての賞金は各ラインのベットと掛け算されます。", "fontSize": 25, "width": 1194, "height": 160, "overflow": 0, "spacingY": 5}, "fileID": 4380}], "fileID": 4469}, {"name": "Paytable/Pages/Page1/ScatterHolder/DescriptionHolder/Label1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 4315, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "children": [], "psr": "d"}, "fileID": 4293}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "582c51ba2c2681f4da96c2d74c9f81b9"}, "_text": "これはSCATTERシンボルです。", "fontSize": 25, "anchorX": 0, "anchorY": 1, "width": 357, "height": 75, "overflow": 0}, "fileID": 4381}], "fileID": 4470}, {"name": "Paytable/Pages/Page4/RulesTop/OnlyTheHighestWin", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 4315, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "children": [], "psr": "d"}, "fileID": 4294}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "582c51ba2c2681f4da96c2d74c9f81b9"}, "_text": "唯一の最高の勝利はラインごとに支払われます。", "fontSize": 25, "anchorY": 0, "width": 1400, "height": 50, "overflow": 0}, "fileID": 4382}], "fileID": 4471}, {"name": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder1/LabelHolder3/Label3", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 4315, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "children": [], "psr": "d"}, "fileID": 4295}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "582c51ba2c2681f4da96c2d74c9f81b9"}, "_text": "3x SCATTERシンボルは10回のフリースピンを授与します。", "fontSize": 25, "width": 1400, "height": 75, "overflow": 0}, "fileID": 4383}], "fileID": 4472}, {"name": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder2/LabelHolder1/Label1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 4315, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "children": [], "psr": "d"}, "fileID": 4296}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "582c51ba2c2681f4da96c2d74c9f81b9"}, "_text": "- ランダムに、画面上のSCATTERがリールの領域を離れることなく1つ下の位置に移動できる場合、\nSCATTERのあるリールが1つ下に位置を移動し、\nSCATTERのないリールが再スピンする、再スピンがトリガーされます。", "fontSize": 25, "anchorX": 0, "width": 1400, "height": 125, "overflow": 0}, "fileID": 4384}], "fileID": 4473}, {"name": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder4/Rule4", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 4315, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "children": [], "psr": "d"}, "fileID": 4297}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "582c51ba2c2681f4da96c2d74c9f81b9"}, "_text": "再誘発されたスピンは、以前に発生したフリースピンが終了後にプレイされます。\nマルチプライヤーは再誘発されたスピンに適用されます。", "fontSize": 25, "width": 920, "height": 125, "overflow": 0}, "fileID": 4385}], "fileID": 4474}, {"name": "Paytable/Pages/Page4/RulesTop/WhenWinningOnMultiplePaylines", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 4315, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "children": [], "psr": "d"}, "fileID": 4298}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "582c51ba2c2681f4da96c2d74c9f81b9"}, "_text": "複数のペイラインで勝つと、配当はすべて合計配当に追加。", "fontSize": 25, "anchorY": 0, "width": 1400, "height": 50, "overflow": 0}, "fileID": 4386}], "fileID": 4475}, {"name": "Paytable/Pages/Page4/Title/PaytableTitleLabel", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 4315, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "children": [], "psr": "d"}, "fileID": 4299}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "582c51ba2c2681f4da96c2d74c9f81b9"}, "_text": "ゲームルール", "fontSize": 30, "width": 1150, "height": 100, "overflow": 0}, "fileID": 4387}], "fileID": 4476}, {"name": "Paytable/Pages/Page3/MaxWin/RuleHolder/Label1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 4315, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "children": [], "psr": "d"}, "fileID": 4300}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "582c51ba2c2681f4da96c2d74c9f81b9"}, "_text": "最大配当額は{0}xベットに制限されています。フリースピンラウンドの合計配当が{1}x\nに達すると、ラウンドはすぐに終了し、配当が与えられ、残りの全フリースピンは没収されます。", "fontSize": 25, "anchorY": 0, "width": 1400, "height": 71, "overflow": 0}, "fileID": 4388}], "fileID": 4477}, {"name": "Paytable/Pages/Page4/RulesBottom/RTP/TheoreticalRTPBONUS/Label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 4315, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "children": [], "psr": "d"}, "fileID": 4301}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "582c51ba2c2681f4da96c2d74c9f81b9"}, "_text": "「フリースピンを購入」を使用した場合のゲームのRTPは{0}％です", "fontSize": 25, "anchorY": 0, "width": 1200, "height": 75, "overflow": 0}, "fileID": 4389}], "fileID": 4478}, {"name": "Paytable/Pages/Page4/Volatility/VolatilityMeter/LabelHolder/VolatilityLabel", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 4315, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "children": [], "psr": "d"}, "fileID": 4302}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "582c51ba2c2681f4da96c2d74c9f81b9"}, "_text": "ボラティリティー", "fontSize": 22, "anchorX": 0, "width": 176, "height": 22}, "fileID": 4390}], "fileID": 4479}, {"name": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder6/Rule6", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 4315, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "children": [], "psr": "d"}, "fileID": 4303}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "582c51ba2c2681f4da96c2d74c9f81b9"}, "_text": "ランダムに、画面に漁師のシンボルが表示されているが、魚が表示されていない場合、\nフリースピンの終了時に、\nダイナマイトスピン機能を介して魚のマネーシンボルがランダムな位置に表示されます。", "fontSize": 25, "width": 1271, "height": 100, "overflow": 0}, "fileID": 4391}], "fileID": 4480}, {"name": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder2/LabelHolder2/Label2", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 4315, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "children": [], "psr": "d"}, "fileID": 4304}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "582c51ba2c2681f4da96c2d74c9f81b9"}, "_text": "- ランダムに、フックがリールの1つを引き上げて、別のSCATTERを表示することができます。", "fontSize": 25, "anchorX": 0, "width": 1400, "height": 75, "overflow": 0}, "fileID": 4392}], "fileID": 4481}, {"name": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/RuleHolder3/Rule3", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 4315, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "children": [], "psr": "d"}, "fileID": 4305}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "582c51ba2c2681f4da96c2d74c9f81b9"}, "_text": "ラウンドが開始する前に、次のラウンドに適用される0から5のモディファイヤーがランダムに選択されます。", "fontSize": 25, "width": 1400, "height": 100, "overflow": 0}, "fileID": 4393}], "fileID": 4482}, {"name": "Paytable/Pages/Page1/WildHolder/DescriptionHolder/Label2", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 4315, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "children": [], "psr": "d"}, "fileID": 4306}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "582c51ba2c2681f4da96c2d74c9f81b9"}, "_text": "フリースピンラウンド中に\nすべてのリールで現れます。", "fontSize": 25, "anchorX": 0, "width": 363, "height": 75, "overflow": 0}, "fileID": 4394}], "fileID": 4483}, {"name": "Paytable/Pages/Page1/WildHolder/DescriptionHolder/Label3", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 4315, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "children": [], "psr": "d"}, "fileID": 4307}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "582c51ba2c2681f4da96c2d74c9f81b9"}, "_text": "SCATTER以外のすべて\nのシンボルの代わりになります。", "fontSize": 25, "anchorX": 0, "anchorY": 0, "width": 363, "height": 75, "overflow": 0}, "fileID": 4395}], "fileID": 4484}, {"name": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder3/Rule3", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 4315, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "children": [], "psr": "d"}, "fileID": 4308}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "582c51ba2c2681f4da96c2d74c9f81b9"}, "_text": "4個目のWILDシンボルが集められるたびに、機能が再誘発され、\nさらに10回のフリースピンが提供され、マネーシンボル収集のマルチプライヤーはレベル2で2x、\nレベル3で3x、レベル4で10xに増加します。", "fontSize": 25, "width": 1327, "height": 150, "overflow": 0}, "fileID": 4396}], "fileID": 4485}, {"name": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder2/Label2", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 4315, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "children": [], "psr": "d"}, "fileID": 4309}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "582c51ba2c2681f4da96c2d74c9f81b9"}, "_text": "-より多くの漁師 - その後のフリースピンラウンド中に、より多くのWILDシンボルがリールストリップに現れます", "fontSize": 25, "anchorX": 0, "width": 1400, "height": 100, "overflow": 0}, "fileID": 4397}], "fileID": 4486}, {"name": "IntroScreen/content/Labels_Holder_landscape/Label_Holder_bigger_1/Label_2 (1)", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 4315, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "children": [], "psr": "d"}, "fileID": 4310}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "582c51ba2c2681f4da96c2d74c9f81b9"}, "_text": "を狙って魚釣りに行きましょう！", "fontSize": 60, "width": 1000, "height": 80, "overflow": 0}, "fileID": 4398}], "fileID": 4487}, {"name": "IntroScreen/content/Labels_Holder_landscape/Label_Holder_bigger_1/Label_1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 4315, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "children": [], "psr": "d"}, "fileID": 4311}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "582c51ba2c2681f4da96c2d74c9f81b9"}, "_text": "大きなフリースピン変形要素", "fontSize": 60, "width": 1000, "height": 80, "overflow": 0}, "fileID": 4399}], "fileID": 4488}, {"name": "IntroScreen/content/IntroButtons/ButtonSkipIntro/content/TextHolder/Label_1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 4315, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "children": [], "psr": "d"}, "fileID": 4312}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "582c51ba2c2681f4da96c2d74c9f81b9"}, "_text": "次回から表示しない", "fontSize": 30, "anchorX": 0, "width": 270, "height": 30}, "fileID": 4400}], "fileID": 4489}, {"name": "Paytable/Pages/Page2/MoneySymbolHolder/DescriptionHolder/PossibleValues/Label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 4315, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "children": [], "psr": "d"}, "fileID": 4313}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "582c51ba2c2681f4da96c2d74c9f81b9"}, "_text": "\n可能な値: 2x、5x、10x、15x、20x、25x、50x、100x、200x、500x、1666x、2500x、または 5000x の合計ベット。", "fontSize": 25, "anchorX": 0, "width": 1104, "height": 100, "overflow": 0}, "fileID": 4401}], "fileID": 4490}, {"name": "Paytable/Pages/Page3/MaxWin/RuleHolder/HolderLabelJackpot/Label1New", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 4315, "guid": "ad2121a9ed6a85d47a07ff1194ef7bda"}, "children": [], "psr": "d"}, "fileID": 4314}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "582c51ba2c2681f4da96c2d74c9f81b9"}, "_text": "最大賞金額はジャックポットを除き{0}xベットに制限されています。 フリースピンラウンドの勝利の合計が{1}xベットに達した場合、ラウンドは即座に終了し、\n賞金が授与され、残りのすべてのフリースピンは失われます。", "fontSize": 25, "anchorY": 0, "width": 1400, "height": 99, "overflow": 0}, "fileID": 4402}], "fileID": 4491}]}}, {"type": "Font", "id": "582c51ba2c2681f4da96c2d74c9f81b9", "data": {"fontName": "f582c51ba2c2681f4da96c2d74c9f81", "path": "@font-face{font-family:'f582c51ba2c2681f4da96c2d74c9f81';src:url('data:application/x-font-woff;base64,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') format('woff')}"}}]}