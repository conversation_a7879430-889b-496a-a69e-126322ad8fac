<?php
/**
 * Script para corrigir problemas de HTTPS em todos os jogos
 */

// Diretório dos jogos
$gamesDir = 'public';

// Função para encontrar todos os arquivos html5Game.php
function findHtml5GameFiles($dir) {
    $files = [];
    $iterator = new RecursiveIteratorIterator(
        new RecursiveDirectoryIterator($dir),
        RecursiveIteratorIterator::SELF_FIRST
    );
    
    foreach ($iterator as $file) {
        if ($file->isFile() && $file->getFilename() === 'html5Game.php') {
            $files[] = $file->getPathname();
        }
    }
    
    return $files;
}

// Função para corrigir HTTPS para HTTP
function fixHttpsToHttp($filePath) {
    $content = file_get_contents($filePath);
    $originalContent = $content;
    
    // Substituir HTTPS por HTTP nos caminhos dos jogos
    $content = preg_replace('/https:\/\/\' \+ location\.hostname \+ \'\/public\//', 'http://\' + location.hostname + \'/public/', $content);
    
    // Substituir HTTPS por HTTP em outros URLs
    $content = preg_replace('/https:\/\/192\.168\.10\.108:8000\//', 'http://localhost/', $content);
    
    // Verificar se houve mudanças
    if ($content !== $originalContent) {
        file_put_contents($filePath, $content);
        return true;
    }
    
    return false;
}

echo "🔧 CORRIGINDO PROBLEMAS DE HTTPS NOS JOGOS\n";
echo str_repeat("=", 50) . "\n";

try {
    // Encontrar todos os arquivos html5Game.php
    $gameFiles = findHtml5GameFiles($gamesDir);
    
    if (empty($gameFiles)) {
        echo "❌ Nenhum arquivo html5Game.php encontrado!\n";
        exit(1);
    }
    
    echo "📁 Encontrados " . count($gameFiles) . " arquivos de jogos\n\n";
    
    $fixedCount = 0;
    $errorCount = 0;
    
    foreach ($gameFiles as $file) {
        $gameCode = basename(dirname(dirname($file)));
        echo "🎮 Processando jogo: $gameCode\n";
        echo "   Arquivo: $file\n";
        
        try {
            if (fixHttpsToHttp($file)) {
                echo "   ✅ Corrigido com sucesso!\n";
                $fixedCount++;
            } else {
                echo "   ℹ️  Nenhuma correção necessária\n";
            }
        } catch (Exception $e) {
            echo "   ❌ Erro: " . $e->getMessage() . "\n";
            $errorCount++;
        }
        
        echo "\n";
    }
    
    // Relatório final
    echo str_repeat("=", 50) . "\n";
    echo "📊 RELATÓRIO FINAL\n";
    echo "🎮 Total de jogos processados: " . count($gameFiles) . "\n";
    echo "✅ Jogos corrigidos: $fixedCount\n";
    echo "❌ Erros encontrados: $errorCount\n";
    
    if ($fixedCount > 0) {
        echo "\n🎉 Correções aplicadas com sucesso!\n";
        echo "Agora todos os jogos devem carregar corretamente via HTTP.\n";
    }
    
    // Listar alguns jogos corrigidos
    if ($fixedCount > 0) {
        echo "\n🎯 JOGOS CORRIGIDOS:\n";
        $count = 0;
        foreach ($gameFiles as $file) {
            if ($count >= 10) break; // Mostrar apenas os primeiros 10
            $gameCode = basename(dirname(dirname($file)));
            echo "   • $gameCode\n";
            $count++;
        }
        
        if (count($gameFiles) > 10) {
            echo "   • ... e mais " . (count($gameFiles) - 10) . " jogos\n";
        }
    }
    
    echo "\n🔗 TESTE OS JOGOS:\n";
    echo "   Interface: http://localhost/games_demo.html\n";
    echo "   Exemplo: http://localhost/play?game=vs20sugarrush&user=demo&lang=pt&cur=R$\n";
    
} catch (Exception $e) {
    echo "❌ ERRO CRÍTICO: " . $e->getMessage() . "\n";
    exit(1);
}

echo "\n" . str_repeat("=", 50) . "\n";
echo "Correção concluída em " . date('Y-m-d H:i:s') . "\n";
?>
