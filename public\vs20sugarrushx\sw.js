// Service Worker for vs20sugarrushx
const CACHE_NAME = 'vs20sugarrushx-v1';

self.addEventListener('install', function(event) {
    console.log('Service Worker installing.');
    self.skipWaiting();
});

self.addEventListener('activate', function(event) {
    console.log('Service Worker activating.');
    event.waitUntil(self.clients.claim());
});

self.addEventListener('fetch', function(event) {
    // Simply pass through all requests without caching
    event.respondWith(fetch(event.request));
});
