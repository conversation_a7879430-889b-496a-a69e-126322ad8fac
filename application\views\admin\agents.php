<div class="row mb-3">
    <div class="col-md-6">
        <h4>Gerenciar Agentes</h4>
    </div>
    <div class="col-md-6 text-end">
        <a href="<?= base_url('admin/create_agent') ?>" class="btn btn-primary">
            <i class="fas fa-plus"></i> Criar Novo Agente
        </a>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-users"></i>
            Lista de Agentes
        </h5>
    </div>
    <div class="card-body">
        <?php if (!empty($agents)): ?>
            <div class="table-responsive">
                <table class="table table-hover data-table">
                    <thead>
                        <tr>
                            <th>Código</th>
                            <th>Nome</th>
                            <th>Email</th>
                            <th>Saldo</th>
                            <th>Tipo</th>
                            <th>RTP Geral</th>
                            <th>Status</th>
                            <th><PERSON><PERSON><PERSON></th>
                            <th>A<PERSON><PERSON>es</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($agents as $agent): ?>
                            <tr>
                                <td>
                                    <strong><?= htmlspecialchars($agent['agentCode']) ?></strong>
                                </td>
                                <td>
                                    <?= htmlspecialchars($agent['agentName'] ?? 'N/A') ?>
                                </td>
                                <td>
                                    <?= htmlspecialchars($agent['email'] ?? 'N/A') ?>
                                </td>
                                <td>
                                    <span class="badge bg-success">
                                        R$ <?= number_format($agent['balance'], 2, ',', '.') ?>
                                    </span>
                                </td>
                                <td>
                                    <?php if (($agent['agentType'] ?? 1) == 1): ?>
                                        <span class="badge bg-primary">Master</span>
                                    <?php else: ?>
                                        <span class="badge bg-secondary">Sub-Agente</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?= number_format(($agent['rtpgeral'] ?? 95), 0) ?>%
                                </td>
                                <td>
                                    <?php if ($agent['status'] == 1): ?>
                                        <span class="badge bg-success">Ativo</span>
                                    <?php else: ?>
                                        <span class="badge bg-danger">Inativo</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if ($agent['last_login']): ?>
                                        <small class="text-muted">
                                            <?= date('d/m/Y H:i', strtotime($agent['last_login'])) ?>
                                        </small>
                                    <?php else: ?>
                                        <small class="text-muted">Nunca</small>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="<?= base_url('admin/edit_agent/' . $agent['id']) ?>" 
                                           class="btn btn-outline-primary" 
                                           data-bs-toggle="tooltip" 
                                           title="Editar">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button class="btn btn-outline-info copy-btn"
                                                data-copy="<?= htmlspecialchars($agent['token'] ?? '') ?>"
                                                data-bs-toggle="tooltip"
                                                title="Copiar Token">
                                            <i class="fas fa-copy"></i>
                                        </button>
                                        <a href="<?= base_url('admin/agent_users/' . $agent['agentCode']) ?>" 
                                           class="btn btn-outline-success" 
                                           data-bs-toggle="tooltip" 
                                           title="Ver Usuários">
                                            <i class="fas fa-users"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php else: ?>
            <div class="text-center py-5">
                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">Nenhum agente encontrado</h5>
                <p class="text-muted">Clique no botão acima para criar o primeiro agente.</p>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Agent Statistics -->
<div class="row mt-4">
    <div class="col-md-4">
        <div class="card">
            <div class="card-body text-center">
                <h3 class="text-primary"><?= count($agents) ?></h3>
                <p class="text-muted mb-0">Total de Agentes</p>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card">
            <div class="card-body text-center">
                <h3 class="text-success">
                    <?= count(array_filter($agents, function($a) { return $a['status'] == 1; })) ?>
                </h3>
                <p class="text-muted mb-0">Agentes Ativos</p>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card">
            <div class="card-body text-center">
                <h3 class="text-info">
                    R$ <?= number_format(array_sum(array_column($agents, 'balance')), 2, ',', '.') ?>
                </h3>
                <p class="text-muted mb-0">Saldo Total</p>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Initialize DataTable
    $('.data-table').DataTable({
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Portuguese-Brasil.json"
        },
        "pageLength": 25,
        "responsive": true,
        "order": [[0, "asc"]]
    });
});
</script>

<!-- Include DataTables CSS and JS -->
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css">
<script type="text/javascript" charset="utf8" src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
<script type="text/javascript" charset="utf8" src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
