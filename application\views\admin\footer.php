            </main>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <script>
        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            $('.alert').fadeOut('slow');
        }, 5000);

        // Confirm delete actions
        $('.btn-delete').on('click', function(e) {
            if (!confirm('Tem certeza que deseja excluir este item?')) {
                e.preventDefault();
            }
        });

        // DataTables initialization
        if (typeof $.fn.DataTable !== 'undefined') {
            $('.data-table').DataTable({
                "language": {
                    "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Portuguese-Brasil.json"
                },
                "pageLength": 25,
                "responsive": true
            });
        }

        // Tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });

        // Format currency inputs
        $('.currency-input').on('input', function() {
            let value = this.value.replace(/[^\d.,]/g, '');
            this.value = value;
        });

        // Format percentage inputs
        $('.percentage-input').on('input', function() {
            let value = parseFloat(this.value);
            if (value > 100) this.value = 100;
            if (value < 0) this.value = 0;
        });

        // AJAX form submissions
        $('.ajax-form').on('submit', function(e) {
            e.preventDefault();
            
            let form = $(this);
            let submitBtn = form.find('button[type="submit"]');
            let originalText = submitBtn.text();
            
            submitBtn.prop('disabled', true).text('Processando...');
            
            $.ajax({
                url: form.attr('action'),
                method: form.attr('method') || 'POST',
                data: form.serialize(),
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        showAlert('success', response.message || 'Operação realizada com sucesso!');
                        if (response.redirect) {
                            setTimeout(() => window.location.href = response.redirect, 1500);
                        }
                        if (response.reload) {
                            setTimeout(() => window.location.reload(), 1500);
                        }
                    } else {
                        showAlert('danger', response.message || 'Erro ao processar solicitação.');
                    }
                },
                error: function() {
                    showAlert('danger', 'Erro de conexão. Tente novamente.');
                },
                complete: function() {
                    submitBtn.prop('disabled', false).text(originalText);
                }
            });
        });

        function showAlert(type, message) {
            let alertHtml = `
                <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                    <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'}"></i>
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            
            $('.main-content').prepend(alertHtml);
            
            setTimeout(function() {
                $('.alert').fadeOut('slow');
            }, 5000);
        }

        // Real-time search
        $('.search-input').on('keyup', function() {
            let searchTerm = $(this).val().toLowerCase();
            let targetTable = $($(this).data('target'));
            
            targetTable.find('tbody tr').each(function() {
                let rowText = $(this).text().toLowerCase();
                if (rowText.includes(searchTerm)) {
                    $(this).show();
                } else {
                    $(this).hide();
                }
            });
        });

        // Copy to clipboard
        $('.copy-btn').on('click', function() {
            let text = $(this).data('copy');
            navigator.clipboard.writeText(text).then(function() {
                showAlert('success', 'Copiado para a área de transferência!');
            });
        });

        // Toggle password visibility
        $('.toggle-password').on('click', function() {
            let input = $($(this).data('target'));
            let icon = $(this).find('i');
            
            if (input.attr('type') === 'password') {
                input.attr('type', 'text');
                icon.removeClass('fa-eye').addClass('fa-eye-slash');
            } else {
                input.attr('type', 'password');
                icon.removeClass('fa-eye-slash').addClass('fa-eye');
            }
        });

        // Auto-refresh for dashboard
        if (window.location.pathname.includes('/admin') && !window.location.pathname.includes('/admin/')) {
            setInterval(function() {
                // Refresh dashboard stats every 5 minutes
                location.reload();
            }, 300000);
        }
    </script>
</body>
</html>
