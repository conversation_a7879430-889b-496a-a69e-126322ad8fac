-- Simple Game Platform Database Setup
CREATE DATABASE IF NOT EXISTS game_platform CHARACTER SET utf8 COLLATE utf8_general_ci;
USE game_platform;

-- Agents table
CREATE TABLE IF NOT EXISTS agents (
  id int(11) NOT NULL AUTO_INCREMENT,
  agentCode varchar(50) NOT NULL,
  agentToken varchar(255) NOT NULL,
  balance decimal(15,2) DEFAULT 0.00,
  currency varchar(10) DEFAULT 'BRL',
  status tinyint(1) DEFAULT 1,
  type tinyint(1) DEFAULT 1,
  rtpgeral decimal(5,4) DEFAULT 0.9500,
  createdAt timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (id),
  UNIQUE KEY agentCode (agentCode)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- Users table
CREATE TABLE IF NOT EXISTS users (
  id int(11) NOT NULL AUTO_INCREMENT,
  agentCode varchar(50) NOT NULL,
  userCode varchar(50) NOT NULL,
  aasUserCode varchar(100) NOT NULL,
  balance decimal(15,2) DEFAULT 0.00,
  status tinyint(1) DEFAULT 1,
  apiType tinyint(1) DEFAULT 1,
  createdAt timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (id),
  UNIQUE KEY aasUserCode (aasUserCode)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- Providers table
CREATE TABLE IF NOT EXISTS providers (
  id int(11) NOT NULL AUTO_INCREMENT,
  code varchar(50) NOT NULL,
  name varchar(100) NOT NULL,
  type varchar(20) DEFAULT 'slot',
  status tinyint(1) DEFAULT 1,
  createdAt timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (id),
  UNIQUE KEY code (code)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- Games table
CREATE TABLE IF NOT EXISTS games (
  id int(11) NOT NULL AUTO_INCREMENT,
  game_code varchar(50) NOT NULL,
  game_name varchar(100) NOT NULL,
  provider varchar(50) NOT NULL,
  banner varchar(255) DEFAULT NULL,
  status tinyint(1) DEFAULT 1,
  rtp decimal(5,4) DEFAULT 0.9600,
  game_lines int(11) DEFAULT 20,
  min_bet decimal(10,2) DEFAULT 0.01,
  max_bet decimal(10,2) DEFAULT 100.00,
  createdAt timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (id),
  UNIQUE KEY game_code (game_code)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- Game sessions table
CREATE TABLE IF NOT EXISTS game_session (
  id int(11) NOT NULL AUTO_INCREMENT,
  code varchar(50) NOT NULL,
  `key` varchar(255) NOT NULL,
  user varchar(100) NOT NULL,
  createdAt timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- Game responses table
CREATE TABLE IF NOT EXISTS game_responses (
  id int(11) NOT NULL AUTO_INCREMENT,
  action varchar(50) NOT NULL,
  response text,
  game_code varchar(50) NOT NULL,
  `index` varchar(50) DEFAULT NULL,
  createdAt timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- Transaction history table
CREATE TABLE IF NOT EXISTS transaction_history (
  id int(11) NOT NULL AUTO_INCREMENT,
  user_code varchar(50) NOT NULL,
  agent_code varchar(50) NOT NULL,
  user_balance decimal(15,2) NOT NULL,
  user_after_balance decimal(15,2) NOT NULL,
  provider_code varchar(50) NOT NULL,
  currency varchar(10) NOT NULL,
  game_code varchar(50) NOT NULL,
  bet_money decimal(15,2) NOT NULL,
  win_money decimal(15,2) NOT NULL,
  txn_id varchar(50) NOT NULL,
  created_at timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- Insert default data
INSERT INTO providers (code, name, type, status) VALUES
('PRAGMATICPLAY', 'Pragmatic Play', 'slot', 1);

INSERT INTO agents (agentCode, agentToken, balance, currency, status, type, rtpgeral) VALUES
('DEMO001', 'demo_token_123456789', 10000.00, 'BRL', 1, 1, 0.9500);
