<?php
// Test login process
echo "<h2>Testing Login Process</h2>";

if ($_POST) {
    $username = $_POST['username'];
    $password = $_POST['password'];
    
    echo "<p>Testing login with:</p>";
    echo "<p>Username: " . htmlspecialchars($username) . "</p>";
    echo "<p>Password: " . htmlspecialchars($password) . "</p>";
    
    try {
        $pdo = new PDO('mysql:host=localhost;dbname=game_platform', 'root', '');
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // Simulate the Admin_model verify_login method
        $stmt = $pdo->prepare("SELECT * FROM admins WHERE username = ? AND status = 1");
        $stmt->execute([$username]);
        $admin = $stmt->fetch();
        
        if ($admin) {
            echo "<p style='color: green;'>✓ Admin user found in database</p>";
            echo "<p>Stored password hash: " . $admin['password'] . "</p>";
            
            if (password_verify($password, $admin['password'])) {
                echo "<p style='color: green;'>✓ PASSWORD VERIFICATION SUCCESS!</p>";
                echo "<p style='color: green; font-weight: bold;'>LOGIN WOULD BE SUCCESSFUL</p>";
                
                // Show what would be stored in session
                echo "<h3>Session data that would be set:</h3>";
                echo "<ul>";
                echo "<li>admin_logged_in: TRUE</li>";
                echo "<li>admin_id: " . $admin['id'] . "</li>";
                echo "<li>admin_username: " . $admin['username'] . "</li>";
                echo "<li>admin_name: " . $admin['full_name'] . "</li>";
                echo "</ul>";
                
            } else {
                echo "<p style='color: red;'>✗ Password verification failed</p>";
                echo "<p style='color: red; font-weight: bold;'>LOGIN WOULD FAIL</p>";
                
                // Try to create a new password hash for testing
                echo "<h3>Creating new password hash for '123456':</h3>";
                $new_hash = password_hash('123456', PASSWORD_DEFAULT);
                echo "<p>New hash: " . $new_hash . "</p>";
                
                // Update the admin password
                $update_stmt = $pdo->prepare("UPDATE admins SET password = ? WHERE id = ?");
                if ($update_stmt->execute([$new_hash, $admin['id']])) {
                    echo "<p style='color: green;'>✓ Password updated in database</p>";
                    echo "<p><a href='test_login_process.php'>Try login again</a></p>";
                } else {
                    echo "<p style='color: red;'>✗ Failed to update password</p>";
                }
            }
        } else {
            echo "<p style='color: red;'>✗ Admin user not found or inactive</p>";
            echo "<p style='color: red; font-weight: bold;'>LOGIN WOULD FAIL</p>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>✗ Database error: " . $e->getMessage() . "</p>";
    }
} else {
    echo "<p>No POST data received</p>";
}

echo "<hr>";
echo "<p><a href='test_login.php'>Back to test page</a></p>";
echo "<p><a href='admin/login'>Go to actual admin login</a></p>";
?>
