<?php
// Check agent login credentials
echo "<h2>🔍 Agent Login Information</h2>";

try {
    // Connect to database
    $pdo = new PDO('mysql:host=localhost;dbname=game_platform', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<p style='color: green;'>✅ Database connected successfully</p>";
    
    // Get all agents
    $stmt = $pdo->prepare("SELECT agentCode, agentName, email, agentType, status, createdAt FROM agents WHERE status = 1 ORDER BY createdAt DESC");
    $stmt->execute();
    $agents = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if ($agents) {
        echo "<h3>📋 Available Agents</h3>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 20px 0;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th style='padding: 10px;'>Agent Code</th>";
        echo "<th style='padding: 10px;'>Agent Name</th>";
        echo "<th style='padding: 10px;'>Email</th>";
        echo "<th style='padding: 10px;'>Type</th>";
        echo "<th style='padding: 10px;'>Status</th>";
        echo "<th style='padding: 10px;'>Created</th>";
        echo "</tr>";
        
        foreach ($agents as $agent) {
            echo "<tr>";
            echo "<td style='padding: 10px; font-weight: bold; color: #007bff;'>" . htmlspecialchars($agent['agentCode']) . "</td>";
            echo "<td style='padding: 10px;'>" . htmlspecialchars($agent['agentName']) . "</td>";
            echo "<td style='padding: 10px;'>" . htmlspecialchars($agent['email']) . "</td>";
            echo "<td style='padding: 10px;'>" . htmlspecialchars($agent['agentType']) . "</td>";
            echo "<td style='padding: 10px;'>" . ($agent['status'] == 1 ? '✅ Active' : '❌ Inactive') . "</td>";
            echo "<td style='padding: 10px;'>" . htmlspecialchars($agent['createdAt']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // Show login information
        echo "<h3>🔑 Agent Login Information</h3>";
        echo "<div style='background: #e7f3ff; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
        echo "<h4>How to Login as Agent:</h4>";
        echo "<ol>";
        echo "<li><strong>URL:</strong> <a href='http://localhost/agent/login' target='_blank'>http://localhost/agent/login</a></li>";
        echo "<li><strong>Username:</strong> Use the <strong>EMAIL</strong> from the table above (NOT the Agent Code)</li>";
        echo "<li><strong>Password:</strong> Try 'password' (default) or the password set when the agent was created</li>";
        echo "</ol>";
        echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<p><strong>⚠️ Important:</strong> The agent login uses EMAIL as username, not the Agent Code!</p>";
        echo "</div>";
        echo "</div>";
        
        // Show first agent details
        $firstAgent = $agents[0];
        echo "<div style='background: #d4edda; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
        echo "<h4>🎯 Quick Test - First Agent:</h4>";
        echo "<p><strong>Agent Code:</strong> " . htmlspecialchars($firstAgent['agentCode']) . "</p>";
        echo "<p><strong>Agent Name:</strong> " . htmlspecialchars($firstAgent['agentName']) . "</p>";
        echo "<p><strong>Login Email:</strong> <span style='font-size: 18px; color: #007bff; font-weight: bold;'>" . htmlspecialchars($firstAgent['email']) . "</span></p>";
        echo "<p><strong>Login Password:</strong> <span style='font-size: 18px; color: #dc3545; font-weight: bold;'>password</span> (try this first)</p>";
        echo "<p><a href='http://localhost/agent/login' style='background: #28a745; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-size: 18px;'>Go to Agent Login</a></p>";
        echo "</div>";
        
    } else {
        echo "<p style='color: red;'>❌ No active agents found in database</p>";
        
        // Check if there are any agents at all
        $stmt = $pdo->prepare("SELECT COUNT(*) as total FROM agents");
        $stmt->execute();
        $total = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
        
        if ($total == 0) {
            echo "<p style='color: orange;'>⚠️ No agents exist in database. You need to create an agent first.</p>";
            echo "<p><a href='http://localhost/admin/create_agent' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Create Agent (Admin Panel)</a></p>";
        } else {
            echo "<p style='color: orange;'>⚠️ There are $total agents in database, but none are active.</p>";
        }
    }
    
    // Check agent login table structure
    echo "<h3>📊 Agent Table Structure</h3>";
    $stmt = $pdo->prepare("DESCRIBE agents");
    $stmt->execute();
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 20px 0;'>";
    echo "<tr style='background: #f8f9fa;'>";
    echo "<th style='padding: 8px;'>Field</th>";
    echo "<th style='padding: 8px;'>Type</th>";
    echo "<th style='padding: 8px;'>Null</th>";
    echo "<th style='padding: 8px;'>Key</th>";
    echo "<th style='padding: 8px;'>Default</th>";
    echo "</tr>";
    
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td style='padding: 8px;'>" . htmlspecialchars($column['Field']) . "</td>";
        echo "<td style='padding: 8px;'>" . htmlspecialchars($column['Type']) . "</td>";
        echo "<td style='padding: 8px;'>" . htmlspecialchars($column['Null']) . "</td>";
        echo "<td style='padding: 8px;'>" . htmlspecialchars($column['Key']) . "</td>";
        echo "<td style='padding: 8px;'>" . htmlspecialchars($column['Default'] ?? 'NULL') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Database error: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<h3>🔗 Quick Links</h3>";
echo "<p>";
echo "<a href='http://localhost/agent/login' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>Agent Login</a>";
echo "<a href='http://localhost/admin/login' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>Admin Login</a>";
echo "<a href='check_agent_login.php' style='background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Refresh</a>";
echo "</p>";
?>
