<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-code"></i>
                    Documentação da API
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <div class="nav flex-column nav-pills" id="v-pills-tab" role="tablist">
                            <button class="nav-link active" id="v-pills-intro-tab" data-bs-toggle="pill" data-bs-target="#v-pills-intro" type="button">Introdução</button>
                            <button class="nav-link" id="v-pills-auth-tab" data-bs-toggle="pill" data-bs-target="#v-pills-auth" type="button">Autenticação</button>
                            <button class="nav-link" id="v-pills-users-tab" data-bs-toggle="pill" data-bs-target="#v-pills-users" type="button">Usuários</button>
                            <button class="nav-link" id="v-pills-games-tab" data-bs-toggle="pill" data-bs-target="#v-pills-games" type="button">Jogos</button>
                            <button class="nav-link" id="v-pills-transactions-tab" data-bs-toggle="pill" data-bs-target="#v-pills-transactions" type="button">Transações</button>
                            <button class="nav-link" id="v-pills-examples-tab" data-bs-toggle="pill" data-bs-target="#v-pills-examples" type="button">Exemplos</button>
                        </div>
                    </div>
                    <div class="col-md-9">
                        <div class="tab-content" id="v-pills-tabContent">
                            <!-- Introdução -->
                            <div class="tab-pane fade show active" id="v-pills-intro">
                                <h4>Introdução à API</h4>
                                <p>A API do Game Platform permite integração completa com nossa plataforma de jogos. Você pode gerenciar usuários, lançar jogos e processar transações.</p>
                                
                                <h5>URL Base</h5>
                                <div class="bg-light p-3 rounded">
                                    <code><?= base_url('api/') ?></code>
                                </div>
                                
                                <h5 class="mt-4">Formato de Resposta</h5>
                                <p>Todas as respostas são retornadas em formato JSON com a seguinte estrutura:</p>
                                <pre class="bg-dark text-light p-3 rounded"><code>{
    "status": 1,
    "msg": "Success",
    "data": {
        // dados da resposta
    }
}</code></pre>
                                
                                <h5 class="mt-4">Códigos de Status</h5>
                                <ul>
                                    <li><code>status: 1</code> - Sucesso</li>
                                    <li><code>status: 0</code> - Erro</li>
                                </ul>
                            </div>

                            <!-- Autenticação -->
                            <div class="tab-pane fade" id="v-pills-auth">
                                <h4>Autenticação</h4>
                                <p>Todas as requisições devem incluir os headers de autenticação do agente:</p>
                                
                                <h5>Headers Obrigatórios</h5>
                                <div class="table-responsive">
                                    <table class="table table-bordered">
                                        <thead>
                                            <tr>
                                                <th>Header</th>
                                                <th>Descrição</th>
                                                <th>Exemplo</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td><code>agent-code</code></td>
                                                <td>Código do agente</td>
                                                <td>DEMO001</td>
                                            </tr>
                                            <tr>
                                                <td><code>agent-token</code></td>
                                                <td>Token de autenticação</td>
                                                <td>demo_token_123456789</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                                
                                <h5>Exemplo de Requisição</h5>
                                <pre class="bg-dark text-light p-3 rounded"><code>curl -X POST <?= base_url('api/user/create') ?> \
  -H "agent-code: DEMO001" \
  -H "agent-token: demo_token_123456789" \
  -H "Content-Type: application/json" \
  -d '{
    "user_code": "user123",
    "balance": 100.00
  }'</code></pre>
                            </div>

                            <!-- Usuários -->
                            <div class="tab-pane fade" id="v-pills-users">
                                <h4>Gerenciamento de Usuários</h4>
                                
                                <h5>Criar Usuário</h5>
                                <div class="mb-3">
                                    <span class="badge bg-success">POST</span>
                                    <code><?= base_url('api/user/create') ?></code>
                                </div>
                                <p><strong>Parâmetros:</strong></p>
                                <ul>
                                    <li><code>user_code</code> (string) - Código único do usuário</li>
                                    <li><code>balance</code> (decimal) - Saldo inicial</li>
                                </ul>
                                
                                <h5 class="mt-4">Obter Saldo do Usuário</h5>
                                <div class="mb-3">
                                    <span class="badge bg-primary">GET</span>
                                    <code><?= base_url('api/user/balance') ?></code>
                                </div>
                                <p><strong>Parâmetros:</strong></p>
                                <ul>
                                    <li><code>user_code</code> (string) - Código do usuário</li>
                                </ul>
                                
                                <h5 class="mt-4">Atualizar Saldo</h5>
                                <div class="mb-3">
                                    <span class="badge bg-warning">PUT</span>
                                    <code><?= base_url('api/user/balance') ?></code>
                                </div>
                                <p><strong>Parâmetros:</strong></p>
                                <ul>
                                    <li><code>user_code</code> (string) - Código do usuário</li>
                                    <li><code>balance</code> (decimal) - Novo saldo</li>
                                    <li><code>txn_id</code> (string) - ID único da transação</li>
                                </ul>
                            </div>

                            <!-- Jogos -->
                            <div class="tab-pane fade" id="v-pills-games">
                                <h4>Gerenciamento de Jogos</h4>
                                
                                <h5>Listar Jogos</h5>
                                <div class="mb-3">
                                    <span class="badge bg-primary">GET</span>
                                    <code><?= base_url('api/games/list') ?></code>
                                </div>
                                <p>Retorna lista de todos os jogos disponíveis.</p>
                                
                                <h5 class="mt-4">Lançar Jogo</h5>
                                <div class="mb-3">
                                    <span class="badge bg-success">POST</span>
                                    <code><?= base_url('api/game/launch') ?></code>
                                </div>
                                <p><strong>Parâmetros:</strong></p>
                                <ul>
                                    <li><code>user_code</code> (string) - Código do usuário</li>
                                    <li><code>game_code</code> (string) - Código do jogo</li>
                                    <li><code>lang</code> (string, opcional) - Idioma (padrão: pt)</li>
                                    <li><code>currency</code> (string, opcional) - Moeda (padrão: R$)</li>
                                </ul>
                                
                                <h5 class="mt-4">Resposta do Lançamento</h5>
                                <pre class="bg-dark text-light p-3 rounded"><code>{
    "status": 1,
    "msg": "GAME_URL",
    "data": {
        "launch_url": "<?= base_url('play?game=vs20sugarrush&user=user123&lang=pt&cur=R$') ?>"
    }
}</code></pre>
                            </div>

                            <!-- Transações -->
                            <div class="tab-pane fade" id="v-pills-transactions">
                                <h4>Sistema de Transações</h4>
                                
                                <h5>Processar Aposta</h5>
                                <div class="mb-3">
                                    <span class="badge bg-danger">POST</span>
                                    <code><?= base_url('api/transaction/bet') ?></code>
                                </div>
                                <p><strong>Parâmetros:</strong></p>
                                <ul>
                                    <li><code>user_code</code> (string) - Código do usuário</li>
                                    <li><code>game_code</code> (string) - Código do jogo</li>
                                    <li><code>amount</code> (decimal) - Valor da aposta</li>
                                    <li><code>txn_id</code> (string) - ID único da transação</li>
                                </ul>
                                
                                <h5 class="mt-4">Processar Ganho</h5>
                                <div class="mb-3">
                                    <span class="badge bg-success">POST</span>
                                    <code><?= base_url('api/transaction/win') ?></code>
                                </div>
                                <p><strong>Parâmetros:</strong></p>
                                <ul>
                                    <li><code>user_code</code> (string) - Código do usuário</li>
                                    <li><code>game_code</code> (string) - Código do jogo</li>
                                    <li><code>amount</code> (decimal) - Valor do ganho</li>
                                    <li><code>txn_id</code> (string) - ID único da transação</li>
                                </ul>
                                
                                <h5 class="mt-4">Histórico de Transações</h5>
                                <div class="mb-3">
                                    <span class="badge bg-primary">GET</span>
                                    <code><?= base_url('api/transactions/history') ?></code>
                                </div>
                                <p><strong>Parâmetros opcionais:</strong></p>
                                <ul>
                                    <li><code>user_code</code> (string) - Filtrar por usuário</li>
                                    <li><code>game_code</code> (string) - Filtrar por jogo</li>
                                    <li><code>date_from</code> (date) - Data inicial</li>
                                    <li><code>date_to</code> (date) - Data final</li>
                                    <li><code>limit</code> (int) - Limite de resultados</li>
                                </ul>
                            </div>

                            <!-- Exemplos -->
                            <div class="tab-pane fade" id="v-pills-examples">
                                <h4>Exemplos de Integração</h4>
                                
                                <h5>PHP</h5>
                                <pre class="bg-dark text-light p-3 rounded"><code><?php
$agent_code = 'DEMO001';
$agent_token = 'demo_token_123456789';
$api_url = '<?= base_url('api/') ?>';

// Criar usuário
$data = [
    'user_code' => 'user123',
    'balance' => 100.00
];

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $api_url . 'user/create');
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'agent-code: ' . $agent_code,
    'agent-token: ' . $agent_token
]);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

$response = curl_exec($ch);
curl_close($ch);

$result = json_decode($response, true);
?></code></pre>
                                
                                <h5 class="mt-4">JavaScript</h5>
                                <pre class="bg-dark text-light p-3 rounded"><code>const apiUrl = '<?= base_url('api/') ?>';
const agentCode = 'DEMO001';
const agentToken = 'demo_token_123456789';

// Lançar jogo
async function launchGame(userCode, gameCode) {
    const response = await fetch(apiUrl + 'game/launch', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'agent-code': agentCode,
            'agent-token': agentToken
        },
        body: JSON.stringify({
            user_code: userCode,
            game_code: gameCode
        })
    });
    
    const result = await response.json();
    
    if (result.status === 1) {
        window.open(result.data.launch_url, '_blank');
    } else {
        console.error('Erro:', result.msg);
    }
}</code></pre>
                                
                                <h5 class="mt-4">Python</h5>
                                <pre class="bg-dark text-light p-3 rounded"><code>import requests
import json

api_url = '<?= base_url('api/') ?>'
agent_code = 'DEMO001'
agent_token = 'demo_token_123456789'

headers = {
    'Content-Type': 'application/json',
    'agent-code': agent_code,
    'agent-token': agent_token
}

# Obter saldo do usuário
def get_user_balance(user_code):
    params = {'user_code': user_code}
    response = requests.get(
        api_url + 'user/balance',
        headers=headers,
        params=params
    )
    return response.json()

# Exemplo de uso
balance = get_user_balance('user123')
print(balance)</code></pre>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-download"></i>
                    Downloads e Recursos
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="text-center">
                            <i class="fas fa-file-code fa-3x text-primary mb-3"></i>
                            <h6>Postman Collection</h6>
                            <p class="text-muted">Coleção completa para testes da API</p>
                            <button class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-download"></i> Download
                            </button>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center">
                            <i class="fas fa-book fa-3x text-success mb-3"></i>
                            <h6>SDK PHP</h6>
                            <p class="text-muted">Biblioteca PHP para integração rápida</p>
                            <button class="btn btn-outline-success btn-sm">
                                <i class="fas fa-download"></i> Download
                            </button>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center">
                            <i class="fas fa-code fa-3x text-warning mb-3"></i>
                            <h6>Exemplos</h6>
                            <p class="text-muted">Códigos de exemplo em várias linguagens</p>
                            <button class="btn btn-outline-warning btn-sm">
                                <i class="fas fa-download"></i> Download
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
