{"resources": [{"type": "GameObject", "id": "9bcff78311cb145448934361cabd3668", "data": {"root": [{"name": "ko_desktop", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 0}, "children": [{"fileID": 115348, "guid": "9bcff78311cb145448934361cabd3668"}, {"fileID": 115349, "guid": "9bcff78311cb145448934361cabd3668"}, {"fileID": 115350, "guid": "9bcff78311cb145448934361cabd3668"}, {"fileID": 115351, "guid": "9bcff78311cb145448934361cabd3668"}, {"fileID": 115352, "guid": "9bcff78311cb145448934361cabd3668"}, {"fileID": 115353, "guid": "9bcff78311cb145448934361cabd3668"}, {"fileID": 115354, "guid": "9bcff78311cb145448934361cabd3668"}, {"fileID": 115355, "guid": "9bcff78311cb145448934361cabd3668"}, {"fileID": 115356, "guid": "9bcff78311cb145448934361cabd3668"}, {"fileID": 115357, "guid": "9bcff78311cb145448934361cabd3668"}, {"fileID": 115358, "guid": "9bcff78311cb145448934361cabd3668"}, {"fileID": 115359, "guid": "9bcff78311cb145448934361cabd3668"}, {"fileID": 115360, "guid": "9bcff78311cb145448934361cabd3668"}, {"fileID": 115361, "guid": "9bcff78311cb145448934361cabd3668"}, {"fileID": 115362, "guid": "9bcff78311cb145448934361cabd3668"}, {"fileID": 115363, "guid": "9bcff78311cb145448934361cabd3668"}, {"fileID": 115364, "guid": "9bcff78311cb145448934361cabd3668"}, {"fileID": 115365, "guid": "9bcff78311cb145448934361cabd3668"}, {"fileID": 115366, "guid": "9bcff78311cb145448934361cabd3668"}, {"fileID": 115367, "guid": "9bcff78311cb145448934361cabd3668"}, {"fileID": 115368, "guid": "9bcff78311cb145448934361cabd3668"}, {"fileID": 115369, "guid": "9bcff78311cb145448934361cabd3668"}, {"fileID": 115370, "guid": "9bcff78311cb145448934361cabd3668"}, {"fileID": 115371, "guid": "9bcff78311cb145448934361cabd3668"}, {"fileID": 115372, "guid": "9bcff78311cb145448934361cabd3668"}, {"fileID": 115373, "guid": "9bcff78311cb145448934361cabd3668"}, {"fileID": 115374, "guid": "9bcff78311cb145448934361cabd3668"}, {"fileID": 115375, "guid": "9bcff78311cb145448934361cabd3668"}, {"fileID": 115376, "guid": "9bcff78311cb145448934361cabd3668"}, {"fileID": 115377, "guid": "9bcff78311cb145448934361cabd3668"}, {"fileID": 115378, "guid": "9bcff78311cb145448934361cabd3668"}, {"fileID": 115379, "guid": "9bcff78311cb145448934361cabd3668"}, {"fileID": 115380, "guid": "9bcff78311cb145448934361cabd3668"}, {"fileID": 115381, "guid": "9bcff78311cb145448934361cabd3668"}, {"fileID": 115382, "guid": "9bcff78311cb145448934361cabd3668"}, {"fileID": 115383, "guid": "9bcff78311cb145448934361cabd3668"}, {"fileID": 115384, "guid": "9bcff78311cb145448934361cabd3668"}, {"fileID": 115385, "guid": "9bcff78311cb145448934361cabd3668"}, {"fileID": 115386, "guid": "9bcff78311cb145448934361cabd3668"}, {"fileID": 115387, "guid": "9bcff78311cb145448934361cabd3668"}, {"fileID": 115388, "guid": "9bcff78311cb145448934361cabd3668"}, {"fileID": 115389, "guid": "9bcff78311cb145448934361cabd3668"}, {"fileID": 115390, "guid": "9bcff78311cb145448934361cabd3668"}, {"fileID": 115391, "guid": "9bcff78311cb145448934361cabd3668"}, {"fileID": 115392, "guid": "9bcff78311cb145448934361cabd3668"}, {"fileID": 115393, "guid": "9bcff78311cb145448934361cabd3668"}, {"fileID": 115394, "guid": "9bcff78311cb145448934361cabd3668"}, {"fileID": 115395, "guid": "9bcff78311cb145448934361cabd3668"}, {"fileID": 115396, "guid": "9bcff78311cb145448934361cabd3668"}, {"fileID": 115397, "guid": "9bcff78311cb145448934361cabd3668"}, {"fileID": 115398, "guid": "9bcff78311cb145448934361cabd3668"}, {"fileID": 115399, "guid": "9bcff78311cb145448934361cabd3668"}, {"fileID": 115400, "guid": "9bcff78311cb145448934361cabd3668"}, {"fileID": 115401, "guid": "9bcff78311cb145448934361cabd3668"}, {"fileID": 115402, "guid": "9bcff78311cb145448934361cabd3668"}, {"fileID": 115403, "guid": "9bcff78311cb145448934361cabd3668"}, {"fileID": 115404, "guid": "9bcff78311cb145448934361cabd3668"}, {"fileID": 115405, "guid": "9bcff78311cb145448934361cabd3668"}, {"fileID": 115406, "guid": "9bcff78311cb145448934361cabd3668"}, {"fileID": 115407, "guid": "9bcff78311cb145448934361cabd3668"}, {"fileID": 115408, "guid": "9bcff78311cb145448934361cabd3668"}, {"fileID": 115409, "guid": "9bcff78311cb145448934361cabd3668"}, {"fileID": 115410, "guid": "9bcff78311cb145448934361cabd3668"}, {"fileID": 115411, "guid": "9bcff78311cb145448934361cabd3668"}, {"fileID": 115412, "guid": "9bcff78311cb145448934361cabd3668"}, {"fileID": 115413, "guid": "9bcff78311cb145448934361cabd3668"}, {"fileID": 115414, "guid": "9bcff78311cb145448934361cabd3668"}, {"fileID": 115415, "guid": "9bcff78311cb145448934361cabd3668"}, {"fileID": 115416, "guid": "9bcff78311cb145448934361cabd3668"}, {"fileID": 115417, "guid": "9bcff78311cb145448934361cabd3668"}, {"fileID": 115418, "guid": "9bcff78311cb145448934361cabd3668"}, {"fileID": 115419, "guid": "9bcff78311cb145448934361cabd3668"}, {"fileID": 115420, "guid": "9bcff78311cb145448934361cabd3668"}, {"fileID": 115421, "guid": "9bcff78311cb145448934361cabd3668"}, {"fileID": 115422, "guid": "9bcff78311cb145448934361cabd3668"}, {"fileID": 115423, "guid": "9bcff78311cb145448934361cabd3668"}, {"fileID": 115424, "guid": "9bcff78311cb145448934361cabd3668"}, {"fileID": 115425, "guid": "9bcff78311cb145448934361cabd3668"}, {"fileID": 115426, "guid": "9bcff78311cb145448934361cabd3668"}, {"fileID": 115427, "guid": "9bcff78311cb145448934361cabd3668"}, {"fileID": 115428, "guid": "9bcff78311cb145448934361cabd3668"}, {"fileID": 115429, "guid": "9bcff78311cb145448934361cabd3668"}, {"fileID": 115430, "guid": "9bcff78311cb145448934361cabd3668"}, {"fileID": 115431, "guid": "9bcff78311cb145448934361cabd3668"}, {"fileID": 115432, "guid": "9bcff78311cb145448934361cabd3668"}, {"fileID": 115433, "guid": "9bcff78311cb145448934361cabd3668"}, {"fileID": 115434, "guid": "9bcff78311cb145448934361cabd3668"}], "s": "0"}, "fileID": 115435}, {"componentType": "ModificationsManager", "enabled": true, "serializableData": {"root": {"fileID": 0}, "EditMode": false, "Atlases": [], "Transforms": [{"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page1/Title/PaytableTitleLabel1", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page1/Rules/AllSymbolsPayLabel", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page1/ScatterHolder/SymbolScatter/Sprite", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0.52, "y": 0.52, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page1/ScatterHolder/DescriptionHolder/Label1", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -24, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page1/ScatterHolder/DescriptionHolder/Label2", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -35, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page1/WildHolder/SymbolScatter/Sprite", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0.25, "y": 0.25, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page1/WildHolder/DescriptionHolder/Label1", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -12.5, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page1/WildHolder/DescriptionHolder/Label2", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -50, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page1/WildHolder/DescriptionHolder/Label3", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -87.5, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/MoneySymbolHolder/TitleHolder/Title", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/MoneySymbolHolder/DescriptionHolder/MoneySymbolHolder/Sprite", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0.7, "y": 0.7, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/MoneySymbolHolder/DescriptionHolder/MoneySymbolRules/Label3", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FreeSpinsHolder1/TitleHolder/Title", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/RuleHolder1/Rule1", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder1/LabelHolder1/Label1", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder1/LabelHolder2/Label2", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder1/LabelHolder3/Label3", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/RuleHolder2/Rule2", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder2/LabelHolder1/Label1", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder2/LabelHolder2/Label2", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/RuleHolder3/Rule3", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder1/Label1", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder2/Label2", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder3/Label3", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder4/Label4", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder5/Label5", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder1/Rule1", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder2/Rule2", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 9.5, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder3/Rule3", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 13, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder4/Rule4", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder5/Rule5", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder6/Rule6", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder7/Rule7", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder8/Rule8", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/FreeSpinsHolder2/SpecialReelsHolder/SpecialReels", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/MaxWin/TitleHolder/Title", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/MaxWin/RuleHolder/Label1", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/CAT/TitleHolder/Title1New", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/CAT/RuleHolder1/Rule1", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/CAT/RuleHolder2/Rule2", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/CAT/RuleHolder3/Rule3", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page4/Title/PaytableTitleLabel", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page4/Volatility/VolatilityDescription", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -30, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page4/RulesTop/AllSymbolsPay", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 50, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page4/RulesTop/AllWinsMultiplied", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 21, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page4/RulesTop/AllValuesExpressed", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -45, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page4/RulesTop/OnlyTheHighestWin", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -65, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page4/RulesTop/WhenWinningOnMultiplePaylines", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -95, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page4/Lines/Sprite", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page4/RulesBottom/SpaceAndEnter", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 92, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page4/RulesBottom/RTP", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page4/RulesBottom/MalfunctionLabel", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -97, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page4/MinMaxHolder", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -185, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/MaxWin/RuleHolder/HolderLabelJackpot/Label1New", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}], "Labels": [{"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 115436, "guid": "9bcff78311cb145448934361cabd3668"}, "extraPayloads": [], "path": "Game/GamePivot/FSWONWindow/content/FreespinsWonWindow/LabelsAndSprites/Catches_label", "oldContent": "CATCHES", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 115437, "guid": "9bcff78311cb145448934361cabd3668"}, "extraPayloads": [], "path": "Game/GamePivot/FreeSpinsPurchase/BuyButtons/Options/Option_0/OptVisual/Texts/BoughtText/LandscapeText/label1", "oldContent": "ACTIVE", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 115438, "guid": "9bcff78311cb145448934361cabd3668"}, "extraPayloads": [], "path": "Game/GamePivot/FSWONWindow/PressAnywhere_Label/label", "oldContent": "Press anywhere to continue", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 115439, "guid": "9bcff78311cb145448934361cabd3668"}, "extraPayloads": [], "path": "Game/GamePivot/FSExtraWindow/content/PressAnywhere_Label/label", "oldContent": "Press anywhere to continue...", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 115440, "guid": "9bcff78311cb145448934361cabd3668"}, "extraPayloads": [], "path": "Game/GamePivot/FreeSpinsPurchase/FSPurchaseWindow/Content/AnimatedPivot/Texts/BuyText/label", "oldContent": "BUY FREE SPINS", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 115441, "guid": "9bcff78311cb145448934361cabd3668"}, "extraPayloads": [], "path": "Game/GamePivot/FsFeature/content/Feature_trail/Holder/fisherman_highlight/ScalerX10/stretcher_fs/spins", "oldContent": "spins", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 115442, "guid": "9bcff78311cb145448934361cabd3668"}, "extraPayloads": [], "path": "Game/Background/PaytableOnScreen/Portrait/Message1/labelMsg1", "oldContent": "AL<PERSON> SYMBOLS PAY FROM LEFT TO RIGHT. BONUS PAYS ON ANY POSITION.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 115443, "guid": "9bcff78311cb145448934361cabd3668"}, "extraPayloads": [], "path": "Game/GamePivot/FsFeature/content/Feature_trail/Holder/fisherman_highlight/ScalerX3/stretcher_fs/free", "oldContent": "free", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 115444, "guid": "9bcff78311cb145448934361cabd3668"}, "extraPayloads": [], "path": "Game/GamePivot/Reels/ThePivot/BonusMessages/Fisherman/Labels/uilabel", "oldContent": "MORE FISHERMEN!", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 115445, "guid": "9bcff78311cb145448934361cabd3668"}, "extraPayloads": [], "path": "Game/GamePivot/FreeSpinsPurchase/BuyButtons/Options/Option_0/OptVisual/Texts/BoughtText/LandscapeText/label0", "oldContent": "FEATURE", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 115446, "guid": "9bcff78311cb145448934361cabd3668"}, "extraPayloads": [], "path": "Game/GamePivot/FreeSpinsPurchase/BuyButtons/Options/Option_0/OptVisual/Texts/BuyText/PortraitText/label0", "oldContent": "BUY FREE SPINS", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 115447, "guid": "9bcff78311cb145448934361cabd3668"}, "extraPayloads": [], "path": "Game/GamePivot/FsFeature/content/Feature_trail/Holder/fisherman_highlight/ScalerX2/stretcher_fs/free", "oldContent": "free", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 115448, "guid": "9bcff78311cb145448934361cabd3668"}, "extraPayloads": [], "path": "Game/GamePivot/FSResultWindow/content/SignPivot/FreespinsCongratsWindow/Labels/Congrats_label", "oldContent": "CONGRATULATIONS!", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 115449, "guid": "9bcff78311cb145448934361cabd3668"}, "extraPayloads": [], "path": "Game/GamePivot/FSExtraWindow/content/SignPivot/FreespinsExtraWindow/Labels/Congrats_label", "oldContent": "CONGRATULATIONS!", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 115450, "guid": "9bcff78311cb145448934361cabd3668"}, "extraPayloads": [], "path": "Game/GamePivot/FSExtraWindow/content/SignPivot/FreespinsExtraWindow/Labels/FreeSpins_label ", "oldContent": "FREESPINS", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 115451, "guid": "9bcff78311cb145448934361cabd3668"}, "extraPayloads": [], "path": "Game/GamePivot/FSExtraWindow/content/SignPivot/FreespinsExtraWindow/Labels/AreNow_label", "oldContent": "ARE NOW", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 115452, "guid": "9bcff78311cb145448934361cabd3668"}, "extraPayloads": [], "path": "Game/GamePivot/FreeSpinsPurchase/BuyButtons/Options/Option_0/OptVisual/Texts/BuyText/LandscapeTest/label0", "oldContent": "BUY FREE SPINS", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 115453, "guid": "9bcff78311cb145448934361cabd3668"}, "extraPayloads": [], "path": "Game/GamePivot/Reels/ThePivot/BonusMessages/PlusFS/Labels/uilabel", "oldContent": "EXTRA FREE SPINS!", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 115454, "guid": "9bcff78311cb145448934361cabd3668"}, "extraPayloads": [], "path": "Game/GamePivot/FsFeature/content/Feature_trail/Holder/fisherman_highlight/ScalerX10/stretcher_fs/free", "oldContent": "free", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 115455, "guid": "9bcff78311cb145448934361cabd3668"}, "extraPayloads": [], "path": "Game/GamePivot/FSWONWindow/content/FreespinsWonWindow/LabelsAndSprites/Congratulations_label", "oldContent": "CONGRATULATIONS!", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 115456, "guid": "9bcff78311cb145448934361cabd3668"}, "extraPayloads": [], "path": "Game/GamePivot/Reels/ThePivot/BonusMessages/Fishes/Labels/uilabel", "oldContent": "MORE FISH!", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 115457, "guid": "9bcff78311cb145448934361cabd3668"}, "extraPayloads": [], "path": "Game/GamePivot/FreeSpinsPurchase/BuyButtons/Options/Option_0/OptVisual/Texts/BoughtText/PortraitText/label0", "oldContent": "FEATURE ACTIVE", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 115458, "guid": "9bcff78311cb145448934361cabd3668"}, "extraPayloads": [], "path": "Game/GamePivot/FSExtraWindow/content/SignPivot/FreespinsExtraWindow/Labels/Extra_label", "oldContent": "THE NEXT", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 115459, "guid": "9bcff78311cb145448934361cabd3668"}, "extraPayloads": [], "path": "Game/GamePivot/FSResultWindow/content/SignPivot/FreespinsCongratsWindow/Labels/YouWon_label", "oldContent": "YOU HAVE WON", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 115460, "guid": "9bcff78311cb145448934361cabd3668"}, "extraPayloads": [], "path": "Game/GamePivot/FsFeature/content/Feature_trail/Holder/fisherman_highlight/ScalerX3/stretcher_fs/spins", "oldContent": "spins", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 115461, "guid": "9bcff78311cb145448934361cabd3668"}, "extraPayloads": [], "path": "Game/Background/PaytableOnScreen/Landscape/Message1/labelMsg1", "oldContent": "AL<PERSON> SYMBOLS PAY FROM LEFT TO RIGHT. BONUS PAYS ON ANY POSITION.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 115462, "guid": "9bcff78311cb145448934361cabd3668"}, "extraPayloads": [], "path": "Game/GamePivot/FsFeature/content/Feature_trail/Holder/fisherman_highlight/ScalerX2/stretcher_fs/spins", "oldContent": "spins", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 115463, "guid": "9bcff78311cb145448934361cabd3668"}, "extraPayloads": [], "path": "Game/GamePivot/Reels/ThePivot/BonusMessages/Level2/Labels/uilabel", "oldContent": "START FROM LEVEL 2!", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 115464, "guid": "9bcff78311cb145448934361cabd3668"}, "extraPayloads": [], "path": "Game/GamePivot/FSWONWindow/content/FreespinsWonWindow/LabelsAndSprites/FreeSpins_label", "oldContent": "FREE SPINS!", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 115465, "guid": "9bcff78311cb145448934361cabd3668"}, "extraPayloads": [], "path": "Game/GamePivot/FSResultWindow/content/PressAnywhere_Label/label", "oldContent": "Press anywhere to continue...", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 115466, "guid": "9bcff78311cb145448934361cabd3668"}, "extraPayloads": [], "path": "Game/GamePivot/Reels/ThePivot/BonusMessages/Hooks/Labels/uilabel", "oldContent": "MORE HOOKS AND EXPLOSIONS!", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 115467, "guid": "9bcff78311cb145448934361cabd3668"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/CAT/RuleHolder2/Rule2", "oldContent": "When buying the FREE SPINS round, on the triggering spin 3, 4 or 5 SCATTERS can hit randomly.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 115468, "guid": "9bcff78311cb145448934361cabd3668"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder4/Label4", "oldContent": "- START FROM LEVEL 2 - The round starts from level 2 in the progressive feature.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 115469, "guid": "9bcff78311cb145448934361cabd3668"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder5/Label5", "oldContent": "- +2 SPINS - The subsequent round starts with 2 more free spins from the beginning and 2 more spins are added to every retrigger.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 115470, "guid": "9bcff78311cb145448934361cabd3668"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/RuleHolder1/Rule1", "oldContent": "Hit 3 or more SCATTER symbols to trigger the FREE SPINS feature.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 115471, "guid": "9bcff78311cb145448934361cabd3668"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/RulesTop/AllValuesExpressed", "oldContent": "All values are expressed as actual wins in coins.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 115472, "guid": "9bcff78311cb145448934361cabd3668"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/Volatility/VolatilityDescription", "oldContent": "High volatility games pay out less often on average but the chance to hit big wins in a short time span is higher", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 115473, "guid": "9bcff78311cb145448934361cabd3668"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/MoneySymbolHolder/DescriptionHolder/MoneySymbolRules/Label3", "oldContent": "The fish paying symbols are also MONEY symbols. At every spin, the fish take a random money value which can be won during the FREE SPINS feature.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 115474, "guid": "9bcff78311cb145448934361cabd3668"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/RulesBottom/RTP/TheoreticalRTP/Label", "oldContent": "The theoretical RTP of this game is {0}%", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 115475, "guid": "9bcff78311cb145448934361cabd3668"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder8/Rule8", "oldContent": "Also randomly, when there are fisherman symbols on the screen but no fish, at the end of a free spin, a bazooka animation can appear and change all the symbols from the screen, except for fisherman symbols to something else.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 115476, "guid": "9bcff78311cb145448934361cabd3668"}, "extraPayloads": [], "path": "Paytable/Pages/Page1/Rules/AllSymbolsPayLabel", "oldContent": "All symbols pay from left to right on adjacent reels starting from the leftmost reel.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 115477, "guid": "9bcff78311cb145448934361cabd3668"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder5/Rule5", "oldContent": "After the fourth level, the feature cannot be retriggered anymore.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 115478, "guid": "9bcff78311cb145448934361cabd3668"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/FreeSpinsHolder2/SpecialReelsHolder/SpecialReels", "oldContent": "Special reels are in play during the feature.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 115479, "guid": "9bcff78311cb145448934361cabd3668"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/CAT/RuleHolder1/Rule1", "oldContent": "The FREE SPINS round can be instantly triggered from the base game by buying it for 100x current total bet.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 115480, "guid": "9bcff78311cb145448934361cabd3668"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/RuleHolder2/Rule2", "oldContent": "In the base game whenever 2 SCATTER symbols hit without a third, there is a chance for another one to be brought onto the screen by a random feature:", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 115481, "guid": "9bcff78311cb145448934361cabd3668"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder1/Label1", "oldContent": "- MORE FISH - More fish symbols are present on the reel strips during the subsequent free spins round", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 115482, "guid": "9bcff78311cb145448934361cabd3668"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder2/Rule2", "oldContent": "All the WILD symbols that hit during the feature are collected until the end of the round.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 115483, "guid": "9bcff78311cb145448934361cabd3668"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/RulesTop/AllSymbolsPay", "oldContent": "All symbols pay from left to right on selected paylines.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 115484, "guid": "9bcff78311cb145448934361cabd3668"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder3/Label3", "oldContent": "- MORE DYNAMITES, HOOKS AND BAZOOKAS - During the round, the chance to hit dynamite, hook or bazooka spin feature is increased.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 115485, "guid": "9bcff78311cb145448934361cabd3668"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/RulesBottom/SpaceAndEnter", "oldContent": "SPACE and ENTER buttons on the keyboard can be used to start and stop the spin.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 115486, "guid": "9bcff78311cb145448934361cabd3668"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder7/Rule7", "oldContent": "Randomly, when there are fish symbols on the screen but no fisherman, at the end of a free spin, a hook will appear pulling a random reel up to bring fisherman symbols onto the screen.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 115487, "guid": "9bcff78311cb145448934361cabd3668"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder1/Rule1", "oldContent": "During the FREE SPINS feature each WILD symbol also collects all the values from MONEY symbols on the screen.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 115488, "guid": "9bcff78311cb145448934361cabd3668"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/MaxWin/TitleHolder/Title", "oldContent": "MAX WIN", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 115489, "guid": "9bcff78311cb145448934361cabd3668"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder1/LabelHolder2/Label2", "oldContent": "4x SCATTER awards 15 free spins", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 115490, "guid": "9bcff78311cb145448934361cabd3668"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/MinMaxHolder/MaxBet/MaximumText", "oldContent": "MAXIMUM BET:", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 115491, "guid": "9bcff78311cb145448934361cabd3668"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FreeSpinsHolder1/TitleHolder/Title", "oldContent": "FREE SPINS", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 115492, "guid": "9bcff78311cb145448934361cabd3668"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/MoneySymbolHolder/TitleHolder/Title", "oldContent": "MONEY SYMBOL", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 115493, "guid": "9bcff78311cb145448934361cabd3668"}, "extraPayloads": [], "path": "Paytable/Pages/Page1/WildHolder/DescriptionHolder/Label1", "oldContent": "This is the WILD symbol.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 115494, "guid": "9bcff78311cb145448934361cabd3668"}, "extraPayloads": [], "path": "Paytable/Pages/Page1/Title/PaytableTitleLabel1", "oldContent": "GAME RULES", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 115495, "guid": "9bcff78311cb145448934361cabd3668"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/CAT/TitleHolder/Title1New", "oldContent": "BUY FREE SPINS", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 115496, "guid": "9bcff78311cb145448934361cabd3668"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/MinMaxHolder/MinBet/MinimumText", "oldContent": "MINIMUM BET:", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 115497, "guid": "9bcff78311cb145448934361cabd3668"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/RulesBottom/MalfunctionLabel", "oldContent": "Malfunction voids all pays and plays.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 115498, "guid": "9bcff78311cb145448934361cabd3668"}, "extraPayloads": [], "path": "Paytable/Pages/Page1/ScatterHolder/DescriptionHolder/Label2", "oldContent": "It appears on all reels.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 115499, "guid": "9bcff78311cb145448934361cabd3668"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder1/LabelHolder1/Label1", "oldContent": "5x SCATTER awards 20 free spins", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 115500, "guid": "9bcff78311cb145448934361cabd3668"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/RulesTop/AllWinsMultiplied", "oldContent": "All wins are multiplied by bet per line.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 115501, "guid": "9bcff78311cb145448934361cabd3668"}, "extraPayloads": [], "path": "Paytable/Pages/Page1/ScatterHolder/DescriptionHolder/Label1", "oldContent": "This is the SCATTER symbol.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 115502, "guid": "9bcff78311cb145448934361cabd3668"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/RulesTop/OnlyTheHighestWin", "oldContent": "Only the highest win is paid per line.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 115503, "guid": "9bcff78311cb145448934361cabd3668"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder1/LabelHolder3/Label3", "oldContent": "3x SCATTER awards 10 free spins", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 115504, "guid": "9bcff78311cb145448934361cabd3668"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder2/LabelHolder1/Label1", "oldContent": "- Randomly, if the SCATTERS on the screen can move down one position without leaving the reel area, a respin is triggered where the reels with SCATTERS move one position down and the reels without SCATTERS respin.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 115505, "guid": "9bcff78311cb145448934361cabd3668"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder4/Rule4", "oldContent": "The retriggered spins are played after the previous batch of free spins ends. The multiplier applies to the retriggered spins.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 115506, "guid": "9bcff78311cb145448934361cabd3668"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/RulesTop/WhenWinningOnMultiplePaylines", "oldContent": "When winning on multiple paylines, all wins are added to the total win.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 115507, "guid": "9bcff78311cb145448934361cabd3668"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/Title/PaytableTitleLabel", "oldContent": "GAME RULES", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 115508, "guid": "9bcff78311cb145448934361cabd3668"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/MaxWin/RuleHolder/Label1", "oldContent": "The maximum win amount is limited to {0}x bet. If the total win of a FREE SPINS ROUND reaches {1}x the round immediately ends, win is awarded and all remaining free spins are forfeited", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 115509, "guid": "9bcff78311cb145448934361cabd3668"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/RulesBottom/RTP/TheoreticalRTPBONUS/Label", "oldContent": "The RTP of the game when using \"BUY FREE SPINS\" is {0}%", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 115510, "guid": "9bcff78311cb145448934361cabd3668"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/Volatility/VolatilityMeter/LabelHolder/VolatilityLabel", "oldContent": "VOLATILITY", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 115511, "guid": "9bcff78311cb145448934361cabd3668"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder6/Rule6", "oldContent": "Randomly, when there are fisherman symbols on the screen but no fish, at the end of a free spin, fish MONEY symbols can appear in random positions via the dynamite spin feature.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 115512, "guid": "9bcff78311cb145448934361cabd3668"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder2/LabelHolder2/Label2", "oldContent": "- Randomly, a hook can pull one of the reels up to reveal another SCATTER.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 115513, "guid": "9bcff78311cb145448934361cabd3668"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/RuleHolder3/Rule3", "oldContent": "Before the round starts, 0 to 5 modifiers that apply to the subsequent round are randomly selected:", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 115514, "guid": "9bcff78311cb145448934361cabd3668"}, "extraPayloads": [], "path": "Paytable/Pages/Page1/WildHolder/DescriptionHolder/Label2", "oldContent": "It appears on all reels during the FREE SPINS round.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 115515, "guid": "9bcff78311cb145448934361cabd3668"}, "extraPayloads": [], "path": "Paytable/Pages/Page1/WildHolder/DescriptionHolder/Label3", "oldContent": "Substitutes for all symbols except SCATTER.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 115516, "guid": "9bcff78311cb145448934361cabd3668"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder3/Rule3", "oldContent": "Every 4th WILD symbol collected retriggers the feature, awards 10 more free spins and the multiplier for MONEY symbol collection increases to 2x for the second level, 3x for the third level and 10x for the fourth level.  ", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 115517, "guid": "9bcff78311cb145448934361cabd3668"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder2/Label2", "oldContent": "- MORE FISHERMAN - More WILD symbols are present on the reel strips during the subsequent free spins round", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 115518, "guid": "9bcff78311cb145448934361cabd3668"}, "extraPayloads": [], "path": "IntroScreen/content/Labels_Holder_landscape/Label_Holder_bigger_1/Label_2 (1)", "oldContent": "BIG FREE SPINS MODIFIERS!", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 115519, "guid": "9bcff78311cb145448934361cabd3668"}, "extraPayloads": [], "path": "IntroScreen/content/Labels_Holder_landscape/Label_Holder_bigger_1/Label_1", "oldContent": "GO FISHIN' FOR", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 115520, "guid": "9bcff78311cb145448934361cabd3668"}, "extraPayloads": [], "path": "IntroScreen/content/IntroButtons/ButtonSkipIntro/content/TextHolder/Label_1", "oldContent": "DON'T SHOW NEXT TIME", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 115521, "guid": "9bcff78311cb145448934361cabd3668"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/MoneySymbolHolder/DescriptionHolder/PossibleValues/Label", "oldContent": "Possible values are: 2x, 5x, 10x, 15x, 20x, 25x, 50x, 100x, 200x, 500x, 1666x, 2500x or 5000x total bet.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 115522, "guid": "9bcff78311cb145448934361cabd3668"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/MaxWin/RuleHolder/HolderLabelJackpot/Label1New", "oldContent": "The maximum win amount is limited to {0}x bet except Jack<PERSON>. If the total win of a FREE SPINS round reaches {1}x bet the round immediately ends, win is awarded and all remaining free spins are forfeited.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}], "Spines": [], "revisionNumber": 0}, "fileID": 115523}], "fileID": 115524}, {"name": "Game/GamePivot/FSWONWindow/content/FreespinsWonWindow/LabelsAndSprites/Catches_label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 115435, "guid": "9bcff78311cb145448934361cabd3668"}, "children": [], "psr": "d"}, "fileID": 115348}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "9c429f956ce019244913b90fa54b99f8"}, "_text": "가 잡는", "fontSize": 50, "width": 338, "height": 110, "overflow": 0}, "fileID": 115436}], "fileID": 115525}, {"name": "Game/GamePivot/FreeSpinsPurchase/BuyButtons/Options/Option_0/OptVisual/Texts/BoughtText/LandscapeText/label1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 115435, "guid": "9bcff78311cb145448934361cabd3668"}, "children": [], "psr": "d"}, "fileID": 115349}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "9c429f956ce019244913b90fa54b99f8"}, "_text": "활성화", "fontSize": 40, "width": 170, "height": 80, "overflow": 0}, "fileID": 115437}], "fileID": 115526}, {"name": "Game/GamePivot/FSWONWindow/PressAnywhere_Label/label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 115435, "guid": "9bcff78311cb145448934361cabd3668"}, "children": [], "psr": "d"}, "fileID": 115350}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "9c429f956ce019244913b90fa54b99f8"}, "_text": "아무 곳이나 눌러 계속", "fontSize": 40, "width": 1340, "height": 65, "overflow": 0}, "fileID": 115438}], "fileID": 115527}, {"name": "Game/GamePivot/FSExtraWindow/content/PressAnywhere_Label/label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 115435, "guid": "9bcff78311cb145448934361cabd3668"}, "children": [], "psr": "d"}, "fileID": 115351}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "9c429f956ce019244913b90fa54b99f8"}, "_text": "아무 곳이나 눌러 계속", "fontSize": 40, "width": 1340, "height": 65, "overflow": 0}, "fileID": 115439}], "fileID": 115528}, {"name": "Game/GamePivot/FreeSpinsPurchase/FSPurchaseWindow/Content/AnimatedPivot/Texts/BuyText/label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 115435, "guid": "9bcff78311cb145448934361cabd3668"}, "children": [], "psr": "d"}, "fileID": 115352}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "9c429f956ce019244913b90fa54b99f8"}, "_text": "무료 스핀 구매", "fontSize": 69, "width": 826, "height": 69, "overflow": 0}, "fileID": 115440}], "fileID": 115529}, {"name": "Game/GamePivot/FsFeature/content/Feature_trail/Holder/fisherman_highlight/ScalerX10/stretcher_fs/spins", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 115435, "guid": "9bcff78311cb145448934361cabd3668"}, "children": [], "psr": "d"}, "fileID": 115353}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "9c429f956ce019244913b90fa54b99f8"}, "_text": "스핀", "fontSize": 30, "width": 64, "height": 33, "overflow": 0}, "fileID": 115441}], "fileID": 115530}, {"name": "Game/Background/PaytableOnScreen/Portrait/Message1/labelMsg1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 115435, "guid": "9bcff78311cb145448934361cabd3668"}, "children": [], "psr": "d"}, "fileID": 115354}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "9c429f956ce019244913b90fa54b99f8"}, "_text": "모든 심볼의 지불은 왼쪽에서 오른쪽입니다. 보너스는 어느 포지션에서든 지급됩니다.", "fontSize": 40, "width": 1398, "height": 40, "alignment": 2}, "fileID": 115442}], "fileID": 115531}, {"name": "Game/GamePivot/FsFeature/content/Feature_trail/Holder/fisherman_highlight/ScalerX3/stretcher_fs/free", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 115435, "guid": "9bcff78311cb145448934361cabd3668"}, "children": [], "psr": "d"}, "fileID": 115355}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "9c429f956ce019244913b90fa54b99f8"}, "_text": "무료", "fontSize": 30, "width": 60, "height": 33, "overflow": 0}, "fileID": 115443}], "fileID": 115532}, {"name": "Game/GamePivot/Reels/ThePivot/BonusMessages/Fisherman/Labels/uilabel", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 115435, "guid": "9bcff78311cb145448934361cabd3668"}, "children": [], "psr": "d"}, "fileID": 115356}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "9c429f956ce019244913b90fa54b99f8"}, "_text": "추가 낚시꾼!", "fontSize": 256, "width": 1000, "height": 450, "overflow": 0}, "fileID": 115444}], "fileID": 115533}, {"name": "Game/GamePivot/FreeSpinsPurchase/BuyButtons/Options/Option_0/OptVisual/Texts/BoughtText/LandscapeText/label0", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 115435, "guid": "9bcff78311cb145448934361cabd3668"}, "children": [], "psr": "d"}, "fileID": 115357}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "9c429f956ce019244913b90fa54b99f8"}, "_text": "기능", "fontSize": 40, "width": 170, "height": 80, "overflow": 0}, "fileID": 115445}], "fileID": 115534}, {"name": "Game/GamePivot/FreeSpinsPurchase/BuyButtons/Options/Option_0/OptVisual/Texts/BuyText/PortraitText/label0", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 115435, "guid": "9bcff78311cb145448934361cabd3668"}, "children": [], "psr": "d"}, "fileID": 115358}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "9c429f956ce019244913b90fa54b99f8"}, "_text": "무료 스핀 구매", "fontSize": 26, "width": 270, "height": 70, "overflow": 0}, "fileID": 115446}], "fileID": 115535}, {"name": "Game/GamePivot/FsFeature/content/Feature_trail/Holder/fisherman_highlight/ScalerX2/stretcher_fs/free", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 115435, "guid": "9bcff78311cb145448934361cabd3668"}, "children": [], "psr": "d"}, "fileID": 115359}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "9c429f956ce019244913b90fa54b99f8"}, "_text": "무료", "fontSize": 30, "width": 60, "height": 33, "overflow": 0}, "fileID": 115447}], "fileID": 115536}, {"name": "Game/GamePivot/FSResultWindow/content/SignPivot/FreespinsCongratsWindow/Labels/Congrats_label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 115435, "guid": "9bcff78311cb145448934361cabd3668"}, "children": [], "psr": "d"}, "fileID": 115360}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "9c429f956ce019244913b90fa54b99f8"}, "_text": "축하합니다!", "fontSize": 169, "width": 375, "height": 169, "overflow": 0}, "fileID": 115448}], "fileID": 115537}, {"name": "Game/GamePivot/FSExtraWindow/content/SignPivot/FreespinsExtraWindow/Labels/Congrats_label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 115435, "guid": "9bcff78311cb145448934361cabd3668"}, "children": [], "psr": "d"}, "fileID": 115361}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "9c429f956ce019244913b90fa54b99f8"}, "_text": "축하합니다!", "fontSize": 169, "width": 362, "height": 169, "overflow": 0}, "fileID": 115449}], "fileID": 115538}, {"name": "Game/GamePivot/FSExtraWindow/content/SignPivot/FreespinsExtraWindow/Labels/FreeSpins_label ", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 115435, "guid": "9bcff78311cb145448934361cabd3668"}, "children": [], "psr": "d"}, "fileID": 115362}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "9c429f956ce019244913b90fa54b99f8"}, "_text": "무료 스핀 개", "fontSize": 75, "width": 1000, "height": 169, "overflow": 0}, "fileID": 115450}], "fileID": 115539}, {"name": "Game/GamePivot/FSExtraWindow/content/SignPivot/FreespinsExtraWindow/Labels/AreNow_label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 115435, "guid": "9bcff78311cb145448934361cabd3668"}, "children": [], "psr": "d"}, "fileID": 115363}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "9c429f956ce019244913b90fa54b99f8"}, "_text": "이제", "fontSize": 70, "width": 500, "height": 169, "overflow": 0}, "fileID": 115451}], "fileID": 115540}, {"name": "Game/GamePivot/FreeSpinsPurchase/BuyButtons/Options/Option_0/OptVisual/Texts/BuyText/LandscapeTest/label0", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 115435, "guid": "9bcff78311cb145448934361cabd3668"}, "children": [], "psr": "d"}, "fileID": 115364}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "9c429f956ce019244913b90fa54b99f8"}, "_text": "무료 스핀 구매", "fontSize": 26, "width": 270, "height": 70, "overflow": 0}, "fileID": 115452}], "fileID": 115541}, {"name": "Game/GamePivot/Reels/ThePivot/BonusMessages/PlusFS/Labels/uilabel", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 115435, "guid": "9bcff78311cb145448934361cabd3668"}, "children": [], "psr": "d"}, "fileID": 115365}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "9c429f956ce019244913b90fa54b99f8"}, "_text": "추가 무료 스핀 개", "fontSize": 256, "width": 1200, "height": 450, "overflow": 0}, "fileID": 115453}], "fileID": 115542}, {"name": "Game/GamePivot/FsFeature/content/Feature_trail/Holder/fisherman_highlight/ScalerX10/stretcher_fs/free", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 115435, "guid": "9bcff78311cb145448934361cabd3668"}, "children": [], "psr": "d"}, "fileID": 115366}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "9c429f956ce019244913b90fa54b99f8"}, "_text": "무료", "fontSize": 30, "width": 60, "height": 33, "overflow": 0}, "fileID": 115454}], "fileID": 115543}, {"name": "Game/GamePivot/FSWONWindow/content/FreespinsWonWindow/LabelsAndSprites/Congratulations_label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 115435, "guid": "9bcff78311cb145448934361cabd3668"}, "children": [], "psr": "d"}, "fileID": 115367}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "9c429f956ce019244913b90fa54b99f8"}, "_text": "축하합니다!", "fontSize": 169, "width": 340, "height": 169, "overflow": 0}, "fileID": 115455}], "fileID": 115544}, {"name": "Game/GamePivot/Reels/ThePivot/BonusMessages/Fishes/Labels/uilabel", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 115435, "guid": "9bcff78311cb145448934361cabd3668"}, "children": [], "psr": "d"}, "fileID": 115368}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "9c429f956ce019244913b90fa54b99f8"}, "_text": "추가 물고기!", "fontSize": 256, "width": 1200, "height": 450, "overflow": 0}, "fileID": 115456}], "fileID": 115545}, {"name": "Game/GamePivot/FreeSpinsPurchase/BuyButtons/Options/Option_0/OptVisual/Texts/BoughtText/PortraitText/label0", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 115435, "guid": "9bcff78311cb145448934361cabd3668"}, "children": [], "psr": "d"}, "fileID": 115369}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "9c429f956ce019244913b90fa54b99f8"}, "_text": "기능 활성화", "fontSize": 60, "width": 164, "height": 208, "overflow": 0}, "fileID": 115457}], "fileID": 115546}, {"name": "Game/GamePivot/FSExtraWindow/content/SignPivot/FreespinsExtraWindow/Labels/Extra_label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 115435, "guid": "9bcff78311cb145448934361cabd3668"}, "children": [], "psr": "d"}, "fileID": 115370}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "9c429f956ce019244913b90fa54b99f8"}, "_text": "추가", "fontSize": 75, "width": 1000, "height": 169, "overflow": 0}, "fileID": 115458}], "fileID": 115547}, {"name": "Game/GamePivot/FSResultWindow/content/SignPivot/FreespinsCongratsWindow/Labels/YouWon_label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 115435, "guid": "9bcff78311cb145448934361cabd3668"}, "children": [], "psr": "d"}, "fileID": 115371}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "9c429f956ce019244913b90fa54b99f8"}, "_text": "당신은이겼습니다", "fontSize": 110, "width": 1000, "height": 169, "overflow": 0}, "fileID": 115459}], "fileID": 115548}, {"name": "Game/GamePivot/FsFeature/content/Feature_trail/Holder/fisherman_highlight/ScalerX3/stretcher_fs/spins", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 115435, "guid": "9bcff78311cb145448934361cabd3668"}, "children": [], "psr": "d"}, "fileID": 115372}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "9c429f956ce019244913b90fa54b99f8"}, "_text": "스핀", "fontSize": 30, "width": 64, "height": 33, "overflow": 0}, "fileID": 115460}], "fileID": 115549}, {"name": "Game/Background/PaytableOnScreen/Landscape/Message1/labelMsg1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 115435, "guid": "9bcff78311cb145448934361cabd3668"}, "children": [], "psr": "d"}, "fileID": 115373}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "9c429f956ce019244913b90fa54b99f8"}, "_text": "모든 심볼의 지불은 왼쪽에서 오른쪽입니다. 보너스는 어느 포지션에서든 지급됩니다.", "fontSize": 30, "width": 1060, "height": 30, "alignment": 2}, "fileID": 115461}], "fileID": 115550}, {"name": "Game/GamePivot/FsFeature/content/Feature_trail/Holder/fisherman_highlight/ScalerX2/stretcher_fs/spins", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 115435, "guid": "9bcff78311cb145448934361cabd3668"}, "children": [], "psr": "d"}, "fileID": 115374}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "9c429f956ce019244913b90fa54b99f8"}, "_text": "스핀", "fontSize": 30, "width": 64, "height": 33, "overflow": 0}, "fileID": 115462}], "fileID": 115551}, {"name": "Game/GamePivot/Reels/ThePivot/BonusMessages/Level2/Labels/uilabel", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 115435, "guid": "9bcff78311cb145448934361cabd3668"}, "children": [], "psr": "d"}, "fileID": 115375}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "9c429f956ce019244913b90fa54b99f8"}, "_text": "레벨 2부터 시작!", "fontSize": 256, "width": 1280, "height": 450, "overflow": 0}, "fileID": 115463}], "fileID": 115552}, {"name": "Game/GamePivot/FSWONWindow/content/FreespinsWonWindow/LabelsAndSprites/FreeSpins_label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 115435, "guid": "9bcff78311cb145448934361cabd3668"}, "children": [], "psr": "d"}, "fileID": 115376}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "9c429f956ce019244913b90fa54b99f8"}, "_text": "무료 스핀 개", "fontSize": 110, "width": 1000, "height": 150, "overflow": 0}, "fileID": 115464}], "fileID": 115553}, {"name": "Game/GamePivot/FSResultWindow/content/PressAnywhere_Label/label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 115435, "guid": "9bcff78311cb145448934361cabd3668"}, "children": [], "psr": "d"}, "fileID": 115377}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "9c429f956ce019244913b90fa54b99f8"}, "_text": "아무 곳이나 눌러 계속", "fontSize": 40, "width": 1340, "height": 65, "overflow": 0}, "fileID": 115465}], "fileID": 115554}, {"name": "Game/GamePivot/Reels/ThePivot/BonusMessages/Hooks/Labels/uilabel", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 115435, "guid": "9bcff78311cb145448934361cabd3668"}, "children": [], "psr": "d"}, "fileID": 115378}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "9c429f956ce019244913b90fa54b99f8"}, "_text": "추가 낚싯바늘과 폭발물!", "fontSize": 256, "width": 1000, "height": 450, "overflow": 0}, "fileID": 115466}], "fileID": 115555}, {"name": "Paytable/Pages/Page3/CAT/RuleHolder2/Rule2", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 115435, "guid": "9bcff78311cb145448934361cabd3668"}, "children": [], "psr": "d"}, "fileID": 115379}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "9c429f956ce019244913b90fa54b99f8"}, "_text": "무료 스핀 라운드를 구매할 때 실행되는 스핀에서 SCATTER 3, 4 또는 5개가 랜덤하게 히트할 수 있습니다.", "fontSize": 25, "anchorY": 0, "width": 1400, "height": 75, "overflow": 0, "spacingY": 5}, "fileID": 115467}], "fileID": 115556}, {"name": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder4/Label4", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 115435, "guid": "9bcff78311cb145448934361cabd3668"}, "children": [], "psr": "d"}, "fileID": 115380}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "9c429f956ce019244913b90fa54b99f8"}, "_text": "레벨 2부터 시작 - 프로그레시브 기능에서 라운드는 레벨 2부터 시작합니다.", "fontSize": 25, "anchorX": 0, "width": 1400, "height": 100, "overflow": 0}, "fileID": 115468}], "fileID": 115557}, {"name": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder5/Label5", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 115435, "guid": "9bcff78311cb145448934361cabd3668"}, "children": [], "psr": "d"}, "fileID": 115381}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "9c429f956ce019244913b90fa54b99f8"}, "_text": "- +2 스핀 - 후속 라운드는 무료 스핀 추가 2개로 시작하며, 재실행마다 스핀 2개가 추가됩니다.", "fontSize": 25, "anchorX": 0, "width": 1347, "height": 100, "overflow": 0}, "fileID": 115469}], "fileID": 115558}, {"name": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/RuleHolder1/Rule1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 115435, "guid": "9bcff78311cb145448934361cabd3668"}, "children": [], "psr": "d"}, "fileID": 115382}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "9c429f956ce019244913b90fa54b99f8"}, "_text": "SCATTER 심벌 3개 이상을 히트하면 무료 스핀 기능이 발동됩니다.", "fontSize": 25, "width": 1400, "height": 100, "overflow": 0}, "fileID": 115470}], "fileID": 115559}, {"name": "Paytable/Pages/Page4/RulesTop/AllValuesExpressed", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 115435, "guid": "9bcff78311cb145448934361cabd3668"}, "children": [], "psr": "d"}, "fileID": 115383}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "9c429f956ce019244913b90fa54b99f8"}, "_text": "모든 값이 동전에 있는 실제 배당금으로 표시됩니다.", "fontSize": 25, "width": 1333, "height": 160, "overflow": 0}, "fileID": 115471}], "fileID": 115560}, {"name": "Paytable/Pages/Page4/Volatility/VolatilityDescription", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 115435, "guid": "9bcff78311cb145448934361cabd3668"}, "children": [], "psr": "d"}, "fileID": 115384}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "9c429f956ce019244913b90fa54b99f8"}, "_text": "변동이 높은 게임은 평균적으로 지불 횟수가 적지만 단시간에 고액을 상금으로 받을 수 있습니다", "fontSize": 25, "anchorY": 0, "width": 1368, "height": 93, "overflow": 0}, "fileID": 115472}], "fileID": 115561}, {"name": "Paytable/Pages/Page2/MoneySymbolHolder/DescriptionHolder/MoneySymbolRules/Label3", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 115435, "guid": "9bcff78311cb145448934361cabd3668"}, "children": [], "psr": "d"}, "fileID": 115385}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "9c429f956ce019244913b90fa54b99f8"}, "_text": "물고기 당첨 심벌 역시 머니 심벌입니다. 매 스핀마다 물고기는 랜덤한 금전 가치를 지니며, 무료 스핀 기능 중 획득 가능합니다.", "fontSize": 25, "anchorX": 0, "width": 935, "height": 100, "overflow": 0}, "fileID": 115473}], "fileID": 115562}, {"name": "Paytable/Pages/Page4/RulesBottom/RTP/TheoreticalRTP/Label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 115435, "guid": "9bcff78311cb145448934361cabd3668"}, "children": [], "psr": "d"}, "fileID": 115386}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "9c429f956ce019244913b90fa54b99f8"}, "_text": "게임의 이론적 RTP는 {0}%", "fontSize": 25, "anchorY": 1, "width": 1200, "height": 75, "overflow": 0}, "fileID": 115474}], "fileID": 115563}, {"name": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder8/Rule8", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 115435, "guid": "9bcff78311cb145448934361cabd3668"}, "children": [], "psr": "d"}, "fileID": 115387}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "9c429f956ce019244913b90fa54b99f8"}, "_text": "또한 화면에 물고기는 없고 낚시꾼 심벌만 있는 경우 무작위로 무료 스핀이 끝날 때 바주카 애니메이션이 나타나 낚시꾼을 제외한 화면의 모든 심벌을 다른 무언가로 변경할 수 있습니다.", "fontSize": 25, "width": 1400, "height": 100, "overflow": 0}, "fileID": 115475}], "fileID": 115564}, {"name": "Paytable/Pages/Page1/Rules/AllSymbolsPayLabel", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 115435, "guid": "9bcff78311cb145448934361cabd3668"}, "children": [], "psr": "d"}, "fileID": 115388}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "9c429f956ce019244913b90fa54b99f8"}, "_text": "모든 심볼가장 왼쪽 릴에서 시작하여 왼쪽에서 오른쪽으로 멈춥니다.", "fontSize": 25, "width": 1000, "height": 60, "overflow": 0}, "fileID": 115476}], "fileID": 115565}, {"name": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder5/Rule5", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 115435, "guid": "9bcff78311cb145448934361cabd3668"}, "children": [], "psr": "d"}, "fileID": 115389}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "9c429f956ce019244913b90fa54b99f8"}, "_text": "네 번째 레벨 후에는 기능을 더 이상 재실행할 수 없습니다.", "fontSize": 25, "width": 1400, "height": 100, "overflow": 0}, "fileID": 115477}], "fileID": 115566}, {"name": "Paytable/Pages/Page3/FreeSpinsHolder2/SpecialReelsHolder/SpecialReels", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 115435, "guid": "9bcff78311cb145448934361cabd3668"}, "children": [], "psr": "d"}, "fileID": 115390}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "9c429f956ce019244913b90fa54b99f8"}, "_text": "기능 중에는 특수 릴이 작동합니다.", "fontSize": 25, "width": 1400, "height": 100, "overflow": 0}, "fileID": 115478}], "fileID": 115567}, {"name": "Paytable/Pages/Page3/CAT/RuleHolder1/Rule1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 115435, "guid": "9bcff78311cb145448934361cabd3668"}, "children": [], "psr": "d"}, "fileID": 115391}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "9c429f956ce019244913b90fa54b99f8"}, "_text": "현재 총 베팅액의 100x배 가격을 지불하면 무료 스핀 라운드를 기본 게임 진행 중 바로 실행할 수 있습니다. ", "fontSize": 25, "anchorY": 1, "width": 1400, "height": 100, "overflow": 0}, "fileID": 115479}], "fileID": 115568}, {"name": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/RuleHolder2/Rule2", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 115435, "guid": "9bcff78311cb145448934361cabd3668"}, "children": [], "psr": "d"}, "fileID": 115392}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "9c429f956ce019244913b90fa54b99f8"}, "_text": "기본 게임에서 세 번째 심벌 없이 SCATTER 심벌 2개가 히트할 때마다 또다른 하나가 화면에 랜덤한 기능으로 나타날 가능성이 있습니다.", "fontSize": 25, "width": 1169, "height": 100, "overflow": 0}, "fileID": 115480}], "fileID": 115569}, {"name": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder1/Label1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 115435, "guid": "9bcff78311cb145448934361cabd3668"}, "children": [], "psr": "d"}, "fileID": 115393}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "9c429f956ce019244913b90fa54b99f8"}, "_text": "- 추가 물고기 - 후속 무료 스핀 라운드 중 추가 물고기 심벌이 릴 스트립에 나타납니다", "fontSize": 25, "anchorX": 0, "width": 1400, "height": 100, "overflow": 0}, "fileID": 115481}], "fileID": 115570}, {"name": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder2/Rule2", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 115435, "guid": "9bcff78311cb145448934361cabd3668"}, "children": [], "psr": "d"}, "fileID": 115394}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "9c429f956ce019244913b90fa54b99f8"}, "_text": "기능 중 히트한 WILD 심벌 모두는 라운드 종료까지 수집됩니다.", "fontSize": 25, "width": 1400, "height": 100, "overflow": 0}, "fileID": 115482}], "fileID": 115571}, {"name": "Paytable/Pages/Page4/RulesTop/AllSymbolsPay", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 115435, "guid": "9bcff78311cb145448934361cabd3668"}, "children": [], "psr": "d"}, "fileID": 115395}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "9c429f956ce019244913b90fa54b99f8"}, "_text": "모든 심볼의 지불은 선택된 페이라인의 왼쪽에서 오른쪽입니다.", "fontSize": 25, "width": 1194, "height": 160, "overflow": 0, "spacingY": 5}, "fileID": 115483}], "fileID": 115572}, {"name": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder3/Label3", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 115435, "guid": "9bcff78311cb145448934361cabd3668"}, "children": [], "psr": "d"}, "fileID": 115396}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "9c429f956ce019244913b90fa54b99f8"}, "_text": "- 추가 다이너마이트, 낚싯바늘, 바주카 - 라운드 중 다이너마이트, 낚싯바늘 또는 바주카 스핀 기능이 히트할 확률이 증가합니다.", "fontSize": 25, "anchorX": 0, "width": 1359, "height": 100, "overflow": 0}, "fileID": 115484}], "fileID": 115573}, {"name": "Paytable/Pages/Page4/RulesBottom/SpaceAndEnter", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 115435, "guid": "9bcff78311cb145448934361cabd3668"}, "children": [], "psr": "d"}, "fileID": 115397}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "9c429f956ce019244913b90fa54b99f8"}, "_text": "키보드의 스페이스로 스핀을 시작하고 엔터로 스핀을 종료할 수 있습니다.", "fontSize": 25, "width": 1400, "height": 60, "overflow": 0}, "fileID": 115485}], "fileID": 115574}, {"name": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder7/Rule7", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 115435, "guid": "9bcff78311cb145448934361cabd3668"}, "children": [], "psr": "d"}, "fileID": 115398}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "9c429f956ce019244913b90fa54b99f8"}, "_text": "랜덤하게, 화면에 낚시꾼 심벌은 없고 물고기 심벌만 있는 경우, 무료 스핀이 끝날 때 낚싯바늘이 나타나 랜덤한 릴을 끌어올려 화면에 낚시꾼 심벌을 표시합니다.", "fontSize": 25, "width": 1384, "height": 100, "overflow": 0}, "fileID": 115486}], "fileID": 115575}, {"name": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder1/Rule1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 115435, "guid": "9bcff78311cb145448934361cabd3668"}, "children": [], "psr": "d"}, "fileID": 115399}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "9c429f956ce019244913b90fa54b99f8"}, "_text": "무료 스핀 기능 동안 각 WILD 심벌은 화면의 머니 심벌에서 모든 값을 수집합니다.", "fontSize": 25, "width": 1400, "height": 100, "overflow": 0}, "fileID": 115487}], "fileID": 115576}, {"name": "Paytable/Pages/Page3/MaxWin/TitleHolder/Title", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 115435, "guid": "9bcff78311cb145448934361cabd3668"}, "children": [], "psr": "d"}, "fileID": 115400}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "9c429f956ce019244913b90fa54b99f8"}, "_text": "최대 당첨금", "fontSize": 35, "width": 1100, "height": 100, "overflow": 0}, "fileID": 115488}], "fileID": 115577}, {"name": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder1/LabelHolder2/Label2", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 115435, "guid": "9bcff78311cb145448934361cabd3668"}, "children": [], "psr": "d"}, "fileID": 115401}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "9c429f956ce019244913b90fa54b99f8"}, "_text": "SCATTER 심벌 4x개는 무료 스핀 15개를 지급합니다.", "fontSize": 25, "width": 1400, "height": 75, "overflow": 0}, "fileID": 115489}], "fileID": 115578}, {"name": "Paytable/Pages/Page4/MinMaxHolder/MaxBet/MaximumText", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 115435, "guid": "9bcff78311cb145448934361cabd3668"}, "children": [], "psr": "d"}, "fileID": 115402}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "9c429f956ce019244913b90fa54b99f8"}, "_text": "최대 베팅:", "fontSize": 25, "anchorX": 0, "width": 106, "height": 26}, "fileID": 115490}], "fileID": 115579}, {"name": "Paytable/Pages/Page2/FreeSpinsHolder1/TitleHolder/Title", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 115435, "guid": "9bcff78311cb145448934361cabd3668"}, "children": [], "psr": "d"}, "fileID": 115403}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "9c429f956ce019244913b90fa54b99f8"}, "_text": "무료 스핀 개", "fontSize": 35, "width": 1150, "height": 100, "overflow": 0}, "fileID": 115491}], "fileID": 115580}, {"name": "Paytable/Pages/Page2/MoneySymbolHolder/TitleHolder/Title", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 115435, "guid": "9bcff78311cb145448934361cabd3668"}, "children": [], "psr": "d"}, "fileID": 115404}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "9c429f956ce019244913b90fa54b99f8"}, "_text": "머니 심벌", "fontSize": 35, "width": 1150, "height": 70, "overflow": 0}, "fileID": 115492}], "fileID": 115581}, {"name": "Paytable/Pages/Page1/WildHolder/DescriptionHolder/Label1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 115435, "guid": "9bcff78311cb145448934361cabd3668"}, "children": [], "psr": "d"}, "fileID": 115405}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "9c429f956ce019244913b90fa54b99f8"}, "_text": "이것은 WILD 심벌입니다.", "fontSize": 25, "anchorX": 0, "anchorY": 1, "width": 363, "height": 75, "overflow": 0}, "fileID": 115493}], "fileID": 115582}, {"name": "Paytable/Pages/Page1/Title/PaytableTitleLabel1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 115435, "guid": "9bcff78311cb145448934361cabd3668"}, "children": [], "psr": "d"}, "fileID": 115406}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "9c429f956ce019244913b90fa54b99f8"}, "_text": "게임 규칙", "fontSize": 35, "width": 1150, "height": 100, "overflow": 0}, "fileID": 115494}], "fileID": 115583}, {"name": "Paytable/Pages/Page3/CAT/TitleHolder/Title1New", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 115435, "guid": "9bcff78311cb145448934361cabd3668"}, "children": [], "psr": "d"}, "fileID": 115407}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "9c429f956ce019244913b90fa54b99f8"}, "_text": "무료 스핀 구매", "fontSize": 35, "width": 1400, "height": 70, "overflow": 0}, "fileID": 115495}], "fileID": 115584}, {"name": "Paytable/Pages/Page4/MinMaxHolder/MinBet/MinimumText", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 115435, "guid": "9bcff78311cb145448934361cabd3668"}, "children": [], "psr": "d"}, "fileID": 115408}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "9c429f956ce019244913b90fa54b99f8"}, "_text": "최소 베팅:", "fontSize": 25, "anchorX": 0, "width": 106, "height": 26}, "fileID": 115496}], "fileID": 115585}, {"name": "Paytable/Pages/Page4/RulesBottom/MalfunctionLabel", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 115435, "guid": "9bcff78311cb145448934361cabd3668"}, "children": [], "psr": "d"}, "fileID": 115409}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "9c429f956ce019244913b90fa54b99f8"}, "_text": "기능 불량은 모든 지불 및 게임을 무효화 합니다.", "fontSize": 25, "width": 888, "height": 60, "overflow": 0}, "fileID": 115497}], "fileID": 115586}, {"name": "Paytable/Pages/Page1/ScatterHolder/DescriptionHolder/Label2", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 115435, "guid": "9bcff78311cb145448934361cabd3668"}, "children": [], "psr": "d"}, "fileID": 115410}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "9c429f956ce019244913b90fa54b99f8"}, "_text": "모든 릴에 나타납니다.", "fontSize": 25, "anchorX": 0, "anchorY": 0, "width": 357, "height": 75, "overflow": 0}, "fileID": 115498}], "fileID": 115587}, {"name": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder1/LabelHolder1/Label1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 115435, "guid": "9bcff78311cb145448934361cabd3668"}, "children": [], "psr": "d"}, "fileID": 115411}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "9c429f956ce019244913b90fa54b99f8"}, "_text": "SCATTER 심벌 5x개는 무료 스핀 20개를 지급합니다.", "fontSize": 25, "width": 1400, "height": 75, "overflow": 0}, "fileID": 115499}], "fileID": 115588}, {"name": "Paytable/Pages/Page4/RulesTop/AllWinsMultiplied", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 115435, "guid": "9bcff78311cb145448934361cabd3668"}, "children": [], "psr": "d"}, "fileID": 115412}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "9c429f956ce019244913b90fa54b99f8"}, "_text": "모든 라인의 배당금은 각 라인에서 베팅하면 배로 증가합니다.", "fontSize": 25, "width": 1194, "height": 160, "overflow": 0, "spacingY": 5}, "fileID": 115500}], "fileID": 115589}, {"name": "Paytable/Pages/Page1/ScatterHolder/DescriptionHolder/Label1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 115435, "guid": "9bcff78311cb145448934361cabd3668"}, "children": [], "psr": "d"}, "fileID": 115413}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "9c429f956ce019244913b90fa54b99f8"}, "_text": "이것은 SCATTER 심벌입니다. ", "fontSize": 25, "anchorX": 0, "anchorY": 1, "width": 357, "height": 75, "overflow": 0}, "fileID": 115501}], "fileID": 115590}, {"name": "Paytable/Pages/Page4/RulesTop/OnlyTheHighestWin", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 115435, "guid": "9bcff78311cb145448934361cabd3668"}, "children": [], "psr": "d"}, "fileID": 115414}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "9c429f956ce019244913b90fa54b99f8"}, "_text": "유일하게 최고의 배당금은 각 라인 당 지급됩니다.", "fontSize": 25, "anchorY": 0, "width": 1400, "height": 50, "overflow": 0}, "fileID": 115502}], "fileID": 115591}, {"name": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder1/LabelHolder3/Label3", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 115435, "guid": "9bcff78311cb145448934361cabd3668"}, "children": [], "psr": "d"}, "fileID": 115415}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "9c429f956ce019244913b90fa54b99f8"}, "_text": "SCATTER 심벌 3x개는 무료 스핀 10개를 지급합니다.", "fontSize": 25, "width": 1400, "height": 75, "overflow": 0}, "fileID": 115503}], "fileID": 115592}, {"name": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder2/LabelHolder1/Label1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 115435, "guid": "9bcff78311cb145448934361cabd3668"}, "children": [], "psr": "d"}, "fileID": 115416}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "9c429f956ce019244913b90fa54b99f8"}, "_text": "- 랜덤하게, 화면의 SCATTER가 한 칸 아래로 내려가 릴 영역을 비우면, 리스핀이 실행되어 SCATTER가 있는 릴이 한 칸 내려오고 SCATTER가 없는 릴은 리스핀합니다.", "fontSize": 25, "anchorX": 0, "width": 1400, "height": 125, "overflow": 0}, "fileID": 115504}], "fileID": 115593}, {"name": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder4/Rule4", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 115435, "guid": "9bcff78311cb145448934361cabd3668"}, "children": [], "psr": "d"}, "fileID": 115417}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "9c429f956ce019244913b90fa54b99f8"}, "_text": "재발동된 스핀은 이전의 무료 스핀이 모두 끝난 후에 플레이됩니다. 멀티플라이어는 재발동된 스핀에 적용됩니다.", "fontSize": 25, "width": 920, "height": 125, "overflow": 0}, "fileID": 115505}], "fileID": 115594}, {"name": "Paytable/Pages/Page4/RulesTop/WhenWinningOnMultiplePaylines", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 115435, "guid": "9bcff78311cb145448934361cabd3668"}, "children": [], "psr": "d"}, "fileID": 115418}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "9c429f956ce019244913b90fa54b99f8"}, "_text": "여러 지급라인에서 우승한 경우, 모든 우승은 총 우승에 추가됩니다.", "fontSize": 25, "anchorY": 0, "width": 1400, "height": 50, "overflow": 0}, "fileID": 115506}], "fileID": 115595}, {"name": "Paytable/Pages/Page4/Title/PaytableTitleLabel", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 115435, "guid": "9bcff78311cb145448934361cabd3668"}, "children": [], "psr": "d"}, "fileID": 115419}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "9c429f956ce019244913b90fa54b99f8"}, "_text": "게임 규칙", "fontSize": 30, "width": 1150, "height": 100, "overflow": 0}, "fileID": 115507}], "fileID": 115596}, {"name": "Paytable/Pages/Page3/MaxWin/RuleHolder/Label1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 115435, "guid": "9bcff78311cb145448934361cabd3668"}, "children": [], "psr": "d"}, "fileID": 115420}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "9c429f956ce019244913b90fa54b99f8"}, "_text": "최대 상금은 베팅액의 {0}x배로 제한됩니다. 무료 스핀 라운드의 총 상금이 {1}x에 도달하면 라운드가 즉시 종료되고 상금이 지급되며, 나머지 무료 스핀은 모두 몰수됩니다.", "fontSize": 25, "anchorY": 0, "width": 1400, "height": 100, "overflow": 0}, "fileID": 115508}], "fileID": 115597}, {"name": "Paytable/Pages/Page4/RulesBottom/RTP/TheoreticalRTPBONUS/Label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 115435, "guid": "9bcff78311cb145448934361cabd3668"}, "children": [], "psr": "d"}, "fileID": 115421}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "9c429f956ce019244913b90fa54b99f8"}, "_text": "\"무무료스핀 구매\"를 이용한 경우 이 게임의 RTP는 {0}%입니다.", "fontSize": 25, "anchorY": 0, "width": 1200, "height": 75, "overflow": 0}, "fileID": 115509}], "fileID": 115598}, {"name": "Paytable/Pages/Page4/Volatility/VolatilityMeter/LabelHolder/VolatilityLabel", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 115435, "guid": "9bcff78311cb145448934361cabd3668"}, "children": [], "psr": "d"}, "fileID": 115422}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "9c429f956ce019244913b90fa54b99f8"}, "_text": "휘발성", "fontSize": 22, "anchorX": 0, "width": 60, "height": 22}, "fileID": 115510}], "fileID": 115599}, {"name": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder6/Rule6", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 115435, "guid": "9bcff78311cb145448934361cabd3668"}, "children": [], "psr": "d"}, "fileID": 115423}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "9c429f956ce019244913b90fa54b99f8"}, "_text": "랜덤하게, 화면에 낚시꾼 심벌은 있으나 물고기가 없는 경우 무료 스핀 종료 시 물고기 머니 심벌은 다이너마이트 스핀 기능으로 랜덤한 위치에 나타날 수 있습니다.", "fontSize": 25, "width": 1271, "height": 100, "overflow": 0}, "fileID": 115511}], "fileID": 115600}, {"name": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder2/LabelHolder2/Label2", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 115435, "guid": "9bcff78311cb145448934361cabd3668"}, "children": [], "psr": "d"}, "fileID": 115424}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "9c429f956ce019244913b90fa54b99f8"}, "_text": "- 낚싯바늘은 릴 하나를 끌어올려 또다른 SCATTER를 랜덤하게 드러냅니다.", "fontSize": 25, "anchorX": 0, "width": 1400, "height": 75, "overflow": 0}, "fileID": 115512}], "fileID": 115601}, {"name": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/RuleHolder3/Rule3", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 115435, "guid": "9bcff78311cb145448934361cabd3668"}, "children": [], "psr": "d"}, "fileID": 115425}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "9c429f956ce019244913b90fa54b99f8"}, "_text": "라운드 시작 전, 후속 라운드에 적용되는 수정자 05개가 랜덤으로 선택됩니다.", "fontSize": 25, "width": 1400, "height": 100, "overflow": 0}, "fileID": 115513}], "fileID": 115602}, {"name": "Paytable/Pages/Page1/WildHolder/DescriptionHolder/Label2", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 115435, "guid": "9bcff78311cb145448934361cabd3668"}, "children": [], "psr": "d"}, "fileID": 115426}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "9c429f956ce019244913b90fa54b99f8"}, "_text": "무료 스핀 라운드 중 모든 릴에 나타납니다.", "fontSize": 25, "anchorX": 0, "width": 363, "height": 75, "overflow": 0}, "fileID": 115514}], "fileID": 115603}, {"name": "Paytable/Pages/Page1/WildHolder/DescriptionHolder/Label3", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 115435, "guid": "9bcff78311cb145448934361cabd3668"}, "children": [], "psr": "d"}, "fileID": 115427}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "9c429f956ce019244913b90fa54b99f8"}, "_text": "SCATTER를 제외한 모든 심벌을 대체합니다.", "fontSize": 25, "anchorX": 0, "anchorY": 0, "width": 363, "height": 75, "overflow": 0}, "fileID": 115515}], "fileID": 115604}, {"name": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder3/Rule3", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 115435, "guid": "9bcff78311cb145448934361cabd3668"}, "children": [], "psr": "d"}, "fileID": 115428}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "9c429f956ce019244913b90fa54b99f8"}, "_text": "4번째 WILD 심벌이 수집될 때마다 이 기능이 다시 발동되고, 무료 스핀을 추가로 10개 드립니다. 머니 심벌 수집 시 멀티플라이어는 두 번째 레벨에서 2x로, 세 번째 레벨에서 3x로, 네 번째 레벨에서 10x가 됩니다.", "fontSize": 25, "width": 1327, "height": 150, "overflow": 0}, "fileID": 115516}], "fileID": 115605}, {"name": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder2/Label2", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 115435, "guid": "9bcff78311cb145448934361cabd3668"}, "children": [], "psr": "d"}, "fileID": 115429}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "9c429f956ce019244913b90fa54b99f8"}, "_text": "- 추가 낚시꾼 - 후속 무료 스핀 라운드 중 추가 WILD 심벌이 릴 스트립에 나타납니다", "fontSize": 25, "anchorX": 0, "width": 1400, "height": 100, "overflow": 0}, "fileID": 115517}], "fileID": 115606}, {"name": "IntroScreen/content/Labels_Holder_landscape/Label_Holder_bigger_1/Label_2 (1)", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 115435, "guid": "9bcff78311cb145448934361cabd3668"}, "children": [], "psr": "d"}, "fileID": 115430}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "9c429f956ce019244913b90fa54b99f8"}, "_text": "낚으러 가 보자!", "fontSize": 60, "width": 1000, "height": 80, "overflow": 0}, "fileID": 115518}], "fileID": 115607}, {"name": "IntroScreen/content/Labels_Holder_landscape/Label_Holder_bigger_1/Label_1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 115435, "guid": "9bcff78311cb145448934361cabd3668"}, "children": [], "psr": "d"}, "fileID": 115431}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "9c429f956ce019244913b90fa54b99f8"}, "_text": "대어 무료 스핀 수정자를", "fontSize": 60, "width": 1000, "height": 80, "overflow": 0}, "fileID": 115519}], "fileID": 115608}, {"name": "IntroScreen/content/IntroButtons/ButtonSkipIntro/content/TextHolder/Label_1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 115435, "guid": "9bcff78311cb145448934361cabd3668"}, "children": [], "psr": "d"}, "fileID": 115432}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "9c429f956ce019244913b90fa54b99f8"}, "_text": "더 보지 않기", "fontSize": 30, "anchorX": 0, "width": 156, "height": 30}, "fileID": 115520}], "fileID": 115609}, {"name": "Paytable/Pages/Page2/MoneySymbolHolder/DescriptionHolder/PossibleValues/Label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 115435, "guid": "9bcff78311cb145448934361cabd3668"}, "children": [], "psr": "d"}, "fileID": 115433}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "9c429f956ce019244913b90fa54b99f8"}, "_text": "가능한 값은 총 베팅액의 2x, 5x, 10x, 15x, 20x, 25x, 50x, 100x, 200x, 500x, 1666x, 2500x 또는 5000x입니다.", "fontSize": 25, "anchorX": 0, "width": 955, "height": 100, "overflow": 0}, "fileID": 115521}], "fileID": 115610}, {"name": "Paytable/Pages/Page3/MaxWin/RuleHolder/HolderLabelJackpot/Label1New", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 115435, "guid": "9bcff78311cb145448934361cabd3668"}, "children": [], "psr": "d"}, "fileID": 115434}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "9c429f956ce019244913b90fa54b99f8"}, "_text": "최대 당첨금은 Jackpot을 제외하고 베팅액의 {0}x로 제한됩니다. 무료 스핀 라운드의 총 당첨금이 베팅액의 {1}x에 도달하면 라운드가 즉시 종료되고 상금이 지급되며, 나머지 무료 스핀은 모두 몰수됩니다.", "fontSize": 25, "anchorY": 0, "width": 1400, "height": 100, "overflow": 0}, "fileID": 115522}], "fileID": 115611}]}}, {"type": "Font", "id": "9c429f956ce019244913b90fa54b99f8", "data": {"fontName": "f9c429f956ce019244913b90fa54b99", "path": "@font-face{font-family:'f9c429f956ce019244913b90fa54b99';src:url('data:application/x-font-woff;base64,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') format('woff')}"}}]}