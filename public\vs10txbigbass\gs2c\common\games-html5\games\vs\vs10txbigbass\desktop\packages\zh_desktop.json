{"resources": [{"type": "GameObject", "id": "b4a47b58421a9ce40b5eff5143ad91d1", "data": {"root": [{"name": "zh_desktop", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 0}, "children": [{"fileID": 223354, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, {"fileID": 223355, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, {"fileID": 223356, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, {"fileID": 223357, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, {"fileID": 223358, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, {"fileID": 223359, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, {"fileID": 223360, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, {"fileID": 223361, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, {"fileID": 223362, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, {"fileID": 223363, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, {"fileID": 223364, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, {"fileID": 223365, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, {"fileID": 223366, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, {"fileID": 223367, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, {"fileID": 223368, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, {"fileID": 223369, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, {"fileID": 223370, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, {"fileID": 223371, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, {"fileID": 223372, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, {"fileID": 223373, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, {"fileID": 223374, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, {"fileID": 223375, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, {"fileID": 223376, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, {"fileID": 223377, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, {"fileID": 223378, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, {"fileID": 223379, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, {"fileID": 223380, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, {"fileID": 223381, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, {"fileID": 223382, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, {"fileID": 223383, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, {"fileID": 223384, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, {"fileID": 223385, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, {"fileID": 223386, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, {"fileID": 223387, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, {"fileID": 223388, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, {"fileID": 223389, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, {"fileID": 223390, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, {"fileID": 223391, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, {"fileID": 223392, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, {"fileID": 223393, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, {"fileID": 223394, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, {"fileID": 223395, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, {"fileID": 223396, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, {"fileID": 223397, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, {"fileID": 223398, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, {"fileID": 223399, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, {"fileID": 223400, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, {"fileID": 223401, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, {"fileID": 223402, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, {"fileID": 223403, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, {"fileID": 223404, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, {"fileID": 223405, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, {"fileID": 223406, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, {"fileID": 223407, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, {"fileID": 223408, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, {"fileID": 223409, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, {"fileID": 223410, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, {"fileID": 223411, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, {"fileID": 223412, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, {"fileID": 223413, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, {"fileID": 223414, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, {"fileID": 223415, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, {"fileID": 223416, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, {"fileID": 223417, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, {"fileID": 223418, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, {"fileID": 223419, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, {"fileID": 223420, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, {"fileID": 223421, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, {"fileID": 223422, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, {"fileID": 223423, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, {"fileID": 223424, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, {"fileID": 223425, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, {"fileID": 223426, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, {"fileID": 223427, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, {"fileID": 223428, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, {"fileID": 223429, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, {"fileID": 223430, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, {"fileID": 223431, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, {"fileID": 223432, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, {"fileID": 223433, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, {"fileID": 223434, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, {"fileID": 223435, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, {"fileID": 223436, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, {"fileID": 223437, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, {"fileID": 223438, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, {"fileID": 223439, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, {"fileID": 223440, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}], "s": "0"}, "fileID": 223441}, {"componentType": "ModificationsManager", "enabled": true, "serializableData": {"root": {"fileID": 0}, "EditMode": false, "Atlases": [], "Transforms": [{"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page1/Title/PaytableTitleLabel1", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page1/Rules/AllSymbolsPayLabel", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page1/ScatterHolder/SymbolScatter/Sprite", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0.52, "y": 0.52, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page1/ScatterHolder/DescriptionHolder/Label1", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -24, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page1/ScatterHolder/DescriptionHolder/Label2", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -35, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page1/WildHolder/SymbolScatter/Sprite", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0.25, "y": 0.25, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page1/WildHolder/DescriptionHolder/Label1", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -12.5, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page1/WildHolder/DescriptionHolder/Label2", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -50, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page1/WildHolder/DescriptionHolder/Label3", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -87.5, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/MoneySymbolHolder/TitleHolder/Title", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 7, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/MoneySymbolHolder/DescriptionHolder/MoneySymbolHolder/Sprite", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0.7, "y": 0.7, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/MoneySymbolHolder/DescriptionHolder/MoneySymbolRules/Label3", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FreeSpinsHolder1/TitleHolder/Title", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/RuleHolder1/Rule1", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder1/LabelHolder1/Label1", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder1/LabelHolder2/Label2", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder1/LabelHolder3/Label3", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/RuleHolder2/Rule2", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder2/LabelHolder1/Label1", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder2/LabelHolder2/Label2", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/RuleHolder3/Rule3", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder1/Label1", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder2/Label2", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder3/Label3", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder4/Label4", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder5/Label5", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder1/Rule1", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder2/Rule2", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 8.7, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder3/Rule3", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 7, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder4/Rule4", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder5/Rule5", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder6/Rule6", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder7/Rule7", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder8/Rule8", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/FreeSpinsHolder2/SpecialReelsHolder/SpecialReels", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/MaxWin/TitleHolder/Title", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/MaxWin/RuleHolder/Label1", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/CAT/TitleHolder/Title1New", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/CAT/RuleHolder1/Rule1", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -15, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/CAT/RuleHolder2/Rule2", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -21, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/CAT/RuleHolder3/Rule3", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page4/Title/PaytableTitleLabel", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page4/Volatility/VolatilityDescription", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -30, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page4/RulesTop/AllSymbolsPay", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 50, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page4/RulesTop/AllWinsMultiplied", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 21, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page4/RulesTop/AllValuesExpressed", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -45, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page4/RulesTop/OnlyTheHighestWin", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -65, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page4/RulesTop/WhenWinningOnMultiplePaylines", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -95, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page4/Lines/Sprite", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page4/RulesBottom/SpaceAndEnter", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 92, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page4/RulesBottom/RTP", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page4/RulesBottom/MalfunctionLabel", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -97, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page4/MinMaxHolder", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -185, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/MaxWin/RuleHolder/HolderLabelJackpot/Label1New", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}], "Labels": [{"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 223442, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "extraPayloads": [], "path": "Game/GamePivot/FSWONWindow/content/FreespinsWonWindow/LabelsAndSprites/Catches_label", "oldContent": "CATCHES", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 223443, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "extraPayloads": [], "path": "Game/GamePivot/FreeSpinsPurchase/BuyButtons/Options/Option_0/OptVisual/Texts/BoughtText/LandscapeText/label1", "oldContent": "ACTIVE", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 223444, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "extraPayloads": [], "path": "Game/GamePivot/FSWONWindow/PressAnywhere_Label/label", "oldContent": "Press anywhere to continue", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 223445, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "extraPayloads": [], "path": "Game/GamePivot/FSExtraWindow/content/PressAnywhere_Label/label", "oldContent": "Press anywhere to continue...", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 223446, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "extraPayloads": [], "path": "Game/GamePivot/FreeSpinsPurchase/FSPurchaseWindow/Content/AnimatedPivot/Texts/BuyText/label", "oldContent": "BUY FREE SPINS", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 223447, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "extraPayloads": [], "path": "Game/GamePivot/FsFeature/content/Feature_trail/Holder/fisherman_highlight/ScalerX10/stretcher_fs/spins", "oldContent": "spins", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 223448, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "extraPayloads": [], "path": "Game/Background/PaytableOnScreen/Portrait/Message1/labelMsg1", "oldContent": "AL<PERSON> SYMBOLS PAY FROM LEFT TO RIGHT. BONUS PAYS ON ANY POSITION.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 223449, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "extraPayloads": [], "path": "Game/GamePivot/FsFeature/content/Feature_trail/Holder/fisherman_highlight/ScalerX3/stretcher_fs/free", "oldContent": "free", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 223450, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "extraPayloads": [], "path": "Game/GamePivot/Reels/ThePivot/BonusMessages/Fisherman/Labels/uilabel", "oldContent": "MORE FISHERMEN!", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 223451, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "extraPayloads": [], "path": "Game/GamePivot/FreeSpinsPurchase/BuyButtons/Options/Option_0/OptVisual/Texts/BoughtText/LandscapeText/label0", "oldContent": "FEATURE", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 223452, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "extraPayloads": [], "path": "Game/GamePivot/FreeSpinsPurchase/BuyButtons/Options/Option_0/OptVisual/Texts/BuyText/PortraitText/label0", "oldContent": "BUY FREE SPINS", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 223453, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "extraPayloads": [], "path": "Game/GamePivot/FsFeature/content/Feature_trail/Holder/fisherman_highlight/ScalerX2/stretcher_fs/free", "oldContent": "free", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 223454, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "extraPayloads": [], "path": "Game/GamePivot/FSResultWindow/content/SignPivot/FreespinsCongratsWindow/Labels/Congrats_label", "oldContent": "CONGRATULATIONS!", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 223455, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "extraPayloads": [], "path": "Game/GamePivot/FSExtraWindow/content/SignPivot/FreespinsExtraWindow/Labels/Congrats_label", "oldContent": "CONGRATULATIONS!", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 223456, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "extraPayloads": [], "path": "Game/GamePivot/FSExtraWindow/content/SignPivot/FreespinsExtraWindow/Labels/FreeSpins_label ", "oldContent": "FREESPINS", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 223457, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "extraPayloads": [], "path": "Game/GamePivot/FSExtraWindow/content/SignPivot/FreespinsExtraWindow/Labels/AreNow_label", "oldContent": "ARE NOW", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 223458, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "extraPayloads": [], "path": "Game/GamePivot/FreeSpinsPurchase/BuyButtons/Options/Option_0/OptVisual/Texts/BuyText/LandscapeTest/label0", "oldContent": "BUY FREE SPINS", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 223459, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "extraPayloads": [], "path": "Game/GamePivot/Reels/ThePivot/BonusMessages/PlusFS/Labels/uilabel", "oldContent": "EXTRA FREE SPINS!", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 223460, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "extraPayloads": [], "path": "Game/GamePivot/FsFeature/content/Feature_trail/Holder/fisherman_highlight/ScalerX10/stretcher_fs/free", "oldContent": "free", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 223461, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "extraPayloads": [], "path": "Game/GamePivot/FSWONWindow/content/FreespinsWonWindow/LabelsAndSprites/Congratulations_label", "oldContent": "CONGRATULATIONS!", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 223462, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "extraPayloads": [], "path": "Game/GamePivot/Reels/ThePivot/BonusMessages/Fishes/Labels/uilabel", "oldContent": "MORE FISH!", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 223463, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "extraPayloads": [], "path": "Game/GamePivot/FreeSpinsPurchase/BuyButtons/Options/Option_0/OptVisual/Texts/BoughtText/PortraitText/label0", "oldContent": "FEATURE ACTIVE", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 223464, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "extraPayloads": [], "path": "Game/GamePivot/FSExtraWindow/content/SignPivot/FreespinsExtraWindow/Labels/Extra_label", "oldContent": "THE NEXT", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 223465, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "extraPayloads": [], "path": "Game/GamePivot/FSResultWindow/content/SignPivot/FreespinsCongratsWindow/Labels/YouWon_label", "oldContent": "YOU HAVE WON", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 223466, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "extraPayloads": [], "path": "Game/GamePivot/FsFeature/content/Feature_trail/Holder/fisherman_highlight/ScalerX3/stretcher_fs/spins", "oldContent": "spins", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 223467, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "extraPayloads": [], "path": "Game/Background/PaytableOnScreen/Landscape/Message1/labelMsg1", "oldContent": "AL<PERSON> SYMBOLS PAY FROM LEFT TO RIGHT. BONUS PAYS ON ANY POSITION.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 223468, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "extraPayloads": [], "path": "Game/GamePivot/FsFeature/content/Feature_trail/Holder/fisherman_highlight/ScalerX2/stretcher_fs/spins", "oldContent": "spins", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 223469, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "extraPayloads": [], "path": "Game/GamePivot/Reels/ThePivot/BonusMessages/Level2/Labels/uilabel", "oldContent": "START FROM LEVEL 2!", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 223470, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "extraPayloads": [], "path": "Game/GamePivot/FSWONWindow/content/FreespinsWonWindow/LabelsAndSprites/FreeSpins_label", "oldContent": "FREE SPINS!", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 223471, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "extraPayloads": [], "path": "Game/GamePivot/FSResultWindow/content/PressAnywhere_Label/label", "oldContent": "Press anywhere to continue...", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 223472, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "extraPayloads": [], "path": "Game/GamePivot/Reels/ThePivot/BonusMessages/Hooks/Labels/uilabel", "oldContent": "MORE HOOKS AND EXPLOSIONS!", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 223473, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/CAT/RuleHolder2/Rule2", "oldContent": "When buying the FREE SPINS round, on the triggering spin 3, 4 or 5 SCATTERS can hit randomly.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 223474, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder4/Label4", "oldContent": "- START FROM LEVEL 2 - The round starts from level 2 in the progressive feature.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 223475, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder5/Label5", "oldContent": "- +2 SPINS - The subsequent round starts with 2 more free spins from the beginning and 2 more spins are added to every retrigger.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 223476, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/RuleHolder1/Rule1", "oldContent": "Hit 3 or more SCATTER symbols to trigger the FREE SPINS feature.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 223477, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/RulesTop/AllValuesExpressed", "oldContent": "All values are expressed as actual wins in coins.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 223478, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/Volatility/VolatilityDescription", "oldContent": "High volatility games pay out less often on average but the chance to hit big wins in a short time span is higher", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 223479, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/MoneySymbolHolder/DescriptionHolder/MoneySymbolRules/Label3", "oldContent": "The fish paying symbols are also MONEY symbols. At every spin, the fish take a random money value which can be won during the FREE SPINS feature.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 223480, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/RulesBottom/RTP/TheoreticalRTP/Label", "oldContent": "The theoretical RTP of this game is {0}%", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 223481, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder8/Rule8", "oldContent": "Also randomly, when there are fisherman symbols on the screen but no fish, at the end of a free spin, a bazooka animation can appear and change all the symbols from the screen, except for fisherman symbols to something else.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 223482, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "extraPayloads": [], "path": "Paytable/Pages/Page1/Rules/AllSymbolsPayLabel", "oldContent": "All symbols pay from left to right on adjacent reels starting from the leftmost reel.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 223483, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder5/Rule5", "oldContent": "After the fourth level, the feature cannot be retriggered anymore.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 223484, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/FreeSpinsHolder2/SpecialReelsHolder/SpecialReels", "oldContent": "Special reels are in play during the feature.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 223485, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/CAT/RuleHolder1/Rule1", "oldContent": "The FREE SPINS round can be instantly triggered from the base game by buying it for 100x current total bet.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 223486, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/RuleHolder2/Rule2", "oldContent": "In the base game whenever 2 SCATTER symbols hit without a third, there is a chance for another one to be brought onto the screen by a random feature:", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 223487, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder1/Label1", "oldContent": "- MORE FISH - More fish symbols are present on the reel strips during the subsequent free spins round", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 223488, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder2/Rule2", "oldContent": "All the WILD symbols that hit during the feature are collected until the end of the round.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 223489, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/RulesTop/AllSymbolsPay", "oldContent": "All symbols pay from left to right on selected paylines.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 223490, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder3/Label3", "oldContent": "- MORE DYNAMITES, HOOKS AND BAZOOKAS - During the round, the chance to hit dynamite, hook or bazooka spin feature is increased.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 223491, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/RulesBottom/SpaceAndEnter", "oldContent": "SPACE and ENTER buttons on the keyboard can be used to start and stop the spin.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 223492, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder7/Rule7", "oldContent": "Randomly, when there are fish symbols on the screen but no fisherman, at the end of a free spin, a hook will appear pulling a random reel up to bring fisherman symbols onto the screen.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 223493, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder1/Rule1", "oldContent": "During the FREE SPINS feature each WILD symbol also collects all the values from MONEY symbols on the screen.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 223494, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/MaxWin/TitleHolder/Title", "oldContent": "MAX WIN", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 223495, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder1/LabelHolder2/Label2", "oldContent": "4x SCATTER awards 15 free spins", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 223496, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/MinMaxHolder/MaxBet/MaximumText", "oldContent": "MAXIMUM BET:", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 223497, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FreeSpinsHolder1/TitleHolder/Title", "oldContent": "FREE SPINS", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 223498, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/MoneySymbolHolder/TitleHolder/Title", "oldContent": "MONEY SYMBOL", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 223499, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "extraPayloads": [], "path": "Paytable/Pages/Page1/WildHolder/DescriptionHolder/Label1", "oldContent": "This is the WILD symbol.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 223500, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "extraPayloads": [], "path": "Paytable/Pages/Page1/Title/PaytableTitleLabel1", "oldContent": "GAME RULES", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 223501, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/CAT/TitleHolder/Title1New", "oldContent": "BUY FREE SPINS", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 223502, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/MinMaxHolder/MinBet/MinimumText", "oldContent": "MINIMUM BET:", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 223503, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/RulesBottom/MalfunctionLabel", "oldContent": "Malfunction voids all pays and plays.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 223504, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "extraPayloads": [], "path": "Paytable/Pages/Page1/ScatterHolder/DescriptionHolder/Label2", "oldContent": "It appears on all reels.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 223505, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder1/LabelHolder1/Label1", "oldContent": "5x SCATTER awards 20 free spins", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 223506, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/RulesTop/AllWinsMultiplied", "oldContent": "All wins are multiplied by bet per line.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 223507, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "extraPayloads": [], "path": "Paytable/Pages/Page1/ScatterHolder/DescriptionHolder/Label1", "oldContent": "This is the SCATTER symbol.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 223508, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/RulesTop/OnlyTheHighestWin", "oldContent": "Only the highest win is paid per line.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 223509, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder1/LabelHolder3/Label3", "oldContent": "3x SCATTER awards 10 free spins", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 223510, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder2/LabelHolder1/Label1", "oldContent": "- Randomly, if the SCATTERS on the screen can move down one position without leaving the reel area, a respin is triggered where the reels with SCATTERS move one position down and the reels without SCATTERS respin.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 223511, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder4/Rule4", "oldContent": "The retriggered spins are played after the previous batch of free spins ends. The multiplier applies to the retriggered spins.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 223512, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/RulesTop/WhenWinningOnMultiplePaylines", "oldContent": "When winning on multiple paylines, all wins are added to the total win.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 223513, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/Title/PaytableTitleLabel", "oldContent": "GAME RULES", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 223514, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/MaxWin/RuleHolder/Label1", "oldContent": "The maximum win amount is limited to {0}x bet. If the total win of a FREE SPINS ROUND reaches {1}x the round immediately ends, win is awarded and all remaining free spins are forfeited", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 223515, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/RulesBottom/RTP/TheoreticalRTPBONUS/Label", "oldContent": "The RTP of the game when using \"BUY FREE SPINS\" is {0}%", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 223516, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/Volatility/VolatilityMeter/LabelHolder/VolatilityLabel", "oldContent": "VOLATILITY", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 223517, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder6/Rule6", "oldContent": "Randomly, when there are fisherman symbols on the screen but no fish, at the end of a free spin, fish MONEY symbols can appear in random positions via the dynamite spin feature.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 223518, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder2/LabelHolder2/Label2", "oldContent": "- Randomly, a hook can pull one of the reels up to reveal another SCATTER.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 223519, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/RuleHolder3/Rule3", "oldContent": "Before the round starts, 0 to 5 modifiers that apply to the subsequent round are randomly selected:", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 223520, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "extraPayloads": [], "path": "Paytable/Pages/Page1/WildHolder/DescriptionHolder/Label2", "oldContent": "It appears on all reels during the FREE SPINS round.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 223521, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "extraPayloads": [], "path": "Paytable/Pages/Page1/WildHolder/DescriptionHolder/Label3", "oldContent": "Substitutes for all symbols except SCATTER.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 223522, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder3/Rule3", "oldContent": "Every 4th WILD symbol collected retriggers the feature, awards 10 more free spins and the multiplier for MONEY symbol collection increases to 2x for the second level, 3x for the third level and 10x for the fourth level.  ", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 223523, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder2/Label2", "oldContent": "- MORE FISHERMAN - More WILD symbols are present on the reel strips during the subsequent free spins round", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 223524, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "extraPayloads": [], "path": "IntroScreen/content/Labels_Holder_landscape/Label_Holder_bigger_1/Label_2 (1)", "oldContent": "BIG FREE SPINS MODIFIERS!", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 223525, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "extraPayloads": [], "path": "IntroScreen/content/Labels_Holder_landscape/Label_Holder_bigger_1/Label_1", "oldContent": "GO FISHIN' FOR", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 223526, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "extraPayloads": [], "path": "IntroScreen/content/IntroButtons/ButtonSkipIntro/content/TextHolder/Label_1", "oldContent": "DON'T SHOW NEXT TIME", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 223527, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/MoneySymbolHolder/DescriptionHolder/PossibleValues/Label", "oldContent": "Possible values are: 2x, 5x, 10x, 15x, 20x, 25x, 50x, 100x, 200x, 500x, 1666x, 2500x or 5000x total bet.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 223528, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/MaxWin/RuleHolder/HolderLabelJackpot/Label1New", "oldContent": "The maximum win amount is limited to {0}x bet except Jack<PERSON>. If the total win of a FREE SPINS round reaches {1}x bet the round immediately ends, win is awarded and all remaining free spins are forfeited.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}], "Spines": [], "revisionNumber": 0}, "fileID": 223529}], "fileID": 223530}, {"name": "Game/GamePivot/FSWONWindow/content/FreespinsWonWindow/LabelsAndSprites/Catches_label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 223441, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "children": [], "psr": "d"}, "fileID": 223354}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "afad9d7612480f543ad2b6d9ce2b351e"}, "_text": "抓到", "fontSize": 50, "width": 338, "height": 110, "overflow": 0}, "fileID": 223442}], "fileID": 223531}, {"name": "Game/GamePivot/FreeSpinsPurchase/BuyButtons/Options/Option_0/OptVisual/Texts/BoughtText/LandscapeText/label1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 223441, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "children": [], "psr": "d"}, "fileID": 223355}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "afad9d7612480f543ad2b6d9ce2b351e"}, "_text": "激活", "fontSize": 40, "width": 170, "height": 80, "overflow": 0}, "fileID": 223443}], "fileID": 223532}, {"name": "Game/GamePivot/FSWONWindow/PressAnywhere_Label/label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 223441, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "children": [], "psr": "d"}, "fileID": 223356}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "afad9d7612480f543ad2b6d9ce2b351e"}, "_text": "按任意位置以继续", "fontSize": 40, "width": 1340, "height": 65, "overflow": 0}, "fileID": 223444}], "fileID": 223533}, {"name": "Game/GamePivot/FSExtraWindow/content/PressAnywhere_Label/label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 223441, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "children": [], "psr": "d"}, "fileID": 223357}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "afad9d7612480f543ad2b6d9ce2b351e"}, "_text": "按任意位置以继续", "fontSize": 40, "width": 1340, "height": 65, "overflow": 0}, "fileID": 223445}], "fileID": 223534}, {"name": "Game/GamePivot/FreeSpinsPurchase/FSPurchaseWindow/Content/AnimatedPivot/Texts/BuyText/label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 223441, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "children": [], "psr": "d"}, "fileID": 223358}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "afad9d7612480f543ad2b6d9ce2b351e"}, "_text": "购买\n免费旋转", "fontSize": 69, "width": 826, "height": 69, "overflow": 0}, "fileID": 223446}], "fileID": 223535}, {"name": "Game/GamePivot/FsFeature/content/Feature_trail/Holder/fisherman_highlight/ScalerX10/stretcher_fs/spins", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 223441, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "children": [], "psr": "d"}, "fileID": 223359}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "afad9d7612480f543ad2b6d9ce2b351e"}, "_text": "旋转", "fontSize": 30, "width": 64, "height": 33, "overflow": 0}, "fileID": 223447}], "fileID": 223536}, {"name": "Game/Background/PaytableOnScreen/Portrait/Message1/labelMsg1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 223441, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "children": [], "psr": "d"}, "fileID": 223360}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "afad9d7612480f543ad2b6d9ce2b351e"}, "_text": "奖励按从左到右顺序支付。 Bonus在任何位置均赔付。", "fontSize": 40, "width": 970, "height": 40, "alignment": 2}, "fileID": 223448}], "fileID": 223537}, {"name": "Game/GamePivot/FsFeature/content/Feature_trail/Holder/fisherman_highlight/ScalerX3/stretcher_fs/free", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 223441, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "children": [], "psr": "d"}, "fileID": 223361}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "afad9d7612480f543ad2b6d9ce2b351e"}, "_text": "免费", "fontSize": 30, "width": 60, "height": 33, "overflow": 0}, "fileID": 223449}], "fileID": 223538}, {"name": "Game/GamePivot/Reels/ThePivot/BonusMessages/Fisherman/Labels/uilabel", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 223441, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "children": [], "psr": "d"}, "fileID": 223362}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "afad9d7612480f543ad2b6d9ce2b351e"}, "_text": "更多渔夫！", "fontSize": 256, "width": 1000, "height": 450, "overflow": 0}, "fileID": 223450}], "fileID": 223539}, {"name": "Game/GamePivot/FreeSpinsPurchase/BuyButtons/Options/Option_0/OptVisual/Texts/BoughtText/LandscapeText/label0", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 223441, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "children": [], "psr": "d"}, "fileID": 223363}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "afad9d7612480f543ad2b6d9ce2b351e"}, "_text": "功能", "fontSize": 40, "width": 170, "height": 80, "overflow": 0}, "fileID": 223451}], "fileID": 223540}, {"name": "Game/GamePivot/FreeSpinsPurchase/BuyButtons/Options/Option_0/OptVisual/Texts/BuyText/PortraitText/label0", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 223441, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "children": [], "psr": "d"}, "fileID": 223364}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "afad9d7612480f543ad2b6d9ce2b351e"}, "_text": "购买\n免费旋转", "fontSize": 26, "width": 270, "height": 70, "overflow": 0}, "fileID": 223452}], "fileID": 223541}, {"name": "Game/GamePivot/FsFeature/content/Feature_trail/Holder/fisherman_highlight/ScalerX2/stretcher_fs/free", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 223441, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "children": [], "psr": "d"}, "fileID": 223365}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "afad9d7612480f543ad2b6d9ce2b351e"}, "_text": "免费", "fontSize": 30, "width": 60, "height": 33, "overflow": 0}, "fileID": 223453}], "fileID": 223542}, {"name": "Game/GamePivot/FSResultWindow/content/SignPivot/FreespinsCongratsWindow/Labels/Congrats_label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 223441, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "children": [], "psr": "d"}, "fileID": 223366}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "afad9d7612480f543ad2b6d9ce2b351e"}, "_text": "恭喜!", "fontSize": 169, "width": 1000, "height": 70, "overflow": 0}, "fileID": 223454}], "fileID": 223543}, {"name": "Game/GamePivot/FSExtraWindow/content/SignPivot/FreespinsExtraWindow/Labels/Congrats_label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 223441, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "children": [], "psr": "d"}, "fileID": 223367}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "afad9d7612480f543ad2b6d9ce2b351e"}, "_text": "恭喜!", "fontSize": 169, "width": 1000, "height": 70, "overflow": 0}, "fileID": 223455}], "fileID": 223544}, {"name": "Game/GamePivot/FSExtraWindow/content/SignPivot/FreespinsExtraWindow/Labels/FreeSpins_label ", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 223441, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "children": [], "psr": "d"}, "fileID": 223368}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "afad9d7612480f543ad2b6d9ce2b351e"}, "_text": "次免费旋转", "fontSize": 75, "width": 1000, "height": 169, "overflow": 0}, "fileID": 223456}], "fileID": 223545}, {"name": "Game/GamePivot/FSExtraWindow/content/SignPivot/FreespinsExtraWindow/Labels/AreNow_label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 223441, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "children": [], "psr": "d"}, "fileID": 223369}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "afad9d7612480f543ad2b6d9ce2b351e"}, "_text": "现在成了", "fontSize": 70, "width": 500, "height": 169, "overflow": 0}, "fileID": 223457}], "fileID": 223546}, {"name": "Game/GamePivot/FreeSpinsPurchase/BuyButtons/Options/Option_0/OptVisual/Texts/BuyText/LandscapeTest/label0", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 223441, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "children": [], "psr": "d"}, "fileID": 223370}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "afad9d7612480f543ad2b6d9ce2b351e"}, "_text": "购买 免费旋转", "fontSize": 26, "width": 270, "height": 70, "overflow": 0}, "fileID": 223458}], "fileID": 223547}, {"name": "Game/GamePivot/Reels/ThePivot/BonusMessages/PlusFS/Labels/uilabel", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 223441, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "children": [], "psr": "d"}, "fileID": 223371}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "afad9d7612480f543ad2b6d9ce2b351e"}, "_text": "额外 次免费旋转", "fontSize": 256, "width": 1200, "height": 450, "overflow": 0}, "fileID": 223459}], "fileID": 223548}, {"name": "Game/GamePivot/FsFeature/content/Feature_trail/Holder/fisherman_highlight/ScalerX10/stretcher_fs/free", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 223441, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "children": [], "psr": "d"}, "fileID": 223372}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "afad9d7612480f543ad2b6d9ce2b351e"}, "_text": "免费", "fontSize": 30, "width": 60, "height": 33, "overflow": 0}, "fileID": 223460}], "fileID": 223549}, {"name": "Game/GamePivot/FSWONWindow/content/FreespinsWonWindow/LabelsAndSprites/Congratulations_label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 223441, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "children": [], "psr": "d"}, "fileID": 223373}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "afad9d7612480f543ad2b6d9ce2b351e"}, "_text": "恭喜!", "fontSize": 169, "anchorY": 1, "width": 500, "height": 70, "overflow": 0}, "fileID": 223461}], "fileID": 223550}, {"name": "Game/GamePivot/Reels/ThePivot/BonusMessages/Fishes/Labels/uilabel", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 223441, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "children": [], "psr": "d"}, "fileID": 223374}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "afad9d7612480f543ad2b6d9ce2b351e"}, "_text": "更多大鱼！", "fontSize": 256, "width": 1200, "height": 450, "overflow": 0}, "fileID": 223462}], "fileID": 223551}, {"name": "Game/GamePivot/FreeSpinsPurchase/BuyButtons/Options/Option_0/OptVisual/Texts/BoughtText/PortraitText/label0", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 223441, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "children": [], "psr": "d"}, "fileID": 223375}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "afad9d7612480f543ad2b6d9ce2b351e"}, "_text": "功能 激活", "fontSize": 60, "width": 164, "height": 208, "overflow": 0}, "fileID": 223463}], "fileID": 223552}, {"name": "Game/GamePivot/FSExtraWindow/content/SignPivot/FreespinsExtraWindow/Labels/Extra_label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 223441, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "children": [], "psr": "d"}, "fileID": 223376}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "afad9d7612480f543ad2b6d9ce2b351e"}, "_text": "额外", "fontSize": 75, "width": 1000, "height": 169, "overflow": 0}, "fileID": 223464}], "fileID": 223553}, {"name": "Game/GamePivot/FSResultWindow/content/SignPivot/FreespinsCongratsWindow/Labels/YouWon_label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 223441, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "children": [], "psr": "d"}, "fileID": 223377}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "afad9d7612480f543ad2b6d9ce2b351e"}, "_text": "您贏了", "fontSize": 110, "width": 1000, "height": 169, "overflow": 0}, "fileID": 223465}], "fileID": 223554}, {"name": "Game/GamePivot/FsFeature/content/Feature_trail/Holder/fisherman_highlight/ScalerX3/stretcher_fs/spins", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 223441, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "children": [], "psr": "d"}, "fileID": 223378}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "afad9d7612480f543ad2b6d9ce2b351e"}, "_text": "旋转", "fontSize": 30, "width": 64, "height": 33, "overflow": 0}, "fileID": 223466}], "fileID": 223555}, {"name": "Game/Background/PaytableOnScreen/Landscape/Message1/labelMsg1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 223441, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "children": [], "psr": "d"}, "fileID": 223379}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "afad9d7612480f543ad2b6d9ce2b351e"}, "_text": "奖励按从左到右顺序支付。 Bonus在任何位置均赔付。", "fontSize": 30, "width": 728, "height": 30, "alignment": 2}, "fileID": 223467}], "fileID": 223556}, {"name": "Game/GamePivot/FsFeature/content/Feature_trail/Holder/fisherman_highlight/ScalerX2/stretcher_fs/spins", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 223441, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "children": [], "psr": "d"}, "fileID": 223380}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "afad9d7612480f543ad2b6d9ce2b351e"}, "_text": "旋转", "fontSize": 30, "width": 64, "height": 33, "overflow": 0}, "fileID": 223468}], "fileID": 223557}, {"name": "Game/GamePivot/Reels/ThePivot/BonusMessages/Level2/Labels/uilabel", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 223441, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "children": [], "psr": "d"}, "fileID": 223381}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "afad9d7612480f543ad2b6d9ce2b351e"}, "_text": "从第2关开始！", "fontSize": 256, "width": 1280, "height": 450, "overflow": 0}, "fileID": 223469}], "fileID": 223558}, {"name": "Game/GamePivot/FSWONWindow/content/FreespinsWonWindow/LabelsAndSprites/FreeSpins_label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 223441, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "children": [], "psr": "d"}, "fileID": 223382}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "afad9d7612480f543ad2b6d9ce2b351e"}, "_text": "次免费旋转", "fontSize": 110, "width": 1000, "height": 150, "overflow": 0}, "fileID": 223470}], "fileID": 223559}, {"name": "Game/GamePivot/FSResultWindow/content/PressAnywhere_Label/label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 223441, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "children": [], "psr": "d"}, "fileID": 223383}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "afad9d7612480f543ad2b6d9ce2b351e"}, "_text": "按任意位置以继续", "fontSize": 40, "width": 1340, "height": 65, "overflow": 0}, "fileID": 223471}], "fileID": 223560}, {"name": "Game/GamePivot/Reels/ThePivot/BonusMessages/Hooks/Labels/uilabel", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 223441, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "children": [], "psr": "d"}, "fileID": 223384}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "afad9d7612480f543ad2b6d9ce2b351e"}, "_text": "更多鱼钩和炸药！", "fontSize": 256, "width": 1000, "height": 450, "overflow": 0}, "fileID": 223472}], "fileID": 223561}, {"name": "Paytable/Pages/Page3/CAT/RuleHolder2/Rule2", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 223441, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "children": [], "psr": "d"}, "fileID": 223385}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "afad9d7612480f543ad2b6d9ce2b351e"}, "_text": "购买免费旋转回合时，在触发旋转中会随机出现3、4或5个SCATTER。", "fontSize": 25, "anchorY": 0, "width": 1400, "height": 75, "overflow": 0, "spacingY": 5}, "fileID": 223473}], "fileID": 223562}, {"name": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder4/Label4", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 223441, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "children": [], "psr": "d"}, "fileID": 223386}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "afad9d7612480f543ad2b6d9ce2b351e"}, "_text": "从第2关开始 - 本回合从累积功能的第二关开始。", "fontSize": 25, "anchorX": 0, "width": 1400, "height": 100, "overflow": 0}, "fileID": 223474}], "fileID": 223563}, {"name": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder5/Label5", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 223441, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "children": [], "psr": "d"}, "fileID": 223387}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "afad9d7612480f543ad2b6d9ce2b351e"}, "_text": "- +2次旋转 - 后续回合从一开始就会增加2次免费旋转，并且每次重新触发都会增加2次旋转。", "fontSize": 25, "anchorX": 0, "width": 1347, "height": 100, "overflow": 0}, "fileID": 223475}], "fileID": 223564}, {"name": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/RuleHolder1/Rule1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 223441, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "children": [], "psr": "d"}, "fileID": 223388}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "afad9d7612480f543ad2b6d9ce2b351e"}, "_text": "出现3个或更多SCATTER符号可触发免费旋转功能。", "fontSize": 25, "width": 1400, "height": 100, "overflow": 0}, "fileID": 223476}], "fileID": 223565}, {"name": "Paytable/Pages/Page4/RulesTop/AllValuesExpressed", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 223441, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "children": [], "psr": "d"}, "fileID": 223389}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "afad9d7612480f543ad2b6d9ce2b351e"}, "_text": "所有数值均为实际奖金的硬币数。", "fontSize": 25, "width": 1333, "height": 160, "overflow": 0}, "fileID": 223477}], "fileID": 223566}, {"name": "Paytable/Pages/Page4/Volatility/VolatilityDescription", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 223441, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "children": [], "psr": "d"}, "fileID": 223390}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "afad9d7612480f543ad2b6d9ce2b351e"}, "_text": "波动性较高的游戏通常较少赔付，但在短时间内赢取大奖的几率更高", "fontSize": 25, "anchorY": 0, "width": 1368, "height": 93, "overflow": 0}, "fileID": 223478}], "fileID": 223567}, {"name": "Paytable/Pages/Page2/MoneySymbolHolder/DescriptionHolder/MoneySymbolRules/Label3", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 223441, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "children": [], "psr": "d"}, "fileID": 223391}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "afad9d7612480f543ad2b6d9ce2b351e"}, "_text": "大鱼赔付符号也是现金符号。每次旋转时，大鱼都会带有一个随机现金数值，\n可在免费旋转功能期间赢取。", "fontSize": 25, "anchorX": 0, "width": 1339, "height": 100, "overflow": 0}, "fileID": 223479}], "fileID": 223568}, {"name": "Paytable/Pages/Page4/RulesBottom/RTP/TheoreticalRTP/Label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 223441, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "children": [], "psr": "d"}, "fileID": 223392}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "afad9d7612480f543ad2b6d9ce2b351e"}, "_text": "本游戏的理论RTP为 {0}%", "fontSize": 25, "anchorY": 1, "width": 1200, "height": 75, "overflow": 0}, "fileID": 223480}], "fileID": 223569}, {"name": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder8/Rule8", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 223441, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "children": [], "psr": "d"}, "fileID": 223393}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "afad9d7612480f543ad2b6d9ce2b351e"}, "_text": "同样，当屏幕上只有渔夫符号而没有大鱼时，在免费旋转结束时会随机出现火箭筒动画，从而将屏幕上除渔夫符号之外的所有符号都变成其他符号。", "fontSize": 25, "width": 1400, "height": 100, "overflow": 0}, "fileID": 223481}], "fileID": 223570}, {"name": "Paytable/Pages/Page1/Rules/AllSymbolsPayLabel", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 223441, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "children": [], "psr": "d"}, "fileID": 223394}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "afad9d7612480f543ad2b6d9ce2b351e"}, "_text": "从最左侧的转轴起临近转轴上所有符号从左向右支付。", "fontSize": 25, "width": 1000, "height": 60, "overflow": 0}, "fileID": 223482}], "fileID": 223571}, {"name": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder5/Rule5", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 223441, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "children": [], "psr": "d"}, "fileID": 223395}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "afad9d7612480f543ad2b6d9ce2b351e"}, "_text": "达到第四级后，功能将无法再次触发。", "fontSize": 25, "width": 1400, "height": 100, "overflow": 0}, "fileID": 223483}], "fileID": 223572}, {"name": "Paytable/Pages/Page3/FreeSpinsHolder2/SpecialReelsHolder/SpecialReels", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 223441, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "children": [], "psr": "d"}, "fileID": 223396}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "afad9d7612480f543ad2b6d9ce2b351e"}, "_text": "功能期间会出现特殊转轴。", "fontSize": 25, "width": 1400, "height": 100, "overflow": 0}, "fileID": 223484}], "fileID": 223573}, {"name": "Paytable/Pages/Page3/CAT/RuleHolder1/Rule1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 223441, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "children": [], "psr": "d"}, "fileID": 223397}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "afad9d7612480f543ad2b6d9ce2b351e"}, "_text": "在基本游戏中以100x当前总赌注进行购买即可马上触发免费旋转回合。", "fontSize": 25, "anchorY": 1, "width": 1400, "height": 100, "overflow": 0}, "fileID": 223485}], "fileID": 223574}, {"name": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/RuleHolder2/Rule2", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 223441, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "children": [], "psr": "d"}, "fileID": 223398}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "afad9d7612480f543ad2b6d9ce2b351e"}, "_text": "在基本游戏中，只要出现2个SCATTER符号但尚未出现第三个，即有机会通过随机功能将另一个符号带到屏幕上：", "fontSize": 25, "width": 1169, "height": 100, "overflow": 0}, "fileID": 223486}], "fileID": 223575}, {"name": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder1/Label1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 223441, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "children": [], "psr": "d"}, "fileID": 223399}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "afad9d7612480f543ad2b6d9ce2b351e"}, "_text": "- 更多大鱼 - 在随后的免费旋转回合中，轴条上会出现更多大鱼符号。", "fontSize": 25, "anchorX": 0, "width": 1400, "height": 100, "overflow": 0}, "fileID": 223487}], "fileID": 223576}, {"name": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder2/Rule2", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 223441, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "children": [], "psr": "d"}, "fileID": 223400}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "afad9d7612480f543ad2b6d9ce2b351e"}, "_text": "功能期间出现的所有WILD符号都会被收集直至回合结束。", "fontSize": 25, "width": 1400, "height": 100, "overflow": 0}, "fileID": 223488}], "fileID": 223577}, {"name": "Paytable/Pages/Page4/RulesTop/AllSymbolsPay", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 223441, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "children": [], "psr": "d"}, "fileID": 223401}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "afad9d7612480f543ad2b6d9ce2b351e"}, "_text": "奖励按从左到右顺序支付", "fontSize": 25, "width": 1194, "height": 160, "overflow": 0, "spacingY": 5}, "fileID": 223489}], "fileID": 223578}, {"name": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder3/Label3", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 223441, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "children": [], "psr": "d"}, "fileID": 223402}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "afad9d7612480f543ad2b6d9ce2b351e"}, "_text": "- 更多炸药、鱼钩和火箭筒 - 在回合期间，有更大几率出现炸药、鱼钩，或者增加火箭筒旋转功能次数。", "fontSize": 25, "anchorX": 0, "width": 1359, "height": 100, "overflow": 0}, "fileID": 223490}], "fileID": 223579}, {"name": "Paytable/Pages/Page4/RulesBottom/SpaceAndEnter", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 223441, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "children": [], "psr": "d"}, "fileID": 223403}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "afad9d7612480f543ad2b6d9ce2b351e"}, "_text": "键盘上的空格和回车按钮可用于开始和停止旋转。", "fontSize": 25, "width": 1400, "height": 60, "overflow": 0}, "fileID": 223491}], "fileID": 223580}, {"name": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder7/Rule7", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 223441, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "children": [], "psr": "d"}, "fileID": 223404}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "afad9d7612480f543ad2b6d9ce2b351e"}, "_text": "如果屏幕上出现了大鱼但没有渔夫符号，那么在免费旋转结束时，出现的鱼钩会钓起一个随机转轴，从而把渔夫符号带到屏幕上。", "fontSize": 25, "width": 1384, "height": 100, "overflow": 0}, "fileID": 223492}], "fileID": 223581}, {"name": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder1/Rule1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 223441, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "children": [], "psr": "d"}, "fileID": 223405}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "afad9d7612480f543ad2b6d9ce2b351e"}, "_text": "在免费旋转功能期间，每个WILD符号还会收集屏幕上现金符号的所有数值。", "fontSize": 25, "width": 1400, "height": 100, "overflow": 0}, "fileID": 223493}], "fileID": 223582}, {"name": "Paytable/Pages/Page3/MaxWin/TitleHolder/Title", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 223441, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "children": [], "psr": "d"}, "fileID": 223406}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "afad9d7612480f543ad2b6d9ce2b351e"}, "_text": "最大奖金", "fontSize": 35, "width": 1100, "height": 100, "overflow": 0}, "fileID": 223494}], "fileID": 223583}, {"name": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder1/LabelHolder2/Label2", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 223441, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "children": [], "psr": "d"}, "fileID": 223407}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "afad9d7612480f543ad2b6d9ce2b351e"}, "_text": "4x个SCATTER符号奖励15次免费旋转。", "fontSize": 25, "width": 1400, "height": 75, "overflow": 0}, "fileID": 223495}], "fileID": 223584}, {"name": "Paytable/Pages/Page4/MinMaxHolder/MaxBet/MaximumText", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 223441, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "children": [], "psr": "d"}, "fileID": 223408}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "afad9d7612480f543ad2b6d9ce2b351e"}, "_text": "最大赌注：", "fontSize": 25, "anchorX": 0, "width": 126, "height": 26}, "fileID": 223496}], "fileID": 223585}, {"name": "Paytable/Pages/Page2/FreeSpinsHolder1/TitleHolder/Title", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 223441, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "children": [], "psr": "d"}, "fileID": 223409}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "afad9d7612480f543ad2b6d9ce2b351e"}, "_text": "次免费旋转", "fontSize": 35, "width": 1150, "height": 100, "overflow": 0}, "fileID": 223497}], "fileID": 223586}, {"name": "Paytable/Pages/Page2/MoneySymbolHolder/TitleHolder/Title", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 223441, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "children": [], "psr": "d"}, "fileID": 223410}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "afad9d7612480f543ad2b6d9ce2b351e"}, "_text": "现金符号", "fontSize": 35, "width": 1150, "height": 70, "overflow": 0}, "fileID": 223498}], "fileID": 223587}, {"name": "Paytable/Pages/Page1/WildHolder/DescriptionHolder/Label1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 223441, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "children": [], "psr": "d"}, "fileID": 223411}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "afad9d7612480f543ad2b6d9ce2b351e"}, "_text": "这是WILD符号。", "fontSize": 25, "anchorX": 0, "anchorY": 1, "width": 363, "height": 75, "overflow": 0}, "fileID": 223499}], "fileID": 223588}, {"name": "Paytable/Pages/Page1/Title/PaytableTitleLabel1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 223441, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "children": [], "psr": "d"}, "fileID": 223412}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "afad9d7612480f543ad2b6d9ce2b351e"}, "_text": "游戏规则", "fontSize": 35, "width": 1150, "height": 100, "overflow": 0}, "fileID": 223500}], "fileID": 223589}, {"name": "Paytable/Pages/Page3/CAT/TitleHolder/Title1New", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 223441, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "children": [], "psr": "d"}, "fileID": 223413}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "afad9d7612480f543ad2b6d9ce2b351e"}, "_text": "购买\n免费旋转", "fontSize": 35, "width": 1400, "height": 70, "overflow": 0}, "fileID": 223501}], "fileID": 223590}, {"name": "Paytable/Pages/Page4/MinMaxHolder/MinBet/MinimumText", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 223441, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "children": [], "psr": "d"}, "fileID": 223414}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "afad9d7612480f543ad2b6d9ce2b351e"}, "_text": "最小赌注：", "fontSize": 25, "anchorX": 0, "width": 126, "height": 26}, "fileID": 223502}], "fileID": 223591}, {"name": "Paytable/Pages/Page4/RulesBottom/MalfunctionLabel", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 223441, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "children": [], "psr": "d"}, "fileID": 223415}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "afad9d7612480f543ad2b6d9ce2b351e"}, "_text": "系统故障期间所有游戏及赔付无效。", "fontSize": 25, "width": 888, "height": 60, "overflow": 0}, "fileID": 223503}], "fileID": 223592}, {"name": "Paytable/Pages/Page1/ScatterHolder/DescriptionHolder/Label2", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 223441, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "children": [], "psr": "d"}, "fileID": 223416}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "afad9d7612480f543ad2b6d9ce2b351e"}, "_text": "它会出现在所有转轴上。", "fontSize": 25, "anchorX": 0, "anchorY": 0, "width": 357, "height": 75, "overflow": 0}, "fileID": 223504}], "fileID": 223593}, {"name": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder1/LabelHolder1/Label1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 223441, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "children": [], "psr": "d"}, "fileID": 223417}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "afad9d7612480f543ad2b6d9ce2b351e"}, "_text": "5x个SCATTER符号奖励20次免费旋转。", "fontSize": 25, "width": 1400, "height": 75, "overflow": 0}, "fileID": 223505}], "fileID": 223594}, {"name": "Paytable/Pages/Page4/RulesTop/AllWinsMultiplied", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 223441, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "children": [], "psr": "d"}, "fileID": 223418}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "afad9d7612480f543ad2b6d9ce2b351e"}, "_text": "所赢奖励将会与每条线的投注相乘", "fontSize": 25, "width": 1194, "height": 160, "overflow": 0, "spacingY": 5}, "fileID": 223506}], "fileID": 223595}, {"name": "Paytable/Pages/Page1/ScatterHolder/DescriptionHolder/Label1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 223441, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "children": [], "psr": "d"}, "fileID": 223419}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "afad9d7612480f543ad2b6d9ce2b351e"}, "_text": "这是SCATTER符号。 ", "fontSize": 25, "anchorX": 0, "anchorY": 1, "width": 357, "height": 75, "overflow": 0}, "fileID": 223507}], "fileID": 223596}, {"name": "Paytable/Pages/Page4/RulesTop/OnlyTheHighestWin", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 223441, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "children": [], "psr": "d"}, "fileID": 223420}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "afad9d7612480f543ad2b6d9ce2b351e"}, "_text": "每条派彩线上仅仅支付最高奖金。", "fontSize": 25, "anchorY": 0, "width": 1400, "height": 50, "overflow": 0}, "fileID": 223508}], "fileID": 223597}, {"name": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder1/LabelHolder3/Label3", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 223441, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "children": [], "psr": "d"}, "fileID": 223421}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "afad9d7612480f543ad2b6d9ce2b351e"}, "_text": "3x个SCATTER符号奖励10次免费旋转。", "fontSize": 25, "width": 1400, "height": 75, "overflow": 0}, "fileID": 223509}], "fileID": 223598}, {"name": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder2/LabelHolder1/Label1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 223441, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "children": [], "psr": "d"}, "fileID": 223422}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "afad9d7612480f543ad2b6d9ce2b351e"}, "_text": "- 如果屏幕上的SCATTER可以向下移动一个位置而不会从转轴区域消失，则会触发一次重新旋转，带有SCATTER的转轴会向下移动一个位置，而没有SCATTER的转轴将重新旋转。", "fontSize": 25, "anchorX": 0, "width": 1400, "height": 125, "overflow": 0}, "fileID": 223510}], "fileID": 223599}, {"name": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder4/Rule4", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 223441, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "children": [], "psr": "d"}, "fileID": 223423}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "afad9d7612480f543ad2b6d9ce2b351e"}, "_text": "重新触发的旋转在上一批免费旋转结束后进行。乘数适用于重新触发的旋转。", "fontSize": 25, "width": 920, "height": 125, "overflow": 0}, "fileID": 223511}], "fileID": 223600}, {"name": "Paytable/Pages/Page4/RulesTop/WhenWinningOnMultiplePaylines", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 223441, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "children": [], "psr": "d"}, "fileID": 223424}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "afad9d7612480f543ad2b6d9ce2b351e"}, "_text": "如果多条支付线上有赢奖，则所有赢奖都将加入到总赢奖中。", "fontSize": 25, "anchorY": 0, "width": 1400, "height": 50, "overflow": 0}, "fileID": 223512}], "fileID": 223601}, {"name": "Paytable/Pages/Page4/Title/PaytableTitleLabel", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 223441, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "children": [], "psr": "d"}, "fileID": 223425}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "afad9d7612480f543ad2b6d9ce2b351e"}, "_text": "游戏规则", "fontSize": 30, "width": 1150, "height": 100, "overflow": 0}, "fileID": 223513}], "fileID": 223602}, {"name": "Paytable/Pages/Page3/MaxWin/RuleHolder/Label1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 223441, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "children": [], "psr": "d"}, "fileID": 223426}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "afad9d7612480f543ad2b6d9ce2b351e"}, "_text": "最高奖金金额限制为{0}x赌注。如果免费旋转回 合的总奖金达到{1}x，回合立即结束并派发奖金，且所有剩余免费旋转次数作废。", "fontSize": 25, "anchorY": 0, "width": 1400, "height": 100, "overflow": 0}, "fileID": 223514}], "fileID": 223603}, {"name": "Paytable/Pages/Page4/RulesBottom/RTP/TheoreticalRTPBONUS/Label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 223441, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "children": [], "psr": "d"}, "fileID": 223427}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "afad9d7612480f543ad2b6d9ce2b351e"}, "_text": "使用“购买免费旋转”时的游戏RTP为{0}%", "fontSize": 25, "anchorY": 0, "width": 1200, "height": 75, "overflow": 0}, "fileID": 223515}], "fileID": 223604}, {"name": "Paytable/Pages/Page4/Volatility/VolatilityMeter/LabelHolder/VolatilityLabel", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 223441, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "children": [], "psr": "d"}, "fileID": 223428}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "afad9d7612480f543ad2b6d9ce2b351e"}, "_text": "波动", "fontSize": 22, "anchorX": 0, "width": 44, "height": 22}, "fileID": 223516}], "fileID": 223605}, {"name": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder6/Rule6", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 223441, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "children": [], "psr": "d"}, "fileID": 223429}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "afad9d7612480f543ad2b6d9ce2b351e"}, "_text": "如果屏幕上出现了渔夫但没有大鱼符号，那么在免费旋转结束时，大鱼现金符号可以通过炸药旋转功能出现在随机位置。", "fontSize": 25, "width": 1271, "height": 100, "overflow": 0}, "fileID": 223517}], "fileID": 223606}, {"name": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder2/LabelHolder2/Label2", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 223441, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "children": [], "psr": "d"}, "fileID": 223430}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "afad9d7612480f543ad2b6d9ce2b351e"}, "_text": "- 随机出现的鱼钩可钓起一个转轴，有机会出现另一个SCATTER。", "fontSize": 25, "anchorX": 0, "width": 1400, "height": 75, "overflow": 0}, "fileID": 223518}], "fileID": 223607}, {"name": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/RuleHolder3/Rule3", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 223441, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "children": [], "psr": "d"}, "fileID": 223431}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "afad9d7612480f543ad2b6d9ce2b351e"}, "_text": "在回合开始之前，会随机选择0到5个适用于后续回合的变换器。", "fontSize": 25, "width": 1400, "height": 100, "overflow": 0}, "fileID": 223519}], "fileID": 223608}, {"name": "Paytable/Pages/Page1/WildHolder/DescriptionHolder/Label2", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 223441, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "children": [], "psr": "d"}, "fileID": 223432}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "afad9d7612480f543ad2b6d9ce2b351e"}, "_text": "它会在免费旋转\n回合期间出现在所有转轴上。", "fontSize": 25, "anchorX": 0, "width": 799, "height": 75, "overflow": 0}, "fileID": 223520}], "fileID": 223609}, {"name": "Paytable/Pages/Page1/WildHolder/DescriptionHolder/Label3", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 223441, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "children": [], "psr": "d"}, "fileID": 223433}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "afad9d7612480f543ad2b6d9ce2b351e"}, "_text": "替代除SCATTER外的所有符号。", "fontSize": 25, "anchorX": 0, "anchorY": 0, "width": 363, "height": 75, "overflow": 0}, "fileID": 223521}], "fileID": 223610}, {"name": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder3/Rule3", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 223441, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "children": [], "psr": "d"}, "fileID": 223434}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "afad9d7612480f543ad2b6d9ce2b351e"}, "_text": "每收集到第4个WILD符号即可重新触发该功能，再奖励10次免费旋转，\n现金符号收集的乘数在第二关将增大为2x，第三关将增大为3x，而第四关将增大为10x。", "fontSize": 25, "width": 1327, "height": 150, "overflow": 0}, "fileID": 223522}], "fileID": 223611}, {"name": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder2/Label2", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 223441, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "children": [], "psr": "d"}, "fileID": 223435}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "afad9d7612480f543ad2b6d9ce2b351e"}, "_text": "- 更多渔夫 - 在随后的免费旋转回合中，轴条上会出现更多WILD符号。", "fontSize": 25, "anchorX": 0, "width": 1400, "height": 100, "overflow": 0}, "fileID": 223523}], "fileID": 223612}, {"name": "IntroScreen/content/Labels_Holder_landscape/Label_Holder_bigger_1/Label_2 (1)", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 223441, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "children": [], "psr": "d"}, "fileID": 223436}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "afad9d7612480f543ad2b6d9ce2b351e"}, "_text": "免费旋转变换器！", "fontSize": 60, "width": 1000, "height": 80, "overflow": 0}, "fileID": 223524}], "fileID": 223613}, {"name": "IntroScreen/content/Labels_Holder_landscape/Label_Holder_bigger_1/Label_1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 223441, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "children": [], "psr": "d"}, "fileID": 223437}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "afad9d7612480f543ad2b6d9ce2b351e"}, "_text": "钓出超大", "fontSize": 60, "width": 1000, "height": 80, "overflow": 0}, "fileID": 223525}], "fileID": 223614}, {"name": "IntroScreen/content/IntroButtons/ButtonSkipIntro/content/TextHolder/Label_1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 223441, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "children": [], "psr": "d"}, "fileID": 223438}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "afad9d7612480f543ad2b6d9ce2b351e"}, "_text": "下次不再显示", "fontSize": 30, "anchorX": 0, "width": 180, "height": 30}, "fileID": 223526}], "fileID": 223615}, {"name": "Paytable/Pages/Page2/MoneySymbolHolder/DescriptionHolder/PossibleValues/Label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 223441, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "children": [], "psr": "d"}, "fileID": 223439}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "afad9d7612480f543ad2b6d9ce2b351e"}, "_text": "可能的数值包括：2x、5x、10x、15x、20x、25x、50x、\n100x、200x、500x、1666x、2500x或5000x总赌注。", "fontSize": 25, "anchorX": 0, "width": 1400, "height": 100, "overflow": 0, "spacingY": 3}, "fileID": 223527}], "fileID": 223616}, {"name": "Paytable/Pages/Page3/MaxWin/RuleHolder/HolderLabelJackpot/Label1New", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 223441, "guid": "b4a47b58421a9ce40b5eff5143ad91d1"}, "children": [], "psr": "d"}, "fileID": 223440}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "afad9d7612480f543ad2b6d9ce2b351e"}, "_text": "最大奖金金额限制为{0}x赌注，但累积奖金除外。\n如果免费旋转回合的总奖金达到{1}x赌注，回合立即结束并派发奖金，所有剩余免费旋转次数作废。", "fontSize": 25, "anchorY": 0, "width": 1400, "height": 100, "overflow": 0}, "fileID": 223528}], "fileID": 223617}]}}, {"type": "Font", "id": "afad9d7612480f543ad2b6d9ce2b351e", "data": {"fontName": "fafad9d7612480f543ad2b6d9ce2b35", "path": "@font-face{font-family:'fafad9d7612480f543ad2b6d9ce2b35';src:url('data:application/x-font-woff;base64,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*******************************+gj5lLQ4EkOnghgebSefXECI+5fIbu96ZqPCLJl5psu/pUdsVbSvcxe1FzSW9JvMwq//yf0RGR59BLWjpzPPJkGPdLRGZJFFpvvp1pm2FdwX0eVFoZhC6WdVdSW+BqPSp9BGHe7+Mn1PlacnhPQr2n5/XfH69cXXoVFDbH1FJo0722v4sLqa3wBoBRy+CWKefG7EQwWoOJVciC6azDDgH4oszgKyuEFHss/muDwdOxVEEPqwxBQJK8sU4Yp30FDj+******************************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') format('woff')}"}}]}