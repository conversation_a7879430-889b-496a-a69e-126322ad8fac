<?php
// Test admin routing directly
echo "<h2>🔍 Admin Routing Test</h2>";

// Test 1: Direct URL access
echo "<h3>1. Direct URL Tests</h3>";
echo "<p><a href='http://localhost/admin/login' target='_blank'>Test: http://localhost/admin/login</a></p>";
echo "<p><a href='http://localhost/admin' target='_blank'>Test: http://localhost/admin</a></p>";
echo "<p><a href='http://localhost/admin/index' target='_blank'>Test: http://localhost/admin/index</a></p>";

// Test 2: Check if CodeIgniter is loading
echo "<h3>2. CodeIgniter Loading Test</h3>";

// Try to load CodeIgniter manually
$ci_path = 'system/core/CodeIgniter.php';
if (file_exists($ci_path)) {
    echo "<p style='color: green;'>✅ CodeIgniter core file exists</p>";
} else {
    echo "<p style='color: red;'>❌ CodeIgniter core file not found</p>";
}

// Check index.php
if (file_exists('index.php')) {
    echo "<p style='color: green;'>✅ index.php exists</p>";
    
    // Read first few lines of index.php
    $index_content = file_get_contents('index.php');
    if (strpos($index_content, 'BASEPATH') !== false) {
        echo "<p style='color: green;'>✅ index.php appears to be CodeIgniter</p>";
    } else {
        echo "<p style='color: red;'>❌ index.php doesn't appear to be CodeIgniter</p>";
    }
} else {
    echo "<p style='color: red;'>❌ index.php not found</p>";
}

// Test 3: Check Admin controller
echo "<h3>3. Admin Controller Test</h3>";
$admin_controller = 'application/controllers/Admin.php';
if (file_exists($admin_controller)) {
    echo "<p style='color: green;'>✅ Admin controller exists</p>";
    
    // Check if class is properly defined
    $controller_content = file_get_contents($admin_controller);
    if (strpos($controller_content, 'class Admin extends CI_Controller') !== false) {
        echo "<p style='color: green;'>✅ Admin class properly defined</p>";
    } else {
        echo "<p style='color: red;'>❌ Admin class not properly defined</p>";
    }
} else {
    echo "<p style='color: red;'>❌ Admin controller not found</p>";
}

// Test 4: Check routes
echo "<h3>4. Routes Configuration</h3>";
$routes_file = 'application/config/routes.php';
if (file_exists($routes_file)) {
    echo "<p style='color: green;'>✅ Routes file exists</p>";
    
    $routes_content = file_get_contents($routes_file);
    echo "<p><strong>Default controller:</strong></p>";
    if (preg_match("/\\\$route\['default_controller'\]\s*=\s*'([^']+)'/", $routes_content, $matches)) {
        echo "<p>Default controller: " . $matches[1] . "</p>";
    }
    
    // Check for admin routes
    if (strpos($routes_content, 'admin') !== false) {
        echo "<p style='color: green;'>✅ Admin routes found in config</p>";
    } else {
        echo "<p style='color: orange;'>⚠️ No specific admin routes found (using default routing)</p>";
    }
} else {
    echo "<p style='color: red;'>❌ Routes file not found</p>";
}

// Test 5: Manual CodeIgniter bootstrap test
echo "<h3>5. Manual CodeIgniter Test</h3>";

try {
    // Set up basic CodeIgniter environment
    define('ENVIRONMENT', 'development');
    
    if (!defined('BASEPATH')) {
        echo "<p>Attempting to bootstrap CodeIgniter...</p>";
        
        // Basic setup
        $system_path = 'system';
        $application_folder = 'application';
        
        if (realpath($system_path) !== FALSE) {
            $system_path = realpath($system_path).'/';
        }
        
        $system_path = rtrim($system_path, '/').'/';
        
        if (!is_dir($system_path)) {
            echo "<p style='color: red;'>❌ System directory not found: $system_path</p>";
        } else {
            echo "<p style='color: green;'>✅ System directory found: $system_path</p>";
        }
        
        if (!is_dir($application_folder)) {
            echo "<p style='color: red;'>❌ Application directory not found: $application_folder</p>";
        } else {
            echo "<p style='color: green;'>✅ Application directory found: $application_folder</p>";
        }
    } else {
        echo "<p style='color: green;'>✅ CodeIgniter already bootstrapped</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error bootstrapping CodeIgniter: " . $e->getMessage() . "</p>";
}

// Test 6: Session test
echo "<h3>6. Session Test</h3>";
session_start();

if (isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in']) {
    echo "<p style='color: green;'>✅ PHP session shows admin is logged in</p>";
} else {
    echo "<p style='color: red;'>❌ PHP session shows admin is NOT logged in</p>";
}

// Check CI session files
$session_path = 'application/cache/sessions';
if (is_dir($session_path)) {
    $files = scandir($session_path);
    $session_files = array_filter($files, function($file) {
        return $file !== '.' && $file !== '..' && strpos($file, 'ci_session') === 0;
    });
    
    $admin_sessions = 0;
    foreach ($session_files as $file) {
        $content = file_get_contents($session_path . '/' . $file);
        if (strpos($content, 'admin_logged_in') !== false) {
            $admin_sessions++;
        }
    }
    
    echo "<p>CI session files with admin data: $admin_sessions</p>";
} else {
    echo "<p style='color: red;'>❌ CI session directory not found</p>";
}

echo "<hr>";
echo "<h3>🔗 Direct Tests</h3>";
echo "<p>Click these links to test directly:</p>";
echo "<ul>";
echo "<li><a href='http://localhost/admin/login' target='_blank'>Admin Login</a></li>";
echo "<li><a href='http://localhost/admin' target='_blank'>Admin Dashboard</a></li>";
echo "<li><a href='http://localhost/index.php/admin' target='_blank'>Admin Dashboard (with index.php)</a></li>";
echo "<li><a href='http://localhost/index.php/admin/login' target='_blank'>Admin Login (with index.php)</a></li>";
echo "</ul>";
?>
