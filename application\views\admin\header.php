<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= isset($title) ? $title . ' - ' : '' ?>Game Platform Admin</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Admin Theme CSS -->
    <link href="<?= base_url('assets/css/admin-theme.css') ?>" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 0;
        }
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 4px 0 25px rgba(0,0,0,0.15);
            position: relative;
            overflow: hidden;
        }
        .sidebar::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.05)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.05)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.03)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }
        .sidebar .nav-link {
            color: rgba(255,255,255,0.85);
            padding: 1rem 1.5rem;
            margin: 0.25rem 1rem;
            border-radius: 1rem;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            z-index: 1;
            font-weight: 500;
            border: 1px solid transparent;
        }
        .sidebar .nav-link:hover {
            color: white;
            background: rgba(255,255,255,0.15);
            transform: translateX(5px);
            border-color: rgba(255,255,255,0.2);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        .sidebar .nav-link.active {
            color: #667eea;
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            transform: translateX(8px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            border-color: rgba(255,255,255,0.3);
            font-weight: 600;
        }
        .sidebar .nav-link i {
            width: 20px;
            margin-right: 12px;
            font-size: 1.1rem;
        }
        .main-content {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            min-height: 100vh;
            position: relative;
        }
        .main-content::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(102,126,234,0.03)"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
            pointer-events: none;
        }
        .card {
            border: none;
            border-radius: 1.5rem;
            box-shadow: 0 10px 40px rgba(0,0,0,0.08);
            backdrop-filter: blur(10px);
            background: rgba(255,255,255,0.95);
            transition: all 0.3s ease;
            position: relative;
            z-index: 1;
        }
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 60px rgba(0,0,0,0.12);
        }
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 1.5rem 1.5rem 0 0 !important;
            padding: 1.5rem;
            border: none;
            position: relative;
            overflow: hidden;
            font-weight: 600;
        }
        .card-header::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 100px;
            height: 100px;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            transform: translate(30px, -30px);
        }
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 1.5rem;
            position: relative;
            overflow: hidden;
            box-shadow: 0 15px 35px rgba(102,126,234,0.2);
        }
        .stats-card::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 50%);
            animation: float 6s ease-in-out infinite;
        }
        @keyframes float {
            0%, 100% { transform: translate(0, 0) rotate(0deg); }
            50% { transform: translate(-20px, -20px) rotate(180deg); }
        }
        .navbar-brand {
            font-weight: 700;
            color: #667eea !important;
            font-size: 1.5rem;
        }
        .admin-logo {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            color: #667eea;
            width: 70px;
            height: 70px;
            border-radius: 1.2rem;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.8rem;
            margin: 0 auto 1.5rem;
            box-shadow: 0 15px 40px rgba(255,255,255,0.2);
            border: 2px solid rgba(255,255,255,0.3);
        }
        .sidebar-title {
            text-align: center;
            margin-bottom: 2rem;
            position: relative;
            z-index: 1;
        }
        .sidebar-title h4 {
            font-weight: 700;
            margin-bottom: 0.5rem;
            font-size: 1.3rem;
        }
        .sidebar-title small {
            opacity: 0.8;
            font-weight: 500;
        }
        .btn {
            border-radius: 1rem;
            font-weight: 600;
            padding: 0.75rem 1.5rem;
            transition: all 0.3s ease;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        .alert {
            border-radius: 1rem;
            border: none;
            box-shadow: 0 5px 20px rgba(0,0,0,0.08);
        }
        .table {
            border-radius: 1rem;
            overflow: hidden;
        }
        .form-control, .form-select {
            border-radius: 1rem;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
            padding: 0.75rem 1rem;
        }
        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102,126,234,0.25);
            outline: none;
        }

        /* Dashboard specific styles */
        .btn-outline-primary:hover {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            border-color: #007bff;
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0,123,255,0.3);
        }

        .btn-outline-success:hover {
            background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
            border-color: #28a745;
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(40,167,69,0.3);
        }

        .btn-outline-warning:hover {
            background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
            border-color: #ffc107;
            color: #212529 !important;
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(255,193,7,0.3);
        }

        .btn-outline-info:hover {
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
            border-color: #17a2b8;
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(23,162,184,0.3);
        }

        .stats-card {
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .stats-card::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 50%);
            animation: float 8s ease-in-out infinite;
        }

        .stats-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }

        @keyframes float {
            0%, 100% { transform: translate(0, 0) rotate(0deg); }
            50% { transform: translate(-10px, -10px) rotate(180deg); }
        }

        /* Enhanced table styles */
        .table {
            border-radius: 1rem;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        .table thead th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            font-weight: 600;
            padding: 1rem;
        }
        .table tbody tr {
            transition: all 0.3s ease;
        }
        .table tbody tr:hover {
            background-color: rgba(102,126,234,0.05);
            transform: translateX(5px);
        }
        .table tbody td {
            padding: 1rem;
            border-color: rgba(0,0,0,0.05);
        }

        /* Enhanced badges */
        .badge {
            font-weight: 500;
            padding: 0.5rem 0.75rem;
            border-radius: 0.75rem;
        }

        /* Enhanced buttons */
        .btn-group .btn {
            border-radius: 0.75rem;
            margin: 0 2px;
        }

        /* Modal improvements */
        .modal-content {
            border-radius: 1.5rem;
            border: none;
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
        }
        .modal-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 1.5rem 1.5rem 0 0;
            border: none;
        }

        /* Search and filter improvements */
        .input-group-text {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
        }

        /* Breadcrumb styles */
        .breadcrumb {
            background: transparent;
            padding: 0;
            margin: 0;
        }
        .breadcrumb-item a {
            color: #667eea;
            transition: all 0.3s ease;
        }
        .breadcrumb-item a:hover {
            color: #764ba2;
            transform: translateX(3px);
        }
        .breadcrumb-item.active {
            color: #6c757d;
        }
        .breadcrumb-item + .breadcrumb-item::before {
            content: "›";
            color: #6c757d;
            font-weight: bold;
        }

        /* Dropdown improvements */
        .dropdown-menu {
            border-radius: 1rem;
            border: none;
            box-shadow: 0 10px 30px rgba(0,0,0,0.15);
            padding: 0.5rem;
        }
        .dropdown-item {
            border-radius: 0.75rem;
            padding: 0.75rem 1rem;
            transition: all 0.3s ease;
        }
        .dropdown-item:hover {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            transform: translateX(5px);
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="sidebar-title">
                        <div class="admin-logo">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <h4 class="text-white">Game Platform</h4>
                        <small class="text-white-50">Admin Panel</small>
                    </div>
                    
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link <?= $this->uri->segment(2) == '' || $this->uri->segment(2) == 'index' ? 'active' : '' ?>" href="<?= base_url('admin') ?>">
                                <i class="fas fa-tachometer-alt"></i>
                                Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?= $this->uri->segment(2) == 'agents' ? 'active' : '' ?>" href="<?= base_url('admin/agents') ?>">
                                <i class="fas fa-users"></i>
                                Agentes
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?= $this->uri->segment(2) == 'users' ? 'active' : '' ?>" href="<?= base_url('admin/users') ?>">
                                <i class="fas fa-user-friends"></i>
                                Usuários
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?= $this->uri->segment(2) == 'games' ? 'active' : '' ?>" href="<?= base_url('admin/games') ?>">
                                <i class="fas fa-gamepad"></i>
                                Jogos
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?= $this->uri->segment(2) == 'rtp_management' ? 'active' : '' ?>" href="<?= base_url('admin/rtp_management') ?>">
                                <i class="fas fa-percentage"></i>
                                Gerenciar RTP
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?= $this->uri->segment(2) == 'reports' ? 'active' : '' ?>" href="<?= base_url('admin/reports') ?>">
                                <i class="fas fa-chart-bar"></i>
                                Relatórios
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?= $this->uri->segment(2) == 'api_docs' ? 'active' : '' ?>" href="<?= base_url('admin/api_docs') ?>">
                                <i class="fas fa-code"></i>
                                API Docs
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?= $this->uri->segment(2) == 'settings' ? 'active' : '' ?>" href="<?= base_url('admin/settings') ?>">
                                <i class="fas fa-cog"></i>
                                Configurações
                            </a>
                        </li>
                        <li class="nav-item mt-3">
                            <a class="nav-link text-danger" href="<?= base_url('admin/logout') ?>">
                                <i class="fas fa-sign-out-alt"></i>
                                Sair
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
                <!-- Header with breadcrumb -->
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-4">
                    <div>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb mb-2">
                                <li class="breadcrumb-item">
                                    <a href="<?= base_url('admin') ?>" class="text-decoration-none">
                                        <i class="fas fa-home"></i> Admin
                                    </a>
                                </li>
                                <?php if ($this->uri->segment(2) && $this->uri->segment(2) != 'index'): ?>
                                    <li class="breadcrumb-item active" aria-current="page">
                                        <?= ucfirst(str_replace('_', ' ', $this->uri->segment(2))) ?>
                                    </li>
                                <?php endif; ?>
                            </ol>
                        </nav>
                        <h1 class="h2 mb-0 fw-bold text-dark"><?= isset($title) ? $title : 'Dashboard' ?></h1>
                    </div>
                    <div class="d-flex align-items-center">
                        <div class="me-3">
                            <small class="text-muted d-block">Bem-vindo de volta!</small>
                            <strong class="text-dark">
                                <i class="fas fa-user-shield me-1"></i>
                                <?= $this->session->userdata('admin_name') ?>
                            </strong>
                        </div>
                        <div class="dropdown">
                            <button class="btn btn-outline-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="fas fa-cog"></i>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <li><a class="dropdown-item" href="<?= base_url('admin/settings') ?>">
                                    <i class="fas fa-cog me-2"></i>Configurações
                                </a></li>
                                <li><a class="dropdown-item" href="<?= base_url('admin/profile') ?>">
                                    <i class="fas fa-user me-2"></i>Perfil
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item text-danger" href="<?= base_url('admin/logout') ?>">
                                    <i class="fas fa-sign-out-alt me-2"></i>Sair
                                </a></li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Flash Messages -->
                <?php if ($this->session->flashdata('success')): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="fas fa-check-circle"></i>
                        <?= $this->session->flashdata('success') ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <?php if ($this->session->flashdata('error')): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-circle"></i>
                        <?= $this->session->flashdata('error') ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <?php if ($this->session->flashdata('warning')): ?>
                    <div class="alert alert-warning alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-triangle"></i>
                        <?= $this->session->flashdata('warning') ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <?php if ($this->session->flashdata('info')): ?>
                    <div class="alert alert-info alert-dismissible fade show" role="alert">
                        <i class="fas fa-info-circle"></i>
                        <?= $this->session->flashdata('info') ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
