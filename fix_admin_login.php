<?php
// Fix admin login - ensure admin user exists with correct credentials
echo "<h2>Fixing Admin Login System</h2>";

try {
    // Connect to database
    $pdo = new PDO('mysql:host=localhost;dbname=game_platform', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<p style='color: green;'>✓ Database connection successful</p>";
    
    // Check if admins table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'admins'");
    if ($stmt->rowCount() == 0) {
        echo "<p style='color: orange;'>Creating admins table...</p>";
        
        $sql = "CREATE TABLE IF NOT EXISTS admins (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(50) UNIQUE NOT NULL,
            password VARCHAR(255) NOT NULL,
            email VARCHAR(100) UNIQUE NOT NULL,
            full_name VARCHAR(100) NOT NULL,
            status TINYINT(1) DEFAULT 1,
            last_login TIMESTAMP NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )";
        
        $pdo->exec($sql);
        echo "<p style='color: green;'>✓ Admins table created</p>";
    } else {
        echo "<p style='color: green;'>✓ Admins table exists</p>";
    }
    
    // Check if admin user exists
    $stmt = $pdo->prepare("SELECT * FROM admins WHERE username = ?");
    $stmt->execute(['admin']);
    $admin = $stmt->fetch();
    
    if ($admin) {
        echo "<p style='color: green;'>✓ Admin user found</p>";
        echo "<p>Current admin details:</p>";
        echo "<ul>";
        echo "<li>ID: " . $admin['id'] . "</li>";
        echo "<li>Username: " . $admin['username'] . "</li>";
        echo "<li>Email: " . $admin['email'] . "</li>";
        echo "<li>Status: " . ($admin['status'] ? 'Active' : 'Inactive') . "</li>";
        echo "</ul>";
        
        // Update password to ensure it's correct
        echo "<p style='color: orange;'>Updating admin password to '123456'...</p>";
        $new_password = password_hash('123456', PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("UPDATE admins SET password = ? WHERE username = ?");
        $result = $stmt->execute([$new_password, 'admin']);
        
        if ($result) {
            echo "<p style='color: green;'>✓ Admin password updated successfully</p>";
        } else {
            echo "<p style='color: red;'>✗ Failed to update admin password</p>";
        }
        
    } else {
        echo "<p style='color: orange;'>Admin user not found. Creating...</p>";
        
        $password_hash = password_hash('123456', PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("INSERT INTO admins (username, password, email, full_name, status) VALUES (?, ?, ?, ?, ?)");
        $result = $stmt->execute(['admin', $password_hash, '<EMAIL>', 'Administrador Principal', 1]);
        
        if ($result) {
            echo "<p style='color: green;'>✓ Admin user created successfully</p>";
        } else {
            echo "<p style='color: red;'>✗ Failed to create admin user</p>";
        }
    }
    
    // Test login credentials
    echo "<h3>Testing Login Credentials</h3>";
    $stmt = $pdo->prepare("SELECT * FROM admins WHERE username = ? AND status = 1");
    $stmt->execute(['admin']);
    $admin = $stmt->fetch();
    
    if ($admin) {
        echo "<p style='color: green;'>✓ Admin user found and active</p>";
        
        if (password_verify('123456', $admin['password'])) {
            echo "<p style='color: green;'>✓ Password verification successful</p>";
            echo "<p style='color: green; font-weight: bold; font-size: 18px;'>🎉 LOGIN SHOULD WORK NOW!</p>";
        } else {
            echo "<p style='color: red;'>✗ Password verification failed</p>";
        }
    } else {
        echo "<p style='color: red;'>✗ Admin user not found or inactive</p>";
    }
    
    // Show final admin details
    echo "<h3>Final Admin User Details</h3>";
    $stmt = $pdo->query("SELECT * FROM admins");
    $admins = $stmt->fetchAll();
    
    if ($admins) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background-color: #f0f0f0;'>";
        echo "<th>ID</th><th>Username</th><th>Email</th><th>Full Name</th><th>Status</th><th>Created At</th>";
        echo "</tr>";
        
        foreach ($admins as $admin) {
            echo "<tr>";
            echo "<td>" . $admin['id'] . "</td>";
            echo "<td>" . $admin['username'] . "</td>";
            echo "<td>" . $admin['email'] . "</td>";
            echo "<td>" . $admin['full_name'] . "</td>";
            echo "<td>" . ($admin['status'] ? 'Active' : 'Inactive') . "</td>";
            echo "<td>" . $admin['created_at'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Error: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<h3>Next Steps</h3>";
echo "<ol>";
echo "<li><a href='admin/login' target='_blank'>Go to Admin Login Page</a></li>";
echo "<li>Use credentials: <strong>admin</strong> / <strong>123456</strong></li>";
echo "<li>Click 'ENTRAR' to login</li>";
echo "</ol>";

echo "<hr>";
echo "<p><strong>Login Credentials:</strong></p>";
echo "<p>Username: <code>admin</code></p>";
echo "<p>Password: <code>123456</code></p>";
?>
