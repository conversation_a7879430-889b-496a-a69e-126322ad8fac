<?php
// Test CodeIgniter session compatibility
echo "<h2>🔍 CodeIgniter Session Compatibility Test</h2>";

// Test 1: Native PHP Session
echo "<h3>1. Native PHP Session Test</h3>";
session_start();

if (isset($_POST['test_native'])) {
    $_SESSION['test_admin_logged_in'] = TRUE;
    $_SESSION['test_admin_id'] = 1;
    $_SESSION['test_admin_username'] = 'admin';
    $_SESSION['test_admin_name'] = 'Administrator';
    
    echo "<p style='color: green;'>✅ Native PHP session data set successfully!</p>";
}

if (isset($_SESSION['test_admin_logged_in']) && $_SESSION['test_admin_logged_in']) {
    echo "<p style='color: green;'>✅ Native PHP session is working!</p>";
    echo "<p>Session data: " . print_r($_SESSION, true) . "</p>";
} else {
    echo "<p style='color: red;'>❌ Native PHP session not found</p>";
    echo "<form method='post'>";
    echo "<button type='submit' name='test_native' style='background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px;'>Set Native Session</button>";
    echo "</form>";
}

// Test 2: CodeIgniter Session Files
echo "<h3>2. CodeIgniter Session Files Test</h3>";
$session_path = 'application/cache/sessions';

if (is_dir($session_path)) {
    echo "<p style='color: green;'>✅ Session directory exists: $session_path</p>";
    
    $files = scandir($session_path);
    $session_files = array_filter($files, function($file) {
        return $file !== '.' && $file !== '..' && strpos($file, 'ci_session') === 0;
    });
    
    echo "<p>Session files found: " . count($session_files) . "</p>";
    
    if (count($session_files) > 0) {
        echo "<p style='color: green;'>✅ CodeIgniter session files exist</p>";
        
        // Show latest session file content
        $latest_file = end($session_files);
        $file_path = $session_path . '/' . $latest_file;
        
        if (file_exists($file_path)) {
            $content = file_get_contents($file_path);
            echo "<p><strong>Latest session file:</strong> $latest_file</p>";
            echo "<p><strong>Content:</strong></p>";
            echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 5px; max-height: 200px; overflow-y: auto;'>" . htmlspecialchars($content) . "</pre>";
            
            // Try to decode session data
            if (strpos($content, 'admin_logged_in') !== false) {
                echo "<p style='color: green;'>✅ Found admin_logged_in in session file!</p>";
            } else {
                echo "<p style='color: red;'>❌ admin_logged_in not found in session file</p>";
            }
        }
    } else {
        echo "<p style='color: orange;'>⚠️ No CodeIgniter session files found</p>";
    }
} else {
    echo "<p style='color: red;'>❌ Session directory not found: $session_path</p>";
}

// Test 3: Database Connection Test
echo "<h3>3. Database Connection Test</h3>";
try {
    $pdo = new PDO('mysql:host=localhost;dbname=game_platform', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<p style='color: green;'>✅ Database connection successful</p>";
    
    // Test admin login
    $stmt = $pdo->prepare("SELECT * FROM admins WHERE username = 'admin' AND status = 1");
    $stmt->execute();
    $admin = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($admin) {
        echo "<p style='color: green;'>✅ Admin user found in database</p>";
        
        if (password_verify('123456', $admin['password'])) {
            echo "<p style='color: green;'>✅ Password verification successful</p>";
        } else {
            echo "<p style='color: red;'>❌ Password verification failed</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ Admin user not found in database</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Database connection failed: " . $e->getMessage() . "</p>";
}

// Test 4: CodeIgniter Session Simulation
echo "<h3>4. CodeIgniter Session Simulation</h3>";

if (isset($_POST['simulate_ci_login'])) {
    // Simulate what CodeIgniter does
    $session_data = [
        'admin_logged_in' => TRUE,
        'admin_id' => 1,
        'admin_username' => 'admin',
        'admin_name' => 'Administrator'
    ];
    
    // Set in PHP session
    foreach ($session_data as $key => $value) {
        $_SESSION[$key] = $value;
    }
    
    // Try to create a CodeIgniter-style session file
    $session_id = session_id();
    $timestamp = time();
    $ci_session_data = 'admin_logged_in|b:1;admin_id|i:1;admin_username|s:5:"admin";admin_name|s:13:"Administrator";';
    $ci_session_content = "admin_logged_in:1;admin_id:1;admin_username:admin;admin_name:Administrator;__ci_last_regenerate:$timestamp;";
    
    if (is_dir($session_path)) {
        $ci_session_file = $session_path . '/ci_session' . $session_id;
        file_put_contents($ci_session_file, $ci_session_content);
        echo "<p style='color: green;'>✅ CodeIgniter session simulation completed!</p>";
        echo "<p>Session file created: $ci_session_file</p>";
    }
    
    echo "<p style='color: green;'>✅ Session data set in PHP session</p>";
}

if (isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in']) {
    echo "<p style='color: green;'>✅ Simulated admin session is active!</p>";
    echo "<p><a href='http://localhost/admin' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Test Admin Dashboard</a></p>";
} else {
    echo "<form method='post'>";
    echo "<button type='submit' name='simulate_ci_login' style='background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px;'>Simulate CodeIgniter Login</button>";
    echo "</form>";
}

// Test 5: Session Cookie Test
echo "<h3>5. Session Cookie Test</h3>";
echo "<p><strong>Current cookies:</strong></p>";
if (!empty($_COOKIE)) {
    echo "<ul>";
    foreach ($_COOKIE as $name => $value) {
        echo "<li><strong>$name:</strong> " . htmlspecialchars($value) . "</li>";
    }
    echo "</ul>";
} else {
    echo "<p>No cookies found</p>";
}

// Clear session option
echo "<h3>🧹 Clear Session</h3>";
if (isset($_GET['clear'])) {
    session_destroy();
    // Also clear CodeIgniter session files
    if (is_dir($session_path)) {
        $files = glob($session_path . '/ci_session*');
        foreach ($files as $file) {
            unlink($file);
        }
    }
    echo "<script>window.location.href = 'test_ci_session_compatibility.php';</script>";
}

echo "<p><a href='?clear=1' style='background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Clear All Sessions</a></p>";

echo "<hr>";
echo "<h3>🔗 Quick Links</h3>";
echo "<p><a href='http://localhost/admin/login'>CodeIgniter Admin Login</a> | ";
echo "<a href='http://localhost/admin'>CodeIgniter Admin Dashboard</a> | ";
echo "<a href='test_admin_session_simple.php'>Simple Session Test</a></p>";
?>
