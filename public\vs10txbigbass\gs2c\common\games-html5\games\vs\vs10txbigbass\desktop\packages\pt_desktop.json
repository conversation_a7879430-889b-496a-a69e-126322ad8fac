{"resources": [{"type": "GameObject", "id": "704d90fd354daea498b48ab7779382a2", "data": {"root": [{"name": "pt_desktop", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 0}, "children": [{"fileID": 1747, "guid": "704d90fd354daea498b48ab7779382a2"}, {"fileID": 1748, "guid": "704d90fd354daea498b48ab7779382a2"}, {"fileID": 1749, "guid": "704d90fd354daea498b48ab7779382a2"}, {"fileID": 1750, "guid": "704d90fd354daea498b48ab7779382a2"}, {"fileID": 1751, "guid": "704d90fd354daea498b48ab7779382a2"}, {"fileID": 1752, "guid": "704d90fd354daea498b48ab7779382a2"}, {"fileID": 1753, "guid": "704d90fd354daea498b48ab7779382a2"}, {"fileID": 1754, "guid": "704d90fd354daea498b48ab7779382a2"}, {"fileID": 1755, "guid": "704d90fd354daea498b48ab7779382a2"}, {"fileID": 1756, "guid": "704d90fd354daea498b48ab7779382a2"}, {"fileID": 1757, "guid": "704d90fd354daea498b48ab7779382a2"}, {"fileID": 1758, "guid": "704d90fd354daea498b48ab7779382a2"}, {"fileID": 1759, "guid": "704d90fd354daea498b48ab7779382a2"}, {"fileID": 1760, "guid": "704d90fd354daea498b48ab7779382a2"}, {"fileID": 1761, "guid": "704d90fd354daea498b48ab7779382a2"}, {"fileID": 1762, "guid": "704d90fd354daea498b48ab7779382a2"}, {"fileID": 1763, "guid": "704d90fd354daea498b48ab7779382a2"}, {"fileID": 1764, "guid": "704d90fd354daea498b48ab7779382a2"}, {"fileID": 1765, "guid": "704d90fd354daea498b48ab7779382a2"}, {"fileID": 1766, "guid": "704d90fd354daea498b48ab7779382a2"}, {"fileID": 1767, "guid": "704d90fd354daea498b48ab7779382a2"}, {"fileID": 1768, "guid": "704d90fd354daea498b48ab7779382a2"}, {"fileID": 1769, "guid": "704d90fd354daea498b48ab7779382a2"}, {"fileID": 1770, "guid": "704d90fd354daea498b48ab7779382a2"}, {"fileID": 1771, "guid": "704d90fd354daea498b48ab7779382a2"}, {"fileID": 1772, "guid": "704d90fd354daea498b48ab7779382a2"}, {"fileID": 1773, "guid": "704d90fd354daea498b48ab7779382a2"}, {"fileID": 1774, "guid": "704d90fd354daea498b48ab7779382a2"}, {"fileID": 1775, "guid": "704d90fd354daea498b48ab7779382a2"}, {"fileID": 1776, "guid": "704d90fd354daea498b48ab7779382a2"}, {"fileID": 1777, "guid": "704d90fd354daea498b48ab7779382a2"}, {"fileID": 1778, "guid": "704d90fd354daea498b48ab7779382a2"}, {"fileID": 1779, "guid": "704d90fd354daea498b48ab7779382a2"}, {"fileID": 1780, "guid": "704d90fd354daea498b48ab7779382a2"}, {"fileID": 1781, "guid": "704d90fd354daea498b48ab7779382a2"}, {"fileID": 1782, "guid": "704d90fd354daea498b48ab7779382a2"}, {"fileID": 1783, "guid": "704d90fd354daea498b48ab7779382a2"}, {"fileID": 1784, "guid": "704d90fd354daea498b48ab7779382a2"}, {"fileID": 1785, "guid": "704d90fd354daea498b48ab7779382a2"}, {"fileID": 1786, "guid": "704d90fd354daea498b48ab7779382a2"}, {"fileID": 1787, "guid": "704d90fd354daea498b48ab7779382a2"}, {"fileID": 1788, "guid": "704d90fd354daea498b48ab7779382a2"}, {"fileID": 1789, "guid": "704d90fd354daea498b48ab7779382a2"}, {"fileID": 1790, "guid": "704d90fd354daea498b48ab7779382a2"}, {"fileID": 1791, "guid": "704d90fd354daea498b48ab7779382a2"}, {"fileID": 1792, "guid": "704d90fd354daea498b48ab7779382a2"}, {"fileID": 1793, "guid": "704d90fd354daea498b48ab7779382a2"}, {"fileID": 1794, "guid": "704d90fd354daea498b48ab7779382a2"}, {"fileID": 1795, "guid": "704d90fd354daea498b48ab7779382a2"}, {"fileID": 1796, "guid": "704d90fd354daea498b48ab7779382a2"}, {"fileID": 1797, "guid": "704d90fd354daea498b48ab7779382a2"}, {"fileID": 1798, "guid": "704d90fd354daea498b48ab7779382a2"}, {"fileID": 1799, "guid": "704d90fd354daea498b48ab7779382a2"}, {"fileID": 1800, "guid": "704d90fd354daea498b48ab7779382a2"}, {"fileID": 1801, "guid": "704d90fd354daea498b48ab7779382a2"}, {"fileID": 1802, "guid": "704d90fd354daea498b48ab7779382a2"}, {"fileID": 1803, "guid": "704d90fd354daea498b48ab7779382a2"}, {"fileID": 1804, "guid": "704d90fd354daea498b48ab7779382a2"}, {"fileID": 1805, "guid": "704d90fd354daea498b48ab7779382a2"}, {"fileID": 1806, "guid": "704d90fd354daea498b48ab7779382a2"}, {"fileID": 1807, "guid": "704d90fd354daea498b48ab7779382a2"}, {"fileID": 1808, "guid": "704d90fd354daea498b48ab7779382a2"}, {"fileID": 1809, "guid": "704d90fd354daea498b48ab7779382a2"}, {"fileID": 1810, "guid": "704d90fd354daea498b48ab7779382a2"}, {"fileID": 1811, "guid": "704d90fd354daea498b48ab7779382a2"}, {"fileID": 1812, "guid": "704d90fd354daea498b48ab7779382a2"}, {"fileID": 1813, "guid": "704d90fd354daea498b48ab7779382a2"}, {"fileID": 1814, "guid": "704d90fd354daea498b48ab7779382a2"}, {"fileID": 1815, "guid": "704d90fd354daea498b48ab7779382a2"}, {"fileID": 1816, "guid": "704d90fd354daea498b48ab7779382a2"}, {"fileID": 1817, "guid": "704d90fd354daea498b48ab7779382a2"}, {"fileID": 1818, "guid": "704d90fd354daea498b48ab7779382a2"}, {"fileID": 1819, "guid": "704d90fd354daea498b48ab7779382a2"}, {"fileID": 1820, "guid": "704d90fd354daea498b48ab7779382a2"}, {"fileID": 1821, "guid": "704d90fd354daea498b48ab7779382a2"}, {"fileID": 1822, "guid": "704d90fd354daea498b48ab7779382a2"}, {"fileID": 1823, "guid": "704d90fd354daea498b48ab7779382a2"}, {"fileID": 1824, "guid": "704d90fd354daea498b48ab7779382a2"}, {"fileID": 1825, "guid": "704d90fd354daea498b48ab7779382a2"}, {"fileID": 1826, "guid": "704d90fd354daea498b48ab7779382a2"}, {"fileID": 1827, "guid": "704d90fd354daea498b48ab7779382a2"}, {"fileID": 1828, "guid": "704d90fd354daea498b48ab7779382a2"}, {"fileID": 1829, "guid": "704d90fd354daea498b48ab7779382a2"}, {"fileID": 1830, "guid": "704d90fd354daea498b48ab7779382a2"}, {"fileID": 1831, "guid": "704d90fd354daea498b48ab7779382a2"}, {"fileID": 1832, "guid": "704d90fd354daea498b48ab7779382a2"}, {"fileID": 1833, "guid": "704d90fd354daea498b48ab7779382a2"}], "s": "0"}, "fileID": 1834}, {"componentType": "ModificationsManager", "enabled": true, "serializableData": {"root": {"fileID": 0}, "EditMode": false, "Atlases": [], "Transforms": [{"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page1/Title/PaytableTitleLabel1", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page1/Rules/AllSymbolsPayLabel", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page1/ScatterHolder/SymbolScatter/Sprite", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0.52, "y": 0.52, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page1/ScatterHolder/DescriptionHolder/Label1", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -24, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page1/ScatterHolder/DescriptionHolder/Label2", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -35, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page1/WildHolder/SymbolScatter/Sprite", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0.25, "y": 0.25, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page1/WildHolder/DescriptionHolder/Label1", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -12.5, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page1/WildHolder/DescriptionHolder/Label2", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -50, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page1/WildHolder/DescriptionHolder/Label3", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -87.5, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/MoneySymbolHolder/TitleHolder/Title", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 15, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/MoneySymbolHolder/DescriptionHolder/MoneySymbolHolder/Sprite", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0.7, "y": 0.7, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/MoneySymbolHolder/DescriptionHolder/MoneySymbolRules/Label3", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 4, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FreeSpinsHolder1/TitleHolder/Title", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 1, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/RuleHolder1/Rule1", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 12, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder1/LabelHolder1/Label1", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 16, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder1/LabelHolder2/Label2", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 16, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder1/LabelHolder3/Label3", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 16, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/RuleHolder2/Rule2", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 12, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder2/LabelHolder1/Label1", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 10, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder2/LabelHolder2/Label2", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -4, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/RuleHolder3/Rule3", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 15, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder1/Label1", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 10, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder2/Label2", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -9, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder3/Label3", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -17, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder4/Label4", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -13, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder5/Label5", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -13, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder1/Rule1", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder2/Rule2", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder3/Rule3", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 1.8, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder4/Rule4", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder5/Rule5", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder6/Rule6", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder7/Rule7", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -7, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder8/Rule8", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -15, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/FreeSpinsHolder2/SpecialReelsHolder/SpecialReels", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -17, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/MaxWin/TitleHolder/Title", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/MaxWin/RuleHolder/Label1", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 4, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/CAT/TitleHolder/Title1New", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 20, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/CAT/RuleHolder1/Rule1", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/CAT/RuleHolder2/Rule2", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/CAT/RuleHolder3/Rule3", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page4/Title/PaytableTitleLabel", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page4/Volatility/VolatilityDescription", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -30, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page4/RulesTop/AllSymbolsPay", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 50, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page4/RulesTop/AllWinsMultiplied", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 21, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page4/RulesTop/AllValuesExpressed", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -45, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page4/RulesTop/OnlyTheHighestWin", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -65, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page4/RulesTop/WhenWinningOnMultiplePaylines", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -95, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page4/Lines/Sprite", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page4/RulesBottom/SpaceAndEnter", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 92, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page4/RulesBottom/RTP", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page4/RulesBottom/MalfunctionLabel", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -97, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page4/MinMaxHolder", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -185, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/MoneySymbolHolder/DescriptionHolder/PossibleValues/Label", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -9, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/MaxWin/RuleHolder/HolderLabelJackpot/Label1New", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 7, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}], "Labels": [{"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 1835, "guid": "704d90fd354daea498b48ab7779382a2"}, "extraPayloads": [], "path": "Game/GamePivot/FSWONWindow/content/FreespinsWonWindow/LabelsAndSprites/Catches_label", "oldContent": "CATCHES", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 1836, "guid": "704d90fd354daea498b48ab7779382a2"}, "extraPayloads": [], "path": "Game/GamePivot/FreeSpinsPurchase/BuyButtons/Options/Option_0/OptVisual/Texts/BoughtText/LandscapeText/label1", "oldContent": "ACTIVE", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 1837, "guid": "704d90fd354daea498b48ab7779382a2"}, "extraPayloads": [], "path": "Game/GamePivot/FSWONWindow/PressAnywhere_Label/label", "oldContent": "Press anywhere to continue", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 1838, "guid": "704d90fd354daea498b48ab7779382a2"}, "extraPayloads": [], "path": "Game/GamePivot/FSExtraWindow/content/PressAnywhere_Label/label", "oldContent": "Press anywhere to continue", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 1839, "guid": "704d90fd354daea498b48ab7779382a2"}, "extraPayloads": [], "path": "Game/GamePivot/FreeSpinsPurchase/FSPurchaseWindow/Content/AnimatedPivot/Texts/BuyText/label", "oldContent": "BUY FREE SPINS", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 1840, "guid": "704d90fd354daea498b48ab7779382a2"}, "extraPayloads": [], "path": "Game/GamePivot/FsFeature/content/Feature_trail/Holder/fisherman_highlight/ScalerX10/stretcher_fs/spins", "oldContent": "spins", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 1841, "guid": "704d90fd354daea498b48ab7779382a2"}, "extraPayloads": [], "path": "Game/Background/PaytableOnScreen/Portrait/Message1/labelMsg1", "oldContent": "AL<PERSON> SYMBOLS PAY FROM LEFT TO RIGHT. BONUS PAYS ON ANY POSITION.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 1842, "guid": "704d90fd354daea498b48ab7779382a2"}, "extraPayloads": [], "path": "Game/GamePivot/FsFeature/content/Feature_trail/Holder/fisherman_highlight/ScalerX3/stretcher_fs/free", "oldContent": "free", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 1843, "guid": "704d90fd354daea498b48ab7779382a2"}, "extraPayloads": [], "path": "Game/GamePivot/Reels/ThePivot/BonusMessages/Fisherman/Labels/uilabel", "oldContent": "MORE FISHERMEN!", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 1844, "guid": "704d90fd354daea498b48ab7779382a2"}, "extraPayloads": [], "path": "Game/GamePivot/FreeSpinsPurchase/BuyButtons/Options/Option_0/OptVisual/Texts/BoughtText/LandscapeText/label0", "oldContent": "FEATURE", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 1845, "guid": "704d90fd354daea498b48ab7779382a2"}, "extraPayloads": [], "path": "Game/GamePivot/FreeSpinsPurchase/BuyButtons/Options/Option_0/OptVisual/Texts/BuyText/PortraitText/label0", "oldContent": "BUY FREE SPINS", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 1846, "guid": "704d90fd354daea498b48ab7779382a2"}, "extraPayloads": [], "path": "Game/GamePivot/FsFeature/content/Feature_trail/Holder/fisherman_highlight/ScalerX2/stretcher_fs/free", "oldContent": "free", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 1847, "guid": "704d90fd354daea498b48ab7779382a2"}, "extraPayloads": [], "path": "Game/GamePivot/FSResultWindow/content/SignPivot/FreespinsCongratsWindow/Labels/Congrats_label", "oldContent": "CONGRATULATIONS!", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 1848, "guid": "704d90fd354daea498b48ab7779382a2"}, "extraPayloads": [], "path": "Game/GamePivot/FSExtraWindow/content/SignPivot/FreespinsExtraWindow/Labels/Congrats_label", "oldContent": "CONGRATULATIONS!", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 1849, "guid": "704d90fd354daea498b48ab7779382a2"}, "extraPayloads": [], "path": "Game/GamePivot/FSExtraWindow/content/SignPivot/FreespinsExtraWindow/Labels/FreeSpins_label ", "oldContent": "FREE SPINS", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 1850, "guid": "704d90fd354daea498b48ab7779382a2"}, "extraPayloads": [], "path": "Game/GamePivot/FSExtraWindow/content/SignPivot/FreespinsExtraWindow/Labels/AreNow_label", "oldContent": "ARE NOW", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 1851, "guid": "704d90fd354daea498b48ab7779382a2"}, "extraPayloads": [], "path": "Game/GamePivot/FreeSpinsPurchase/BuyButtons/Options/Option_0/OptVisual/Texts/BuyText/LandscapeTest/label0", "oldContent": "BUY FREE SPINS", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 1852, "guid": "704d90fd354daea498b48ab7779382a2"}, "extraPayloads": [], "path": "Game/GamePivot/Reels/ThePivot/BonusMessages/PlusFS/Labels/uilabel", "oldContent": "EXTRA FREE SPINS!", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 1853, "guid": "704d90fd354daea498b48ab7779382a2"}, "extraPayloads": [], "path": "Game/GamePivot/FsFeature/content/Feature_trail/Holder/fisherman_highlight/ScalerX10/stretcher_fs/free", "oldContent": "free", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 1854, "guid": "704d90fd354daea498b48ab7779382a2"}, "extraPayloads": [], "path": "Game/GamePivot/FSWONWindow/content/FreespinsWonWindow/LabelsAndSprites/Congratulations_label", "oldContent": "CONGRATULATIONS!", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 1855, "guid": "704d90fd354daea498b48ab7779382a2"}, "extraPayloads": [], "path": "Game/GamePivot/Reels/ThePivot/BonusMessages/Fishes/Labels/uilabel", "oldContent": "MORE FISH!", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 1856, "guid": "704d90fd354daea498b48ab7779382a2"}, "extraPayloads": [], "path": "Game/GamePivot/FreeSpinsPurchase/BuyButtons/Options/Option_0/OptVisual/Texts/BoughtText/PortraitText/label0", "oldContent": "FEATURE ACTIVE", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 1857, "guid": "704d90fd354daea498b48ab7779382a2"}, "extraPayloads": [], "path": "Game/GamePivot/FSExtraWindow/content/SignPivot/FreespinsExtraWindow/Labels/Extra_label", "oldContent": "EXTRA", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 1858, "guid": "704d90fd354daea498b48ab7779382a2"}, "extraPayloads": [], "path": "Game/GamePivot/FSResultWindow/content/SignPivot/FreespinsCongratsWindow/Labels/YouWon_label", "oldContent": "YOU HAVE WON", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 1859, "guid": "704d90fd354daea498b48ab7779382a2"}, "extraPayloads": [], "path": "Game/GamePivot/FsFeature/content/Feature_trail/Holder/fisherman_highlight/ScalerX3/stretcher_fs/spins", "oldContent": "spins", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 1860, "guid": "704d90fd354daea498b48ab7779382a2"}, "extraPayloads": [], "path": "Game/Background/PaytableOnScreen/Landscape/Message1/labelMsg1", "oldContent": "AL<PERSON> SYMBOLS PAY FROM LEFT TO RIGHT. BONUS PAYS ON ANY POSITION.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 1861, "guid": "704d90fd354daea498b48ab7779382a2"}, "extraPayloads": [], "path": "Game/GamePivot/FsFeature/content/Feature_trail/Holder/fisherman_highlight/ScalerX2/stretcher_fs/spins", "oldContent": "spins", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 1862, "guid": "704d90fd354daea498b48ab7779382a2"}, "extraPayloads": [], "path": "Game/GamePivot/Reels/ThePivot/BonusMessages/Level2/Labels/uilabel", "oldContent": "START FROM LEVEL 2!", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 1863, "guid": "704d90fd354daea498b48ab7779382a2"}, "extraPayloads": [], "path": "Game/GamePivot/FSWONWindow/content/FreespinsWonWindow/LabelsAndSprites/FreeSpins_label", "oldContent": "FREE SPINS!", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 1864, "guid": "704d90fd354daea498b48ab7779382a2"}, "extraPayloads": [], "path": "Game/GamePivot/FSResultWindow/content/PressAnywhere_Label/label", "oldContent": "Press anywhere to continue", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 1865, "guid": "704d90fd354daea498b48ab7779382a2"}, "extraPayloads": [], "path": "Game/GamePivot/Reels/ThePivot/BonusMessages/Hooks/Labels/uilabel", "oldContent": "MORE HOOKS AND EXPLOSIONS!", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 1866, "guid": "704d90fd354daea498b48ab7779382a2"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/CAT/RuleHolder2/Rule2", "oldContent": "When buying the FREE SPINS round, on the triggering spin 3, 4 or 5 SCATTERS can hit randomly.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 1867, "guid": "704d90fd354daea498b48ab7779382a2"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder4/Label4", "oldContent": "- START FROM LEVEL 2 - The round starts from level 2 in the progressive feature.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 1868, "guid": "704d90fd354daea498b48ab7779382a2"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder5/Label5", "oldContent": "- +2 SPINS - The subsequent round starts with 2 more free spins from the beginning and 2 more spins are added to every retrigger.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 1869, "guid": "704d90fd354daea498b48ab7779382a2"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/RuleHolder1/Rule1", "oldContent": "Hit 3 or more SCATTER symbols to trigger the FREE SPINS feature.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 1870, "guid": "704d90fd354daea498b48ab7779382a2"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/RulesTop/AllValuesExpressed", "oldContent": "All values are expressed as actual wins in coins.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 1871, "guid": "704d90fd354daea498b48ab7779382a2"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/Volatility/VolatilityDescription", "oldContent": "High volatility games pay out less often on average but the chance to hit big wins in a short time span is higher", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 1872, "guid": "704d90fd354daea498b48ab7779382a2"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/MoneySymbolHolder/DescriptionHolder/MoneySymbolRules/Label3", "oldContent": "The fish paying symbols are also MONEY symbols. At every spin, the fish take a random money value which can be won during the FREE SPINS feature.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 1873, "guid": "704d90fd354daea498b48ab7779382a2"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/RulesBottom/RTP/TheoreticalRTP/Label", "oldContent": "The theoretical RTP of this game is {0}%", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 1874, "guid": "704d90fd354daea498b48ab7779382a2"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder8/Rule8", "oldContent": "Also randomly, when there are fisherman symbols on the screen but no fish, at the end of a free spin, a bazooka animation can appear and change all the symbols from the screen, except for fisherman symbols to something else.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 1875, "guid": "704d90fd354daea498b48ab7779382a2"}, "extraPayloads": [], "path": "Paytable/Pages/Page1/Rules/AllSymbolsPayLabel", "oldContent": "All symbols pay from left to right on adjacent reels starting from the leftmost reel.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 1876, "guid": "704d90fd354daea498b48ab7779382a2"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder5/Rule5", "oldContent": "After the fourth level, the feature cannot be retriggered anymore.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 1877, "guid": "704d90fd354daea498b48ab7779382a2"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/FreeSpinsHolder2/SpecialReelsHolder/SpecialReels", "oldContent": "Special reels are in play during the feature.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 1878, "guid": "704d90fd354daea498b48ab7779382a2"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/CAT/RuleHolder1/Rule1", "oldContent": "The FREE SPINS round can be instantly triggered from the base game by buying it for 100x current total bet.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 1879, "guid": "704d90fd354daea498b48ab7779382a2"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/RuleHolder2/Rule2", "oldContent": "In the base game whenever 2 SCATTER symbols hit without a third, there is a chance for another one to be brought onto the screen by a random feature:", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 1880, "guid": "704d90fd354daea498b48ab7779382a2"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder1/Label1", "oldContent": "- MORE FISH - More fish symbols are present on the reel strips during the subsequent free spins round", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 1881, "guid": "704d90fd354daea498b48ab7779382a2"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder2/Rule2", "oldContent": "All the WILD symbols that hit during the feature are collected until the end of the round.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 1882, "guid": "704d90fd354daea498b48ab7779382a2"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/RulesTop/AllSymbolsPay", "oldContent": "All symbols pay from left to right on selected paylines.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 1883, "guid": "704d90fd354daea498b48ab7779382a2"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder3/Label3", "oldContent": "- MORE DYNAMITES, HOOKS AND BAZOOKAS - During the round, the chance to hit dynamite, hook or bazooka spin feature is increased.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 1884, "guid": "704d90fd354daea498b48ab7779382a2"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/RulesBottom/SpaceAndEnter", "oldContent": "SPACE and ENTER buttons on the keyboard can be used to start and stop the spin.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 1885, "guid": "704d90fd354daea498b48ab7779382a2"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder7/Rule7", "oldContent": "Randomly, when there are fish symbols on the screen but no fisherman, at the end of a free spin, a hook will appear pulling a random reel up to bring fisherman symbols onto the screen.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 1886, "guid": "704d90fd354daea498b48ab7779382a2"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder1/Rule1", "oldContent": "During the FREE SPINS feature each WILD symbol also collects all the values from MONEY symbols on the screen.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 1887, "guid": "704d90fd354daea498b48ab7779382a2"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/MaxWin/TitleHolder/Title", "oldContent": "MAX WIN", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 1888, "guid": "704d90fd354daea498b48ab7779382a2"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder1/LabelHolder2/Label2", "oldContent": "4x SCATTER awards 15 free spins", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 1889, "guid": "704d90fd354daea498b48ab7779382a2"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/MinMaxHolder/MaxBet/MaximumText", "oldContent": "MAXIMUM BET:", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 1890, "guid": "704d90fd354daea498b48ab7779382a2"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FreeSpinsHolder1/TitleHolder/Title", "oldContent": "FREE SPINS", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 1891, "guid": "704d90fd354daea498b48ab7779382a2"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/MoneySymbolHolder/TitleHolder/Title", "oldContent": "MONEY SYMBOL", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 1892, "guid": "704d90fd354daea498b48ab7779382a2"}, "extraPayloads": [], "path": "Paytable/Pages/Page1/WildHolder/DescriptionHolder/Label1", "oldContent": "This is the WILD symbol.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 1893, "guid": "704d90fd354daea498b48ab7779382a2"}, "extraPayloads": [], "path": "Paytable/Pages/Page1/Title/PaytableTitleLabel1", "oldContent": "GAME RULES", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 1894, "guid": "704d90fd354daea498b48ab7779382a2"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/CAT/TitleHolder/Title1New", "oldContent": "BUY FREE SPINS", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 1895, "guid": "704d90fd354daea498b48ab7779382a2"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/MinMaxHolder/MinBet/MinimumText", "oldContent": "MINIMUM BET:", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 1896, "guid": "704d90fd354daea498b48ab7779382a2"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/RulesBottom/MalfunctionLabel", "oldContent": "Malfunction voids all pays and plays.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 1897, "guid": "704d90fd354daea498b48ab7779382a2"}, "extraPayloads": [], "path": "Paytable/Pages/Page1/ScatterHolder/DescriptionHolder/Label2", "oldContent": "It appears on all reels.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 1898, "guid": "704d90fd354daea498b48ab7779382a2"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder1/LabelHolder1/Label1", "oldContent": "5x SCATTER awards 20 free spins", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 1899, "guid": "704d90fd354daea498b48ab7779382a2"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/RulesTop/AllWinsMultiplied", "oldContent": "All wins are multiplied by bet per line.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 1900, "guid": "704d90fd354daea498b48ab7779382a2"}, "extraPayloads": [], "path": "Paytable/Pages/Page1/ScatterHolder/DescriptionHolder/Label1", "oldContent": "This is the SCATTER symbol.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 1901, "guid": "704d90fd354daea498b48ab7779382a2"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/RulesTop/OnlyTheHighestWin", "oldContent": "Only the highest win is paid per line.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 1902, "guid": "704d90fd354daea498b48ab7779382a2"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder1/LabelHolder3/Label3", "oldContent": "3x SCATTER awards 10 free spins", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 1903, "guid": "704d90fd354daea498b48ab7779382a2"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder2/LabelHolder1/Label1", "oldContent": "- Randomly, if the SCATTERS on the screen can move down one position without leaving the reel area, a respin is triggered where the reels with SCATTERS move one position down and the reels without SCATTERS respin.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 1904, "guid": "704d90fd354daea498b48ab7779382a2"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder4/Rule4", "oldContent": "The retriggered spins are played after the previous batch of free spins ends. The multiplier applies to the retriggered spins.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 1905, "guid": "704d90fd354daea498b48ab7779382a2"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/RulesTop/WhenWinningOnMultiplePaylines", "oldContent": "When winning on multiple paylines, all wins are added to the total win.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 1906, "guid": "704d90fd354daea498b48ab7779382a2"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/Title/PaytableTitleLabel", "oldContent": "GAME RULES", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 1907, "guid": "704d90fd354daea498b48ab7779382a2"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/MaxWin/RuleHolder/Label1", "oldContent": "The maximum win amount is limited to {0}x bet. If the total win of a FREE SPINS ROUND reaches {1}x the round immediately ends, win is awarded and all remaining free spins are forfeited", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 1908, "guid": "704d90fd354daea498b48ab7779382a2"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/RulesBottom/RTP/TheoreticalRTPBONUS/Label", "oldContent": "The RTP of the game when using \"BUY FREE SPINS\" is {0}%", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 1909, "guid": "704d90fd354daea498b48ab7779382a2"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/Volatility/VolatilityMeter/LabelHolder/VolatilityLabel", "oldContent": "VOLATILITY", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 1910, "guid": "704d90fd354daea498b48ab7779382a2"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder6/Rule6", "oldContent": "Randomly, when there are fisherman symbols on the screen but no fish, at the end of a free spin, fish MONEY symbols can appear in random positions via the dynamite spin feature.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 1911, "guid": "704d90fd354daea498b48ab7779382a2"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder2/LabelHolder2/Label2", "oldContent": "- Randomly, a hook can pull one of the reels up to reveal another SCATTER.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 1912, "guid": "704d90fd354daea498b48ab7779382a2"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/RuleHolder3/Rule3", "oldContent": "Before the round starts, 0 to 5 modifiers that apply to the subsequent round are randomly selected:", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 1913, "guid": "704d90fd354daea498b48ab7779382a2"}, "extraPayloads": [], "path": "Paytable/Pages/Page1/WildHolder/DescriptionHolder/Label2", "oldContent": "It appears on all reels during the FREE SPINS round.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 1914, "guid": "704d90fd354daea498b48ab7779382a2"}, "extraPayloads": [], "path": "Paytable/Pages/Page1/WildHolder/DescriptionHolder/Label3", "oldContent": "Substitutes for all symbols except SCATTER.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 1915, "guid": "704d90fd354daea498b48ab7779382a2"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder3/Rule3", "oldContent": "Every 4th WILD symbol collected retriggers the feature, awards 10 more free spins and the multiplier for MONEY symbol collection increases to 2x for the second level, 3x for the third level and 10x for the fourth level.  ", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 1916, "guid": "704d90fd354daea498b48ab7779382a2"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder2/Label2", "oldContent": "- MORE FISHERMAN - More WILD symbols are present on the reel strips during the subsequent free spins round", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 1917, "guid": "704d90fd354daea498b48ab7779382a2"}, "extraPayloads": [], "path": "IntroScreen/content/Labels_Holder_landscape/Label_Holder_bigger_1/Label_2 (1)", "oldContent": "BIG FREE SPINS MODIFIERS!", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 1918, "guid": "704d90fd354daea498b48ab7779382a2"}, "extraPayloads": [], "path": "IntroScreen/content/Labels_Holder_landscape/Label_Holder_bigger_1/Label_1", "oldContent": "GO FISHIN' FOR", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 1919, "guid": "704d90fd354daea498b48ab7779382a2"}, "extraPayloads": [], "path": "IntroScreen/content/IntroButtons/ButtonSkipIntro/content/TextHolder/Label_1", "oldContent": "DON'T SHOW NEXT TIME", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 1920, "guid": "704d90fd354daea498b48ab7779382a2"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/MoneySymbolHolder/DescriptionHolder/PossibleValues/Label", "oldContent": "Possible values are: 2x, 5x, 10x, 15x, 20x, 25x, 50x, 100x, 200x, 500x, 1666x, 2500x or 5000x total bet.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 1921, "guid": "704d90fd354daea498b48ab7779382a2"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/MaxWin/RuleHolder/HolderLabelJackpot/Label1New", "oldContent": "The maximum win amount is limited to {0}x bet except Jack<PERSON>. If the total win of a FREE SPINS round reaches {1}x bet the round immediately ends, win is awarded and all remaining free spins are forfeited.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}], "Spines": [], "revisionNumber": 0}, "fileID": 1922}], "fileID": 1923}, {"name": "Game/GamePivot/FSWONWindow/content/FreespinsWonWindow/LabelsAndSprites/Catches_label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 1834, "guid": "704d90fd354daea498b48ab7779382a2"}, "children": [], "psr": "d"}, "fileID": 1747}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "1b5a1f73d7753974da5dd27a21e4f091"}, "_text": "CAPTURAS", "fontSize": 50, "width": 292, "height": 110, "overflow": 0}, "fileID": 1835}], "fileID": 1924}, {"name": "Game/GamePivot/FreeSpinsPurchase/BuyButtons/Options/Option_0/OptVisual/Texts/BoughtText/LandscapeText/label1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 1834, "guid": "704d90fd354daea498b48ab7779382a2"}, "children": [], "psr": "d"}, "fileID": 1748}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "f2d59b0d1332955479cb70e85387e7de"}, "_text": "ATIVO", "fontSize": 40, "width": 170, "height": 80, "overflow": 0}, "fileID": 1836}], "fileID": 1925}, {"name": "Game/GamePivot/FSWONWindow/PressAnywhere_Label/label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 1834, "guid": "704d90fd354daea498b48ab7779382a2"}, "children": [], "psr": "d"}, "fileID": 1749}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "1b5a1f73d7753974da5dd27a21e4f091"}, "_text": "Toque em qualquer lado para continuar", "fontSize": 40, "width": 1340, "height": 65, "overflow": 0}, "fileID": 1837}], "fileID": 1926}, {"name": "Game/GamePivot/FSExtraWindow/content/PressAnywhere_Label/label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 1834, "guid": "704d90fd354daea498b48ab7779382a2"}, "children": [], "psr": "d"}, "fileID": 1750}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "1b5a1f73d7753974da5dd27a21e4f091"}, "_text": "Toque em qualquer lado para continuar", "fontSize": 40, "width": 1340, "height": 65, "overflow": 0}, "fileID": 1838}], "fileID": 1927}, {"name": "Game/GamePivot/FreeSpinsPurchase/FSPurchaseWindow/Content/AnimatedPivot/Texts/BuyText/label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 1834, "guid": "704d90fd354daea498b48ab7779382a2"}, "children": [], "psr": "d"}, "fileID": 1751}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "1b5a1f73d7753974da5dd27a21e4f091"}, "_text": "COMPRAR RODADAS GRÁTIS", "fontSize": 69, "width": 826, "height": 69, "overflow": 0}, "fileID": 1839}], "fileID": 1928}, {"name": "Game/GamePivot/FsFeature/content/Feature_trail/Holder/fisherman_highlight/ScalerX10/stretcher_fs/spins", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 1834, "guid": "704d90fd354daea498b48ab7779382a2"}, "children": [], "psr": "d"}, "fileID": 1752}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "25471c78efdb3a746b8dc96f6d01805c"}, "_text": "rodadas", "fontSize": 30, "width": 64, "height": 33, "overflow": 0}, "fileID": 1840}], "fileID": 1929}, {"name": "Game/Background/PaytableOnScreen/Portrait/Message1/labelMsg1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 1834, "guid": "704d90fd354daea498b48ab7779382a2"}, "children": [], "psr": "d"}, "fileID": 1753}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "8d3bb74cec4162a4987d1775862b8d44"}, "_text": "TODOS OS SÍMBOLOS PAGAM DA ESQUERDA PARA A DIREITA. BÓNUS PAGA EM QUALQUER POSIÇÃO", "fontSize": 40, "width": 1712, "height": 40, "alignment": 2}, "fileID": 1841}], "fileID": 1930}, {"name": "Game/GamePivot/FsFeature/content/Feature_trail/Holder/fisherman_highlight/ScalerX3/stretcher_fs/free", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 1834, "guid": "704d90fd354daea498b48ab7779382a2"}, "children": [], "psr": "d"}, "fileID": 1754}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "25471c78efdb3a746b8dc96f6d01805c"}, "_text": "<PERSON><PERSON><PERSON><PERSON>", "fontSize": 30, "width": 60, "height": 33, "overflow": 0}, "fileID": 1842}], "fileID": 1931}, {"name": "Game/GamePivot/Reels/ThePivot/BonusMessages/Fisherman/Labels/uilabel", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 1834, "guid": "704d90fd354daea498b48ab7779382a2"}, "children": [], "psr": "d"}, "fileID": 1755}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "1b5a1f73d7753974da5dd27a21e4f091"}, "_text": "MAIS PESCADORES!", "fontSize": 256, "width": 1000, "height": 450, "overflow": 0}, "fileID": 1843}], "fileID": 1932}, {"name": "Game/GamePivot/FreeSpinsPurchase/BuyButtons/Options/Option_0/OptVisual/Texts/BoughtText/LandscapeText/label0", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 1834, "guid": "704d90fd354daea498b48ab7779382a2"}, "children": [], "psr": "d"}, "fileID": 1756}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "f2d59b0d1332955479cb70e85387e7de"}, "_text": "MODO", "fontSize": 40, "width": 170, "height": 80, "overflow": 0}, "fileID": 1844}], "fileID": 1933}, {"name": "Game/GamePivot/FreeSpinsPurchase/BuyButtons/Options/Option_0/OptVisual/Texts/BuyText/PortraitText/label0", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 1834, "guid": "704d90fd354daea498b48ab7779382a2"}, "children": [], "psr": "d"}, "fileID": 1757}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "1b5a1f73d7753974da5dd27a21e4f091"}, "_text": "COMPRAR RODADAS GRÁTIS", "fontSize": 26, "width": 270, "height": 70, "overflow": 0}, "fileID": 1845}], "fileID": 1934}, {"name": "Game/GamePivot/FsFeature/content/Feature_trail/Holder/fisherman_highlight/ScalerX2/stretcher_fs/free", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 1834, "guid": "704d90fd354daea498b48ab7779382a2"}, "children": [], "psr": "d"}, "fileID": 1758}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "25471c78efdb3a746b8dc96f6d01805c"}, "_text": "<PERSON><PERSON><PERSON><PERSON>", "fontSize": 30, "width": 60, "height": 33, "overflow": 0}, "fileID": 1846}], "fileID": 1935}, {"name": "Game/GamePivot/FSResultWindow/content/SignPivot/FreespinsCongratsWindow/Labels/Congrats_label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 1834, "guid": "704d90fd354daea498b48ab7779382a2"}, "children": [], "psr": "d"}, "fileID": 1759}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "1b5a1f73d7753974da5dd27a21e4f091"}, "_text": "PARABÉNS!", "fontSize": 169, "width": 389, "height": 169, "overflow": 0}, "fileID": 1847}], "fileID": 1936}, {"name": "Game/GamePivot/FSExtraWindow/content/SignPivot/FreespinsExtraWindow/Labels/Congrats_label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 1834, "guid": "704d90fd354daea498b48ab7779382a2"}, "children": [], "psr": "d"}, "fileID": 1760}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "1b5a1f73d7753974da5dd27a21e4f091"}, "_text": "PARABÉNS!", "fontSize": 169, "width": 368, "height": 169, "overflow": 0}, "fileID": 1848}], "fileID": 1937}, {"name": "Game/GamePivot/FSExtraWindow/content/SignPivot/FreespinsExtraWindow/Labels/FreeSpins_label ", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 1834, "guid": "704d90fd354daea498b48ab7779382a2"}, "children": [], "psr": "d"}, "fileID": 1761}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "1b5a1f73d7753974da5dd27a21e4f091"}, "_text": "RODADAS GRÁTIS", "fontSize": 75, "width": 500, "height": 58, "overflow": 0}, "fileID": 1849}], "fileID": 1938}, {"name": "Game/GamePivot/FSExtraWindow/content/SignPivot/FreespinsExtraWindow/Labels/AreNow_label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 1834, "guid": "704d90fd354daea498b48ab7779382a2"}, "children": [], "psr": "d"}, "fileID": 1762}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "1b5a1f73d7753974da5dd27a21e4f091"}, "_text": "ESTÃO AGORA", "fontSize": 70, "width": 500, "height": 69, "overflow": 0}, "fileID": 1850}], "fileID": 1939}, {"name": "Game/GamePivot/FreeSpinsPurchase/BuyButtons/Options/Option_0/OptVisual/Texts/BuyText/LandscapeTest/label0", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 1834, "guid": "704d90fd354daea498b48ab7779382a2"}, "children": [], "psr": "d"}, "fileID": 1763}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "1b5a1f73d7753974da5dd27a21e4f091"}, "_text": "COMPRAR RODADAS GRÁTIS", "fontSize": 26, "width": 214, "height": 33, "overflow": 0}, "fileID": 1851}], "fileID": 1940}, {"name": "Game/GamePivot/Reels/ThePivot/BonusMessages/PlusFS/Labels/uilabel", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 1834, "guid": "704d90fd354daea498b48ab7779382a2"}, "children": [], "psr": "d"}, "fileID": 1764}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "1b5a1f73d7753974da5dd27a21e4f091"}, "_text": "EXTRA RODADAS GRÁTIS", "fontSize": 256, "width": 1200, "height": 450, "overflow": 0}, "fileID": 1852}], "fileID": 1941}, {"name": "Game/GamePivot/FsFeature/content/Feature_trail/Holder/fisherman_highlight/ScalerX10/stretcher_fs/free", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 1834, "guid": "704d90fd354daea498b48ab7779382a2"}, "children": [], "psr": "d"}, "fileID": 1765}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "25471c78efdb3a746b8dc96f6d01805c"}, "_text": "<PERSON><PERSON><PERSON><PERSON>", "fontSize": 30, "width": 60, "height": 33, "overflow": 0}, "fileID": 1853}], "fileID": 1942}, {"name": "Game/GamePivot/FSWONWindow/content/FreespinsWonWindow/LabelsAndSprites/Congratulations_label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 1834, "guid": "704d90fd354daea498b48ab7779382a2"}, "children": [], "psr": "d"}, "fileID": 1766}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "1b5a1f73d7753974da5dd27a21e4f091"}, "_text": "PARABÉNS!", "fontSize": 169, "width": 384, "height": 169, "overflow": 0}, "fileID": 1854}], "fileID": 1943}, {"name": "Game/GamePivot/Reels/ThePivot/BonusMessages/Fishes/Labels/uilabel", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 1834, "guid": "704d90fd354daea498b48ab7779382a2"}, "children": [], "psr": "d"}, "fileID": 1767}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "1b5a1f73d7753974da5dd27a21e4f091"}, "_text": "MAIS PEIXE!", "fontSize": 256, "width": 1200, "height": 450, "overflow": 0}, "fileID": 1855}], "fileID": 1944}, {"name": "Game/GamePivot/FreeSpinsPurchase/BuyButtons/Options/Option_0/OptVisual/Texts/BoughtText/PortraitText/label0", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 1834, "guid": "704d90fd354daea498b48ab7779382a2"}, "children": [], "psr": "d"}, "fileID": 1768}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "f2d59b0d1332955479cb70e85387e7de"}, "_text": "MODO ATIVO", "fontSize": 60, "width": 164, "height": 208, "overflow": 0}, "fileID": 1856}], "fileID": 1945}, {"name": "Game/GamePivot/FSExtraWindow/content/SignPivot/FreespinsExtraWindow/Labels/Extra_label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 1834, "guid": "704d90fd354daea498b48ab7779382a2"}, "children": [], "psr": "d"}, "fileID": 1769}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "1b5a1f73d7753974da5dd27a21e4f091"}, "_text": "EXTRA", "fontSize": 75, "width": 500, "height": 58, "overflow": 0}, "fileID": 1857}], "fileID": 1946}, {"name": "Game/GamePivot/FSResultWindow/content/SignPivot/FreespinsCongratsWindow/Labels/YouWon_label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 1834, "guid": "704d90fd354daea498b48ab7779382a2"}, "children": [], "psr": "d"}, "fileID": 1770}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "1b5a1f73d7753974da5dd27a21e4f091"}, "_text": "GANHOU", "fontSize": 110, "width": 1000, "height": 169, "overflow": 0}, "fileID": 1858}], "fileID": 1947}, {"name": "Game/GamePivot/FsFeature/content/Feature_trail/Holder/fisherman_highlight/ScalerX3/stretcher_fs/spins", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 1834, "guid": "704d90fd354daea498b48ab7779382a2"}, "children": [], "psr": "d"}, "fileID": 1771}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "25471c78efdb3a746b8dc96f6d01805c"}, "_text": "rodadas", "fontSize": 30, "width": 64, "height": 33, "overflow": 0}, "fileID": 1859}], "fileID": 1948}, {"name": "Game/Background/PaytableOnScreen/Landscape/Message1/labelMsg1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 1834, "guid": "704d90fd354daea498b48ab7779382a2"}, "children": [], "psr": "d"}, "fileID": 1772}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "8d3bb74cec4162a4987d1775862b8d44"}, "_text": "TODOS OS SÍMBOLOS PAGAM DA ESQUERDA PARA A DIREITA. BÓNUS PAGA EM QUALQUER POSIÇÃO", "fontSize": 30, "width": 1282, "height": 30, "alignment": 2}, "fileID": 1860}], "fileID": 1949}, {"name": "Game/GamePivot/FsFeature/content/Feature_trail/Holder/fisherman_highlight/ScalerX2/stretcher_fs/spins", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 1834, "guid": "704d90fd354daea498b48ab7779382a2"}, "children": [], "psr": "d"}, "fileID": 1773}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "25471c78efdb3a746b8dc96f6d01805c"}, "_text": "rodadas", "fontSize": 30, "width": 64, "height": 33, "overflow": 0}, "fileID": 1861}], "fileID": 1950}, {"name": "Game/GamePivot/Reels/ThePivot/BonusMessages/Level2/Labels/uilabel", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 1834, "guid": "704d90fd354daea498b48ab7779382a2"}, "children": [], "psr": "d"}, "fileID": 1774}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "1b5a1f73d7753974da5dd27a21e4f091"}, "_text": "COMECE NO NÍVEL 2!", "fontSize": 256, "width": 1280, "height": 450, "overflow": 0}, "fileID": 1862}], "fileID": 1951}, {"name": "Game/GamePivot/FSWONWindow/content/FreespinsWonWindow/LabelsAndSprites/FreeSpins_label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 1834, "guid": "704d90fd354daea498b48ab7779382a2"}, "children": [], "psr": "d"}, "fileID": 1775}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "1b5a1f73d7753974da5dd27a21e4f091"}, "_text": "RODADAS GRÁTIS", "fontSize": 110, "width": 845, "height": 150, "overflow": 0}, "fileID": 1863}], "fileID": 1952}, {"name": "Game/GamePivot/FSResultWindow/content/PressAnywhere_Label/label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 1834, "guid": "704d90fd354daea498b48ab7779382a2"}, "children": [], "psr": "d"}, "fileID": 1776}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "1b5a1f73d7753974da5dd27a21e4f091"}, "_text": "Toque em qualquer lado para continuar", "fontSize": 40, "width": 1340, "height": 65, "overflow": 0}, "fileID": 1864}], "fileID": 1953}, {"name": "Game/GamePivot/Reels/ThePivot/BonusMessages/Hooks/Labels/uilabel", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 1834, "guid": "704d90fd354daea498b48ab7779382a2"}, "children": [], "psr": "d"}, "fileID": 1777}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "1b5a1f73d7753974da5dd27a21e4f091"}, "_text": "MAIS ANZÓIS E EXPLOSÕES!", "fontSize": 256, "width": 1000, "height": 450, "overflow": 0}, "fileID": 1865}], "fileID": 1954}, {"name": "Paytable/Pages/Page3/CAT/RuleHolder2/Rule2", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 1834, "guid": "704d90fd354daea498b48ab7779382a2"}, "children": [], "psr": "d"}, "fileID": 1778}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "3d220d8d5d5edc9419eb9686f9a53aa8"}, "_text": "Aquando da compra da ronda de RODADAS GRÁTIS, na rodada de ativação podem aparecer aleatoriamente 3, 4 ou 5 SCATTERS aleatoriamente.", "fontSize": 25, "anchorY": 0, "width": 1400, "height": 75, "overflow": 0, "spacingY": 5}, "fileID": 1866}], "fileID": 1955}, {"name": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder4/Label4", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 1834, "guid": "704d90fd354daea498b48ab7779382a2"}, "children": [], "psr": "d"}, "fileID": 1779}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "3d220d8d5d5edc9419eb9686f9a53aa8"}, "_text": "COMECE NO NÍVEL 2 - A ronda começa no nível 2 no modo progressivo.", "fontSize": 25, "anchorX": 0, "width": 1400, "height": 100, "overflow": 0}, "fileID": 1867}], "fileID": 1956}, {"name": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder5/Label5", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 1834, "guid": "704d90fd354daea498b48ab7779382a2"}, "children": [], "psr": "d"}, "fileID": 1780}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "3d220d8d5d5edc9419eb9686f9a53aa8"}, "_text": "- +2 RODADAS - A ronda subsequente começa com mais 2 rodadas grátis desde o início, sendo adicionadas mais 2 rodadas com cada reativação.", "fontSize": 25, "anchorX": 0, "width": 1400, "height": 100, "overflow": 0}, "fileID": 1868}], "fileID": 1957}, {"name": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/RuleHolder1/Rule1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 1834, "guid": "704d90fd354daea498b48ab7779382a2"}, "children": [], "psr": "d"}, "fileID": 1781}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "3d220d8d5d5edc9419eb9686f9a53aa8"}, "_text": "Obtenha 3 símbolos SCATTER ou mais para ativar o modo RODADAS GRÁTIS.", "fontSize": 25, "width": 1400, "height": 100, "overflow": 0}, "fileID": 1869}], "fileID": 1958}, {"name": "Paytable/Pages/Page4/RulesTop/AllValuesExpressed", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 1834, "guid": "704d90fd354daea498b48ab7779382a2"}, "children": [], "psr": "d"}, "fileID": 1782}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "3d220d8d5d5edc9419eb9686f9a53aa8"}, "_text": "Todos os valores são expressos como prémios reais em moedas.", "fontSize": 25, "width": 1333, "height": 160, "overflow": 0}, "fileID": 1870}], "fileID": 1959}, {"name": "Paytable/Pages/Page4/Volatility/VolatilityDescription", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 1834, "guid": "704d90fd354daea498b48ab7779382a2"}, "children": [], "psr": "d"}, "fileID": 1783}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "3d220d8d5d5edc9419eb9686f9a53aa8"}, "_text": "Os jogos de alta volatilidade pagam menos frequentemente, em média, mas a possibilidade de grandes prémios num curto período de tempo é maior", "fontSize": 25, "anchorY": 0, "width": 1368, "height": 93, "overflow": 0}, "fileID": 1871}], "fileID": 1960}, {"name": "Paytable/Pages/Page2/MoneySymbolHolder/DescriptionHolder/MoneySymbolRules/Label3", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 1834, "guid": "704d90fd354daea498b48ab7779382a2"}, "children": [], "psr": "d"}, "fileID": 1784}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "3d220d8d5d5edc9419eb9686f9a53aa8"}, "_text": "Os símbolos de pagamento dos peixes são também símbolos DINHEIRO. Em cada rodada, os peixes ficam com um valor monetário aleatório, que os jogadores podem ganhar durante o modo das RODADAS GRÁTIS.", "fontSize": 25, "anchorX": 0, "width": 935, "height": 100, "overflow": 0}, "fileID": 1872}], "fileID": 1961}, {"name": "Paytable/Pages/Page4/RulesBottom/RTP/TheoreticalRTP/Label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 1834, "guid": "704d90fd354daea498b48ab7779382a2"}, "children": [], "psr": "d"}, "fileID": 1785}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "3d220d8d5d5edc9419eb9686f9a53aa8"}, "_text": "O RTP teórico deste jogo <PERSON> {0}%", "fontSize": 25, "anchorY": 1, "width": 1200, "height": 75, "overflow": 0}, "fileID": 1873}], "fileID": 1962}, {"name": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder8/Rule8", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 1834, "guid": "704d90fd354daea498b48ab7779382a2"}, "children": [], "psr": "d"}, "fileID": 1786}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "3d220d8d5d5edc9419eb9686f9a53aa8"}, "_text": "Tamb<PERSON><PERSON> aleator<PERSON>, quando existem símbolos de pescador no ecrã mas não há símbolos de peixe, no final de uma rodada gr<PERSON><PERSON>, uma animação de bazuca pode aparecer e alterar todos os símbolos no ecrã (exceto os símbolos pescador) para outra coisa.", "fontSize": 25, "width": 1400, "height": 100, "overflow": 0}, "fileID": 1874}], "fileID": 1963}, {"name": "Paytable/Pages/Page1/Rules/AllSymbolsPayLabel", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 1834, "guid": "704d90fd354daea498b48ab7779382a2"}, "children": [], "psr": "d"}, "fileID": 1787}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "3d220d8d5d5edc9419eb9686f9a53aa8"}, "_text": "Todos os símbolos pagam da esquerda para a direita em rolos adjacentes a começar do mais à esquerda.", "fontSize": 25, "width": 1000, "height": 60, "overflow": 0}, "fileID": 1875}], "fileID": 1964}, {"name": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder5/Rule5", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 1834, "guid": "704d90fd354daea498b48ab7779382a2"}, "children": [], "psr": "d"}, "fileID": 1788}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "3d220d8d5d5edc9419eb9686f9a53aa8"}, "_text": "Após o quarto nível o modo já não pode ser reativado.", "fontSize": 25, "width": 1400, "height": 100, "overflow": 0}, "fileID": 1876}], "fileID": 1965}, {"name": "Paytable/Pages/Page3/FreeSpinsHolder2/SpecialReelsHolder/SpecialReels", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 1834, "guid": "704d90fd354daea498b48ab7779382a2"}, "children": [], "psr": "d"}, "fileID": 1789}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "3d220d8d5d5edc9419eb9686f9a53aa8"}, "_text": "Durante o modo são utilizados rolos especiais para o jogo.", "fontSize": 25, "width": 1400, "height": 100, "overflow": 0}, "fileID": 1877}], "fileID": 1966}, {"name": "Paytable/Pages/Page3/CAT/RuleHolder1/Rule1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 1834, "guid": "704d90fd354daea498b48ab7779382a2"}, "children": [], "psr": "d"}, "fileID": 1790}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "3d220d8d5d5edc9419eb9686f9a53aa8"}, "_text": "A RONDA DE RODADAS GRÁTIS pode ser ativada instantaneamente a partir do jogo base, comprando-a por 100x a aposta total atual. ", "fontSize": 25, "anchorY": 1, "width": 1400, "height": 70, "overflow": 0}, "fileID": 1878}], "fileID": 1967}, {"name": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/RuleHolder2/Rule2", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 1834, "guid": "704d90fd354daea498b48ab7779382a2"}, "children": [], "psr": "d"}, "fileID": 1791}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "3d220d8d5d5edc9419eb9686f9a53aa8"}, "_text": "No jogo base, sempre que aparecem 2 símbolos SCATTER sem aparecer um terceiro, há uma hipótese de um modo aleatório puxar mais um símbolo para o ecrã.", "fontSize": 25, "width": 1400, "height": 100, "overflow": 0}, "fileID": 1879}], "fileID": 1968}, {"name": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder1/Label1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 1834, "guid": "704d90fd354daea498b48ab7779382a2"}, "children": [], "psr": "d"}, "fileID": 1792}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "3d220d8d5d5edc9419eb9686f9a53aa8"}, "_text": "- MAIS PEIXE - <PERSON><PERSON> s<PERSON> de peixe presentes nas faixas dos rolos durante a ronda de rodadas grátis <PERSON>e", "fontSize": 25, "anchorX": 0, "width": 1128, "height": 55, "overflow": 0}, "fileID": 1880}], "fileID": 1969}, {"name": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder2/Rule2", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 1834, "guid": "704d90fd354daea498b48ab7779382a2"}, "children": [], "psr": "d"}, "fileID": 1793}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "3d220d8d5d5edc9419eb9686f9a53aa8"}, "_text": "Todos os símbolos WILD obtidos durante o modo são recolhidos no final da ronda.", "fontSize": 25, "width": 1400, "height": 100, "overflow": 0}, "fileID": 1881}], "fileID": 1970}, {"name": "Paytable/Pages/Page4/RulesTop/AllSymbolsPay", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 1834, "guid": "704d90fd354daea498b48ab7779382a2"}, "children": [], "psr": "d"}, "fileID": 1794}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "3d220d8d5d5edc9419eb9686f9a53aa8"}, "_text": "Todos os símbolos pagam da esquerda para a direita nas linha de pagamento selecionadas.", "fontSize": 25, "width": 1194, "height": 160, "overflow": 0, "spacingY": 5}, "fileID": 1882}], "fileID": 1971}, {"name": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder3/Label3", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 1834, "guid": "704d90fd354daea498b48ab7779382a2"}, "children": [], "psr": "d"}, "fileID": 1795}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "3d220d8d5d5edc9419eb9686f9a53aa8"}, "_text": "- MAIS DINAMITES, ANZÓIS E BAZUCAS - Durante a ronda, a possibilidade de obter o modo de rodada dinamite, anzol ou bazuca é aumentada.", "fontSize": 25, "anchorX": 0, "width": 1400, "height": 100, "overflow": 0}, "fileID": 1883}], "fileID": 1972}, {"name": "Paytable/Pages/Page4/RulesBottom/SpaceAndEnter", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 1834, "guid": "704d90fd354daea498b48ab7779382a2"}, "children": [], "psr": "d"}, "fileID": 1796}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "3d220d8d5d5edc9419eb9686f9a53aa8"}, "_text": "Os botões ESPAÇO e ENTER do teclado podem ser usados para iniciar e parar de rodar.", "fontSize": 25, "width": 1400, "height": 60, "overflow": 0}, "fileID": 1884}], "fileID": 1973}, {"name": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder7/Rule7", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 1834, "guid": "704d90fd354daea498b48ab7779382a2"}, "children": [], "psr": "d"}, "fileID": 1797}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "3d220d8d5d5edc9419eb9686f9a53aa8"}, "_text": "Aleatoriamente, quando existem símbolos de peixe no ecrã mas não há símbolos de pescador, no final de uma rodada grátis aparecerá um anzol que irá puxar um rolo aleatório para trazer símbolos de pescador para o ecrã.", "fontSize": 25, "width": 1384, "height": 100, "overflow": 0}, "fileID": 1885}], "fileID": 1974}, {"name": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder1/Rule1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 1834, "guid": "704d90fd354daea498b48ab7779382a2"}, "children": [], "psr": "d"}, "fileID": 1798}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "3d220d8d5d5edc9419eb9686f9a53aa8"}, "_text": "Durante o modo de RODADAS GRÁTIS, os símbolos WILD acumulam também os valores dos símbolos DINHEIRO presentes no ecrã.", "fontSize": 25, "width": 1037, "height": 100, "overflow": 0}, "fileID": 1886}], "fileID": 1975}, {"name": "Paytable/Pages/Page3/MaxWin/TitleHolder/Title", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 1834, "guid": "704d90fd354daea498b48ab7779382a2"}, "children": [], "psr": "d"}, "fileID": 1799}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "3d220d8d5d5edc9419eb9686f9a53aa8"}, "_text": "PRÉMIO MÁX.", "fontSize": 35, "width": 1100, "height": 100, "overflow": 0}, "fileID": 1887}], "fileID": 1976}, {"name": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder1/LabelHolder2/Label2", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 1834, "guid": "704d90fd354daea498b48ab7779382a2"}, "children": [], "psr": "d"}, "fileID": 1800}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "3d220d8d5d5edc9419eb9686f9a53aa8"}, "_text": "4x símbolos SCATTER atribuem 15 rodadas gr<PERSON>tis.", "fontSize": 25, "width": 1400, "height": 75, "overflow": 0}, "fileID": 1888}], "fileID": 1977}, {"name": "Paytable/Pages/Page4/MinMaxHolder/MaxBet/MaximumText", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 1834, "guid": "704d90fd354daea498b48ab7779382a2"}, "children": [], "psr": "d"}, "fileID": 1801}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "3d220d8d5d5edc9419eb9686f9a53aa8"}, "_text": "APOSTA MÁXIMA:", "fontSize": 25, "anchorX": 0, "width": 212, "height": 26}, "fileID": 1889}], "fileID": 1978}, {"name": "Paytable/Pages/Page2/FreeSpinsHolder1/TitleHolder/Title", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 1834, "guid": "704d90fd354daea498b48ab7779382a2"}, "children": [], "psr": "d"}, "fileID": 1802}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "3d220d8d5d5edc9419eb9686f9a53aa8"}, "_text": "RODADAS GRÁTIS", "fontSize": 35, "width": 1150, "height": 100, "overflow": 0}, "fileID": 1890}], "fileID": 1979}, {"name": "Paytable/Pages/Page2/MoneySymbolHolder/TitleHolder/Title", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 1834, "guid": "704d90fd354daea498b48ab7779382a2"}, "children": [], "psr": "d"}, "fileID": 1803}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "3d220d8d5d5edc9419eb9686f9a53aa8"}, "_text": "SÍMBOLO DINHEIRO", "fontSize": 35, "width": 1150, "height": 70, "overflow": 0}, "fileID": 1891}], "fileID": 1980}, {"name": "Paytable/Pages/Page1/WildHolder/DescriptionHolder/Label1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 1834, "guid": "704d90fd354daea498b48ab7779382a2"}, "children": [], "psr": "d"}, "fileID": 1804}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "3d220d8d5d5edc9419eb9686f9a53aa8"}, "_text": "Este é o símbolo WILD.", "fontSize": 25, "anchorX": 0, "anchorY": 1, "width": 363, "height": 75, "overflow": 0}, "fileID": 1892}], "fileID": 1981}, {"name": "Paytable/Pages/Page1/Title/PaytableTitleLabel1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 1834, "guid": "704d90fd354daea498b48ab7779382a2"}, "children": [], "psr": "d"}, "fileID": 1805}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "3d220d8d5d5edc9419eb9686f9a53aa8"}, "_text": "REGRAS DO JOGO", "fontSize": 35, "width": 1150, "height": 100, "overflow": 0}, "fileID": 1893}], "fileID": 1982}, {"name": "Paytable/Pages/Page3/CAT/TitleHolder/Title1New", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 1834, "guid": "704d90fd354daea498b48ab7779382a2"}, "children": [], "psr": "d"}, "fileID": 1806}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "3d220d8d5d5edc9419eb9686f9a53aa8"}, "_text": "COMPRAR RODADAS GRÁTIS", "fontSize": 35, "width": 1400, "height": 70, "overflow": 0}, "fileID": 1894}], "fileID": 1983}, {"name": "Paytable/Pages/Page4/MinMaxHolder/MinBet/MinimumText", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 1834, "guid": "704d90fd354daea498b48ab7779382a2"}, "children": [], "psr": "d"}, "fileID": 1807}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "3d220d8d5d5edc9419eb9686f9a53aa8"}, "_text": "APOSTA MÍNIMA:", "fontSize": 25, "anchorX": 0, "width": 208, "height": 26}, "fileID": 1895}], "fileID": 1984}, {"name": "Paytable/Pages/Page4/RulesBottom/MalfunctionLabel", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 1834, "guid": "704d90fd354daea498b48ab7779382a2"}, "children": [], "psr": "d"}, "fileID": 1808}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "3d220d8d5d5edc9419eb9686f9a53aa8"}, "_text": "Uma avaria anula todos os pagamentos e jogadas.", "fontSize": 25, "width": 888, "height": 60, "overflow": 0}, "fileID": 1896}], "fileID": 1985}, {"name": "Paytable/Pages/Page1/ScatterHolder/DescriptionHolder/Label2", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 1834, "guid": "704d90fd354daea498b48ab7779382a2"}, "children": [], "psr": "d"}, "fileID": 1809}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "3d220d8d5d5edc9419eb9686f9a53aa8"}, "_text": "Aparece em todos os rolos.", "fontSize": 25, "anchorX": 0, "anchorY": 0, "width": 357, "height": 75, "overflow": 0}, "fileID": 1897}], "fileID": 1986}, {"name": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder1/LabelHolder1/Label1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 1834, "guid": "704d90fd354daea498b48ab7779382a2"}, "children": [], "psr": "d"}, "fileID": 1810}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "3d220d8d5d5edc9419eb9686f9a53aa8"}, "_text": "5x símbolos SCATTER atribuem 20 rodadas grátis.", "fontSize": 25, "width": 1400, "height": 75, "overflow": 0}, "fileID": 1898}], "fileID": 1987}, {"name": "Paytable/Pages/Page4/RulesTop/AllWinsMultiplied", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 1834, "guid": "704d90fd354daea498b48ab7779382a2"}, "children": [], "psr": "d"}, "fileID": 1811}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "3d220d8d5d5edc9419eb9686f9a53aa8"}, "_text": "Todos os prémios são multiplicados por aposta por linha.", "fontSize": 25, "width": 1194, "height": 160, "overflow": 0, "spacingY": 5}, "fileID": 1899}], "fileID": 1988}, {"name": "Paytable/Pages/Page1/ScatterHolder/DescriptionHolder/Label1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 1834, "guid": "704d90fd354daea498b48ab7779382a2"}, "children": [], "psr": "d"}, "fileID": 1812}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "3d220d8d5d5edc9419eb9686f9a53aa8"}, "_text": "Este é o símbolo de SCATTER. ", "fontSize": 25, "anchorX": 0, "anchorY": 1, "width": 357, "height": 75, "overflow": 0}, "fileID": 1900}], "fileID": 1989}, {"name": "Paytable/Pages/Page4/RulesTop/OnlyTheHighestWin", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 1834, "guid": "704d90fd354daea498b48ab7779382a2"}, "children": [], "psr": "d"}, "fileID": 1813}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "3d220d8d5d5edc9419eb9686f9a53aa8"}, "_text": "Só o prémio mais alto é pago em cada linha.", "fontSize": 25, "anchorY": 0, "width": 1400, "height": 50, "overflow": 0}, "fileID": 1901}], "fileID": 1990}, {"name": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder1/LabelHolder3/Label3", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 1834, "guid": "704d90fd354daea498b48ab7779382a2"}, "children": [], "psr": "d"}, "fileID": 1814}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "3d220d8d5d5edc9419eb9686f9a53aa8"}, "_text": "3x símbolos SCATTER atribuem 10 rodadas gr<PERSON>tis.", "fontSize": 25, "width": 1400, "height": 75, "overflow": 0}, "fileID": 1902}], "fileID": 1991}, {"name": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder2/LabelHolder1/Label1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 1834, "guid": "704d90fd354daea498b48ab7779382a2"}, "children": [], "psr": "d"}, "fileID": 1815}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "3d220d8d5d5edc9419eb9686f9a53aa8"}, "_text": "- Aleatoriamente, se os SCATTERS no ecrã puderem mover-se para baixo uma posição sem sair da área do rolo, é ativada uma nova rodada onde os rolos com SCATTERS se movem uma posição para baixo e os rolos sem SCATTERS rodam novamente.", "fontSize": 25, "anchorX": 0, "width": 1286, "height": 125, "overflow": 0}, "fileID": 1903}], "fileID": 1992}, {"name": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder4/Rule4", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 1834, "guid": "704d90fd354daea498b48ab7779382a2"}, "children": [], "psr": "d"}, "fileID": 1816}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "3d220d8d5d5edc9419eb9686f9a53aa8"}, "_text": "As rodadas reativadas jogam-se depois de concluído o grupo anterior de rodadas grátis. O multiplicador aplica-se às rodadas reativadas.", "fontSize": 25, "width": 920, "height": 125, "overflow": 0}, "fileID": 1904}], "fileID": 1993}, {"name": "Paytable/Pages/Page4/RulesTop/WhenWinningOnMultiplePaylines", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 1834, "guid": "704d90fd354daea498b48ab7779382a2"}, "children": [], "psr": "d"}, "fileID": 1817}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "3d220d8d5d5edc9419eb9686f9a53aa8"}, "_text": "Quando se ganha em múltiplas linhas de pagamento, todos os prémios são adicionados ao prémio total.", "fontSize": 25, "anchorY": 0, "width": 1400, "height": 50, "overflow": 0}, "fileID": 1905}], "fileID": 1994}, {"name": "Paytable/Pages/Page4/Title/PaytableTitleLabel", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 1834, "guid": "704d90fd354daea498b48ab7779382a2"}, "children": [], "psr": "d"}, "fileID": 1818}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "3d220d8d5d5edc9419eb9686f9a53aa8"}, "_text": "REGRAS DO JOGO", "fontSize": 30, "width": 1150, "height": 100, "overflow": 0}, "fileID": 1906}], "fileID": 1995}, {"name": "Paytable/Pages/Page3/MaxWin/RuleHolder/Label1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 1834, "guid": "704d90fd354daea498b48ab7779382a2"}, "children": [], "psr": "d"}, "fileID": 1819}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "3d220d8d5d5edc9419eb9686f9a53aa8"}, "_text": "O montante máximo de prémio está limitado a {0}x a aposta. Se o prémio total de uma RONDA DE RODADAS GRÁTIS atingir {1}x a aposta, a ronda termina imediatamente, o prémio é atribuído e todas as rodadas gr<PERSON>tis restantes são retiradas", "fontSize": 25, "anchorY": 0, "width": 1400, "height": 100, "overflow": 0}, "fileID": 1907}], "fileID": 1996}, {"name": "Paytable/Pages/Page4/RulesBottom/RTP/TheoreticalRTPBONUS/Label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 1834, "guid": "704d90fd354daea498b48ab7779382a2"}, "children": [], "psr": "d"}, "fileID": 1820}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "3d220d8d5d5edc9419eb9686f9a53aa8"}, "_text": "O RTP do jogo quando se usa a opção \"COMPRAR RODADAS GRÁTIS\" é de {0}%", "fontSize": 25, "anchorY": 0, "width": 1200, "height": 75, "overflow": 0}, "fileID": 1908}], "fileID": 1997}, {"name": "Paytable/Pages/Page4/Volatility/VolatilityMeter/LabelHolder/VolatilityLabel", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 1834, "guid": "704d90fd354daea498b48ab7779382a2"}, "children": [], "psr": "d"}, "fileID": 1821}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "8d3bb74cec4162a4987d1775862b8d44"}, "_text": "VOLATILIDADE", "fontSize": 22, "anchorX": 0, "width": 132, "height": 22}, "fileID": 1909}], "fileID": 1998}, {"name": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder6/Rule6", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 1834, "guid": "704d90fd354daea498b48ab7779382a2"}, "children": [], "psr": "d"}, "fileID": 1822}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "3d220d8d5d5edc9419eb9686f9a53aa8"}, "_text": "Aleatoriamente, quando existem símbolos de pescador no ecrã mas não há símbolos peixe, no final de uma rodada grátis podem aparecer símbolos de DINHEIRO peixe em posições aleatórias através do modo rodada dinamite.", "fontSize": 25, "width": 1271, "height": 100, "overflow": 0}, "fileID": 1910}], "fileID": 1999}, {"name": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder2/LabelHolder2/Label2", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 1834, "guid": "704d90fd354daea498b48ab7779382a2"}, "children": [], "psr": "d"}, "fileID": 1823}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "3d220d8d5d5edc9419eb9686f9a53aa8"}, "_text": "- Aleatoriamente, um anzol pode puxar um dos rolos para cima para revelar mais um SCATTER.", "fontSize": 25, "anchorX": 0, "width": 1400, "height": 75, "overflow": 0}, "fileID": 1911}], "fileID": 2000}, {"name": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/RuleHolder3/Rule3", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 1834, "guid": "704d90fd354daea498b48ab7779382a2"}, "children": [], "psr": "d"}, "fileID": 1824}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "3d220d8d5d5edc9419eb9686f9a53aa8"}, "_text": "Antes do início da ronda, são selecionados aleatoriamente 0 a 5 modificadores que são aplicáveis à ronda seguinte.", "fontSize": 25, "width": 1400, "height": 100}, "fileID": 1912}], "fileID": 2001}, {"name": "Paytable/Pages/Page1/WildHolder/DescriptionHolder/Label2", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 1834, "guid": "704d90fd354daea498b48ab7779382a2"}, "children": [], "psr": "d"}, "fileID": 1825}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "3d220d8d5d5edc9419eb9686f9a53aa8"}, "_text": "Aparece em todos os rolos durante a ronda de RODADAS GRÁTIS.", "fontSize": 25, "anchorX": 0, "width": 363, "height": 75, "overflow": 0}, "fileID": 1913}], "fileID": 2002}, {"name": "Paytable/Pages/Page1/WildHolder/DescriptionHolder/Label3", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 1834, "guid": "704d90fd354daea498b48ab7779382a2"}, "children": [], "psr": "d"}, "fileID": 1826}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "3d220d8d5d5edc9419eb9686f9a53aa8"}, "_text": "Substitui todos os símbolos exceto o SCATTER.", "fontSize": 25, "anchorX": 0, "anchorY": 0, "width": 363, "height": 75, "overflow": 0}, "fileID": 1914}], "fileID": 2003}, {"name": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder3/Rule3", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 1834, "guid": "704d90fd354daea498b48ab7779382a2"}, "children": [], "psr": "d"}, "fileID": 1827}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "3d220d8d5d5edc9419eb9686f9a53aa8"}, "_text": "Cada 4º símbolo WILD recolhido reativa o modo, atribui mais 10 rodadas grátis e o multiplicador para a recolha do símbolo DINHEIRO aumenta para 2x para o segundo nível, 3x para o terceiro nível e 10x para o quarto nível.", "fontSize": 25, "width": 1400, "height": 150, "overflow": 0}, "fileID": 1915}], "fileID": 2004}, {"name": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder2/Label2", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 1834, "guid": "704d90fd354daea498b48ab7779382a2"}, "children": [], "psr": "d"}, "fileID": 1828}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "3d220d8d5d5edc9419eb9686f9a53aa8"}, "_text": "- MAIS PESCADORES - <PERSON><PERSON> sí<PERSON> WILD presentes nas faixas dos rolos durante a ronda de rodadas grátis <PERSON>e", "fontSize": 25, "anchorX": 0, "width": 1200, "height": 100, "overflow": 0}, "fileID": 1916}], "fileID": 2005}, {"name": "IntroScreen/content/Labels_Holder_landscape/Label_Holder_bigger_1/Label_2 (1)", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 1834, "guid": "704d90fd354daea498b48ab7779382a2"}, "children": [], "psr": "d"}, "fileID": 1829}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "25471c78efdb3a746b8dc96f6d01805c"}, "_text": "GRANDES MODIFICADORES DE RODADAS GRÁTIS!", "fontSize": 60, "width": 1000, "height": 80, "overflow": 0}, "fileID": 1917}], "fileID": 2006}, {"name": "IntroScreen/content/Labels_Holder_landscape/Label_Holder_bigger_1/Label_1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 1834, "guid": "704d90fd354daea498b48ab7779382a2"}, "children": [], "psr": "d"}, "fileID": 1830}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "25471c78efdb3a746b8dc96f6d01805c"}, "_text": "VÁ PESCAR", "fontSize": 60, "width": 1000, "height": 80, "overflow": 0}, "fileID": 1918}], "fileID": 2007}, {"name": "IntroScreen/content/IntroButtons/ButtonSkipIntro/content/TextHolder/Label_1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 1834, "guid": "704d90fd354daea498b48ab7779382a2"}, "children": [], "psr": "d"}, "fileID": 1831}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "3d220d8d5d5edc9419eb9686f9a53aa8"}, "_text": "NÃO MOSTRAR NOVAMENTE", "fontSize": 30, "anchorX": 0, "width": 414, "height": 30}, "fileID": 1919}], "fileID": 2008}, {"name": "Paytable/Pages/Page2/MoneySymbolHolder/DescriptionHolder/PossibleValues/Label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 1834, "guid": "704d90fd354daea498b48ab7779382a2"}, "children": [], "psr": "d"}, "fileID": 1832}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "3d220d8d5d5edc9419eb9686f9a53aa8"}, "_text": "Os valores possíveis são: 2x, 5x, 10x, 15x, 20x, 25x, 50x, 100x, 200x, 500x, 1666x, 2500x ou 5000x a aposta total.", "fontSize": 25, "anchorX": 0, "width": 953, "height": 100, "overflow": 0}, "fileID": 1920}], "fileID": 2009}, {"name": "Paytable/Pages/Page3/MaxWin/RuleHolder/HolderLabelJackpot/Label1New", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 1834, "guid": "704d90fd354daea498b48ab7779382a2"}, "children": [], "psr": "d"}, "fileID": 1833}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "3d220d8d5d5edc9419eb9686f9a53aa8"}, "_text": "O montante máximo de prémio está limitado a {0}x a aposta, exceto para o Jackpot. Se o prémio total de uma ronda de RODADAS GRÁTIS atingir {1}x a aposta, a ronda termina imediatamente, o prémio é atribuído e todas as rodadas gr<PERSON>tis restantes são retiradas.", "fontSize": 25, "anchorY": 0, "width": 1400, "height": 100, "overflow": 0}, "fileID": 1921}], "fileID": 2010}]}}, {"type": "Font", "id": "1b5a1f73d7753974da5dd27a21e4f091", "data": {"fontName": "f1b5a1f73d7753974da5dd27a21e4f0", "path": "@font-face{font-family:'f1b5a1f73d7753974da5dd27a21e4f0';src:url('data:application/x-font-woff;base64,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') format('woff')}"}}, {"type": "Font", "id": "f2d59b0d1332955479cb70e85387e7de", "data": {"fontName": "ff2d59b0d1332955479cb70e85387e7", "path": "@font-face{font-family:'ff2d59b0d1332955479cb70e85387e7';src:url('data:application/x-font-woff;base64,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') format('woff')}"}}, {"type": "Font", "id": "25471c78efdb3a746b8dc96f6d01805c", "data": {"fontName": "f25471c78efdb3a746b8dc96f6d0180", "path": "@font-face{font-family:'f25471c78efdb3a746b8dc96f6d0180';src:url('data:application/x-font-woff;base64,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') format('woff')}"}}, {"type": "Font", "id": "8d3bb74cec4162a4987d1775862b8d44", "data": {"fontName": "f8d3bb74cec4162a4987d1775862b8d", "path": "@font-face{font-family:'f8d3bb74cec4162a4987d1775862b8d';src:url('data:application/x-font-woff;base64,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') format('woff')}"}}, {"type": "Font", "id": "3d220d8d5d5edc9419eb9686f9a53aa8", "data": {"fontName": "f3d220d8d5d5edc9419eb9686f9a53a", "path": "@font-face{font-family:'f3d220d8d5d5edc9419eb9686f9a53a';src:url('data:application/x-font-woff;base64,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') format('woff')}"}}]}