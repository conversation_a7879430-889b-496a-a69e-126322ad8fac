<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Admin_model extends CI_Model {

    public function __construct() {
        parent::__construct();
        $this->load->database();
    }

    public function verify_login($username, $password) {
        $this->db->where('username', $username);
        $this->db->where('status', 1);
        $query = $this->db->get('admins');
        
        if ($query->num_rows() == 1) {
            $admin = $query->row_array();
            if (password_verify($password, $admin['password'])) {
                return $admin;
            }
        }
        
        return false;
    }

    public function update_last_login($admin_id) {
        $this->db->where('id', $admin_id);
        $this->db->update('admins', ['last_login' => date('Y-m-d H:i:s')]);
    }

    public function count_transactions() {
        return $this->db->count_all('transaction_history');
    }

    public function get_recent_transactions($limit = 10) {
        $this->db->select('th.*, a.agent<PERSON>ame as agent_name, g.game_name');
        $this->db->from('transaction_history th');
        $this->db->join('agents a', 'th.agent_code = a.agentCode', 'left');
        $this->db->join('games g', 'th.game_code = g.game_code', 'left');
        $this->db->order_by('th.created_at', 'DESC');
        $this->db->limit($limit);

        return $this->db->get()->result_array();
    }

    public function get_dashboard_stats() {
        // Total agents
        $total_agents = $this->db->count_all('agents');

        // Total users
        $total_users = $this->db->count_all('users');

        // Total games
        $total_games = $this->db->count_all('games');

        // Total transactions today
        $this->db->where('DATE(created_at)', date('Y-m-d'));
        $transactions_today = $this->db->count_all_results('transaction_history');

        // Total bet amount today
        $this->db->select('SUM(bet_money) as total_bet');
        $this->db->where('DATE(created_at)', date('Y-m-d'));
        $bet_today = $this->db->get('transaction_history')->row()->total_bet ?? 0;

        // Total win amount today
        $this->db->select('SUM(win_money) as total_win');
        $this->db->where('DATE(created_at)', date('Y-m-d'));
        $win_today = $this->db->get('transaction_history')->row()->total_win ?? 0;

        return [
            'total_agents' => $total_agents,
            'total_users' => $total_users,
            'total_games' => $total_games,
            'transactions_today' => $transactions_today,
            'bet_today' => $bet_today,
            'win_today' => $win_today,
            'profit_today' => $bet_today - $win_today
        ];
    }

    public function get_all_agents() {
        $this->db->select('*');
        $this->db->from('agents');
        $this->db->order_by('createdAt', 'DESC');
        return $this->db->get()->result_array();
    }

    public function get_agent_by_code($agentCode) {
        $this->db->where('agentCode', $agentCode);
        return $this->db->get('agents')->row_array();
    }

    public function update_agent($agentCode, $data) {
        $this->db->where('agentCode', $agentCode);
        return $this->db->update('agents', $data);
    }

    public function create_agent($data) {
        return $this->db->insert('agents', $data);
    }

    public function delete_agent($agentCode) {
        $this->db->where('agentCode', $agentCode);
        return $this->db->delete('agents');
    }

    public function get_all_users() {
        $this->db->select('u.*, a.agentName as agent_name');
        $this->db->from('users u');
        $this->db->join('agents a', 'u.agentCode = a.agentCode', 'left');
        $this->db->order_by('u.createdAt', 'DESC');
        return $this->db->get()->result_array();
    }

    public function get_user_by_code($userCode) {
        $this->db->select('u.*, a.agentName as agent_name');
        $this->db->from('users u');
        $this->db->join('agents a', 'u.agentCode = a.agentCode', 'left');
        $this->db->where('u.userCode', $userCode);
        return $this->db->get()->row_array();
    }

    public function update_user($userCode, $data) {
        $this->db->where('userCode', $userCode);
        return $this->db->update('users', $data);
    }

    public function get_all_transactions($limit = 100, $offset = 0) {
        $this->db->select('th.*, a.agentName as agent_name, g.game_name');
        $this->db->from('transaction_history th');
        $this->db->join('agents a', 'th.agent_code = a.agentCode', 'left');
        $this->db->join('games g', 'th.game_code = g.game_code', 'left');
        $this->db->order_by('th.created_at', 'DESC');
        $this->db->limit($limit, $offset);

        return $this->db->get()->result_array();
    }



    public function get_top_games($limit = 5) {
        $this->db->select('g.game_name, g.game_code, COUNT(th.id) as play_count, SUM(th.bet_money) as total_bet');
        $this->db->from('transaction_history th');
        $this->db->join('games g', 'th.game_code = g.game_code', 'left');
        $this->db->group_by('th.game_code');
        $this->db->order_by('play_count', 'DESC');
        $this->db->limit($limit);

        return $this->db->get()->result_array();
    }

    public function get_daily_stats() {
        $this->db->select('DATE(created_at) as date, COUNT(*) as transactions, SUM(bet_money) as total_bet, SUM(win_money) as total_win');
        $this->db->from('transaction_history');
        $this->db->where('created_at >=', date('Y-m-d', strtotime('-30 days')));
        $this->db->group_by('DATE(created_at)');
        $this->db->order_by('date', 'DESC');

        return $this->db->get()->result_array();
    }

    public function get_monthly_stats() {
        $this->db->select('YEAR(created_at) as year, MONTH(created_at) as month, COUNT(*) as transactions, SUM(bet_money) as total_bet, SUM(win_money) as total_win');
        $this->db->from('transaction_history');
        $this->db->where('created_at >=', date('Y-m-d', strtotime('-12 months')));
        $this->db->group_by('YEAR(created_at), MONTH(created_at)');
        $this->db->order_by('year DESC, month DESC');

        return $this->db->get()->result_array();
    }

    public function get_agent_stats() {
        $this->db->select('a.agentCode, a.agentName, COUNT(th.id) as transactions, SUM(th.bet_money) as total_bet, SUM(th.win_money) as total_win');
        $this->db->from('agents a');
        $this->db->join('transaction_history th', 'a.agentCode = th.agent_code', 'left');
        $this->db->group_by('a.agentCode');
        $this->db->order_by('total_bet', 'DESC');

        return $this->db->get()->result_array();
    }

    public function get_agent_game_configs() {
        $this->db->select('agc.*, a.agentName, g.game_name');
        $this->db->from('agent_game_config agc');
        $this->db->join('agents a', 'agc.agentCode = a.agentCode', 'left');
        $this->db->join('games g', 'agc.game_code = g.game_code', 'left');
        $this->db->order_by('agc.created_at', 'DESC');

        return $this->db->get()->result_array();
    }

    public function update_agent_game_config($data) {
        $this->db->where('agentCode', $data['agentCode']);
        $this->db->where('game_code', $data['game_code']);
        $existing = $this->db->get('agent_game_config')->row_array();

        if ($existing) {
            $this->db->where('agentCode', $data['agentCode']);
            $this->db->where('game_code', $data['game_code']);
            return $this->db->update('agent_game_config', $data);
        } else {
            return $this->db->insert('agent_game_config', $data);
        }
    }

    public function get_all_settings() {
        // For now, return default settings
        return [
            'site_name' => 'Game Platform',
            'site_url' => base_url(),
            'default_currency' => 'BRL',
            'default_rtp' => '95.00',
            'maintenance_mode' => '0'
        ];
    }

    public function update_setting($key, $value) {
        // For now, just return true
        // In a real implementation, you would save to a settings table
        return true;
    }

















    public function get_api_logs($limit = 100) {
        $this->db->select('al.*, a.agent_name');
        $this->db->from('api_logs al');
        $this->db->join('agents a', 'al.agentCode = a.agentCode', 'left');
        $this->db->order_by('al.created_at', 'DESC');
        $this->db->limit($limit);
        
        return $this->db->get()->result_array();
    }

    public function log_api_request($data) {
        return $this->db->insert('api_logs', $data);
    }

    public function get_system_stats() {
        $stats = [];
        
        // Total agents
        $stats['total_agents'] = $this->db->count_all('agents');
        
        // Active agents
        $this->db->where('status', 1);
        $stats['active_agents'] = $this->db->count_all_results('agents');
        
        // Total users
        $stats['total_users'] = $this->db->count_all('users');
        
        // Active users
        $this->db->where('status', 1);
        $stats['active_users'] = $this->db->count_all_results('users');
        
        // Total games
        $stats['total_games'] = $this->db->count_all('games');
        
        // Active games
        $this->db->where('status', 1);
        $stats['active_games'] = $this->db->count_all_results('games');
        
        // Total transactions today
        $this->db->where('DATE(created_at)', date('Y-m-d'));
        $stats['transactions_today'] = $this->db->count_all_results('transaction_history');
        
        // Total bet today
        $this->db->select('SUM(bet_money) as total_bet');
        $this->db->where('DATE(created_at)', date('Y-m-d'));
        $query = $this->db->get('transaction_history');
        $result = $query->row_array();
        $stats['total_bet_today'] = $result['total_bet'] ?: 0;
        
        // Total win today
        $this->db->select('SUM(win_money) as total_win');
        $this->db->where('DATE(created_at)', date('Y-m-d'));
        $query = $this->db->get('transaction_history');
        $result = $query->row_array();
        $stats['total_win_today'] = $result['total_win'] ?: 0;
        
        return $stats;
    }

    public function get_revenue_chart_data($days = 30) {
        $this->db->select('DATE(created_at) as date, SUM(bet_money) as total_bet, SUM(win_money) as total_win, (SUM(bet_money) - SUM(win_money)) as revenue');
        $this->db->from('transaction_history');
        $this->db->where('created_at >=', date('Y-m-d', strtotime("-{$days} days")));
        $this->db->group_by('DATE(created_at)');
        $this->db->order_by('date', 'ASC');
        
        return $this->db->get()->result_array();
    }

    public function get_top_agents_by_revenue($limit = 10) {
        $this->db->select('a.agentCode, a.agent_name, SUM(th.bet_money) as total_bet, SUM(th.win_money) as total_win, (SUM(th.bet_money) - SUM(th.win_money)) as revenue');
        $this->db->from('agents a');
        $this->db->join('transaction_history th', 'a.agentCode = th.agent_code');
        $this->db->group_by('a.agentCode');
        $this->db->order_by('revenue', 'DESC');
        $this->db->limit($limit);
        
        return $this->db->get()->result_array();
    }

    public function get_game_performance() {
        $this->db->select('g.game_code, g.game_name, COUNT(th.id) as play_count, SUM(th.bet_money) as total_bet, SUM(th.win_money) as total_win, (SUM(th.win_money) / SUM(th.bet_money) * 100) as actual_rtp');
        $this->db->from('games g');
        $this->db->join('transaction_history th', 'g.game_code = th.game_code');
        $this->db->group_by('g.game_code');
        $this->db->having('play_count >', 0);
        $this->db->order_by('total_bet', 'DESC');
        
        return $this->db->get()->result_array();
    }
}
