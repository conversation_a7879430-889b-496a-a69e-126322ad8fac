{"resources": [{"type": "GameObject", "id": "9a734e621c318984ca31bc524efef04a", "data": {"root": [{"name": "pt_desktop", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 0}, "children": [{"fileID": 19561, "guid": "9a734e621c318984ca31bc524efef04a"}, {"fileID": 19562, "guid": "9a734e621c318984ca31bc524efef04a"}, {"fileID": 19563, "guid": "9a734e621c318984ca31bc524efef04a"}, {"fileID": 19564, "guid": "9a734e621c318984ca31bc524efef04a"}, {"fileID": 19565, "guid": "9a734e621c318984ca31bc524efef04a"}, {"fileID": 19566, "guid": "9a734e621c318984ca31bc524efef04a"}, {"fileID": 19567, "guid": "9a734e621c318984ca31bc524efef04a"}, {"fileID": 19568, "guid": "9a734e621c318984ca31bc524efef04a"}, {"fileID": 19569, "guid": "9a734e621c318984ca31bc524efef04a"}, {"fileID": 19570, "guid": "9a734e621c318984ca31bc524efef04a"}, {"fileID": 19571, "guid": "9a734e621c318984ca31bc524efef04a"}, {"fileID": 19572, "guid": "9a734e621c318984ca31bc524efef04a"}, {"fileID": 19573, "guid": "9a734e621c318984ca31bc524efef04a"}, {"fileID": 19574, "guid": "9a734e621c318984ca31bc524efef04a"}, {"fileID": 19575, "guid": "9a734e621c318984ca31bc524efef04a"}, {"fileID": 19576, "guid": "9a734e621c318984ca31bc524efef04a"}, {"fileID": 19577, "guid": "9a734e621c318984ca31bc524efef04a"}, {"fileID": 19578, "guid": "9a734e621c318984ca31bc524efef04a"}, {"fileID": 19579, "guid": "9a734e621c318984ca31bc524efef04a"}, {"fileID": 19580, "guid": "9a734e621c318984ca31bc524efef04a"}, {"fileID": 19581, "guid": "9a734e621c318984ca31bc524efef04a"}, {"fileID": 19582, "guid": "9a734e621c318984ca31bc524efef04a"}, {"fileID": 19583, "guid": "9a734e621c318984ca31bc524efef04a"}, {"fileID": 19584, "guid": "9a734e621c318984ca31bc524efef04a"}, {"fileID": 19585, "guid": "9a734e621c318984ca31bc524efef04a"}, {"fileID": 19586, "guid": "9a734e621c318984ca31bc524efef04a"}, {"fileID": 19587, "guid": "9a734e621c318984ca31bc524efef04a"}, {"fileID": 19588, "guid": "9a734e621c318984ca31bc524efef04a"}, {"fileID": 19589, "guid": "9a734e621c318984ca31bc524efef04a"}, {"fileID": 19590, "guid": "9a734e621c318984ca31bc524efef04a"}, {"fileID": 19591, "guid": "9a734e621c318984ca31bc524efef04a"}, {"fileID": 19592, "guid": "9a734e621c318984ca31bc524efef04a"}, {"fileID": 19593, "guid": "9a734e621c318984ca31bc524efef04a"}, {"fileID": 19594, "guid": "9a734e621c318984ca31bc524efef04a"}, {"fileID": 19595, "guid": "9a734e621c318984ca31bc524efef04a"}, {"fileID": 19596, "guid": "9a734e621c318984ca31bc524efef04a"}, {"fileID": 19597, "guid": "9a734e621c318984ca31bc524efef04a"}, {"fileID": 19598, "guid": "9a734e621c318984ca31bc524efef04a"}, {"fileID": 19599, "guid": "9a734e621c318984ca31bc524efef04a"}, {"fileID": 19600, "guid": "9a734e621c318984ca31bc524efef04a"}, {"fileID": 19601, "guid": "9a734e621c318984ca31bc524efef04a"}, {"fileID": 19602, "guid": "9a734e621c318984ca31bc524efef04a"}, {"fileID": 19603, "guid": "9a734e621c318984ca31bc524efef04a"}, {"fileID": 19604, "guid": "9a734e621c318984ca31bc524efef04a"}, {"fileID": 19605, "guid": "9a734e621c318984ca31bc524efef04a"}, {"fileID": 19606, "guid": "9a734e621c318984ca31bc524efef04a"}, {"fileID": 19607, "guid": "9a734e621c318984ca31bc524efef04a"}, {"fileID": 19608, "guid": "9a734e621c318984ca31bc524efef04a"}, {"fileID": 19609, "guid": "9a734e621c318984ca31bc524efef04a"}, {"fileID": 19610, "guid": "9a734e621c318984ca31bc524efef04a"}, {"fileID": 19611, "guid": "9a734e621c318984ca31bc524efef04a"}, {"fileID": 19612, "guid": "9a734e621c318984ca31bc524efef04a"}, {"fileID": 19613, "guid": "9a734e621c318984ca31bc524efef04a"}, {"fileID": 19614, "guid": "9a734e621c318984ca31bc524efef04a"}, {"fileID": 19615, "guid": "9a734e621c318984ca31bc524efef04a"}, {"fileID": 19616, "guid": "9a734e621c318984ca31bc524efef04a"}, {"fileID": 19617, "guid": "9a734e621c318984ca31bc524efef04a"}, {"fileID": 19618, "guid": "9a734e621c318984ca31bc524efef04a"}, {"fileID": 19619, "guid": "9a734e621c318984ca31bc524efef04a"}, {"fileID": 19620, "guid": "9a734e621c318984ca31bc524efef04a"}, {"fileID": 19621, "guid": "9a734e621c318984ca31bc524efef04a"}, {"fileID": 19622, "guid": "9a734e621c318984ca31bc524efef04a"}, {"fileID": 19623, "guid": "9a734e621c318984ca31bc524efef04a"}, {"fileID": 19624, "guid": "9a734e621c318984ca31bc524efef04a"}, {"fileID": 19625, "guid": "9a734e621c318984ca31bc524efef04a"}, {"fileID": 19626, "guid": "9a734e621c318984ca31bc524efef04a"}, {"fileID": 19627, "guid": "9a734e621c318984ca31bc524efef04a"}, {"fileID": 19628, "guid": "9a734e621c318984ca31bc524efef04a"}, {"fileID": 19629, "guid": "9a734e621c318984ca31bc524efef04a"}, {"fileID": 19630, "guid": "9a734e621c318984ca31bc524efef04a"}, {"fileID": 19631, "guid": "9a734e621c318984ca31bc524efef04a"}, {"fileID": 19632, "guid": "9a734e621c318984ca31bc524efef04a"}, {"fileID": 19633, "guid": "9a734e621c318984ca31bc524efef04a"}, {"fileID": 19634, "guid": "9a734e621c318984ca31bc524efef04a"}, {"fileID": 19635, "guid": "9a734e621c318984ca31bc524efef04a"}, {"fileID": 19636, "guid": "9a734e621c318984ca31bc524efef04a"}, {"fileID": 19637, "guid": "9a734e621c318984ca31bc524efef04a"}, {"fileID": 19638, "guid": "9a734e621c318984ca31bc524efef04a"}, {"fileID": 19639, "guid": "9a734e621c318984ca31bc524efef04a"}, {"fileID": 19640, "guid": "9a734e621c318984ca31bc524efef04a"}, {"fileID": 19641, "guid": "9a734e621c318984ca31bc524efef04a"}, {"fileID": 19642, "guid": "9a734e621c318984ca31bc524efef04a"}, {"fileID": 19643, "guid": "9a734e621c318984ca31bc524efef04a"}, {"fileID": 19644, "guid": "9a734e621c318984ca31bc524efef04a"}, {"fileID": 19645, "guid": "9a734e621c318984ca31bc524efef04a"}, {"fileID": 19646, "guid": "9a734e621c318984ca31bc524efef04a"}, {"fileID": 19647, "guid": "9a734e621c318984ca31bc524efef04a"}, {"fileID": 19648, "guid": "9a734e621c318984ca31bc524efef04a"}, {"fileID": 19649, "guid": "9a734e621c318984ca31bc524efef04a"}, {"fileID": 19650, "guid": "9a734e621c318984ca31bc524efef04a"}, {"fileID": 19651, "guid": "9a734e621c318984ca31bc524efef04a"}, {"fileID": 19652, "guid": "9a734e621c318984ca31bc524efef04a"}, {"fileID": 19653, "guid": "9a734e621c318984ca31bc524efef04a"}, {"fileID": 19654, "guid": "9a734e621c318984ca31bc524efef04a"}, {"fileID": 19655, "guid": "9a734e621c318984ca31bc524efef04a"}, {"fileID": 19656, "guid": "9a734e621c318984ca31bc524efef04a"}, {"fileID": 19657, "guid": "9a734e621c318984ca31bc524efef04a"}, {"fileID": 19658, "guid": "9a734e621c318984ca31bc524efef04a"}, {"fileID": 19659, "guid": "9a734e621c318984ca31bc524efef04a"}, {"fileID": 19660, "guid": "9a734e621c318984ca31bc524efef04a"}, {"fileID": 19661, "guid": "9a734e621c318984ca31bc524efef04a"}, {"fileID": 19662, "guid": "9a734e621c318984ca31bc524efef04a"}, {"fileID": 19663, "guid": "9a734e621c318984ca31bc524efef04a"}, {"fileID": 19664, "guid": "9a734e621c318984ca31bc524efef04a"}, {"fileID": 19665, "guid": "9a734e621c318984ca31bc524efef04a"}, {"fileID": 19666, "guid": "9a734e621c318984ca31bc524efef04a"}, {"fileID": 19667, "guid": "9a734e621c318984ca31bc524efef04a"}, {"fileID": 19668, "guid": "9a734e621c318984ca31bc524efef04a"}], "s": "0"}, "fileID": 19669}, {"componentType": "ModificationsManager", "enabled": true, "serializableData": {"root": {"fileID": 0}, "EditMode": false, "Atlases": [], "Transforms": [{"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page4/RulesTop/AllValuesExpressed", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -15, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/AnteBetHolder/ARuleHolder2/Rule2", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page4/RulesTop/FSWins1", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -75, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FSHolder/RuleHolder4/Rule4", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page1/ScatterHolder/RulesHolder/RuleHolder3/Rule3", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page1/ScatterHolder/RulesHolder/RuleHolder/Rule2", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FSHolder/RuleHolder2/Rule2", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/AnteBetHolder/ARuleHolder3/Rule3", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page1/TitleHolder/Title", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page4/RulesBottom/MalfunctionLabel", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/BuyFSCAT/RuleHolder1/Rule1", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/BuyFSCAT/RuleHolder3/Rule3", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page4/TitleHolder/Title", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/MultiplierHolder/RulesHolder/RuleHolder1/Rule1", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page4/RTP/TheoreticalANTERTP/Label", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page4/Volatility/VolatilityDescription", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/BuyFSCAT/RuleHolder2/Rule2", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FSHolder/RuleHolder1/Rule1", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page4/RTP/TheoreticalRTPBONUS4/Label", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page1/ScatterHolder/RulesHolder/RuleHolder1/Rule1", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page1/RuleHolder/Rule", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/TumbleHolder/RuleHolder2/Rule2", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/TumbleHolder/RuleHolder3/Rule3", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/TumbleHolder/TitleHolder/Title", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page4/RTP/TheoreticalRTPBONUS3/Label", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/AnteBetHolder/TitleHolder/Title", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page4/RulesTop/FSWins2", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -105, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page4/RulesTop/AllWinsMultiplied", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 43, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FSHolder/RuleHolder3/Rule3", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/MaxWinHolder/RuleHolder1/Rule1", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/AnteBetHolder/ARuleHolder1/Rule1", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/TumbleHolder/RuleHolder1/Rule1", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/MaxWinHolder/TitleHolder1/Title1", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FSHolder/TitleHolder/Title", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/BuyFSCAT/RuleHolder4/Rule4", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/AnteBetHolder/ARuleHolder4/Rule4", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/MultiplierHolder/RulesHolder/RuleHolder2/Rule2", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page4/RulesTop/WhenWinningOnMultiplePaylines", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -45, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page4/RulesBottom/SpaceAndEnter", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FSHolder/RuleHolder5/Rule5", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/BuyFSCAT/TitleHolder/Title", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/MultiplierHolder/SpriteHolder/Sprite", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0.6, "y": 0.6, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/MultiplierHolder/SpriteHolder/Value", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": -1, "y": -10, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Game/Reels/TitlesHolder/TitleMessageControllerLandscape/Normal/TitleMessage3/LabelHolder/SpriteHolder/Sprite", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": -317, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0.3173333, "y": 0.3173333, "z": 0}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Game/Reels/TitlesHolder/TitleMessageControllerLandscape/FreeSpins/TitleMessage2/LabelHolder/SpriteHolder/Sprite", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": -428, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0.3173333, "y": 0.3173333, "z": 0}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Game/Reels/TitlesHolder/TitleMessageControllerLandscape/FreeSpins/TitleMessage1/LabelHolder/SpriteHolder/Sprite", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": -444, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0.3173333, "y": 0.3173333, "z": 0}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}], "Labels": [{"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 19670, "guid": "9a734e621c318984ca31bc524efef04a"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/RulesTop/AllValuesExpressed", "oldContent": "All values are expressed as actual wins in coins.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 19671, "guid": "9a734e621c318984ca31bc524efef04a"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/AnteBetHolder/ARuleHolder2/Rule2", "oldContent": "Bet multiplier 25x - the chance to win the free spins feature naturally is double.\nThe BUY FREE SPINS feature is disabled.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 19672, "guid": "9a734e621c318984ca31bc524efef04a"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/RulesTop/FSWins1", "oldContent": "Free spins win is awarded to the player after the round completes.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 19673, "guid": "9a734e621c318984ca31bc524efef04a"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FSHolder/RuleHolder4/Rule4", "oldContent": "When the tumbling sequence ends, the values of all MULTIPLIER symbols on the screen are added together and the total win of the sequence is multiplied by the final value.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 19674, "guid": "9a734e621c318984ca31bc524efef04a"}, "extraPayloads": [], "path": "Paytable/Pages/Page1/ScatterHolder/RulesHolder/RuleHolder3/Rule3", "oldContent": "SCATTER pays on any position.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 19675, "guid": "9a734e621c318984ca31bc524efef04a"}, "extraPayloads": [], "path": "Paytable/Pages/Page1/ScatterHolder/RulesHolder/RuleHolder/Rule2", "oldContent": "SCATTER symbol is present on all reels.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 19676, "guid": "9a734e621c318984ca31bc524efef04a"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FSHolder/RuleHolder2/Rule2", "oldContent": "Whenever 3 or more SCATTER symbols hit during the FREE SPINS ROUND, 5 additional free spins are awarded.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 19677, "guid": "9a734e621c318984ca31bc524efef04a"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/AnteBetHolder/ARuleHolder3/Rule3", "oldContent": "Bet multiplier 20x - Normal play.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 19678, "guid": "9a734e621c318984ca31bc524efef04a"}, "extraPayloads": [], "path": "Paytable/Pages/Page1/TitleHolder/Title", "oldContent": "GAME RULES", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 19679, "guid": "9a734e621c318984ca31bc524efef04a"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/RulesBottom/MalfunctionLabel", "oldContent": "Malfunction voids all pays and plays.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 19680, "guid": "9a734e621c318984ca31bc524efef04a"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/BuyFSCAT/RuleHolder1/Rule1", "oldContent": "The FREE SPINS round can be instantly triggered from the base game by buying it.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 19681, "guid": "9a734e621c318984ca31bc524efef04a"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/BuyFSCAT/RuleHolder3/Rule3", "oldContent": "Pay 100x total bet to trigger the FREE SPINS feature with 4 or more SCATTER symbols guaranteed to hit on the triggering spin.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 19682, "guid": "9a734e621c318984ca31bc524efef04a"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/TitleHolder/Title", "oldContent": "GAME RULES", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 19683, "guid": "9a734e621c318984ca31bc524efef04a"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/MultiplierHolder/RulesHolder/RuleHolder1/Rule1", "oldContent": "This is the MULTIPLIER symbol.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 19684, "guid": "9a734e621c318984ca31bc524efef04a"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/RTP/TheoreticalANTERTP/Label", "oldContent": "The RTP of the game when using the \"ANTE BET\" is {0}%", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 19685, "guid": "9a734e621c318984ca31bc524efef04a"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/Volatility/VolatilityDescription", "oldContent": "High volatility games pay out less often on average but the chance to hit big wins in a short time span is higher.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 19686, "guid": "9a734e621c318984ca31bc524efef04a"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/BuyFSCAT/RuleHolder2/Rule2", "oldContent": "There are two options to buy the FREE SPINS:", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 19687, "guid": "9a734e621c318984ca31bc524efef04a"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/MinMaxBetHolder/MaxBet/MaximumText", "oldContent": "MAXIMUM BET:", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 19688, "guid": "9a734e621c318984ca31bc524efef04a"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FSHolder/RuleHolder1/Rule1", "oldContent": "Hit 4 or mroe SCATTER symbols to trigger the FREE SPINS feature and win 10 free spins.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 19689, "guid": "9a734e621c318984ca31bc524efef04a"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/RTP/TheoreticalRTPBONUS4/Label", "oldContent": "The RTP of the game when using \"BUY SUPER FREE SPINS\" is {0}%", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 19690, "guid": "9a734e621c318984ca31bc524efef04a"}, "extraPayloads": [], "path": "Paytable/Pages/Page1/ScatterHolder/RulesHolder/RuleHolder1/Rule1", "oldContent": "This is the SCATTER symbol.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 19691, "guid": "9a734e621c318984ca31bc524efef04a"}, "extraPayloads": [], "path": "Paytable/Pages/Page1/RuleHolder/Rule", "oldContent": "Symbols pay anywhere on the screen. The total number of the same symbol on the screen at the end of a spin determines the value of the win.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 19692, "guid": "9a734e621c318984ca31bc524efef04a"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/TumbleHolder/RuleHolder2/Rule2", "oldContent": "Tumbling will continue until no more winning combinations appear as a result of a tumble. There's no limit to the number of possible tumbles.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 19693, "guid": "9a734e621c318984ca31bc524efef04a"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/TumbleHolder/RuleHolder3/Rule3", "oldContent": "All wins are added to the player's balance after all of the tumbles resulted from a base spin have been played.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 19694, "guid": "9a734e621c318984ca31bc524efef04a"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/TumbleHolder/TitleHolder/Title", "oldContent": "TUMBLE FEATURE", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 19695, "guid": "9a734e621c318984ca31bc524efef04a"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/RTP/TheoreticalRTPBONUS3/Label", "oldContent": "The RTP of the game when using \"BUY FREE SPINS\" is {0}%", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 19696, "guid": "9a734e621c318984ca31bc524efef04a"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/MinMaxBetHolder/MinBet/MinimumText", "oldContent": "MINIMUM BET:", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 19697, "guid": "9a734e621c318984ca31bc524efef04a"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/Volatility/VolatilityMeter/LabelHolder/VolatilityLabel", "oldContent": "VOLATILITY", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 19698, "guid": "9a734e621c318984ca31bc524efef04a"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/AnteBetHolder/TitleHolder/Title", "oldContent": "ANTE BET", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 19699, "guid": "9a734e621c318984ca31bc524efef04a"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/RulesTop/FSWins2", "oldContent": "Free spins total win in the history contains the whole win of the cycle.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 19700, "guid": "9a734e621c318984ca31bc524efef04a"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/RulesTop/AllWinsMultiplied", "oldContent": "Symbols pay anywhere.\nAll wins are multiplied by base bet.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 19701, "guid": "9a734e621c318984ca31bc524efef04a"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FSHolder/RuleHolder3/Rule3", "oldContent": "Whenever a MULTIPLIER symbol hits, it takes a random multiplier value of 2x, 3x, 4x, 5x, 6x, 8x, 10x, 12x, 15x, 20x, 25x, 50x, 100x or 1000x.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 19702, "guid": "9a734e621c318984ca31bc524efef04a"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/MaxWinHolder/RuleHolder1/Rule1", "oldContent": "The maximum win amount is limited to {0}x bet in both base game and free spins. If the total win of a FREE SPINS round reaches {1}x bet the round immediately ends, win is awarded and all remaining free spins are forfeited.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 19703, "guid": "9a734e621c318984ca31bc524efef04a"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/AnteBetHolder/ARuleHolder1/Rule1", "oldContent": "The player has the option to select the bet multiplier. Depending on the selected bet, the game behaves differently. The possible values are:", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 19704, "guid": "9a734e621c318984ca31bc524efef04a"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/TumbleHolder/RuleHolder1/Rule1", "oldContent": "The TUMBLE FEATURE means that after every spin, winning combinations are paid and all winning symbols disappear. The remaining symbols fall to the bottom of the screen and the empty positions are replaced with new symbols coming from above. ", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 19705, "guid": "9a734e621c318984ca31bc524efef04a"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/MaxWinHolder/TitleHolder1/Title1", "oldContent": "MAX WIN", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 19706, "guid": "9a734e621c318984ca31bc524efef04a"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FSHolder/TitleHolder/Title", "oldContent": "FREE SPINS RULES", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 19707, "guid": "9a734e621c318984ca31bc524efef04a"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/BuyFSCAT/RuleHolder4/Rule4", "oldContent": "Pay 500x total bet to trigger the SUPER FREE SPINS feature with 4 or more SCATTER symbols guaranteed to hit on the triggering spin. In this mode all multiplier symbols carry a multiplier of minimum 20x.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 19708, "guid": "9a734e621c318984ca31bc524efef04a"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/AnteBetHolder/ARuleHolder4/Rule4", "oldContent": "The player has the option to increase the bet multiplier to 25x. When playing with ante bet active, the chance to hit SCATTER symbols increases.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 19709, "guid": "9a734e621c318984ca31bc524efef04a"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/MultiplierHolder/RulesHolder/RuleHolder2/Rule2", "oldContent": "It is present on the reels only during the FREE SPINS feature and it stays on the screen until the end of the tumbling sequence.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 19710, "guid": "9a734e621c318984ca31bc524efef04a"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/RulesTop/WhenWinningOnMultiplePaylines", "oldContent": "When winning with multiple symbols, all wins are added to the total win.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 19711, "guid": "9a734e621c318984ca31bc524efef04a"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/RulesBottom/SpaceAndEnter", "oldContent": "SPACE and ENTER buttons on the keyboard can be used to start and stop the spin.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 19712, "guid": "9a734e621c318984ca31bc524efef04a"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FSHolder/RuleHolder5/Rule5", "oldContent": "Special reels are in play during the feature.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 19713, "guid": "9a734e621c318984ca31bc524efef04a"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/BuyFSCAT/TitleHolder/Title", "oldContent": "BUY FREE SPINS", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 19714, "guid": "9a734e621c318984ca31bc524efef04a"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/RTP/TheoreticalRTP/Label", "oldContent": "The theoretical RTP of this game is {0}%", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 19715, "guid": "9a734e621c318984ca31bc524efef04a"}, "extraPayloads": [], "path": "Game/Reels/FSWindows/FSStart/Content/Texts/Continue/continueLabel", "oldContent": "PRESS ANYWHERE TO CONTINUE", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 19716, "guid": "9a734e621c318984ca31bc524efef04a"}, "extraPayloads": [], "path": "Game/Reels/FSWindows/FSResult/Content/Texts/FreeSpins/freeSpinsText", "oldContent": "FREE SPINS", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 19717, "guid": "9a734e621c318984ca31bc524efef04a"}, "extraPayloads": [], "path": "Game/Reels/FSWindows/FSResult/Content/Texts/Title/gratsLabel", "oldContent": "CONGRATULATIONS", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 19718, "guid": "9a734e621c318984ca31bc524efef04a"}, "extraPayloads": [], "path": "Game/Reels/FSWindows/FSResult/Content/Texts/FreeSpins/inText", "oldContent": "IN", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 19719, "guid": "9a734e621c318984ca31bc524efef04a"}, "extraPayloads": [], "path": "Game/Reels/FSWindows/FSStart/Content/Texts/YouWon/youWonLabel", "oldContent": "YOU HAVE WON", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 19720, "guid": "9a734e621c318984ca31bc524efef04a"}, "extraPayloads": [], "path": "Game/Reels/FSWindows/FSStart/Content/Texts/FSText/fsTextLabel", "oldContent": "FREE SPINS", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 19721, "guid": "9a734e621c318984ca31bc524efef04a"}, "extraPayloads": [], "path": "Game/Reels/FSWindows/FSStart/Content/Texts/Title/gratsLabel", "oldContent": "CONGRATULATIONS", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 19722, "guid": "9a734e621c318984ca31bc524efef04a"}, "extraPayloads": [], "path": "Game/Reels/FSWindows/FSResult/Content/Texts/YouWon/youWonLabel", "oldContent": "YOU HAVE WON", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 19723, "guid": "9a734e621c318984ca31bc524efef04a"}, "extraPayloads": [], "path": "Game/Reels/FSWindows/FSExtra/Content/Texts/FSText/extraFSText", "oldContent": "FREE SPINS", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 19724, "guid": "9a734e621c318984ca31bc524efef04a"}, "extraPayloads": [], "path": "Game/Reels/FSWindows/FSResult/Content/Texts/Continue/continueLabel", "oldContent": "PRESS ANYWHERE TO CONTINUE", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 19725, "guid": "9a734e621c318984ca31bc524efef04a"}, "extraPayloads": [], "path": "Game/SWBO_BuyFeatureV2/FeaturePurchase/FSPurchaseWindow_opt1/content/Texts/Text04/lbl04", "oldContent": "BOMBS MINIMUM", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 19726, "guid": "9a734e621c318984ca31bc524efef04a"}, "extraPayloads": [], "path": "Game/SWBO_BuyFeatureV2/FeaturePurchase/FSPurchaseWindow/content/Texts/MoneyToPay/costTextLabel", "oldContent": "AT THE COST OF", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 19727, "guid": "9a734e621c318984ca31bc524efef04a"}, "extraPayloads": [], "path": "Game/SWBO_BuyFeatureV2/FeaturePurchase/FSPurchaseButton/content/Visual_FSButton/Texts/BuyText/buy_text1", "oldContent": "FREE SPINS", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 19728, "guid": "9a734e621c318984ca31bc524efef04a"}, "extraPayloads": [], "path": "Game/SWBO_BuyFeatureV2/FeaturePurchase/FSPurchaseWindow_opt1/content/Texts/ButtonsVisual/ButtonYes/Label", "oldContent": "YES", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 19729, "guid": "9a734e621c318984ca31bc524efef04a"}, "extraPayloads": [], "path": "Game/SWBO_BuyFeatureV2/FeaturePurchase/FSPurchaseWindow/content/Texts/FSToBuy/fsTextLabel", "oldContent": "FREE SPINS", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 19730, "guid": "9a734e621c318984ca31bc524efef04a"}, "extraPayloads": [], "path": "Game/SWBO_BuyFeatureV2/FeaturePurchase/FSPurchaseWindow/content/Texts/AIO_Window/LabelHolder/LocalizedLabel", "oldContent": "ARE YOU SURE YOU WANT TO PURCHASE (0) FREE SPINS AT THE COST OF {0}?", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 19731, "guid": "9a734e621c318984ca31bc524efef04a"}, "extraPayloads": [], "path": "Game/SWBO_BuyFeatureV2/FeaturePurchase/FSPurchaseWindow/content/Texts/ButtonsVisual/ButtonYes/Label", "oldContent": "YES", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 19732, "guid": "9a734e621c318984ca31bc524efef04a"}, "extraPayloads": [], "path": "Game/SWBO_BuyFeatureV2/FeaturePurchase/FSPurchaseButton_opt1/content/Visual_FSButton/Texts/BuyText/buy_text1", "oldContent": "FREE SPINS", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 19733, "guid": "9a734e621c318984ca31bc524efef04a"}, "extraPayloads": [], "path": "Game/SWBO_BuyFeatureV2/FeaturePurchase/FSPurchaseWindow_opt1/content/Texts/Text01/lbl01", "oldContent": "BUY", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 19734, "guid": "9a734e621c318984ca31bc524efef04a"}, "extraPayloads": [], "path": "Game/SWBO_BuyFeatureV2/FeaturePurchase/FSPurchaseButton/content/Visual_FSButton/Texts/BuyText/buy_text0", "oldContent": "BUY", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 19735, "guid": "9a734e621c318984ca31bc524efef04a"}, "extraPayloads": [], "path": "Game/SWBO_BuyFeatureV2/FeaturePurchase/FSPurchaseWindow_opt1/content/Texts/ButtonsVisual/ButtonNo/Label", "oldContent": "NO", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 19736, "guid": "9a734e621c318984ca31bc524efef04a"}, "extraPayloads": [], "path": "Game/SWBO_BuyFeatureV2/FeaturePurchase/FSPurchaseWindow_opt1/content/Texts/Text02/fsTextLabel", "oldContent": "FREE SPINS", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 19737, "guid": "9a734e621c318984ca31bc524efef04a"}, "extraPayloads": [], "path": "Game/SWBO_BuyFeatureV2/FeaturePurchase/FSPurchaseWindow_opt1/content/Texts/Text03/lbl03", "oldContent": "WITH ALL MULTIPLIER", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 19738, "guid": "9a734e621c318984ca31bc524efef04a"}, "extraPayloads": [], "path": "Game/SWBO_BuyFeatureV2/FeaturePurchase/FSPurchaseWindow/content/Texts/AreYouSure/youSureLbl", "oldContent": "ARE YOU SURE YOU WANT TO PURCHASE", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 19739, "guid": "9a734e621c318984ca31bc524efef04a"}, "extraPayloads": [], "path": "Game/SWBO_BuyFeatureV2/FeaturePurchase/FSPurchaseButton_opt1/content/Visual_FSButton/Texts/BuyText/buy_text0", "oldContent": "BUY", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 19740, "guid": "9a734e621c318984ca31bc524efef04a"}, "extraPayloads": [], "path": "Game/SWBO_BuyFeatureV2/FeaturePurchase/FSPurchaseWindow/content/Texts/ButtonsVisual/ButtonNo/Label", "oldContent": "NO", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 19741, "guid": "9a734e621c318984ca31bc524efef04a"}, "extraPayloads": [], "path": "Game/SWBO_AnteBet_v2/AnteBet/BetLevelButtons/Visual_AnteBet/AnimatedPivot/Level0Visual/Landscape/TextsLvl0/betLevel_prefix", "oldContent": "BET", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 19742, "guid": "9a734e621c318984ca31bc524efef04a"}, "extraPayloads": [], "path": "Game/SWBO_AnteBet_v2/AnteBet/BetLevelButtons/Visual_AnteBet/AnimatedPivot/Level1Visual/Landscape/TextsLvl1/betLevel_prefix", "oldContent": "BET", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 19743, "guid": "9a734e621c318984ca31bc524efef04a"}, "extraPayloads": [], "path": "Game/SWBO_AnteBet_v2/AnteBet/BetLevelButtons/Visual_AnteBet/AnimatedPivot/Slider/Texts/text_off", "oldContent": "OFF", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 19744, "guid": "9a734e621c318984ca31bc524efef04a"}, "extraPayloads": [], "path": "Game/SWBO_AnteBet_v2/AnteBet/BetLevelButtons/Visual_AnteBet/AnimatedPivot/Level1Visual/Landscape/TextsLvl1/betLevel1Feature_lbl1", "oldContent": "CHANCE TO\nWIN FEATURE", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 19745, "guid": "9a734e621c318984ca31bc524efef04a"}, "extraPayloads": [], "path": "Game/SWBO_AnteBet_v2/AnteBet/BetLevelButtons/Visual_AnteBet/AnimatedPivot/Level1Visual/Landscape/TextsLvl1/betLevel1Feature_lbl0", "oldContent": "DOUBLE", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 19746, "guid": "9a734e621c318984ca31bc524efef04a"}, "extraPayloads": [], "path": "Game/SWBO_AnteBet_v2/AnteBet/BetLevelButtons/Visual_AnteBet/AnimatedPivot/Level0Visual/Landscape/TextsLvl0/LocalizedLabel", "oldContent": "DOUBLE THE CHANCE TO WIN FEATURE", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 19747, "guid": "9a734e621c318984ca31bc524efef04a"}, "extraPayloads": [], "path": "Game/SWBO_AnteBet_v2/AnteBet/BetLevelButtons/Visual_AnteBet/AnimatedPivot/Level0Visual/Landscape/TextsLvl0/betLevel1Feature_lbl0", "oldContent": "DOUBLE", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 19748, "guid": "9a734e621c318984ca31bc524efef04a"}, "extraPayloads": [], "path": "Game/SWBO_AnteBet_v2/AnteBet/BetLevelButtons/Visual_AnteBet/AnimatedPivot/Level0Visual/Landscape/TextsLvl0/betLevel1Feature_lbl1", "oldContent": "CHANCE TO\nWIN FEATURE", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 19749, "guid": "9a734e621c318984ca31bc524efef04a"}, "extraPayloads": [], "path": "Game/SWBO_AnteBet_v2/AnteBet/BetLevelButtons/Visual_AnteBet/AnimatedPivot/BetLevelText_portrait/Texts_portrait/betLevel_prefix", "oldContent": "BET", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 19750, "guid": "9a734e621c318984ca31bc524efef04a"}, "extraPayloads": [], "path": "Game/SWBO_AnteBet_v2/AnteBet/BetLevelButtons/Visual_AnteBet/AnimatedPivot/BetLevelText_portrait/Texts_portrait/betLevel1Feature_lbl0", "oldContent": "DOUBLE CHANCE\nTO WIN FEATURE", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 19751, "guid": "9a734e621c318984ca31bc524efef04a"}, "extraPayloads": [], "path": "Game/SWBO_AnteBet_v2/AnteBet/BetLevelButtons/Visual_AnteBet/AnimatedPivot/BetLevelText_portrait/Texts_portrait/LocalizedLabel", "oldContent": "DOUBLE THE CHANCE TO WIN FEATURE", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 19752, "guid": "9a734e621c318984ca31bc524efef04a"}, "extraPayloads": [], "path": "Game/SWBO_AnteBet_v2/AnteBet/BetLevelButtons/Visual_AnteBet/AnimatedPivot/Level1Visual/Landscape/TextsLvl1/LocalizedLabel", "oldContent": "DOUBLE THE CHANCE TO WIN FEATURE", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 19753, "guid": "9a734e621c318984ca31bc524efef04a"}, "extraPayloads": [], "path": "Game/SWBO_AnteBet_v2/AnteBet/BetLevelButtons/Visual_AnteBet/AnimatedPivot/Slider/Texts_portrait/text_on", "oldContent": "ON", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 19754, "guid": "9a734e621c318984ca31bc524efef04a"}, "extraPayloads": [], "path": "Game/SWBO_AnteBet_v2/AnteBet/BetLevelButtons/Visual_AnteBet/AnimatedPivot/Slider/Texts_portrait/text_off", "oldContent": "OFF", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 19755, "guid": "9a734e621c318984ca31bc524efef04a"}, "extraPayloads": [], "path": "Game/SWBO_AnteBet_v2/AnteBet/BetLevelButtons/Visual_AnteBet/AnimatedPivot/Slider/Texts/text_on", "oldContent": "ON", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 19756, "guid": "9a734e621c318984ca31bc524efef04a"}, "extraPayloads": [], "path": "Game/RSMultiplierVisual/TotalWinMultiplied/Labels/TitleText/TextLabel", "oldContent": "TUMBLE WIN", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 19757, "guid": "9a734e621c318984ca31bc524efef04a"}, "extraPayloads": [], "path": "Game/Reels/TitlesHolder/TitleMessageControllerPortrait/Normal/TitleMessage1/LabelHolder/Bet", "oldContent": "BET", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 19758, "guid": "9a734e621c318984ca31bc524efef04a"}, "extraPayloads": [], "path": "Game/Reels/TitlesHolder/TitleMessageControllerLandscape/FreeSpins/TitleMessage1/LabelHolder/FinalTumbleWin", "oldContent": "      MULTIPLIES FINAL TUMBLE WIN", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 19759, "guid": "9a734e621c318984ca31bc524efef04a"}, "extraPayloads": [], "path": "Game/Reels/TitlesHolder/TitleMessageControllerPortrait/FreeSpins/TitleMessage2/LabelHolder/Multiplier", "oldContent": "MULTIPLIER", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 19760, "guid": "9a734e621c318984ca31bc524efef04a"}, "extraPayloads": [], "path": "Game/Reels/TitlesHolder/TitleMessageControllerLandscape/FreeSpins/TitleMessage2/LabelHolder/WithUpTo", "oldContent": "      WITH UP TO", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 19761, "guid": "9a734e621c318984ca31bc524efef04a"}, "extraPayloads": [], "path": "Game/Reels/TitlesHolder/TitleMessageControllerPortrait/Normal/TitleMessage2/LabelHolder/SymbolsPay", "oldContent": "SYMBOLS PAY ANYWHERE ON THE SCREEN", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 19762, "guid": "9a734e621c318984ca31bc524efef04a"}, "extraPayloads": [], "path": "Game/Reels/TitlesHolder/TitleMessageControllerPortrait/Normal/TitleMessage1/LabelHolder/WinUpTo", "oldContent": "WIN UP TO", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 19763, "guid": "9a734e621c318984ca31bc524efef04a"}, "extraPayloads": [], "path": "Game/Reels/TitlesHolder/TitleMessageControllerLandscape/FreeSpins/TitleMessage2/LabelHolder/Multiplier", "oldContent": "MULTIPLIER", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 19764, "guid": "9a734e621c318984ca31bc524efef04a"}, "extraPayloads": [], "path": "Game/Reels/TitlesHolder/TitleMessageControllerPortrait/FreeSpins/TitleMessage2/LabelHolder/WithUpTo", "oldContent": "      WITH UP TO", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 19765, "guid": "9a734e621c318984ca31bc524efef04a"}, "extraPayloads": [], "path": "Game/Reels/TitlesHolder/TitleMessageControllerLandscape/Normal/TitleMessage1/LabelHolder/WinUpTo", "oldContent": "WIN UP TO", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 19766, "guid": "9a734e621c318984ca31bc524efef04a"}, "extraPayloads": [], "path": "Game/Reels/TitlesHolder/TitleMessageControllerLandscape/Normal/TitleMessage2/LabelHolder/SymbolsPay", "oldContent": "SYMBOLS PAY ANYWHERE ON THE SCREEN", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 19767, "guid": "9a734e621c318984ca31bc524efef04a"}, "extraPayloads": [], "path": "Game/Reels/TitlesHolder/TitleMessageControllerLandscape/Normal/TitleMessage3/LabelHolder/3xWins", "oldContent": "4X      WINS FREE SPINS", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 19768, "guid": "9a734e621c318984ca31bc524efef04a"}, "extraPayloads": [], "path": "Game/Reels/TitlesHolder/TitleMessageControllerLandscape/Normal/TitleMessage1/LabelHolder/Bet", "oldContent": "BET", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 19769, "guid": "9a734e621c318984ca31bc524efef04a"}, "extraPayloads": [], "path": "Game/Reels/TitlesHolder/TitleMessageControllerPortrait/Normal/TitleMessage3/LabelHolder/3xWins", "oldContent": "4X      WINS FREE SPINS", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 19770, "guid": "9a734e621c318984ca31bc524efef04a"}, "extraPayloads": [], "path": "Game/Reels/TitlesHolder/TitleMessageControllerPortrait/FreeSpins/TitleMessage1/LabelHolder/FinalTumbleWin", "oldContent": "      MULTIPLIES FINAL TUMBLE WIN", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 19771, "guid": "9a734e621c318984ca31bc524efef04a"}, "extraPayloads": [], "path": "Game/FreeSpinsLeftPanel/FSLeftContent/BuyText/fsLeftText", "oldContent": "FREE\nSPINS\nLEFT", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 19772, "guid": "9a734e621c318984ca31bc524efef04a"}, "extraPayloads": [], "path": "Game/FreeSpinsLeftPanel/FSLeftContent/BuyText/fsLastText", "oldContent": "LAST\nFREE\nSPIN", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 19773, "guid": "9a734e621c318984ca31bc524efef04a"}, "extraPayloads": [], "path": "IntroScreen/content/ContentII/Content/Pages/Page1/LabelHolder/WinUpTo", "oldContent": "WIN UP TO 25,000X BET", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 19774, "guid": "9a734e621c318984ca31bc524efef04a"}, "extraPayloads": [], "path": "IntroScreen/content/ContentII/Content/Pages/Page2/LabelHolder/Label", "oldContent": "SYMBOLS PAY ANYWHERE ON THE SCREEN", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 19775, "guid": "9a734e621c318984ca31bc524efef04a"}, "extraPayloads": [], "path": "IntroScreen/content/ContentII/Content/Pages/Page3/LabelHolder/Label", "oldContent": "RANDOM MULTIPLIER UP TO 1000X IN FREE SPINS", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 19776, "guid": "9a734e621c318984ca31bc524efef04a"}, "extraPayloads": [], "path": "IntroScreen/content/ContentII/IntroButtons/VolatilityMeter/LabelHolder/VolatilityLabel", "oldContent": "VOLATILITY", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 19777, "guid": "9a734e621c318984ca31bc524efef04a"}, "extraPayloads": [], "path": "IntroScreen/content/ContentII/IntroButtons/ButtonSkipIntro/content/TextHolder/Label", "oldContent": "DON'T SHOW NEXT TIME", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}], "Spines": [], "revisionNumber": 0}, "fileID": 19778}], "fileID": 19779}, {"name": "Paytable/Pages/Page4/RulesTop/AllValuesExpressed", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 19669, "guid": "9a734e621c318984ca31bc524efef04a"}, "children": [], "psr": "d"}, "fileID": 19561}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "5e27b3cad3e223744b46502a29164112"}, "_text": "Todos os valores são expressos como prémios reais em moedas.", "fontSize": 25, "anchorY": 0, "width": 1333, "height": 50, "overflow": 0}, "fileID": 19670}], "fileID": 19780}, {"name": "Paytable/Pages/Page3/AnteBetHolder/ARuleHolder2/Rule2", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 19669, "guid": "9a734e621c318984ca31bc524efef04a"}, "children": [], "psr": "d"}, "fileID": 19562}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "5e27b3cad3e223744b46502a29164112"}, "_text": "Multiplicador de aposta 25x - a possibilidade de ganhar o modo rodadas grátis é naturalmente o dobro.\nO modo COMPRAR RODADAS GRÁTIS está desativado.", "fontSize": 25, "width": 1296, "height": 150, "overflow": 0}, "fileID": 19671}], "fileID": 19781}, {"name": "Paytable/Pages/Page4/RulesTop/FSWins1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 19669, "guid": "9a734e621c318984ca31bc524efef04a"}, "children": [], "psr": "d"}, "fileID": 19563}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "5e27b3cad3e223744b46502a29164112"}, "_text": "O prémio de rodadas grátis é atribuído ao jogador após concluída a ronda. ", "fontSize": 25, "anchorY": 0, "width": 1400, "height": 50, "overflow": 0}, "fileID": 19672}], "fileID": 19782}, {"name": "Paytable/Pages/Page2/FSHolder/RuleHolder4/Rule4", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 19669, "guid": "9a734e621c318984ca31bc524efef04a"}, "children": [], "psr": "d"}, "fileID": 19564}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "5e27b3cad3e223744b46502a29164112"}, "_text": "Quando a sequência de quedas termina, os valores de todos os símbolos MULTIPLICADOR no ecrã são somados e o prémio total da sequência é multiplicado pelo valor final.", "fontSize": 25, "anchorY": 0, "width": 1186, "height": 100, "overflow": 0}, "fileID": 19673}], "fileID": 19783}, {"name": "Paytable/Pages/Page1/ScatterHolder/RulesHolder/RuleHolder3/Rule3", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 19669, "guid": "9a734e621c318984ca31bc524efef04a"}, "children": [], "psr": "d"}, "fileID": 19565}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "5e27b3cad3e223744b46502a29164112"}, "_text": "O símbolo SCATTER dá prémio independentemente da posição em que se encontre.", "fontSize": 25, "anchorX": 0, "anchorY": 0, "width": 659, "height": 75, "overflow": 0}, "fileID": 19674}], "fileID": 19784}, {"name": "Paytable/Pages/Page1/ScatterHolder/RulesHolder/RuleHolder/Rule2", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 19669, "guid": "9a734e621c318984ca31bc524efef04a"}, "children": [], "psr": "d"}, "fileID": 19566}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "5e27b3cad3e223744b46502a29164112"}, "_text": "O símbolo SCATTER está presente em todos os rolos.", "fontSize": 25, "anchorX": 0, "anchorY": 0, "width": 686, "height": 75, "overflow": 0}, "fileID": 19675}], "fileID": 19785}, {"name": "Paytable/Pages/Page2/FSHolder/RuleHolder2/Rule2", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 19669, "guid": "9a734e621c318984ca31bc524efef04a"}, "children": [], "psr": "d"}, "fileID": 19567}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "5e27b3cad3e223744b46502a29164112"}, "_text": "Quando aparecem 3 ou mais símbolos SCATTER durante a RONDA DE RODADAS GRÁTIS, são atribuídas 5 rodadas grátis adicionais.", "fontSize": 25, "anchorY": 0, "width": 1400, "height": 100, "overflow": 0}, "fileID": 19676}], "fileID": 19786}, {"name": "Paytable/Pages/Page3/AnteBetHolder/ARuleHolder3/Rule3", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 19669, "guid": "9a734e621c318984ca31bc524efef04a"}, "children": [], "psr": "d"}, "fileID": 19568}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "5e27b3cad3e223744b46502a29164112"}, "_text": "Multiplicador de apostas 20x - Jogo normal", "fontSize": 25, "width": 1400, "height": 50, "overflow": 0}, "fileID": 19677}], "fileID": 19787}, {"name": "Paytable/Pages/Page1/TitleHolder/Title", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 19669, "guid": "9a734e621c318984ca31bc524efef04a"}, "children": [], "psr": "d"}, "fileID": 19569}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "5e27b3cad3e223744b46502a29164112"}, "_text": "REGRAS DO JOGO", "fontSize": 35, "anchorY": 1, "width": 1150, "height": 70, "overflow": 0}, "fileID": 19678}], "fileID": 19788}, {"name": "Paytable/Pages/Page4/RulesBottom/MalfunctionLabel", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 19669, "guid": "9a734e621c318984ca31bc524efef04a"}, "children": [], "psr": "d"}, "fileID": 19570}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "5e27b3cad3e223744b46502a29164112"}, "_text": "Uma avaria anula todos os pagamentos e jogadas.", "fontSize": 25, "anchorY": 0, "width": 1400, "height": 50, "overflow": 0}, "fileID": 19679}], "fileID": 19789}, {"name": "Paytable/Pages/Page3/BuyFSCAT/RuleHolder1/Rule1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 19669, "guid": "9a734e621c318984ca31bc524efef04a"}, "children": [], "psr": "d"}, "fileID": 19571}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "5e27b3cad3e223744b46502a29164112"}, "_text": "A ronda de RODADAS GRÁTIS pode ser ativada instantaneamente a partir do jogo base se for comprada.", "fontSize": 25, "anchorY": 0, "width": 1400, "height": 75, "overflow": 0}, "fileID": 19680}], "fileID": 19790}, {"name": "Paytable/Pages/Page3/BuyFSCAT/RuleHolder3/Rule3", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 19669, "guid": "9a734e621c318984ca31bc524efef04a"}, "children": [], "psr": "d"}, "fileID": 19572}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "5e27b3cad3e223744b46502a29164112"}, "_text": "Pague 100x a aposta total para ativar o modo RODADAS GRÁTIS com a garantia de que aparecem 4 ou mais símbolos SCATTER na rodada de ativação.", "fontSize": 25, "anchorY": 0, "width": 1154, "height": 75, "overflow": 0}, "fileID": 19681}], "fileID": 19791}, {"name": "Paytable/Pages/Page4/TitleHolder/Title", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 19669, "guid": "9a734e621c318984ca31bc524efef04a"}, "children": [], "psr": "d"}, "fileID": 19573}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "5e27b3cad3e223744b46502a29164112"}, "_text": "REGRAS DO JOGO", "fontSize": 35, "anchorY": 1, "width": 1150, "height": 100, "overflow": 0}, "fileID": 19682}], "fileID": 19792}, {"name": "Paytable/Pages/Page2/MultiplierHolder/RulesHolder/RuleHolder1/Rule1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 19669, "guid": "9a734e621c318984ca31bc524efef04a"}, "children": [], "psr": "d"}, "fileID": 19574}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "5e27b3cad3e223744b46502a29164112"}, "_text": "Este é o símbolo MULTIPLICADOR.", "fontSize": 25, "anchorX": 0, "anchorY": 1, "width": 1099, "height": 100, "overflow": 0}, "fileID": 19683}], "fileID": 19793}, {"name": "Paytable/Pages/Page4/RTP/TheoreticalANTERTP/Label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 19669, "guid": "9a734e621c318984ca31bc524efef04a"}, "children": [], "psr": "d"}, "fileID": 19575}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "5e27b3cad3e223744b46502a29164112"}, "_text": "O RTP do jogo ao usar a \"APOSTA ANTE\" é de {0}%", "fontSize": 25, "anchorY": 0, "width": 1400, "height": 50, "overflow": 0}, "fileID": 19684}], "fileID": 19794}, {"name": "Paytable/Pages/Page4/Volatility/VolatilityDescription", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 19669, "guid": "9a734e621c318984ca31bc524efef04a"}, "children": [], "psr": "d"}, "fileID": 19576}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "5e27b3cad3e223744b46502a29164112"}, "_text": "Os jogos de alta volatilidade pagam menos frequentemente, em média, mas a possibilidade de grandes prémios num curto período de tempo é maior.", "fontSize": 25, "anchorY": 0, "width": 1400, "height": 50, "overflow": 0}, "fileID": 19685}], "fileID": 19795}, {"name": "Paytable/Pages/Page3/BuyFSCAT/RuleHolder2/Rule2", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 19669, "guid": "9a734e621c318984ca31bc524efef04a"}, "children": [], "psr": "d"}, "fileID": 19577}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "5e27b3cad3e223744b46502a29164112"}, "_text": "Existem duas opções para comprar RODADAS GRÁTIS:", "fontSize": 25, "anchorY": 0, "width": 1400, "height": 75, "overflow": 0}, "fileID": 19686}], "fileID": 19796}, {"name": "Paytable/Pages/Page4/MinMaxBetHolder/MaxBet/MaximumText", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 19669, "guid": "9a734e621c318984ca31bc524efef04a"}, "children": [], "psr": "d"}, "fileID": 19578}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "5e27b3cad3e223744b46502a29164112"}, "_text": "APOSTA MÁXIMA:", "fontSize": 25, "anchorX": 0, "width": 212, "height": 26}, "fileID": 19687}], "fileID": 19797}, {"name": "Paytable/Pages/Page2/FSHolder/RuleHolder1/Rule1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 19669, "guid": "9a734e621c318984ca31bc524efef04a"}, "children": [], "psr": "d"}, "fileID": 19579}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "5e27b3cad3e223744b46502a29164112"}, "_text": "Obtenha 4 ou mais símbolos SCATTER para ativar o modo RODADAS GRÁTIS e ganhar 10 rodadas gr<PERSON>tis.", "fontSize": 25, "anchorY": 0, "width": 1425, "height": 100, "overflow": 0}, "fileID": 19688}], "fileID": 19798}, {"name": "Paytable/Pages/Page4/RTP/TheoreticalRTPBONUS4/Label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 19669, "guid": "9a734e621c318984ca31bc524efef04a"}, "children": [], "psr": "d"}, "fileID": 19580}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "5e27b3cad3e223744b46502a29164112"}, "_text": "O RTP do jogo aquando do uso de “COMPRAR SUPER RODADAS GRÁTIS” é {0}%", "fontSize": 25, "anchorY": 0, "width": 1400, "height": 50, "overflow": 0}, "fileID": 19689}], "fileID": 19799}, {"name": "Paytable/Pages/Page1/ScatterHolder/RulesHolder/RuleHolder1/Rule1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 19669, "guid": "9a734e621c318984ca31bc524efef04a"}, "children": [], "psr": "d"}, "fileID": 19581}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "5e27b3cad3e223744b46502a29164112"}, "_text": "Este é o símbolo de SCATTER. ", "fontSize": 25, "anchorX": 0, "anchorY": 0, "width": 510, "height": 75, "overflow": 0}, "fileID": 19690}], "fileID": 19800}, {"name": "Paytable/Pages/Page1/RuleHolder/Rule", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 19669, "guid": "9a734e621c318984ca31bc524efef04a"}, "children": [], "psr": "d"}, "fileID": 19582}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "5e27b3cad3e223744b46502a29164112"}, "_text": "Os símbolos pagam em qualquer posição no ecrã. O número total do mesmo símbolo no ecrã no final de uma rodada determina o valor do prémio.", "fontSize": 25, "anchorY": 0, "width": 1160, "height": 100, "overflow": 0}, "fileID": 19691}], "fileID": 19801}, {"name": "Paytable/Pages/Page2/TumbleHolder/RuleHolder2/Rule2", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 19669, "guid": "9a734e621c318984ca31bc524efef04a"}, "children": [], "psr": "d"}, "fileID": 19583}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "5e27b3cad3e223744b46502a29164112"}, "_text": "As quedas continuarão até que não apareçam mais combinações premiadas em resultado de uma queda. Não há limite para o número de quedas possíveis.", "fontSize": 25, "anchorY": 0, "width": 1179, "height": 100, "overflow": 0}, "fileID": 19692}], "fileID": 19802}, {"name": "Paytable/Pages/Page2/TumbleHolder/RuleHolder3/Rule3", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 19669, "guid": "9a734e621c318984ca31bc524efef04a"}, "children": [], "psr": "d"}, "fileID": 19584}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "5e27b3cad3e223744b46502a29164112"}, "_text": "Todos os prémios são adicionados ao saldo do jogador depois de todas as quedas resultantes de uma rodada de base terem sido jogadas.", "fontSize": 25, "anchorY": 0, "width": 1400, "height": 75, "overflow": 0}, "fileID": 19693}], "fileID": 19803}, {"name": "Paytable/Pages/Page2/TumbleHolder/TitleHolder/Title", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 19669, "guid": "9a734e621c318984ca31bc524efef04a"}, "children": [], "psr": "d"}, "fileID": 19585}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "5e27b3cad3e223744b46502a29164112"}, "_text": "EXTRA DE QUEDA ", "fontSize": 35, "anchorY": 1, "width": 1150, "height": 35, "overflow": 0}, "fileID": 19694}], "fileID": 19804}, {"name": "Paytable/Pages/Page4/RTP/TheoreticalRTPBONUS3/Label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 19669, "guid": "9a734e621c318984ca31bc524efef04a"}, "children": [], "psr": "d"}, "fileID": 19586}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "5e27b3cad3e223744b46502a29164112"}, "_text": "O RTP do jogo quando se usa a opção \"COMPRAR RODADAS GRÁTIS\" é de {0}%", "fontSize": 25, "anchorY": 0, "width": 1400, "height": 50, "overflow": 0}, "fileID": 19695}], "fileID": 19805}, {"name": "Paytable/Pages/Page4/MinMaxBetHolder/MinBet/MinimumText", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 19669, "guid": "9a734e621c318984ca31bc524efef04a"}, "children": [], "psr": "d"}, "fileID": 19587}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "5e27b3cad3e223744b46502a29164112"}, "_text": "APOSTA MÍNIMA:", "fontSize": 25, "anchorX": 0, "width": 208, "height": 26}, "fileID": 19696}], "fileID": 19806}, {"name": "Paytable/Pages/Page4/Volatility/VolatilityMeter/LabelHolder/VolatilityLabel", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 19669, "guid": "9a734e621c318984ca31bc524efef04a"}, "children": [], "psr": "d"}, "fileID": 19588}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "5e27b3cad3e223744b46502a29164112"}, "_text": "VOLATILIDADE", "fontSize": 22, "anchorX": 0, "width": 156, "height": 22}, "fileID": 19697}], "fileID": 19807}, {"name": "Paytable/Pages/Page3/AnteBetHolder/TitleHolder/Title", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 19669, "guid": "9a734e621c318984ca31bc524efef04a"}, "children": [], "psr": "d"}, "fileID": 19589}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "5e27b3cad3e223744b46502a29164112"}, "_text": "APOSTA ANTE", "fontSize": 35, "anchorY": 1, "width": 1400, "height": 70, "overflow": 0}, "fileID": 19698}], "fileID": 19808}, {"name": "Paytable/Pages/Page4/RulesTop/FSWins2", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 19669, "guid": "9a734e621c318984ca31bc524efef04a"}, "children": [], "psr": "d"}, "fileID": 19590}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "5e27b3cad3e223744b46502a29164112"}, "_text": "O prémio total de rodadas grátis no histórico contém o prémio total do ciclo. ", "fontSize": 25, "anchorY": 0, "width": 1400, "height": 50, "overflow": 0}, "fileID": 19699}], "fileID": 19809}, {"name": "Paytable/Pages/Page4/RulesTop/AllWinsMultiplied", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 19669, "guid": "9a734e621c318984ca31bc524efef04a"}, "children": [], "psr": "d"}, "fileID": 19591}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "5e27b3cad3e223744b46502a29164112"}, "_text": "Os símbolos pagam em qualquer posição.\nTodos os ganhos são multiplicados pela aposta base.", "fontSize": 25, "anchorY": 0, "width": 1400, "height": 150, "overflow": 0, "spacingY": 5}, "fileID": 19700}], "fileID": 19810}, {"name": "Paytable/Pages/Page2/FSHolder/RuleHolder3/Rule3", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 19669, "guid": "9a734e621c318984ca31bc524efef04a"}, "children": [], "psr": "d"}, "fileID": 19592}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "5e27b3cad3e223744b46502a29164112"}, "_text": "Quando aparece um símbolo MULTIPLICADOR, este assume um valor multiplicador aleatório de 2x, 3x, 4x, 5x, 6x, 8x, 10x, 12x, 15x, 20x, 25x, 50x, 100x ou 1000x.", "fontSize": 25, "anchorY": 0, "width": 1135, "height": 100, "overflow": 0}, "fileID": 19701}], "fileID": 19811}, {"name": "Paytable/Pages/Page3/MaxWinHolder/RuleHolder1/Rule1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 19669, "guid": "9a734e621c318984ca31bc524efef04a"}, "children": [], "psr": "d"}, "fileID": 19593}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "5e27b3cad3e223744b46502a29164112"}, "_text": "O valor máximo de prémio é limitado a {0}x a aposta tanto no jogo base como em rodadas grátis. Se o prémio total de uma RONDA DE RODADAS GRÁTIS atingir {1}x a aposta a ronda termina imediatamente, o prémio é atribuído e todas as rodadas grátis restantes são removidas", "fontSize": 25, "anchorY": 0, "width": 1400, "height": 125, "overflow": 0}, "fileID": 19702}], "fileID": 19812}, {"name": "Paytable/Pages/Page3/AnteBetHolder/ARuleHolder1/Rule1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 19669, "guid": "9a734e621c318984ca31bc524efef04a"}, "children": [], "psr": "d"}, "fileID": 19594}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "5e27b3cad3e223744b46502a29164112"}, "_text": "O jogador tem a opção de selecionar o multiplicador de aposta. O jogo comporta-se de forma diferente dependendo da aposta selecionada. Os valores possíveis são:", "fontSize": 25, "anchorY": 0, "width": 1400, "height": 100, "overflow": 0}, "fileID": 19703}], "fileID": 19813}, {"name": "Paytable/Pages/Page2/TumbleHolder/RuleHolder1/Rule1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 19669, "guid": "9a734e621c318984ca31bc524efef04a"}, "children": [], "psr": "d"}, "fileID": 19595}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "5e27b3cad3e223744b46502a29164112"}, "_text": "O MODO QUEDA significa que, após cada rodada, as combinações vencedoras são pagas e todos os símbolos vencedores desaparecem. Os símbolos restantes caem na parte inferior do ecrã e as posições vazias são substituídas por novos símbolos vindos de cima.", "fontSize": 25, "anchorY": 0, "width": 1275, "height": 125, "overflow": 0}, "fileID": 19704}], "fileID": 19814}, {"name": "Paytable/Pages/Page3/MaxWinHolder/TitleHolder1/Title1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 19669, "guid": "9a734e621c318984ca31bc524efef04a"}, "children": [], "psr": "d"}, "fileID": 19596}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "5e27b3cad3e223744b46502a29164112"}, "_text": "PRÉMIO MÁX.", "fontSize": 35, "anchorY": 1, "width": 1400, "height": 70, "overflow": 0}, "fileID": 19705}], "fileID": 19815}, {"name": "Paytable/Pages/Page2/FSHolder/TitleHolder/Title", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 19669, "guid": "9a734e621c318984ca31bc524efef04a"}, "children": [], "psr": "d"}, "fileID": 19597}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "5e27b3cad3e223744b46502a29164112"}, "_text": "REGRAS DE RODADAS GRÁTIS", "fontSize": 35, "anchorY": 1, "width": 1150, "height": 70, "overflow": 0}, "fileID": 19706}], "fileID": 19816}, {"name": "Paytable/Pages/Page3/BuyFSCAT/RuleHolder4/Rule4", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 19669, "guid": "9a734e621c318984ca31bc524efef04a"}, "children": [], "psr": "d"}, "fileID": 19598}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "5e27b3cad3e223744b46502a29164112"}, "_text": "Pague 500x a aposta total para ativar o modo SUPER RODADAS GRÁTIS com a garantia de que aparecem 4 ou mais símbolos SCATTER na rodada de ativação. Neste modo, todos os símbolos multiplicadores têm um multiplicador mínimo de 20x.", "fontSize": 25, "anchorY": 0, "width": 1400, "height": 75, "overflow": 0}, "fileID": 19707}], "fileID": 19817}, {"name": "Paytable/Pages/Page3/AnteBetHolder/ARuleHolder4/Rule4", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 19669, "guid": "9a734e621c318984ca31bc524efef04a"}, "children": [], "psr": "d"}, "fileID": 19599}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "5e27b3cad3e223744b46502a29164112"}, "_text": "O jogador tem a opção de aumentar o multiplicador de aposta para 25x. Quando se joga com a aposta ante ativa, a possibilidade de aparecerem símbolos SCATTER aumenta.", "fontSize": 25, "anchorY": 0, "width": 1259, "height": 100, "overflow": 0}, "fileID": 19708}], "fileID": 19818}, {"name": "Paytable/Pages/Page2/MultiplierHolder/RulesHolder/RuleHolder2/Rule2", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 19669, "guid": "9a734e621c318984ca31bc524efef04a"}, "children": [], "psr": "d"}, "fileID": 19600}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "5e27b3cad3e223744b46502a29164112"}, "_text": "Está presente em todos os rolos apenas durante o modo RODADAS GRÁTIS e permanece no ecrã até ao final da sequência de quedas.", "fontSize": 25, "anchorX": 0, "anchorY": 0, "width": 925, "height": 100, "overflow": 0}, "fileID": 19709}], "fileID": 19819}, {"name": "Paytable/Pages/Page4/RulesTop/WhenWinningOnMultiplePaylines", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 19669, "guid": "9a734e621c318984ca31bc524efef04a"}, "children": [], "psr": "d"}, "fileID": 19601}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "5e27b3cad3e223744b46502a29164112"}, "_text": "Ao ganhar com vários símbolos, todos os ganhos são adicionados ao ganho total.", "fontSize": 25, "anchorY": 0, "width": 1400, "height": 50, "overflow": 0}, "fileID": 19710}], "fileID": 19820}, {"name": "Paytable/Pages/Page4/RulesBottom/SpaceAndEnter", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 19669, "guid": "9a734e621c318984ca31bc524efef04a"}, "children": [], "psr": "d"}, "fileID": 19602}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "5e27b3cad3e223744b46502a29164112"}, "_text": "Os botões ESPAÇO e ENTER do teclado podem ser usados para iniciar e parar de rodar.", "fontSize": 25, "anchorY": 1, "width": 1400, "height": 50, "overflow": 0}, "fileID": 19711}], "fileID": 19821}, {"name": "Paytable/Pages/Page2/FSHolder/RuleHolder5/Rule5", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 19669, "guid": "9a734e621c318984ca31bc524efef04a"}, "children": [], "psr": "d"}, "fileID": 19603}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "5e27b3cad3e223744b46502a29164112"}, "_text": "Durante o modo são utilizados rolos especiais para o jogo.", "fontSize": 25, "anchorY": 0, "width": 896, "height": 100, "overflow": 0}, "fileID": 19712}], "fileID": 19822}, {"name": "Paytable/Pages/Page3/BuyFSCAT/TitleHolder/Title", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 19669, "guid": "9a734e621c318984ca31bc524efef04a"}, "children": [], "psr": "d"}, "fileID": 19604}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "5e27b3cad3e223744b46502a29164112"}, "_text": "COMPRAR RODADAS GRÁTIS", "fontSize": 35, "anchorY": 1, "width": 1400, "height": 70, "overflow": 0}, "fileID": 19713}], "fileID": 19823}, {"name": "Paytable/Pages/Page4/RTP/TheoreticalRTP/Label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 19669, "guid": "9a734e621c318984ca31bc524efef04a"}, "children": [], "psr": "d"}, "fileID": 19605}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "5e27b3cad3e223744b46502a29164112"}, "_text": "O RTP teórico deste jogo <PERSON> {0}%", "fontSize": 25, "anchorY": 0, "width": 1400, "height": 50, "overflow": 0}, "fileID": 19714}], "fileID": 19824}, {"name": "Game/Reels/FSWindows/FSStart/Content/Texts/Continue/continueLabel", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 19669, "guid": "9a734e621c318984ca31bc524efef04a"}, "children": [], "psr": "d"}, "fileID": 19606}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "13dfbbd6cf8faee40b022516aeb5968c"}, "_text": "TOQUE EM QUALQUER LADO PARA CONTINUAR", "fontSize": 45, "width": 1100, "height": 45, "overflow": 0}, "fileID": 19715}], "fileID": 19825}, {"name": "Game/Reels/FSWindows/FSResult/Content/Texts/FreeSpins/freeSpinsText", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 19669, "guid": "9a734e621c318984ca31bc524efef04a"}, "children": [], "psr": "d"}, "fileID": 19607}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "13dfbbd6cf8faee40b022516aeb5968c"}, "_text": "RODADAS GRÁTIS", "fontSize": 75, "anchorX": 0, "width": 726, "height": 76}, "fileID": 19716}], "fileID": 19826}, {"name": "Game/Reels/FSWindows/FSResult/Content/Texts/Title/gratsLabel", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 19669, "guid": "9a734e621c318984ca31bc524efef04a"}, "children": [], "psr": "d"}, "fileID": 19608}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "13dfbbd6cf8faee40b022516aeb5968c"}, "_text": "PARABÉNS!", "fontSize": 90, "width": 1100, "height": 90, "overflow": 0}, "fileID": 19717}], "fileID": 19827}, {"name": "Game/Reels/FSWindows/FSResult/Content/Texts/FreeSpins/inText", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 19669, "guid": "9a734e621c318984ca31bc524efef04a"}, "children": [], "psr": "d"}, "fileID": 19609}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "13dfbbd6cf8faee40b022516aeb5968c"}, "_text": "EM", "fontSize": 75, "anchorX": 0, "width": 126, "height": 76}, "fileID": 19718}], "fileID": 19828}, {"name": "Game/Reels/FSWindows/FSStart/Content/Texts/YouWon/youWonLabel", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 19669, "guid": "9a734e621c318984ca31bc524efef04a"}, "children": [], "psr": "d"}, "fileID": 19610}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "13dfbbd6cf8faee40b022516aeb5968c"}, "_text": "GANHOU", "fontSize": 75, "width": 1100, "height": 75, "overflow": 0}, "fileID": 19719}], "fileID": 19829}, {"name": "Game/Reels/FSWindows/FSStart/Content/Texts/FSText/fsTextLabel", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 19669, "guid": "9a734e621c318984ca31bc524efef04a"}, "children": [], "psr": "d"}, "fileID": 19611}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "13dfbbd6cf8faee40b022516aeb5968c"}, "_text": "RODADAS GRÁTIS", "fontSize": 75, "width": 1100, "height": 75, "overflow": 0}, "fileID": 19720}], "fileID": 19830}, {"name": "Game/Reels/FSWindows/FSStart/Content/Texts/Title/gratsLabel", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 19669, "guid": "9a734e621c318984ca31bc524efef04a"}, "children": [], "psr": "d"}, "fileID": 19612}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "13dfbbd6cf8faee40b022516aeb5968c"}, "_text": "PARABÉNS!", "fontSize": 90, "width": 1100, "height": 90, "overflow": 0}, "fileID": 19721}], "fileID": 19831}, {"name": "Game/Reels/FSWindows/FSResult/Content/Texts/YouWon/youWonLabel", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 19669, "guid": "9a734e621c318984ca31bc524efef04a"}, "children": [], "psr": "d"}, "fileID": 19613}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "13dfbbd6cf8faee40b022516aeb5968c"}, "_text": "GANHOU", "fontSize": 75, "width": 1100, "height": 75, "overflow": 0}, "fileID": 19722}], "fileID": 19832}, {"name": "Game/Reels/FSWindows/FSExtra/Content/Texts/FSText/extraFSText", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 19669, "guid": "9a734e621c318984ca31bc524efef04a"}, "children": [], "psr": "d"}, "fileID": 19614}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "13dfbbd6cf8faee40b022516aeb5968c"}, "_text": "RODADAS GRÁTIS", "fontSize": 60, "width": 499, "height": 60, "overflow": 0}, "fileID": 19723}], "fileID": 19833}, {"name": "Game/Reels/FSWindows/FSResult/Content/Texts/Continue/continueLabel", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 19669, "guid": "9a734e621c318984ca31bc524efef04a"}, "children": [], "psr": "d"}, "fileID": 19615}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "13dfbbd6cf8faee40b022516aeb5968c"}, "_text": "TOQUE EM QUALQUER LADO PARA CONTINUAR", "fontSize": 45, "width": 1100, "height": 45, "overflow": 0}, "fileID": 19724}], "fileID": 19834}, {"name": "Game/SWBO_BuyFeatureV2/FeaturePurchase/FSPurchaseWindow_opt1/content/Texts/Text04/lbl04", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 19669, "guid": "9a734e621c318984ca31bc524efef04a"}, "children": [], "psr": "d"}, "fileID": 19616}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "13dfbbd6cf8faee40b022516aeb5968c"}, "_text": "NO MÍNIMO A", "fontSize": 60, "anchorX": 0, "width": 442, "height": 60}, "fileID": 19725}], "fileID": 19835}, {"name": "Game/SWBO_BuyFeatureV2/FeaturePurchase/FSPurchaseWindow/content/Texts/MoneyToPay/costTextLabel", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 19669, "guid": "9a734e621c318984ca31bc524efef04a"}, "children": [], "psr": "d"}, "fileID": 19617}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "13dfbbd6cf8faee40b022516aeb5968c"}, "_text": "COM O CUSTO DE ", "fontSize": 60, "anchorX": 0, "width": 602, "height": 60}, "fileID": 19726}], "fileID": 19836}, {"name": "Game/SWBO_BuyFeatureV2/FeaturePurchase/FSPurchaseButton/content/Visual_FSButton/Texts/BuyText/buy_text1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 19669, "guid": "9a734e621c318984ca31bc524efef04a"}, "children": [], "psr": "d"}, "fileID": 19618}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "13dfbbd6cf8faee40b022516aeb5968c"}, "_text": "RODADAS GRÁTIS", "fontSize": 24, "width": 190, "height": 25, "overflow": 0}, "fileID": 19727}], "fileID": 19837}, {"name": "Game/SWBO_BuyFeatureV2/FeaturePurchase/FSPurchaseWindow_opt1/content/Texts/ButtonsVisual/ButtonYes/Label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 19669, "guid": "9a734e621c318984ca31bc524efef04a"}, "children": [], "psr": "d"}, "fileID": 19619}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "13dfbbd6cf8faee40b022516aeb5968c"}, "_text": "SIM", "fontSize": 69, "width": 195, "height": 80, "overflow": 0}, "fileID": 19728}], "fileID": 19838}, {"name": "Game/SWBO_BuyFeatureV2/FeaturePurchase/FSPurchaseWindow/content/Texts/FSToBuy/fsTextLabel", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 19669, "guid": "9a734e621c318984ca31bc524efef04a"}, "children": [], "psr": "d"}, "fileID": 19620}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "13dfbbd6cf8faee40b022516aeb5968c"}, "_text": "RODADAS GRÁTIS", "fontSize": 60, "anchorX": 0, "width": 584, "height": 60}, "fileID": 19729}], "fileID": 19839}, {"name": "Game/SWBO_BuyFeatureV2/FeaturePurchase/FSPurchaseWindow/content/Texts/AIO_Window/LabelHolder/LocalizedLabel", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 19669, "guid": "9a734e621c318984ca31bc524efef04a"}, "children": [], "psr": "d"}, "fileID": 19621}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "13dfbbd6cf8faee40b022516aeb5968c"}, "_text": "TEM A CERTEZA QUE \nQUER COMPRAR (0) \nRODADAS GRÁTIS \nAO CUSTO DE {0}?", "fontSize": 60, "width": 860, "height": 400, "overflow": 0}, "fileID": 19730}], "fileID": 19840}, {"name": "Game/SWBO_BuyFeatureV2/FeaturePurchase/FSPurchaseWindow/content/Texts/ButtonsVisual/ButtonYes/Label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 19669, "guid": "9a734e621c318984ca31bc524efef04a"}, "children": [], "psr": "d"}, "fileID": 19622}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "13dfbbd6cf8faee40b022516aeb5968c"}, "_text": "SIM", "fontSize": 69, "width": 195, "height": 80, "overflow": 0}, "fileID": 19731}], "fileID": 19841}, {"name": "Game/SWBO_BuyFeatureV2/FeaturePurchase/FSPurchaseButton_opt1/content/Visual_FSButton/Texts/BuyText/buy_text1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 19669, "guid": "9a734e621c318984ca31bc524efef04a"}, "children": [], "psr": "d"}, "fileID": 19623}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "13dfbbd6cf8faee40b022516aeb5968c"}, "_text": "RODADAS GRÁTIS", "fontSize": 24, "width": 190, "height": 25, "overflow": 0}, "fileID": 19732}], "fileID": 19842}, {"name": "Game/SWBO_BuyFeatureV2/FeaturePurchase/FSPurchaseWindow_opt1/content/Texts/Text01/lbl01", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 19669, "guid": "9a734e621c318984ca31bc524efef04a"}, "children": [], "psr": "d"}, "fileID": 19624}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "13dfbbd6cf8faee40b022516aeb5968c"}, "_text": "COMPRAR", "fontSize": 65, "width": 860, "height": 65, "overflow": 0}, "fileID": 19733}], "fileID": 19843}, {"name": "Game/SWBO_BuyFeatureV2/FeaturePurchase/FSPurchaseButton/content/Visual_FSButton/Texts/BuyText/buy_text0", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 19669, "guid": "9a734e621c318984ca31bc524efef04a"}, "children": [], "psr": "d"}, "fileID": 19625}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "13dfbbd6cf8faee40b022516aeb5968c"}, "_text": "COMPRAR", "fontSize": 24, "width": 190, "height": 25, "overflow": 0}, "fileID": 19734}], "fileID": 19844}, {"name": "Game/SWBO_BuyFeatureV2/FeaturePurchase/FSPurchaseWindow_opt1/content/Texts/ButtonsVisual/ButtonNo/Label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 19669, "guid": "9a734e621c318984ca31bc524efef04a"}, "children": [], "psr": "d"}, "fileID": 19626}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "13dfbbd6cf8faee40b022516aeb5968c"}, "_text": "NÃO", "fontSize": 69, "width": 195, "height": 80, "overflow": 0}, "fileID": 19735}], "fileID": 19845}, {"name": "Game/SWBO_BuyFeatureV2/FeaturePurchase/FSPurchaseWindow_opt1/content/Texts/Text02/fsTextLabel", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 19669, "guid": "9a734e621c318984ca31bc524efef04a"}, "children": [], "psr": "d"}, "fileID": 19627}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "13dfbbd6cf8faee40b022516aeb5968c"}, "_text": "RODADAS GRÁTIS", "fontSize": 80, "anchorX": 0, "width": 780, "height": 80}, "fileID": 19736}], "fileID": 19846}, {"name": "Game/SWBO_BuyFeatureV2/FeaturePurchase/FSPurchaseWindow_opt1/content/Texts/Text03/lbl03", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 19669, "guid": "9a734e621c318984ca31bc524efef04a"}, "children": [], "psr": "d"}, "fileID": 19628}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "13dfbbd6cf8faee40b022516aeb5968c"}, "_text": "COM TODAS AS BOMBAS MULTIPLICADORAS", "fontSize": 60, "width": 860, "height": 60, "overflow": 0}, "fileID": 19737}], "fileID": 19847}, {"name": "Game/SWBO_BuyFeatureV2/FeaturePurchase/FSPurchaseWindow/content/Texts/AreYouSure/youSureLbl", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 19669, "guid": "9a734e621c318984ca31bc524efef04a"}, "children": [], "psr": "d"}, "fileID": 19629}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "13dfbbd6cf8faee40b022516aeb5968c"}, "_text": "TEM A CERTEZA QUE DESEJA COMPRAR", "fontSize": 60, "width": 860, "height": 120, "overflow": 0}, "fileID": 19738}], "fileID": 19848}, {"name": "Game/SWBO_BuyFeatureV2/FeaturePurchase/FSPurchaseButton_opt1/content/Visual_FSButton/Texts/BuyText/buy_text0", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 19669, "guid": "9a734e621c318984ca31bc524efef04a"}, "children": [], "psr": "d"}, "fileID": 19630}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "13dfbbd6cf8faee40b022516aeb5968c"}, "_text": "COMPRAR", "fontSize": 24, "width": 190, "height": 25, "overflow": 0}, "fileID": 19739}], "fileID": 19849}, {"name": "Game/SWBO_BuyFeatureV2/FeaturePurchase/FSPurchaseWindow/content/Texts/ButtonsVisual/ButtonNo/Label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 19669, "guid": "9a734e621c318984ca31bc524efef04a"}, "children": [], "psr": "d"}, "fileID": 19631}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "13dfbbd6cf8faee40b022516aeb5968c"}, "_text": "NÃO", "fontSize": 69, "width": 195, "height": 80, "overflow": 0}, "fileID": 19740}], "fileID": 19850}, {"name": "Game/SWBO_AnteBet_v2/AnteBet/BetLevelButtons/Visual_AnteBet/AnimatedPivot/Level0Visual/Landscape/TextsLvl0/betLevel_prefix", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 19669, "guid": "9a734e621c318984ca31bc524efef04a"}, "children": [], "psr": "d"}, "fileID": 19632}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "13dfbbd6cf8faee40b022516aeb5968c"}, "_text": "APOSTA", "fontSize": 50, "width": 195, "height": 80, "overflow": 0}, "fileID": 19741}], "fileID": 19851}, {"name": "Game/SWBO_AnteBet_v2/AnteBet/BetLevelButtons/Visual_AnteBet/AnimatedPivot/Level1Visual/Landscape/TextsLvl1/betLevel_prefix", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 19669, "guid": "9a734e621c318984ca31bc524efef04a"}, "children": [], "psr": "d"}, "fileID": 19633}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "13dfbbd6cf8faee40b022516aeb5968c"}, "_text": "APOSTA", "fontSize": 50, "width": 195, "height": 80, "overflow": 0}, "fileID": 19742}], "fileID": 19852}, {"name": "Game/SWBO_AnteBet_v2/AnteBet/BetLevelButtons/Visual_AnteBet/AnimatedPivot/Slider/Texts/text_off", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 19669, "guid": "9a734e621c318984ca31bc524efef04a"}, "children": [], "psr": "d"}, "fileID": 19634}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "13dfbbd6cf8faee40b022516aeb5968c"}, "_text": "DESLIGADO", "fontSize": 32, "width": 80, "height": 36, "overflow": 0}, "fileID": 19743}], "fileID": 19853}, {"name": "Game/SWBO_AnteBet_v2/AnteBet/BetLevelButtons/Visual_AnteBet/AnimatedPivot/Level1Visual/Landscape/TextsLvl1/betLevel1Feature_lbl1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 19669, "guid": "9a734e621c318984ca31bc524efef04a"}, "children": [], "psr": "d"}, "fileID": 19635}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "13dfbbd6cf8faee40b022516aeb5968c"}, "_text": "RECURSO OPORTUNIDADE \nDE GANHAR ", "fontSize": 20, "width": 200, "height": 40, "overflow": 0}, "fileID": 19744}], "fileID": 19854}, {"name": "Game/SWBO_AnteBet_v2/AnteBet/BetLevelButtons/Visual_AnteBet/AnimatedPivot/Level1Visual/Landscape/TextsLvl1/betLevel1Feature_lbl0", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 19669, "guid": "9a734e621c318984ca31bc524efef04a"}, "children": [], "psr": "d"}, "fileID": 19636}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "13dfbbd6cf8faee40b022516aeb5968c"}, "_text": "DUPLICAR", "fontSize": 32, "width": 200, "height": 32, "overflow": 0}, "fileID": 19745}], "fileID": 19855}, {"name": "Game/SWBO_AnteBet_v2/AnteBet/BetLevelButtons/Visual_AnteBet/AnimatedPivot/Level0Visual/Landscape/TextsLvl0/LocalizedLabel", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 19669, "guid": "9a734e621c318984ca31bc524efef04a"}, "children": [], "psr": "d"}, "fileID": 19637}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "13dfbbd6cf8faee40b022516aeb5968c"}, "_text": "RECURSO DUPLA\nOPORTUNIDADE DE GANHAR ", "fontSize": 19, "width": 180, "height": 80, "overflow": 0}, "fileID": 19746}], "fileID": 19856}, {"name": "Game/SWBO_AnteBet_v2/AnteBet/BetLevelButtons/Visual_AnteBet/AnimatedPivot/Level0Visual/Landscape/TextsLvl0/betLevel1Feature_lbl0", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 19669, "guid": "9a734e621c318984ca31bc524efef04a"}, "children": [], "psr": "d"}, "fileID": 19638}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "13dfbbd6cf8faee40b022516aeb5968c"}, "_text": "DUPLICAR", "fontSize": 32, "width": 200, "height": 32, "overflow": 0}, "fileID": 19747}], "fileID": 19857}, {"name": "Game/SWBO_AnteBet_v2/AnteBet/BetLevelButtons/Visual_AnteBet/AnimatedPivot/Level0Visual/Landscape/TextsLvl0/betLevel1Feature_lbl1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 19669, "guid": "9a734e621c318984ca31bc524efef04a"}, "children": [], "psr": "d"}, "fileID": 19639}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "13dfbbd6cf8faee40b022516aeb5968c"}, "_text": "RECURSO OPORTUNIDADE \nDE GANHAR ", "fontSize": 20, "width": 200, "height": 40, "overflow": 0}, "fileID": 19748}], "fileID": 19858}, {"name": "Game/SWBO_AnteBet_v2/AnteBet/BetLevelButtons/Visual_AnteBet/AnimatedPivot/BetLevelText_portrait/Texts_portrait/betLevel_prefix", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 19669, "guid": "9a734e621c318984ca31bc524efef04a"}, "children": [], "psr": "d"}, "fileID": 19640}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "13dfbbd6cf8faee40b022516aeb5968c"}, "_text": "APOSTA", "fontSize": 20, "width": 90, "height": 20}, "fileID": 19749}], "fileID": 19859}, {"name": "Game/SWBO_AnteBet_v2/AnteBet/BetLevelButtons/Visual_AnteBet/AnimatedPivot/BetLevelText_portrait/Texts_portrait/betLevel1Feature_lbl0", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 19669, "guid": "9a734e621c318984ca31bc524efef04a"}, "children": [], "psr": "d"}, "fileID": 19641}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "13dfbbd6cf8faee40b022516aeb5968c"}, "_text": "DUPLA OPORTUNIDADE DE GANHAR RECURSO", "fontSize": 18, "width": 200, "height": 41, "overflow": 0}, "fileID": 19750}], "fileID": 19860}, {"name": "Game/SWBO_AnteBet_v2/AnteBet/BetLevelButtons/Visual_AnteBet/AnimatedPivot/BetLevelText_portrait/Texts_portrait/LocalizedLabel", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 19669, "guid": "9a734e621c318984ca31bc524efef04a"}, "children": [], "psr": "d"}, "fileID": 19642}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "13dfbbd6cf8faee40b022516aeb5968c"}, "_text": "RECURSO DUPLA\nOPORTUNIDADE DE GANHAR ", "fontSize": 19, "width": 180, "height": 80, "overflow": 0}, "fileID": 19751}], "fileID": 19861}, {"name": "Game/SWBO_AnteBet_v2/AnteBet/BetLevelButtons/Visual_AnteBet/AnimatedPivot/Level1Visual/Landscape/TextsLvl1/LocalizedLabel", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 19669, "guid": "9a734e621c318984ca31bc524efef04a"}, "children": [], "psr": "d"}, "fileID": 19643}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "13dfbbd6cf8faee40b022516aeb5968c"}, "_text": "RECURSO DUPLA\nOPORTUNIDADE DE GANHAR ", "fontSize": 19, "width": 180, "height": 80, "overflow": 0}, "fileID": 19752}], "fileID": 19862}, {"name": "Game/SWBO_AnteBet_v2/AnteBet/BetLevelButtons/Visual_AnteBet/AnimatedPivot/Slider/Texts_portrait/text_on", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 19669, "guid": "9a734e621c318984ca31bc524efef04a"}, "children": [], "psr": "d"}, "fileID": 19644}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "13dfbbd6cf8faee40b022516aeb5968c"}, "_text": "LIGADO", "fontSize": 21, "width": 80, "height": 22, "overflow": 0}, "fileID": 19753}], "fileID": 19863}, {"name": "Game/SWBO_AnteBet_v2/AnteBet/BetLevelButtons/Visual_AnteBet/AnimatedPivot/Slider/Texts_portrait/text_off", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 19669, "guid": "9a734e621c318984ca31bc524efef04a"}, "children": [], "psr": "d"}, "fileID": 19645}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "13dfbbd6cf8faee40b022516aeb5968c"}, "_text": "DESLIGADO", "fontSize": 21, "width": 80, "height": 22, "overflow": 0}, "fileID": 19754}], "fileID": 19864}, {"name": "Game/SWBO_AnteBet_v2/AnteBet/BetLevelButtons/Visual_AnteBet/AnimatedPivot/Slider/Texts/text_on", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 19669, "guid": "9a734e621c318984ca31bc524efef04a"}, "children": [], "psr": "d"}, "fileID": 19646}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "13dfbbd6cf8faee40b022516aeb5968c"}, "_text": "LIGADO", "fontSize": 32, "width": 80, "height": 36, "overflow": 0}, "fileID": 19755}], "fileID": 19865}, {"name": "Game/RSMultiplierVisual/TotalWinMultiplied/Labels/TitleText/TextLabel", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 19669, "guid": "9a734e621c318984ca31bc524efef04a"}, "children": [], "psr": "d"}, "fileID": 19647}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "13dfbbd6cf8faee40b022516aeb5968c"}, "_text": "GANHO DE QUEDA", "fontSize": 40, "width": 398, "height": 40}, "fileID": 19756}], "fileID": 19866}, {"name": "Game/Reels/TitlesHolder/TitleMessageControllerPortrait/Normal/TitleMessage1/LabelHolder/Bet", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 19669, "guid": "9a734e621c318984ca31bc524efef04a"}, "children": [], "psr": "d"}, "fileID": 19648}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "13dfbbd6cf8faee40b022516aeb5968c"}, "_text": "APOSTA", "fontSize": 80, "anchorX": 0, "width": 356, "height": 80}, "fileID": 19757}], "fileID": 19867}, {"name": "Game/Reels/TitlesHolder/TitleMessageControllerLandscape/FreeSpins/TitleMessage1/LabelHolder/FinalTumbleWin", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 19669, "guid": "9a734e621c318984ca31bc524efef04a"}, "children": [], "psr": "d"}, "fileID": 19649}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "13dfbbd6cf8faee40b022516aeb5968c"}, "_text": "O       MULTIPLICA O GANHO DE QUEDA FINAL", "fontSize": 50, "width": 1100, "height": 50, "overflow": 0}, "fileID": 19758}], "fileID": 19868}, {"name": "Game/Reels/TitlesHolder/TitleMessageControllerPortrait/FreeSpins/TitleMessage2/LabelHolder/Multiplier", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 19669, "guid": "9a734e621c318984ca31bc524efef04a"}, "children": [], "psr": "d"}, "fileID": 19650}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "13dfbbd6cf8faee40b022516aeb5968c"}, "_text": "MULTIPLICADOR", "fontSize": 80, "anchorX": 0, "width": 736, "height": 80}, "fileID": 19759}], "fileID": 19869}, {"name": "Game/Reels/TitlesHolder/TitleMessageControllerLandscape/FreeSpins/TitleMessage2/LabelHolder/WithUpTo", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 19669, "guid": "9a734e621c318984ca31bc524efef04a"}, "children": [], "psr": "d"}, "fileID": 19651}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "13dfbbd6cf8faee40b022516aeb5968c"}, "_text": "       COM ATÉ", "fontSize": 50, "anchorX": 0, "width": 368, "height": 50}, "fileID": 19760}], "fileID": 19870}, {"name": "Game/Reels/TitlesHolder/TitleMessageControllerPortrait/Normal/TitleMessage2/LabelHolder/SymbolsPay", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 19669, "guid": "9a734e621c318984ca31bc524efef04a"}, "children": [], "psr": "d"}, "fileID": 19652}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "13dfbbd6cf8faee40b022516aeb5968c"}, "_text": "OS SÍMBOLOS PAGAM EM QUALQUER POSIÇÃO NO ECRÃ", "fontSize": 80, "width": 1100, "height": 50, "overflow": 0}, "fileID": 19761}], "fileID": 19871}, {"name": "Game/Reels/TitlesHolder/TitleMessageControllerPortrait/Normal/TitleMessage1/LabelHolder/WinUpTo", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 19669, "guid": "9a734e621c318984ca31bc524efef04a"}, "children": [], "psr": "d"}, "fileID": 19653}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "13dfbbd6cf8faee40b022516aeb5968c"}, "_text": "GANHE ATÉ", "fontSize": 80, "anchorX": 0, "width": 512, "height": 80}, "fileID": 19762}], "fileID": 19872}, {"name": "Game/Reels/TitlesHolder/TitleMessageControllerLandscape/FreeSpins/TitleMessage2/LabelHolder/Multiplier", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 19669, "guid": "9a734e621c318984ca31bc524efef04a"}, "children": [], "psr": "d"}, "fileID": 19654}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "13dfbbd6cf8faee40b022516aeb5968c"}, "_text": "MULTIPLICADOR", "fontSize": 50, "anchorX": 0, "width": 460, "height": 50}, "fileID": 19763}], "fileID": 19873}, {"name": "Game/Reels/TitlesHolder/TitleMessageControllerPortrait/FreeSpins/TitleMessage2/LabelHolder/WithUpTo", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 19669, "guid": "9a734e621c318984ca31bc524efef04a"}, "children": [], "psr": "d"}, "fileID": 19655}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "13dfbbd6cf8faee40b022516aeb5968c"}, "_text": "       COM ATÉ", "fontSize": 80, "anchorX": 0, "width": 590, "height": 80}, "fileID": 19764}], "fileID": 19874}, {"name": "Game/Reels/TitlesHolder/TitleMessageControllerLandscape/Normal/TitleMessage1/LabelHolder/WinUpTo", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 19669, "guid": "9a734e621c318984ca31bc524efef04a"}, "children": [], "psr": "d"}, "fileID": 19656}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "13dfbbd6cf8faee40b022516aeb5968c"}, "_text": "GANHE ATÉ", "fontSize": 50, "anchorX": 0, "width": 320, "height": 50}, "fileID": 19765}], "fileID": 19875}, {"name": "Game/Reels/TitlesHolder/TitleMessageControllerLandscape/Normal/TitleMessage2/LabelHolder/SymbolsPay", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 19669, "guid": "9a734e621c318984ca31bc524efef04a"}, "children": [], "psr": "d"}, "fileID": 19657}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "13dfbbd6cf8faee40b022516aeb5968c"}, "_text": "OS SÍMBOLOS PAGAM EM QUALQUER POSIÇÃO NO ECRÃ", "fontSize": 50, "width": 1100, "height": 50, "overflow": 0}, "fileID": 19766}], "fileID": 19876}, {"name": "Game/Reels/TitlesHolder/TitleMessageControllerLandscape/Normal/TitleMessage3/LabelHolder/3xWins", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 19669, "guid": "9a734e621c318984ca31bc524efef04a"}, "children": [], "psr": "d"}, "fileID": 19658}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "13dfbbd6cf8faee40b022516aeb5968c"}, "_text": "4X     GANHA RODADAS GRÁTIS", "fontSize": 50, "width": 1100, "height": 50, "overflow": 0}, "fileID": 19767}], "fileID": 19877}, {"name": "Game/Reels/TitlesHolder/TitleMessageControllerLandscape/Normal/TitleMessage1/LabelHolder/Bet", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 19669, "guid": "9a734e621c318984ca31bc524efef04a"}, "children": [], "psr": "d"}, "fileID": 19659}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "13dfbbd6cf8faee40b022516aeb5968c"}, "_text": "APOSTA", "fontSize": 50, "anchorX": 0, "width": 222, "height": 50}, "fileID": 19768}], "fileID": 19878}, {"name": "Game/Reels/TitlesHolder/TitleMessageControllerPortrait/Normal/TitleMessage3/LabelHolder/3xWins", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 19669, "guid": "9a734e621c318984ca31bc524efef04a"}, "children": [], "psr": "d"}, "fileID": 19660}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "13dfbbd6cf8faee40b022516aeb5968c"}, "_text": "4X        GANHA RODADAS GRÁTIS", "fontSize": 80, "width": 1100, "height": 50, "overflow": 0}, "fileID": 19769}], "fileID": 19879}, {"name": "Game/Reels/TitlesHolder/TitleMessageControllerPortrait/FreeSpins/TitleMessage1/LabelHolder/FinalTumbleWin", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 19669, "guid": "9a734e621c318984ca31bc524efef04a"}, "children": [], "psr": "d"}, "fileID": 19661}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "13dfbbd6cf8faee40b022516aeb5968c"}, "_text": "O       MULTIPLICA O GANHO DE QUEDA FINAL", "fontSize": 80, "width": 1100, "height": 50, "overflow": 0}, "fileID": 19770}], "fileID": 19880}, {"name": "Game/FreeSpinsLeftPanel/FSLeftContent/BuyText/fsLeftText", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 19669, "guid": "9a734e621c318984ca31bc524efef04a"}, "children": [], "psr": "d"}, "fileID": 19662}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "13dfbbd6cf8faee40b022516aeb5968c"}, "_text": "RODADAS GRÁTIS RESTANTES", "fontSize": 40, "anchorY": 1, "width": 205, "height": 120, "overflow": 0}, "fileID": 19771}], "fileID": 19881}, {"name": "Game/FreeSpinsLeftPanel/FSLeftContent/BuyText/fsLastText", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 19669, "guid": "9a734e621c318984ca31bc524efef04a"}, "children": [], "psr": "d"}, "fileID": 19663}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "13dfbbd6cf8faee40b022516aeb5968c"}, "_text": "ÚLTIMA RODADA GRÁTIS", "fontSize": 45, "width": 205, "height": 135, "overflow": 0}, "fileID": 19772}], "fileID": 19882}, {"name": "IntroScreen/content/ContentII/Content/Pages/Page1/LabelHolder/WinUpTo", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 19669, "guid": "9a734e621c318984ca31bc524efef04a"}, "children": [], "psr": "d"}, "fileID": 19664}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "13dfbbd6cf8faee40b022516aeb5968c"}, "_text": "GANHE ATÉ 25,000 X A APOSTA", "fontSize": 50, "anchorY": 0, "width": 790, "height": 120, "overflow": 0}, "fileID": 19773}], "fileID": 19883}, {"name": "IntroScreen/content/ContentII/Content/Pages/Page2/LabelHolder/Label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 19669, "guid": "9a734e621c318984ca31bc524efef04a"}, "children": [], "psr": "d"}, "fileID": 19665}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "13dfbbd6cf8faee40b022516aeb5968c"}, "_text": "OS SÍMBOLOS PAGAM EM QUALQUER POSIÇÃO NO ECRÃ", "fontSize": 50, "anchorY": 0, "width": 790, "height": 120, "overflow": 0}, "fileID": 19774}], "fileID": 19884}, {"name": "IntroScreen/content/ContentII/Content/Pages/Page3/LabelHolder/Label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 19669, "guid": "9a734e621c318984ca31bc524efef04a"}, "children": [], "psr": "d"}, "fileID": 19666}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "13dfbbd6cf8faee40b022516aeb5968c"}, "_text": "MULTIPLICADOR ALEATÓRIO DE ATÉ 1000X NAS RODADAS GRÁTIS", "fontSize": 50, "anchorY": 0, "width": 790, "height": 120, "overflow": 0}, "fileID": 19775}], "fileID": 19885}, {"name": "IntroScreen/content/ContentII/IntroButtons/VolatilityMeter/LabelHolder/VolatilityLabel", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 19669, "guid": "9a734e621c318984ca31bc524efef04a"}, "children": [], "psr": "d"}, "fileID": 19667}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "067523e87952abd4e939a88dc57f62cb"}, "_text": "VOLATILIDADE", "fontSize": 22, "anchorX": 0, "width": 156, "height": 22}, "fileID": 19776}], "fileID": 19886}, {"name": "IntroScreen/content/ContentII/IntroButtons/ButtonSkipIntro/content/TextHolder/Label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 19669, "guid": "9a734e621c318984ca31bc524efef04a"}, "children": [], "psr": "d"}, "fileID": 19668}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "067523e87952abd4e939a88dc57f62cb"}, "_text": "NÃO MOSTRAR NOVAMENTE", "fontSize": 35, "anchorX": 0, "width": 478, "height": 36}, "fileID": 19777}], "fileID": 19887}]}}, {"type": "Font", "id": "5e27b3cad3e223744b46502a29164112", "data": {"fontName": "f5e27b3cad3e223744b46502a291641", "path": "@font-face{font-family:'f5e27b3cad3e223744b46502a291641';src:url('data:application/x-font-woff;base64,d09GRgABAAAAAEsIABIAAAAAg0wAAgANAAAAAAAAAAAAAAAAAAAAAAAAAABGRlRNAABK7AAAABsAAAAco+LG1kdERUYAAESoAAAAVAAAAHoG9wrfR1BPUwAARwAAAAPqAAAJ/Find51HU1VCAABE/AAAAgIAAAPWt8EA609TLzIAAAIMAAAAVQAAAGBptv7vY21hcAAABJQAAAD9AAABoH/DAfNjdnQgAAANbAAAAKUAAAFCHNRDfmZwZ20AAAWUAAAG7gAADgxiLw2EZ2FzcAAARKAAAAAIAAAACAAAABBnbHlmAAAP7AAAL2sAAE/0RTuM02hlYWQAAAGUAAAANQAAADYmJUKaaGhlYQAAAcwAAAAgAAAAJAcVAuJobXR4AAACZAAAAi0AAAOsujQpaWxvY2EAAA4UAAAB2AAAAdiXpqwObWF4cAAAAewAAAAgAAAAIAWNAiluYW1lAAA/WAAAAewAAAPMOr3cYHBvc3QAAEFEAAADWgAAB4ng5JDZcHJlcAAADIQAAADmAAABEAFXCZx4nGNgZGBgYGIOuVQq+Tme3+YrgzzzC6AIwyPWvyeh9O3/jv8FmJcwrwVyORiYQKIAmMkOcwAAAHicY2BkYGDR/XebgYH55n/H/07MSxiAIijgNQCYwwb8AAEAAADrAE4ABQA+AAQAAgD0APwAjQAAAxQAngADAAF4nGNgYZJi/MLAysDA1MUUwcDA4A2hGeMYjBidgaLcbEwsrCxMTCwNDAzMDEjA3d/fneEAg4KCLIvuv9sMDCwpjN1AYUaQHJME0ykgpcDADAAIGgsFAAAAeJyVk89rE1EQx78zq5iojZvEQgK1xbTNFuImTawkaBDXFhuqLW3XgwEhtCi5FKEHxZ7Ugh48+OPixT9AVOpB8eTBm6BnRaR4UBSk6L/Q+H0vS/SoCx/mvZl58+PtPD0H+8ks2UFSmJKnCPQWMlpHzTmLQ/oFZfxCIDWMkUEdRZ62cUkiLycxSjkl1xGnzifzpEgKZD85RsrEi+y+9U9iyMQgEzbWa8SdddT0DqD30NI11PUzpU9c8pH7n2jJMnPmOi/0KnUZtJwHJEaStF+I5BpteVT0MPr0K5raBpwNxHQZjk4z/nHkZAazpmbKLPPn5HlnW8oYo83XFYSyxbpWbG0TchGuXV+h3sUZcTtvtGTXofMSodHrkvUPjZ88pu0bynINKdrmNMH8HvboEBK6DyKbGGbujHrYoszZezB9m579bo+2DmLrqEd1uIxl8izZWuZ4dq+ljSzpN/VjEzu59uQV/9U77tc7AfdxcpAMkAxJW/8263vL3i53cT51MX1FPl4v9p8Y/X/FSf2zH2frP+cjb6WZRzNjZjZv4q46WNX7GHYWeB8zWJVnuEQWtIVpbeKUpFGU86jYs+9xhHdcIKeJT0ZIjBwlFeKREilKCYukZGL0SDDGE9w2cSjnqRuXh5yTR6jqAL7LB+ZtoqG7sEhCMe8osO9hUn+whirvpYoDOsJe0ly7mJQbvB8jkxjkzO+O/mEf39cJ2SANzlcDWb67Qq/voCt/A82hhvwAAAB4nGNgYGBmgGAZBkYGEJgF5DGC+SwMdUBaAAhB8nUMBxnOMrxl+MrwS0H2/3+giAJQ5DjDQ4bPQBGZ////P/6/EGwCw/+SB8FQ03gYJBgMIKIMyUCcAmWnMqQByXQoLwOIMxmywOxshhwgmcvAwMjGADUESDMBCSYGVACUZGZhZWPn4OTi5uHl4xcQFBIWERUTl5CUkpaRlZNXUFRSVlFVU9fQ1NLW0dXTNzA0MjYxNTO3sLSytrG1s3dwdHJ2cXVz9/D08vbx9fMPCAwKDgkNC4+IjIqOiY2LT0gEORrkYohbM0BOzAbZDHZhHgM5IL8Ai2ASKECwAQDzbjMOAAAAeJytV2tbG8cVntUNjAEDQtjNuu4oY1GXHckkcRxiKw7ZZVEcJanAuN11brtIuE2TXpLe6DW9X5Q/c1a0T51v+Wl5z8xKAQfcp89TPui8M+edmXObMwsJLUncj8JYyu4jMb/Tpcq9BxHdcOlanDyUw/sRFRrpZ9NiWvT7at+t10nEJAK1NRKOCBK/SY4mmTxsUkGruqo3qajl4Ki4XBN+QNVAJomfFZYDP2sUAyoEe4eSZhVAkA6o1DscFQoFbEP1g8t1nh3N1xz/sgRU/qjqVKFTJHrRQTxacQrmwJKmoke1IOLzaCUIcoIrB5I+71Fp9cHomjMXhP2QKmFUp2Ij3n0rAtkdRpJ6PUxtgk0bjDbiWGaWDYuuYSofSVpn/TozP+9FEtEYppJmelGCGcm6GUY3Gd1M3CSOYxfRotmgT2I3ItFlch1jt0tXGF3ppo8WRJ8Zj8piP44HaUyOF8e5B7EcwB/lx00qawkLSo0UPk0FvYimlE/TykcGsCRpUsWEG5GQg2xq35esZHddaz7/wr6wT+W1OpSBHMohzsrWyw1EaCdKem66G0cqrseSNu9F0Lkcl9yUJk1pOhd4I1GwaZ7GUPkK5aL8lAr7D8npwxCaWmvSOS3Z2nm4VRL7knegzSRmSrJlrJ3Ro3PzIgj9tfqkcM7rk4U0a3dxPJgQwPVEhkOVclJNsIXLCSHpwsixlUitSrfsEXNnLKerWCXcL107vmheG4eO5mZFMcQprqrHayjiCzorFEIapFtNWtCgSkkXgtd4AwBkiBZ4tIvRgsnXIjZaMEGRiEEfJ9NikMhhImkRYWvSku7uRVlpsBVfpbkDddikqu7uRN17dtKtY75q5pd1JpaC+1G2tBSQk/q06PGVQ2n52QX+WcAPOSvIRbHRizIOH/z1h8gwjl1YqyssG2PX6nkJbjLPxPCkA/s7mD2ZrDNSmAlRVYhXQOLOyHEck62aFpkohHsRLSlfhjSP8ptTKDlfJv+5dMkRi6IqfN/nCCxD56TZ8rRHn3ru0wjXCnyseU26qDOH5SXEm+XXdFZk+ZTOSixdnZVZXtZZheXXdTbF8orOpll+Q2fnWHpajeNPlQSRVrJFzjt8W5qkjylXJsqPrLJ5TLk6UX5slVILuuCd6Sec+rd1lf087l8d/knY9TT8Y6ngH8ur8I9lA/6xXIV/LL8J/1heg38svwX/WK7BP5YtLdumYK9rHHspkWh6ThKYlOIStrhm1zVd9+g67uMzuAodeUY2VbqhuLE/keGy98+OU5zNV0KuOHpmLSs7tTBCU2QvnzsWnrM4N7R83lj+PHaznPCrZ+LanmoLz4uVfwn+27qjNrIbTo19vYl4wIHT7cdlSTea9IJuXWw3aeO/UVHYfdBfRIrESkO2ZIdbAkJ7dzjsqA56SISHD10XL9KG49SWEeFb6F0rdBG0Etppw9CyWeHT+cA7GLaUlO0h9rx9kiZbdj+qKH/MlpRwT9nciY5Ksizdo9Jq+anY5047g6atzAq1nVAlePy6Jtzt7KtUCpKBojIeVahLQeoCJ9zpHl+TwjT0f7WNHKOKg21+sWYCcwr2O+UQZXtqBU0EySij4Mpf2RU7shENNqKI37yTfnkWCqE9joXEbHk1j4VqI0wvTVQ0Y/TbqsOHchbvTELIzthIk9iLWrKNB52tzycl25WngioNjO4e/3axSTyt2vNsKS75l49ZEozTlfAHzuMuj1O8if7R4ihu08Ug6rl4U2U7bmXrzjLu7SsntLtu74TWP3Xtk1YEmm55TzpwS9NtbwjbuMbg1JlUJLRF61gRGpe5Pldt5FN8oPnWdS5QhevTws2z+2/rbAZvzXjJ/1jSnf9XFbNP3MfaCq3qWL3U49zODhrwLW8clVcxuu3VVR6X3JtJCO4iBDV77fE1ghtebdFN3PLXzpjvYjtnuUovAL+u6UWINziKIcItt/HwjqP1puaCpjcAv61HQmwD9AAcBjt65JiZXQAzc485HYA95jC4zxwG32EOg+/qI/TCACgCcgyK9ZFj5x4A2bm3mOcwept5Br3DPIPeZZ5B7/GZIUDCZzJI+UwG+3wmgz5zXgUYMIfBAXMYPGQOg+8Zu7aAvm/sYvS+sYvRD4xdjD4wdjH60NjF6IfGLkY/MnYx+jFi3J4k8CdmRJuAH1n4CuDHHHQz8jH6Kd7anPMzC5nzc8Nxcs4vsPilya6/NCOz4tBCXvErC5n+a+yTE35jIRN+ayETfgfuncl+vzcjQ//EQqb/wUKm/xErc8KfLGTCny1kwl/AfXmy31/NyND/ZiHT/24h0/+BlTnhnxYyYWghEz7Vo/Pmy5Yq7qhUKIb4pwltMPY9mj6g4tXe4fixbn4BWSMBXwAAeJxj8N7BcCIoYiMjY1/kBsadHAwcDMkFGxnYnRx45vXVMWTE+DAwGHAxaIGFxCZzNHMUc+QyJbIFcriy2bKYsqizc3FBJWezdbNVsxUwpLNEsnmzODJZMumxgiUFnRx5VBkUGWQZpBskGkQaBBv4G3gbuBgYuBi4gdLSQGkGBywQJL2TgZmBwWWjCmNHYMQGh44IED/FZaMGiL+DgwEiwOASKb1RHSS0i6OBgZHFoSM5BCYBAg5iU9la2UrZ8hmTWYLZ3FnsmcyZNFm5+LR2MP5v3cDSu5GJwWUzawobg4sLALE0PNIAAHicY2CgGsgDwlCGUKZTDAxM0gwM/wWYrv7/xqT6/9t/AQYJCGSSYdACYYZcIAxnCGeyB6r1+P8DwQeJgMTAogxw8VNAk34wSYPMApv6F2ouUI7Bi8GLMYPh5f8F/wXA9CwkcXlGeYQZQHM/ILnsA9QMdyA0YTBh1Ppfx5jM8BhsjvH/UiAbYhZUnuk04wKmR0xJQBP7mR4xTgeyMxglGfoBDyZMdgAAAAAAAAAAAAAAAAAALABUAKgBEgGiAjICTgJyApYCxgLuAw4DKgNKA2YDoAPIBAoEaASiBPYFVgV8BegGSgaABrQGygb2BwwHZAgOCEgIngjmCRYJQgloCbgJ4AoICjoKaAqGCsIK8gsuC2oLtAv6DFAMbgyaDMgNFg1CDWYNkg2yDc4N7g4WDjQOXg7YD0gPjBACEFQQphE2EW4RohHqEh4SNBKiEvITLBOiFBQUaBS+FQYVThV8FcwV9hY4FmQWpha8Fv4XRBdWF2IXdBeGF5IXnheqF7YXwhfOF9oX5hfyGCgYWBiaGPwZJBleGYwZwhn+GhoaVhqmGs4bNBtwG9AcMBw8HEgcVBxkHHQcihyaHKocuhzKHNoc6hz6HQodGh0qHTodSh1aHWodeh2KHZodqh26Hcodyh30Hg4eOh54Hpwezh8QHyIfdB+2H8wf4CAGICQgMCAwIE4gZCCUINwg6iEEIR4hPCFSIWYhciGIIZYhwCHYIgQiQiJoIpoi3CLuIzwjfiOkI8oj3CPwJAQkQiRUJGIkdCSIJJYkwiTQJPwlJiVIJWwlbCV4JZYl6CYCJh4mPiZUJmomeiagJsYnBCcsJ1InYCd0J4YnmCfiJ+4n7if6J/p4nM18B3xk1XnvOedOkTSaPqPpI83cqRqVkUajrtFIq65R79KqrdjVSqvtTbAFdjGYapMFTDHGOAGCbWzA4A4u2HFgg+HFBl6cOLZjw3PiGBI7LiRGo/edc+8djXYX2+8lv7yn3Wn3nvKd73zf/yvnnIsIatv8Jfo2uYg4pEWFSSdCCM8jjI/3IUK4RcRxp7l+H6/XyTT2SA3Pxbiq6lhlgdmk4L2BJ3ptZ94XcjnCYYcr9Cr+zcbhYFFRMOj1IoJa8At4lbWrRL6kh2DasJyTcdA26pfJEJIpZUqFHApweoXGEjFyPOeB1+OmHSYchjdyMZ3GhL4QtGeFt/3QngMVos5kW34eydHq1JwS5Sj3ahQEyTkCBfGyCufmHu+TYUJOkH6ECt0uJ9Rx2G1WC1Bt1Gf+NIURrIyZeSVfw17xGHvFlOxllsNP/GPnDXeFhkPnAoP+U7e4zt8RHAldHxwMnU7feM51Dn/qQt/L8Nd3oe9/wF/fhQsIBlO7+S2SS36LvCiMoqgxWRctDvM2q9mUI8vDpIgDGkkKyOMwEHwdArbIMVpFcjk3D8w+Ke8v1OsLzDqFxhmpKcPBuBtb4mU4XlVdE4+Z4YcSLprdBCZAaeb1Gmw2FViMCSgQCG7UL/O+/bG9s2tTLdXTIQ/fH0jMVO5daikvC1e1xKKR2jJs1O3SO/CXi0a6esYVoyPEpB/XGLkvmEpaqzqn89bI9TK/2xvUpv9BESws8ulAHJAclWz+hguRV5EKZMSCeBRB9/U+aRucTJaosUKDc+SKHBgBwrlyvJKPYW4XVVweh9CpPpSbSxaVwmQ4klWXF1dhjjveJ1TKw6wKFKb1lItIqTyt7J9KRvw+m1Wvy8/HKBz0RfyRQpeVt/Fmo86it+Rr87UatVKOVFil1RREjJJwAsvkmMdyr9kUq4xXybOuW8Rr3+wb6B3q6e8mwfSxKXxb+lxLSzu+PjXYO9Tb3z3Y0tJGXj2+unry5Orq8byNanIxefc999wNl1bW11dWj/fc/aEP3Y3onFds/ob8iLwGkhlBcXQmaQjYFRyWxTVKTo71mMi5lMAvXskGqhAGijFZ5DAbrlwufqVs8lxaah6JhSSFnEpaPEUYxSqKIp4I8EGrykOFuDAHxo/ZOJWWBK6JabDSwgc1WBh1DRMji1dBRYZJDMfEJ1ZZXYMrh5eGm56yFqnWtFXBC7f0lx3u2392oM0b3Tvmr/QW1lSUDZbFO+trhlzktaGe5LTvk0UDpYmjZR/oDZSvzKZmg4GhWM/YWNDvqfMU1ga8ofRfuZrKY01eG4g4YAF6nGGBLVnAEIZgBgOMe5yeE9X/8YzWU7nDKLS5C9uhnhoZkzpgBJqHy8dxf4GRaKwRIwwpJigAX4ZDnan5js7O9rlUJzZc85OZuR+fPPaj2ckf0nbc0I4u046EcEhsBwO7eEGDYm6MdV29c+0dne3zvV3YcPUPJ2d/dOzkj+dmfgLtBHAFTpN3kAbxySJ1viovNwcQTAaKrEbdQB46yYakQRpCZ0JZY1FalEFlsCZYYwniDtNz2qePzY70LBx5SvclM3nn2fhu7f2Vjz1Web92d/xZaD+KDpI8MgJ6FkjyoCFEPiHDCBCTAhuVERAEORlg8MUQAgN+mfk44FY8hju++c1h+I9vHnrxxaGLdNyBzWvRi+gYykHWpJlCDZ4QuYjRgJENvgZkhKey8qI6auvTOHNWV+LXVte/v4qW8aMfYheugzlyJx2IVp2QZg/hgazJo9jkf+eH09PCvDGbgs7DOER+L0Ld07jf52VdZlmQtqDLGQ47XcEGyXLQ+rnwti7JCxOVPrh+Ypu85IJu4/X0b3bjPHKRKifYiFLQQy3ooRrZmC3jONYzwxS0CJVPo36fz89TW4ZNol4kMFAjZ2oAIsB7FURbsTpx6nj56sQ16+mbGnu6E4nunka859jDk9/41NFHJr7+6dtuveX2W2+9ldE6CG/HoPd85Eha83LlHHSYQlsk23V0zB5eD9KqwVocrInhYyuEqJ2G/uqXJvHf+hMutU3b3PaQwLsysHMawFoHKk0Wa2Dq1RiAAaSASzHAgKHAeDiO6QK0H/SHmG0O43hNgggjUAbZoMwm2qHZQzTpoi9Ud/mSsYr+4rrIgdSe42WjdTfgmeEv1wz1llTX+rydvvjeibrFhZpzgMsoAnzMARqKUXOy0Y9lch+mhptwWqBAA3MvT8E8ELBgK0gmky+CaDKgknjs93l8/mImoNRMubFZYDa1ZgKbIzie4bdILf7lSOvYeMOphbpdJaXL5TODCwstrTPDzQ01Q3xV8HDPwaPE3z2ukeVNdCYWawzmBYu1PdFYO9FWE417rd3uCO2cIBO8hcm3UR58iyRDGizjAEhlJCXHMmAjlqFlRE0OkHsC99v0BlGV/FSRPHEc08eCMFU86AUXI+Gl5fRncffK0kUZluVY8p/76YMPPjiK/y4deb58xGkq1qXfBn41AL8UIANu1JZscVoIhwrywHvKBW5xKQrjYPW5FeCSbB74xVwrSSUwc01MBk0+mDA3diszEL6NX4xNRo/Zo0xgbF89/4HztYte33J57/TQhKvNXOHAB9Nv6fRV+MsH9hw6ZzIumu3t3S3teXIXnpr7MnhJjDfUTunJd4HOEpRINgB9CHwSsFAysFBwXy4j8hVpHk/1KcAMSRanqDDgKywpKvEFfX4liJuxKsHBpFnMQJqGKGOiwAHVZdgikkyx2Y0r9JHU8RFzlSEQbZ+usnKKgsHm2cNn9rUfbv1tXVdzcyTpK0rgaOLqfcPlMtlUfnHPrlpf9OjK4XNtt57sHWju8dW6wv0xqhtBGATFcQXyJ73gNlFNy/irnOTywW2FXi8DrYthT9xjxuewK30z/qf0G2RtLrVxHjEUoVhB9cyJQmCzq5OxGF+gpFAOgkLRQiZYXxFCYKbcLoxKI66QO6TTICd2KthMUdlWKKk/loETZmcTeJuhleClHINKu5p31fXXFHdGGta6jp0NtQarOup3NVYm2yvKW5LPtXW0dfY1aJtayKvBVF1y2GmOd0YbJ6K7Z6LdVQb3WEttb2ikorSypsBUV1laka6JJpsrK5I2hb0diRioFucYtDcPZDAXU59Tj+W4lxoVLCcrmandpreXTjJgJMfEEKhPEKoTgicRg2EH2fDgOoyvDFAKZvnEiLlaH4q2zVRZiaKgv3X24NnVjkN0lhPNJUlfYTO+r+nqfSPlMvlUfrhnV4234ujew+fbbl1P9Sdhlt2h/ipBToWYJAT2w8JwXKTw7LaYxOfzypggblkT/ZUsi/SJfxjyeEIBrzd9fsvWEFQINvIl1pcZOZM2imscWcp0iftN5ss7MjKrSWpirSGh9dA5dYW1P8+ok+/b6udJsKSBiTGe4Tq17Sqymwrn02AjKiOlOO43qzFRpf2AJ3fhtYN/XfvW1OP3Ak1V6DWiwP/MYqfyZAmESqILQDiOTFAmzMsBfLmBK4VRIPFyeFXh76TL8Hfw/UNDfzE8jC6jARzUykgNECE3y6N4DQj4u7T/hXsfn3qr9q8pX3I3f4kfAf1wgxWoTJZT1AdiEaE4QXl0nWTXmZLIFgHYTssgUnT4w3KNgxr5K5kjpYa4MK/MYuWt/gpfsb6uurzfH4jsSeza54z7PJ78CXPQTeNKd/AXNZ0hVzjkjJTZDIMW98JgpLnKbQ8E7Kb7suexdvMxrhewoRn1oVNJYwHOJZ05gMHlWI6iGLw0cMGd4IIHcsCeYggXEQAyUsgVu4FyFmscF6IVYO96LnjhPsoEjuSuvHcFGTjimt5uf9DvD4T8fl8ejSlNGkJ1hs92LyxKhSlLWwRoZI63mRZjgEGoVgl+SDMWynC9BZHW0ki1UmFsjw1OxJd2lE7Z87DH7Aqau68dOnjfwH13NB4tKznYsv6J6fqDH1la+bO5dE5FcXW5TKH2maINdry7bLIrUu7xlC+Pte6p1+b9lU7PVRRWDFdMn23d+dDeO+536FMG6+Qjx2YfPN4y/tCnm2qGkuucoqyL8pQy9nawpUqkRyXJcD61oCkEMgdWdC/w6XgfjbqpHUVIr4NiSnBMFRpbxBP0KHkcy8VlJEh9kBhxptI/Tx3C2kNYk/57TquR5ZgUXa+//DL59kYM/1pVEtUWduhfRMytITMwjzrw4bzUnioAr3AKYsgcTO3TMvN9qFGF+ZLJGPgDAXa7QY+R3Wv3uBzWAr3NYNOqkQ7rcrdCIiFq5j16o+iCuIhoXHH0ptt3JIrbQzcd20Q/GW1Ojey7fry9E8gY3NU8opIrA+21i/sgMnm9Oh4vf2Xy7bbaWDPoUyNg7RTz1WqSVVbw0Wy5RDCpXIrajy2EZWJ1moC5LzAxU+/ADjklTUTRbEEQBKYM4zv2rR86UjZWUjzRcOCOroF7D1QvR0L8EDl553XXXtBrZnXamUcOrj06azFP62x0vijvPga8y4X5orgJboaYy9nyFz1+j1fwfxURrKcyp6S80MeKyMf+8ukX/n7/Q5OHr19fnQBDtfbZzwMHPnzi/ddu/JzhBw0cfgHtq1A4GYC5ArdBtpzVi1wu9bIVooSxHtwW4R/+RfoAvpD+W2xK/xwHyMWdL838LLvdXBpX0XbR9nYzTeootMR4qcWL+6C179HWoK1/2slopHNyK8xJIWpI1moBJXUwLy7wcEBQZTLqL8hliEmR5EuDEIkm3uj3+3khsPLot+svVUgFdWfoXMV5fDMmdSdTDz8x8eiJhrWyoH+m8uRNlWsTkQbbPfi69ANG4zceP/TItMU0rbPede3QqY5cxfNI4iG5jfHQk3TDSME3xctylgWhObJtvIOowaPn9TF4j5Hb1tKfXFvDY2u4I/0lkMW/wSEW48FbA2uvKOmiESmNR3MuCULMLAihIYI+CLZBqbfghvQ/j41h8+howkYc9kTCvvEmyBDefHqzEf0l079Q0k/tDse8B4TpJ9pNtZ6KsTo/R0H1SwZCbJE8VUA3INakIPagRucz2Uoca/u/qVRMyxSmqnLypY3ew3szPKDxnYbSrMnhiAy8khR14I73YYEJZr2RhTUenlMyFiRwM45hsn7mZx1ra/Yq+9dPp/8Fd9enQW7Sf+5ucv8o/UJGRt+FtuViNpN5+mRhyzuUI7noHVLGmufXcAUEkHfOCnW5GqhrofG3WUUoF4XwE5jKSRGoBVn0Rr1FQanjeAAVSp8R/sEndf94Dn/obyBcILvu/7bsu/evgAsp+wGZN+/oD6T3Q1ffIE0b1fg+b29rwcafZvjxcejXgFxJO0jBpcyw6AtEZmAeHE7aH44BtIJUfPz69D0yoth1Hu9lGcWrSEtouTr9PujneXOyuwhfB7hAdeI1iIu1NHv6XnGxP5AVF0tAJFkkMTJ+reeuA4fv6uq+6/CBO3s2vrd6+Mje5SNHVvGeg4/s3PnowQMP7wREuu6uu6677sIFEY9iTDZN1Ieg0k5A2hWYwpFcgHA2Lep8jHSafJPaBAKsoKm8TBykwQBOvN4CY2Zk4OBt18VPr/atfW20ZWZ2kFw8vLtxbwsVg5n07xJdyWY23nE2XhMqojEgmG+ORgoY3QjKvhVCZ9IS/kCLiSILjVYtPHi2782ApaVP3nWTXCW/Iheu+5zvcx/DlzECSdh8kMm8hSIT2DM50CS448ss4ScaNaCWGjSdFiOjXmvRWYREE9JgTc4WU5RGnuMZX8wSX26+PpzkXzj9+Om1r49Nj6SANXv21vSWa9LfwJ70DwEh/605kaymbKjc/A3+d8DIMCpLRhwgNk4lmCzoHuxWFjBmBfQlfmY0/NvD0jIcLCMsHshGSIub4H+/Zb1+2Rdaq+3q8nTUxYfKrttbv1TqDY+VNiTcrdWNYyVfn18zGvYYXLXlntKAxVQ9WD95lcU0pS+oKvaU+gqs8V4BL9XANz/wTUnzK5Q3LBuaZRREzdBD9G7m8cDa68T6Otk/S/V5CuT+Zqirl+oez5Z4o9fIC4MSoJ5CFx0WaNXNiTMLN92+9unpoX3v9pxs/5OT+KV03VMLw/hbjCY6ocss5wMYnsPiUA6sVXaaKh/lCyiDOZ6LGYErgAxGsvzuod8dvE9tVSis6vuEvFU66Rvn+XE//iprOwcw6GFouwgFkz6nUUnzLCkKP1l4AAWLUJFVb9dTEKI9UD+LKLO7ynzhHn726FmlNp/gPK3y9NEvH1xRGeRyU/7JtVP5JrncqFqhdOAvWhvjKlVVwp7uAZqmI/P+wK4gfjQ9GVry+hYi+FGgDSCQzGdsTY78CrhtMBjECVFh3sjFHDjGkflTn/vC0bd+sP7Yx4/+ABenX8dH8QSgtz79GMrwsxPazaERmIKjaZoUwSJ2gy4YDHoOvEpsjHEg8lwM+5449va+t49T5U33pv8Z6/EXaTsQCRAH8yPAP1GCqNC8GUmhLaHZ8oMMeoOUNxOyBB7iSCvxgfRH8JvpC/hkHyma7dv4EaOvb3MXNRIgg0KECP+XMu4JyQihHDDKTLG54o03zp9/kNs7tFH/R+czjZjH+7Fqd/o3YjoT6tk2d6GNTL+s7FJmMDjTrwUsTxxetnPn3nijj3xr6N17Bb56sZLYySsQ7wWfUlzVlnTKaeKWIeENTGBPZGdLnlKgNpguI8yc99Xan0996n6sxC+ka/F0+lHW3mZ6czd+ZvNRoKOItXdZTlnP/AzakBx4ip9J9+DPfbhFyOWTOQjJ0jS3zeo68mSSfUX4BsYOBDdVZsoM2oLRTSxmHgQ7/mf5voCu09pUoyHpgpoqY76htt6QJ+Q68NvED1YtADGfxgZhiRVzMgWEe9KKC7iTEDcoOPD46JqKErM0gkKBF4mAszTQu1IRuEnLSY77VNLmcmLEe5wBV6DApNeqcsGPBC9eCjD0fCbkg/htK/A1SqFeE8a+3bt7zTK+r6174fS5ndUV4RSUXxrY2VkZSowQ/9R8+s7ekvDA6PC4jHM3VFj1fSZH+vuyeLQ40NFRLtiREcC0WvIOMiMPWhfGmAcOBvhiYEhSDukHgR9Twm1q70+yZJ6URZHLheGfpsMvuuy2uNgkmceppNZS4HYWeCwevckXYNk/MJRZKT+IdpXUB/IqXFi0ls/JsLxgILF4cn2xua8gx9S/ekt/Y8NQspW0eBp13rLbbrn19tLCVvJO+vWZuZzZpavmvirIbBXM6UWYUxuNqWgoVaAiHLEICXAxb3qWKTJL0QGOY2QyaNXgNtiwjcZU8kvc9q0AC5ccuqn1WE/pgL3I2ey80NVR2u5yteXd8MGrTjcY88dVuq/tXDDpdkIoSygdxMb47EXnRT7nYjk2sGSyQ/rByTJ8dgF5JzOMO9snrOJxCsKodCQ9l9yHMZzo21rqgzJTSYulACPgtdfi1eu0GggUzdicIw2KGdpYGW7CMUHcglSsmKjBIHE+cHtXU79VQRSrIwa5pd/fn2wabmltzQNuf6DMo642X9zI6eLLJyYmdHO7V2ZmdgkyRceqAp4XIBfakUyqgOv54BUQ6p9QP2V5i8ptOTirhaavLS6rS6fJVYI+FOAC0UdxYY8QK2XFtRbQCKAeB1evSX+IjPRWDUXCw3UDJ3c07e+ua1ekIY7xX3+4fmTSaJw0GBKnpmfO7hjuaWseoHKhg7c7yAbQuDOppVl9rZLIQdURU/UA8N+BxJzmSRZJyeeB1euiBXEw3Kbx6spl96aSuQa/X1pRo04VH6/J9mpcEHhM37A6MzM/xtebHPqwZeAGfDFdjS9WerzLE/nKIXnuCs/4qAOZ8UPsWYIeFITCAP4dsEWmUIOo5EM0JQPZoRfx9ouiDHkBieRoEXBbxCkhsdYneWXX0PXhANC93ieUzADa5QUBsugicdBfVOIpcdh0mjyIDekysQBZllgNjdWk/C4VpOqtFK9FSkxQ+cLqffXy/PFTZxsWw+PHW7sIljmG43uOHj3dvGNHS097B/EPdW784OgJnX62uLfNoC5WFQfvvP78PZNXzc3tGtppWp5muj0CwhYAnSqgvg1HU5opIPpkxik7Kzm/evDNAtQjzwIaGl1RQafRz3MyorRPxOZ3rxwcSayuklJ7ha44NLec/hgePLu0E6KBd6hc02j/L8g/sn0Lpcli5tBS0DjZRwPudTH4oPsKsncRKC7ZRWCBHm+2+nxWO8/3r5J/dHqKXK4ij/PbG28SB9WfzW9t6sR+WCZIB7OqhVllngcb2EmaZVhnk7OLLi9cQ0Meq8VkyHeoHT6vQljNzsApt+Vjs3g6xduF/ivtEa2uyFRYvNpHPulxMkrevUEpH5PL+QqysPHh3hmJz2FggYHuvNGo8zgZM/tUM4CY9T4icdlglW2hOcTXEFUE6baS2LMyzKmC2kdOfe+nZz6TWFnBp021BdoK3VM/Tb/0i6rnKHuFfhD9zonr8yclr5y5ATE6UTA5mbLcHtANDzohiLrKygTADWDT62C/FOIvURcKZGwhRqnI4ahoSKlGR9IO4r7eJ97NIdk3AUExcjnoVh+9Vp3PQMmDPVtGOiNDGVmyMPmi7+SJmZ2ru7sSK3tSdaujWoV1PHommGPfSfxze0Gyhs5MjaR/AB+7dqb/lTi6ikMTpcVZcu1HRjTHSP+sjg3NQT9lWNJtIxNzmZxkCbojaREkg96S46w7U0k9RiCTwhiM2Ki4fAyMdnzV3FVMDYBi+4xI6+DZDJEivpcBfZesi5/dHv/7pbyggNfVlwW/Zbcc7z7cctOJnsMt6aPtvb3tHb29bVhz44XWM1M3/knL6emZPXtmdi4tiX6KDvwU/zb7aQDXJFfyU+AHJ/yQ7GeWwTy75YD0SbDnQVRnt5miS8r8Hvu5tabKu7ELi7yjFsksDREvUG8l0WdRcrLVca3c0hfoTeDGwdZkkvhvu/XmD5YWGiqM3+dUXd7oxMSkbnb32sTkHpSF+3Sskk+mA4jnJF8BfhDhR8Ynk3yws1uuF0PvazjmkxGynuUqXFZE8snoOEWfTJ4N38xRAHznOZquzHgIT3CcwjKQWDh59WKi3ypTj6/enErixFBrS4vKWGbgy6mjEPV2vftr3Dg+YVhYAh9hCUkyju+FMRrQHlF/6aqIVgYuGR0i/OKkX+IYDQz9wEGSnEo6iQVsEjNILN2A8WAEwq6CGTNgg1yYMYtSWEXOiPqsxpOvsjtDnZHVUY3MPkz8HFlShnrj6TeIYyFCc/IV4D9+Eej8fbmNs/+p3MYXjy3XDXn5+dLGuAl42xk4PFsxEeb5nkC00lpXWdnpv3dgRK+b1TsCRQZXockcrivt6DUZhrRmt8PodJqMoXqBpwagdQ+5C1lRMtmkAawAGQHAAF8XSJVxN1BTwbYMrPdRgd/aM2A2GQ16HcvBWrGV5sr8Wa4W+C+A5WYai8Kcm80lOkOdq6Qn0jvgb7n//khKqRjMUbl7WnFxNHTk6qH035QUC/T0A07wYNOMKPVZjm48EoMooxwmljD7hXaJgT4gl3QVfrN02i4pXlDDJSMCG+4TEhRbsVEmx0L4Ma3cvjM2f9W+/SNJ4th4s7M4OLcHL6afOLNrBuszeQEN0KOmeRYFw1Ta+3rWWr8aqW1CNldIfcRY7oN76aVDh1QFnExmzTt06NsQ2f9FYZ/N1u/GDRtvCmNVgUA4iRMiR2jbpZdxmSwONS2CGXPY9XaWF4QolM/Kr8QShCVYiPiN4/FXc/QacD81+pzBE3evdaiM8EOlVowfG1VoVERG8rTKrn33XI019rYmnS7RZk//Cqj6l+J5l762zoD59N+DLBl1tUkX1gOJoAwwdj2MXUURO1fBcZeN3mAw6Jn55mKWmhhHTTf/8uevvu669c9/bf3mW9axEec8/3z6nfRbL78sjBkDTmmhTTv1v+xGOUyngfkpQrvMPRGQ1M/bWGrBmM3U7D08zRgfOaQGwZCb1atHfupa7Lf51HlF+rJaXQEM7GW+pdDe4cMVGw/0T8m5USKLR4RcCA9vnwca3iNHs/77czT48+k1XJD+HT6V/lds3YFvHO5Nn2ftWjd34TlyESKYkmS4AFrDdKWarh3SZVv4v1sKD47L+h16s17YYyun0RTgYzxBP6tjoEMQwyrNBTSSteC55auaGxubl6+K1h44UFPxIO7uv7uyeByHKz7U3zOCg+V3XGVQGJfuLA/Q1ZGfbRpxE/mt6A8xtw/ja7DoD9EFgp+NjJDfvuukuRmgd69EL+A7zVALK18rwiLFbmnZ8DjuB2r1DkavhdFZEw9QqulcsIjbzCJvwC+BTInoXiDwT5aMQOAFIHBEJL6Y0i7sB8B2osJ/jiyokeVkKpTANiMAOUkZMO5RYK6brvaTKaqLAAgrYuxHuCFp+wXN1LDVaoUyzrBSjPoolmK1P1rgTFgrulz1Zf6o2cW+NuB6bT5vnYznm+jnRHU+Qpubwloz9ylDAPkQ0ivRUfQGegLlPEXwk088GY+wMo0gv1PkVSgTQEinRMewil1n64esbkis+zKri7Pq0nWySVamRCwzcFkZIZ+UhjJtrP2j6Lms62+K1xXoZANi18Hu01wFXO8Q6EHPiNdpPE3b6RLb+coVrivQiR1COwBkuJxdv4FdPxpAmfJl7Pqw2M4rWdffFK8DPSmxHYbftPykWP57zBfLx6+xPYo5SH+Fvfi5uQjl6nP16nwokGOkm0gw3d8SpGtZ0hd8TONUrik1Udu61qncp9CU28jF+vdXqYTdovQDbesrFzwFS9Ik9MUJ232NJpOR+ZjS9hmltPsUH1NHbf15Rr189SuaqLUPOttHvirun6GN191UJWCYDT+Em7gx8GSjydJL9keCvnNU32mGdkLEEQ4NFNAtkmro1nOFLZIKLUT3TenvHw4EtV67I2qLuLvKkr11tV3P4ocSB31lPrPTZTFV2wuba6N98R2UBjO+F3cADSHUl+wJQIBP90iCtsj0gKY6oEnOdv3RheYVqtJg2EF14PqUmMEieIjmEl0Ou9Vs1OSjEA4p1VLUKWyY3OaMXLpd8pNtPcXe4vKxKX/S7dlRVF6ypzYeC/uczkpHqBAoS1QEYipZblWwvkuj6zIYfZ5kk98JrochXuBEl+RjxVyuAhFh1VhMx5pNUi4Xg+kDgIT5wrna6oS1Qxvw5Z8mvzLU1xryjVU1BbQ9OcTA32F2v5K1F9RrFBxNHrHVY3ALb2DJ4inqee2iFYYLeC/N6tMeLHS4QhqCbsMGwXDj73T1eQtsHpcnGipuK/DGbqysVTQr9BarTT5Slhtm+wHxQ2gD5j2f2jNVnoKTEsoIT1DfWsxKi8sxnNoSMeqzNuJujHI416qqCL+fS6VHMWetNOQU5PjjSxJ/7gT+6FGYjcetzpVx4nIHIjcIXhAgCdKbzVL2HAspaxxUCPzCBerSCkO3vc5RXd1lLC9h+eu4IV9X5qjxipyT+vpT8ibwror1FUbUHkxR3YE+ZXRLJreooCEENyS6Vl6916sE14RlyoWFylgl3Q5PPUDeqwwpEhXOeLQgqKlTJKKOeEVBWE3mkh0OZy17Y/n8FnhL/jH7rJPpyQn8WGafdS7o4Bw3gPKAFKaF0h5bsF6Ym9i205YuknB4wCpstFXTDBvbsW4UN9qy7UF4rmnya8+PN91NsNyl6jmG/2N6uh470m9NmePW/ArdAYFPHnwP7ob5dqPWZLML1Ao7dUTItdB9tmDm4bVMt1RwGLgG3hw3Jfr8HKGccyO3z+/j9Uo1OHVmXpmdwt6+17YGV3wuR+XSt85MjHraLfY2eyRWUVbg0wT0XKplkcjakx39anWvzugL+4I5nOk70K0T+JIC+hSoOBkU9qcyabnx0nWSzLKL2goxCl255Po//nc4+ig0sNxem74NsRmwwnj7AWesYBMrUF2y2l9oULA9quChQnAA8rhCjwewUVJc4fCQ3YZRSbHNZ/epVTQ4UGzhigQr0lmQ7XtUldJCBdXAZ+pGit2B6Fh771BpkcsbbPG7CnmH3VNU7C70+QP+MM+NVdf4ozp9VXFte2ttcZXeWBrwVdgTDou1MF9VaLM4vqDxQw2P3cHng8wYYSwjMBa6z7IiWUZXEyEuW5Gz011TmT23BA3RzQB6bb5ZbRbzcXQEUlQLIwhWxquEePYzA4OpkYHh1FhPj9/rxfcc37d67Oi+1RO9d9/V09vTw+wRxepRbhQ5UBD1JDtp0CoD/M0DGRX2ZtOwAtBZSbcIgdCAUyyfEo/ayLkhcdEn6AqajVq1sHErR2CpsPcZb1sIofv4MqlTYOQvtbzK7qlpsnMyTVWwoXu0o3POWDGeFywJhb2RADcq49pyAy0hk7sz2Ta8sKt8qqv4+8UWb0NxmA8x+g1A/yA3LtFvwJxcBTOfJ+0tp3RTiJADP+WXm5kt+i1mrTpXmUW/me1UlHbuXCH1C/Q/3jFniI7v4B15haaaRjuRaSqCie7RLk/EFwiWhrjx+V3lM10RZbtMEWoNmtxdyZbR1uIQHw5b/bXMJwE/mPyabOgCqJv5JN14d+Z6cdb1VNZ1b9b1ga3r3Acy14NCO7QeeTFz/8Yr3B+g9zO5SkcmV7l+xVylg7Vl3PwteMufNSrRGYTSCyj3KUS+kF5gbiK9H4SPDfw43D8u3cfZ9+lGqiZW//AV75s338Ed7P6RK96n68Vz+Am4f/UV+/cAfd2s/2ves/1R/AzcP3vF+uACQCxJ65+6Yn0rtN/P6Dst3eey7xug/UH8Obh/7RXbN2IzHiHfFfi3eVa4v3l2i3/YTde1Bf4J93H2fRt2Qxz1XYF/V7hvxgW4g90/csX7uVB/jnxb4N8V+vcAfd2s/2ves/1R8h2Bf1eo74T2U6z+qSvWt0L7/Yy+09J9Lvu+AdofJK8K/Lu0/UvOMaH/koNK2eeU0B95GCn7LBL6rztetO18EfpvPiC0/XwQyjr0w8elUz+XH/r5/Wd+0H/LeR30/+DEzfYzN+g9jtNcepYG/X93KubSczHov/FgS7evRjzYkn0uEv2BQ5DZZzdRjUUMk59Xioc0j4lh9yVnZdB/6jDM9mcBoD/+sD/aflYUZZ0GJeiDm7/E1kyb0GT24Y8nbGds+7LaTOdtO9NxGP9P/BD5HjsHg4xQl/672XrSesR6wgr3iPCAAFaWZ88HWGfPB0CXnebnt076N2Ne+cDV73dee3vRUNG1nkHP1Tc5zt3hGfJeWzS44bzW8XjqttRj8Acfjz/++Du3MV9cTnMunAvap2vJNhhtOVCU5ZRCDMMbM44pvqK7+lJvX9dg70AP5ifSZyZ17e3ewkL8t7193QOp/p7BtjYefmoXxsbn5sbHFvD8xgU8tW+lKh6vTn9kcXR8fn58dDG1sloFGgP8Bt6gf2S8YZyhXBE4AvcObJ7Gs5yV7p2UC88OEM9Jo83urq7u2bbOjo73LfzyzLm355bePn3mrd1QZwjqJIU6lsxhaKrFw230VDWt976ln5+59q2rFn559rq35+icd+Ii4iH/RHPVRnbGOSYdeb7x6Ge0z5rXTF/RPH1sbqQHj3xYvRx/9tn4svrDsY8/Rut2oFliIGGqC5idXzazs8yYPPvsyHPPjWAbfX9WkK2yzZvRX4OtzdaFi0p1uXVAa1OeUlVfXdt4nvKkEv0HLsaTjCfQWuU77+DJuf9DvYtvXo8/CnyANuic4uaF9G8WOOu7+0DGwG7hDckub20zoigXlGCfzr7xxJGyPaMnjpUvj7bV17e11dbmgUF+ePLrnwbTPH3z7bfecotwfBj664W3VbanEnmoWVYwq6ynW5Yt3t7qv5zEz/gXPDZnU/tHNqqhfCmU/5Vgk5vxe1hks6f0E9E2X315SQ/fGF7pWDhcNlB5Dd6Rrhv7QtVwf3llbaCwA0zydM3SQtU5GYtJfTC2Xwg22fIHDLF5y2b/r5qFSMlS6WRq51RT48hAorqyr6g6sL9j9fB428RU3ck8MMMFts7GxrrRjpryuMfS7S5e7O+aVHN5k9RKC9gM80qMzCabwSYL9pgKQxMGBseZUcZd6a/hxOrS0l6Ok+XYVE89ME4t8s4H3nnQnWo3mCoN6X9n/KyHcfwb8LNQGodwyCfbIMcE+/Tz2JzHnGh19gx91J40gUme3UR6XVVh857G9+cZTbu0ZlVb9w25MheenP+STBGdaz98TqCX7p9OS7ZELuxuuqJtDm6zy7hox1qzLtR1eNBYqfeVto5VFBCFsbt+54H1vfEd9Q2hBo+rLq/t1n2SZVaFexZrebDMh85P9Qw0gykBu1yZscvsnAPYZcEs8/jW9AP4tfSvjpB98/0b7wMJLhf3edPYMSZKrBT6x7YZaS6BM+IrHI8KMsTApgMnh+qiO+rmq2vnartrAu0l9cs7ihtro2W1DcXJZHeytqExb890106neyxZlwoEUvUtww4wzuWNk1H8pLGuojQaLY1VpT/YVBltKXJ0VEabGA/LgLb/kHhoeQ9bDKRxxsyhOUrcF4F1fBmwzswpTN11s/vX9+zYl9SFug/hUKPHVR+Jt9bnyWTT4NQs1vCiU7OPWWe8BH5NqD/WM5DsFuZxGHDh+xnbmYXZXNb3e1wh6ZAn+8T3eAPhoqJwwJue8QZDRUWhIH3eTTlglNCWaXtbRgmxpIZsSk2ZDZArJ6ulNyQQQwI2dgI2NtO5NbJDouYO/FL6Y3geu2p/NvWJByC+hzKfJwb8imgbxQNRtFgVe70ycnT40rbE054deC79p7TQQw98Yupnta8LvACMxC+K5z0R3f72nsc3lVmD+7Iv6vbrY5WBXp8/vFgxu99azhd58wPiUF+1VneGbKFIUbjKoR8qKFwYK26Oux1+v0P74wzvMLoFbM8UYK5SwG2q/Le8cvjwK5x1cEM2KPAkDmVEXDYCLsfnsYrC8lUstoF7bk5P60t7oQcPHXr55Sry60EAbnGtK4cE2F5oJOxx5rG+7vtTH314mrySrsve47wLf4TtcUZK4CjBH00vTrcJbdRAPytAgwtoEDfMbV8vNIuriTWNydmpUHRtLRqamk024uPFsWf62vAIdpV/ZrdZYdzzZLmLjOC2vs/EItDum9BujTQ2Pf/m8LAwMiTa5fcJfWJhlU8Zz14D5KU1QPy+pubZ6XD52lp5eHq2uQm7i2NP006Jq/zJ3SaFefdT5e5R6PNp1mcX+h3R4luoJTMyXctauaN7H9iSXldFj7EoVOgLFzgbLVHhe8jiaLTh0bhMo6brd1NxuVqtU3ltVJ/wG/gr4JPQdSZkDIqPoIlJX6aUmnJbv8aRM6PURK0D8AW/oYqfrWm8oUr8oPuGoI2vSW1gSXd46Qv+olR3hrY2oHEqyfdo7Yb3x8QPyR/9AfClfssf/cHOnVTOd/w+H/fTgo8raPt38Dvg43o8ko/LUdwnP8085wf9Xz5qJz66ONj0pK1QtaaL+e/8wGDZgdG1U4MdRbE9E4EK3lNdVjIUjbfV1404yWsjPc1T/Mfd/aWtR4o/2B+sWJntmQvxw/GeseGQ31vnKaz3e8PpF91N0cqE387sAsivjbxM/bjtD8gJdvQutre3ty30dmDTNW/OzL154tiPZyf/Aeq4oI5WqHPJw3C07akFqNSxmGrHpqv/YXL2x8dOvDk3I+6hoM/D2WDPw0FXeNiN5jPH5oZ7Fo88qf2SiT7sZo8m62E3gg3bPIMuoqvB7qMausZEl5gu5jqL9H259CT9AX5szBeamqRbBoR5Q9df6sPtCDld4bDLGWrIzBRGuZtpvE5eonOf/ZiYlzYqYZAZGfgDMVOmZekzO2aS+iLIDWMQnh9QsL0tozSi1qCLNuAKnst1enQDeaZtDwn4LB1kcHqS7h/OAwx+NIPBf+wJ+tv5Sn+xrj4e7fMHipfpCfoqv8erHrVIgvyv1V3BwnDIESm3G4Ys7vmhSHO1yxEM2I33SAOh67Yv46OgF8wHYyt+QWaNxS/4aC6lW0mHcII950CR6/DoycXQ9ASv4kfHfZkvgj1RQ3vHt7UnLPiKX/DxXFeRbiDXpJetfYU22pdngEbJV31jY3xwatKnkr7AfCbxC+hTTGcR3WyT3G9qp4+JYv3APbxffM4cuyuVeFwsJcZEqc0FXA7TD6XkgonR4/Kf/ORc/zB3YHijWlhb3lxA7wplLHHBkNjOnfvJQ8Pk4vC7F4Q4MJL1LDQpDrzskWNCBuHSh45JV7/ZO5Qa7B7sJQHxsWMQ+PEd+Dy7PNDTz35e8uixRF9Pb9+997wuPXysu7cXfn/o98Xe74GB/xtQ5NOgAHicnZLNattAFIXPyE5KN1mVrkqZXdxSSbYSExN3kwaqRYgdigmUrmxZliZRNO5oFMi79Bm6LN121WfpW/RIHlwSSqDVMOi7lzP3Z+4A2MNXCGy+T/juWOC5eOfYwxOhHHewL3447uKlt+94B6+9L4538aIjqBTdp7S+tacaFngjnjn2sCc+Ou5gIj477uKt+OV4BzPvveNdjLyfOIXGGncwUMiQw0IiQp87Is3oSfmfUGW5JS6o1LiiN2m1J6j5z+kzqGj32hiWMSscI+TKGLlR1Fgg4CmNG3pLF3HFXZIq+grMSYq2z1OGOVJckxNXX8GlaL0CTvX6zqgstzLqR5Gc5amcaKvlhdFXaWLlSW1zbSrZy61dV8dhmCmb14sg0TdhSeFKl7YKi7lVpZ+ZNL32E8YrCpUweNx2+qfnM9Zu2nqaSn2ck5aspGYniPUm81ltTFpa/zxdqpr+DxRllDRNGZppVhdzQsRL6GOAA4yZaMoVt6S3CR9L5z+MGwX9wcE4nk7jcaybQu7X4W/z/ktT8mGWvzcpt8EvKW/mr9ppyntNyvY9rBioblPn7YRt+1ZuqQkw4j7kPmIZQxYx5EvhGC5TUyldyk2L0trVvLY6V6WVvdtBMAoOgyN/uBwu3Mj+6/4evbTfhHDBnnicvVPnUhNRGD0HRJIAScDeG3aNJHQb9oK9d3GzuUmubAm7GwQdfRIfQx/CGd/Eh9Dde3dJEJzRP2Ymc8/97vm+87VFB9Tv52eUsNrvR/gnOtCJNejCWnQjhTQy6EEv+pBFDnn0YwDrsB4bsBGbsBlbsBXbsB07sBO7sBt7sBf7MIj9OICDOITDOIKjOIbjKOAEhlAMtYcxglGMYRwTmMRJnMJpnMFZTOEczuMCLuISLuMKruIarmMaN3ATt3Abd3AX93AfD/AQj/AYT/AUz/AcL/ASr/AaM3gDA99QgcB3SNhw0YCHAIv4iE/4gq/sYCfXsItr2c0U08ywh73sY5Y55tnPAa7jem7gRm7iZm7hVm7jdu7gTu7ibu7hXu7jIPfzAA/yEA/zCI/yGI+zwBMcYpElDnOEoxzjOCc4yZM8xdM8w7Oc4jme5wVe5CVe5hVe5TVe5zRv8CZv8Tbv8C7v8T4f8CEf8TGf8Cmf8Tlf8CVf8TVn+IYGyzRZoWCVNdYp+ZaztGjTocsG5+jRZ8Am5/mOC1zke37orlmLjfrkWHyOx+dEfE6m1FkcGopBcSQBoxnfMvx6oeoZZmJL6KWEXiomoJSA4QQkkUqjqepMtWDJmhECmQBLgUz4FNsU1NbYc7iYfi88t1BxXDvlOkKD4J22ZIK6J7QtXXWbXozkfMzz5YLm+WJeOBoKWasHmujIOKDWcJq2pzQUiDQiEGtEUGtoFGkoXqSheFpDQa2hiEpDvzcMUxQs17S0XISUnAKRXARiOUVTchpFcooXySmellNQyymikotQT0N40q1ogunatpFAy9VuPWLBtAxb43rY7CRcK9F2St9c0w1EpWypWy4sqSw8X9a0U28oZwpHZ5FXVF86NUvHyTYMTziWqOr3nLp6S1n3GX4QZuvP6hoaVtP/Pe/2BJeVpjf0f3W1rX1ZX9iydU1bwo+zFnNNQ3ept+YJIywtaaDwAxnz8+Xwo5oVwVJTsuXw3qqmP35vdSlv+KaUpvTMpp5IrulUwhGYrhd3OXIRSwFTZUML55S9FSinAgXSqmi//mSyS64DiaXl1L4V7bNo25CsYYeT8Q2n8u9Dbx/16uP9c+uXN/avqlmxocu3e8VwVk5j2cL/6fNZ3fwLHORAkAAAAAEAAf//AA94nGNgZGBg4AFiPSBmYmBlUGGwZWBkcGGIBZJJDHlAsoGhBSgzCwgZGZyAKvgYxBjkGNQYTIB8FgZGRisMmomBjYELyPJgYGSaiMGDmMgAABF+COB4nJWSyWsUURDGf92dnsxMFGPiSWIOEdwQD3MQ8TCIBHFgMDnMQSSIPWuiPT3SjkEPihu56tF9HZeAxg304s097h48+mcEb2N19TNEowEv31fvvarve1XvYQFpvlvDpAe35QtkvdArMuyF9YCRYlDLMFo6HPo0y5XxDBO1SiPkbC2s7KPle82AJ81Rb4w3dIgO7bayhU1ydsfCmRO7c+LEAlV/0+n8z/wOUuWgUWdTNfRKZP2xmsd2v1Hy2aFYUNyluEexGhysh/iqFU3GEUXHaCcUOxVTimnFpGKXIixiOQOsI8NmBslTYISyKDb11KJoeK/h0PC46tsc5arZeSa+SeEXZv0jZmu94ZbhmZjt+I5HRMOilz5z5wN/rKvaWc+8XEeiPj2J+3D17AznzCwOLZgRRROcjj3aM7/VbtQZuTKZZVI/wBpOiVaOIU4q5zkh+UMcF8xzTDCnjjmJVtHNee5wi0lucoGLXOIyV2RG17jODVrc1heK32gJS2XVywq9207Wau1d7jHFfR7wkEc8NlpP/6H2XOb9kle8lj/9lmne8Z4PfOQTn/nCV9FNiFv0Vkn9a2npyCXLVvrZjcdKGuxnteRtkH5j/ym+zfr/co+cIo9pVbUkN+rBFc2UaHbJtBZLP93SUY/00y8uWbaoti3ntihP6n93JGfezk87fZXfAAB4nK2WPWxbVRTH//c9mzp2imPXaRdACEIaQA1VWilpk0YRaqs2fBRCWj4aQuw6JG1TN7IiJJaqAwMzYmRggE4IdUAZKsZMDJkze87QqRPS6++e95w8xw1E0PvXvfe8+3HO+Z9rnWM5SXmt62/lz198f1aT1Wa1pulq805Ds7XG0ojmb3zbXNFyffGbEa0tLd5t6t5Sc/G2vl+prjX049py9aZ+VgY9iiI5pLasf5Tcv+67lByk5FDZ24vNhl61ccjGkzaO2TjJwYAe2uySObA51unljLLqVUUvaVDDrOTYeyuZH9scuGvBTLySW0jmX5jXTcclrTIf0n9tgV5QAYQqgkB9wKkEAhhMsj4PQr4KeJplzoMAn3ux6+84uxPaHaeX9Qoap0BGCyCjuyCLl6sWhTxyHl1F7pTQP8+u35kzNkM6bTHZrznuhaYnMM/THnX70rZ5yGzGFr01p7/MxjGNaVqz+kJ13WK9rh/oP+kB+I2ddf1p8gM7dYs7o9GGzkTbOkufiFpoP8HtD/DbIY14L6InKtGdxjnjVyd4Mf/CRe6W6K8hD7A3xbxAryEH6uHOeCJt2xlno/9uJd8n9JX/1SHn6au2dkw9RHNAb+g4XryJD6eI4ajO6CyWJ/WuLupzfJwj0jXYBdk/PPdwK/O7joKOhuWuFj3BswO37rNtnX6nvRttwanjfKdlosSZna9HcX++LdE/1LX+DLZ74xJtRttRi3Fjr854LX3evlvstBnk6RVba8VS2o6PNuOWOpqtbu7nVeyx17Zr10vRw/0Z/K+243P0CF83o4f2Qh2c2dlQxfNoc4n39/mFLZjQ9RbPbl3R2eywsBH3tod7bR3Mxq6+53vyQHombExFo/vtWbu3I3lOgV63bJizbJizbNhnKFo27CNHDJA9BkGBXHGcG0MgQ9UZVlnvgDzZw+ewU6CfLHIaeRQcJk+OkW19TunROCjj4wR59Rwo6Txwugwqeg84faSPGa+CF/Up6LUclNV1cIRcNIdcA6FugAJ5qY4nX2uJWz5D53Qf+Oz3trEqG6uyVZ1+4xazO2LcisYta9wCqqevnzGTXQ59KQ4F877HvK9Yreo3DhldADky5qWET57MPw0HzyqnD3WF0XMLNQOcPgEhtWE2YRvqGjiccP4MhDCfI1JfgpJVxH5jnjXmgXHOaJnq4rSiO4wNEFrtKhOD7/DB6WSqgg3A0uf6YV6sne3HjNc4rM7B4wL+X8Zj7+kMHl7FGx//63hSw2rdrN7Eprfo430fT6asJh6lfsf/VXxzSQ/sFfbW2s4qmzEf42o+aLr8vxpn/4HSmpx+paI620F6Ck0SRxkAAHicY2BgYGQAgvvXThuC6Eesf09C6dsAVTgIHwA=') format('woff')}"}}, {"type": "Font", "id": "13dfbbd6cf8faee40b022516aeb5968c", "data": {"fontName": "f13dfbbd6cf8faee40b022516aeb596", "path": "@font-face{font-family:'f13dfbbd6cf8faee40b022516aeb596';src:url('data:application/x-font-woff;base64,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') format('woff')}"}}, {"type": "Font", "id": "067523e87952abd4e939a88dc57f62cb", "data": {"fontName": "f067523e87952abd4e939a88dc57f62", "path": "@font-face{font-family:'f067523e87952abd4e939a88dc57f62';src:url('data:application/x-font-woff;base64,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') format('woff')}"}}]}