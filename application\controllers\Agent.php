<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Agent extends CI_Controller {

    public function __construct() {
        parent::__construct();
        $this->load->model('Agent_model');
        $this->load->model('User_model');
        $this->load->model('Game_model');
        $this->load->library('session');
        $this->load->helper('url');
        $this->load->helper('form');
        
        // Check if agent is logged in
        if (!$this->session->userdata('agent_logged_in') && $this->router->method != 'login' && $this->router->method != 'do_login') {
            redirect('agent/login');
        }
    }

    public function index() {
        $agent_code = $this->session->userdata('agent_code');
        
        $data['title'] = 'Dashboard do Agente';
        $data['agent_info'] = $this->Agent_model->get_agent_by_code($agent_code);
        $data['total_users'] = $this->User_model->count_users_by_agent($agent_code);
        $data['total_balance'] = $this->User_model->get_total_balance_by_agent($agent_code);
        $data['recent_transactions'] = $this->Agent_model->get_recent_transactions($agent_code, 10);
        $data['top_games'] = $this->Agent_model->get_top_games_by_agent($agent_code, 5);
        
        $this->load->view('agent/header', $data);
        $this->load->view('agent/dashboard', $data);
        $this->load->view('agent/footer');
    }

    public function login() {
        if ($this->session->userdata('agent_logged_in')) {
            redirect('agent');
        }
        
        $data['title'] = 'Login do Agente';
        $this->load->view('agent/login', $data);
    }

    public function do_login() {
        $email = $this->input->post('email');
        $password = $this->input->post('password');
        
        $agent = $this->Agent_model->verify_login($email, $password);
        
        if ($agent) {
            $this->session->set_userdata([
                'agent_logged_in' => TRUE,
                'agent_id' => $agent['id'],
                'agent_code' => $agent['agentCode'],
                'agent_name' => $agent['agent_name'],
                'agent_type' => $agent['type']
            ]);
            
            $this->Agent_model->update_last_login($agent['id']);
            redirect('agent');
        } else {
            $this->session->set_flashdata('error', 'Email ou senha inválidos');
            redirect('agent/login');
        }
    }

    public function logout() {
        $this->session->unset_userdata(['agent_logged_in', 'agent_id', 'agent_code', 'agent_name', 'agent_type']);
        $this->session->set_flashdata('success', 'Logout realizado com sucesso');
        redirect('agent/login');
    }

    // Users Management
    public function users() {
        $agent_code = $this->session->userdata('agent_code');
        
        $data['title'] = 'Gerenciar Usuários';
        $data['users'] = $this->User_model->get_users_by_agent($agent_code);
        
        $this->load->view('agent/header', $data);
        $this->load->view('agent/users', $data);
        $this->load->view('agent/footer');
    }

    public function create_user() {
        $agent_code = $this->session->userdata('agent_code');
        
        if ($this->input->method() == 'post') {
            $user_data = [
                'agentCode' => $agent_code,
                'userCode' => $this->input->post('userCode'),
                'aasUserCode' => $agent_code . md5(rand(0, 20000) . date('Ymdhhmmss')),
                'balance' => $this->input->post('balance'),
                'status' => 1,
                'apiType' => 1
            ];
            
            if ($this->User_model->create_user($user_data)) {
                $this->session->set_flashdata('success', 'Usuário criado com sucesso');
            } else {
                $this->session->set_flashdata('error', 'Erro ao criar usuário');
            }
            
            redirect('agent/users');
        }
        
        $data['title'] = 'Criar Usuário';
        $this->load->view('agent/header', $data);
        $this->load->view('agent/create_user', $data);
        $this->load->view('agent/footer');
    }

    public function edit_user($id) {
        $agent_code = $this->session->userdata('agent_code');
        $data['user'] = $this->User_model->get_user_by_id_and_agent($id, $agent_code);
        
        if (!$data['user']) {
            show_404();
        }
        
        if ($this->input->method() == 'post') {
            $user_data = [
                'balance' => $this->input->post('balance'),
                'status' => $this->input->post('status')
            ];
            
            if ($this->User_model->update_user($id, $user_data)) {
                $this->session->set_flashdata('success', 'Usuário atualizado com sucesso');
            } else {
                $this->session->set_flashdata('error', 'Erro ao atualizar usuário');
            }
            
            redirect('agent/users');
        }
        
        $data['title'] = 'Editar Usuário';
        $this->load->view('agent/header', $data);
        $this->load->view('agent/edit_user', $data);
        $this->load->view('agent/footer');
    }

    // Sub-agents Management (only for master agents)
    public function subagents() {
        $agent_type = $this->session->userdata('agent_type');
        
        if ($agent_type != 1) {
            show_error('Acesso negado. Apenas agentes master podem gerenciar sub-agentes.');
        }
        
        $agent_code = $this->session->userdata('agent_code');
        
        $data['title'] = 'Gerenciar Sub-Agentes';
        $data['subagents'] = $this->Agent_model->get_subagents($agent_code);
        
        $this->load->view('agent/header', $data);
        $this->load->view('agent/subagents', $data);
        $this->load->view('agent/footer');
    }

    public function create_subagent() {
        $agent_type = $this->session->userdata('agent_type');
        
        if ($agent_type != 1) {
            show_error('Acesso negado. Apenas agentes master podem criar sub-agentes.');
        }
        
        $parent_agent = $this->session->userdata('agent_code');
        
        if ($this->input->method() == 'post') {
            $agent_data = [
                'agentCode' => $this->input->post('agentCode'),
                'agentToken' => bin2hex(random_bytes(32)),
                'agent_name' => $this->input->post('agent_name'),
                'email' => $this->input->post('email'),
                'password' => password_hash($this->input->post('password'), PASSWORD_DEFAULT),
                'balance' => $this->input->post('balance'),
                'currency' => 'BRL',
                'type' => 0, // Sub-agent
                'parent_agent' => $parent_agent,
                'commission_rate' => $this->input->post('commission_rate'),
                'rtpgeral' => $this->input->post('rtpgeral'),
                'status' => 1
            ];
            
            if ($this->Agent_model->create_agent($agent_data)) {
                $this->session->set_flashdata('success', 'Sub-agente criado com sucesso');
            } else {
                $this->session->set_flashdata('error', 'Erro ao criar sub-agente');
            }
            
            redirect('agent/subagents');
        }
        
        $data['title'] = 'Criar Sub-Agente';
        $this->load->view('agent/header', $data);
        $this->load->view('agent/create_subagent', $data);
        $this->load->view('agent/footer');
    }

    // Reports
    public function reports() {
        $agent_code = $this->session->userdata('agent_code');
        
        $data['title'] = 'Relatórios';
        $data['daily_stats'] = $this->Agent_model->get_daily_stats($agent_code);
        $data['monthly_stats'] = $this->Agent_model->get_monthly_stats($agent_code);
        $data['user_stats'] = $this->Agent_model->get_user_stats($agent_code);
        
        $this->load->view('agent/header', $data);
        $this->load->view('agent/reports', $data);
        $this->load->view('agent/footer');
    }

    // Transactions
    public function transactions() {
        $agent_code = $this->session->userdata('agent_code');
        
        $data['title'] = 'Histórico de Transações';
        $data['transactions'] = $this->Agent_model->get_transactions($agent_code);
        
        $this->load->view('agent/header', $data);
        $this->load->view('agent/transactions', $data);
        $this->load->view('agent/footer');
    }

    // API Information
    public function api_info() {
        $agent_code = $this->session->userdata('agent_code');
        $data['agent_info'] = $this->Agent_model->get_agent_by_code($agent_code);
        
        $data['title'] = 'Informações da API';
        
        $this->load->view('agent/header', $data);
        $this->load->view('agent/api_info', $data);
        $this->load->view('agent/footer');
    }

    // Profile Management
    public function profile() {
        $agent_code = $this->session->userdata('agent_code');
        $data['agent'] = $this->Agent_model->get_agent_by_code($agent_code);
        
        if ($this->input->method() == 'post') {
            $agent_data = [
                'agent_name' => $this->input->post('agent_name'),
                'email' => $this->input->post('email'),
                'api_url' => $this->input->post('api_url'),
                'webhook_url' => $this->input->post('webhook_url')
            ];
            
            if ($this->input->post('password')) {
                $agent_data['password'] = password_hash($this->input->post('password'), PASSWORD_DEFAULT);
            }
            
            if ($this->Agent_model->update_agent_by_code($agent_code, $agent_data)) {
                $this->session->set_flashdata('success', 'Perfil atualizado com sucesso');
            } else {
                $this->session->set_flashdata('error', 'Erro ao atualizar perfil');
            }
            
            redirect('agent/profile');
        }
        
        $data['title'] = 'Meu Perfil';
        $this->load->view('agent/header', $data);
        $this->load->view('agent/profile', $data);
        $this->load->view('agent/footer');
    }

    // Balance Management
    public function balance() {
        $agent_code = $this->session->userdata('agent_code');
        $data['agent_info'] = $this->Agent_model->get_agent_by_code($agent_code);
        $data['balance_history'] = $this->Agent_model->get_balance_history($agent_code);
        
        $data['title'] = 'Gerenciar Saldo';
        
        $this->load->view('agent/header', $data);
        $this->load->view('agent/balance', $data);
        $this->load->view('agent/footer');
    }
}
