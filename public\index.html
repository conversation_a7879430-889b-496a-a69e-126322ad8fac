<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Game Platform - Todos os Jogos</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }

        .header h1 {
            font-size: 3rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .stats {
            display: flex;
            justify-content: center;
            gap: 30px;
            margin-bottom: 40px;
            flex-wrap: wrap;
        }

        .stat-card {
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            color: white;
            min-width: 150px;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 0.9rem;
            opacity: 0.8;
        }

        .games-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }

        .game-card {
            background: rgba(255,255,255,0.95);
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .game-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
        }

        .game-image {
            width: 100%;
            height: 160px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
            font-weight: bold;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
        }

        .game-info {
            padding: 20px;
        }

        .game-title {
            font-size: 1.1rem;
            font-weight: bold;
            color: #333;
            margin-bottom: 8px;
        }

        .game-code {
            font-size: 0.9rem;
            color: #666;
            margin-bottom: 15px;
            font-family: monospace;
            background: #f5f5f5;
            padding: 4px 8px;
            border-radius: 4px;
            display: inline-block;
        }

        .play-button {
            width: 100%;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .play-button:hover {
            background: linear-gradient(45deg, #764ba2, #667eea);
            transform: scale(1.05);
        }

        .loading {
            text-align: center;
            color: white;
            font-size: 1.2rem;
            margin: 50px 0;
        }

        .error {
            background: rgba(255,0,0,0.1);
            border: 1px solid rgba(255,0,0,0.3);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            margin: 20px 0;
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }

            .stats {
                gap: 15px;
            }

            .games-grid {
                grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
                gap: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎮 Game Platform</h1>
            <p>Plataforma Completa de Jogos - Todos os Jogos Funcionando</p>
        </div>

        <div class="stats">
            <div class="stat-card">
                <div class="stat-number" id="totalGames">-</div>
                <div class="stat-label">Total de Jogos</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="activeGames">-</div>
                <div class="stat-label">Jogos Ativos</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">1</div>
                <div class="stat-label">Provedor</div>
            </div>
        </div>

        <div id="loading" class="loading">
            🔄 Carregando jogos...
        </div>

        <div id="error" class="error" style="display: none;">
            ❌ Erro ao carregar jogos. Verifique a conexão com a API.
        </div>

        <div id="gamesGrid" class="games-grid" style="display: none;">
        </div>
    </div>

    <script>
        const API_BASE = window.location.origin;
        const AGENT_CODE = 'admin';
        const AGENT_TOKEN = '5f2dbdcb-a59d-42f8-9815-cb34a9723cd9';

        async function loadGames() {
            try {
                const response = await fetch(API_BASE, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        method: 'game_list',
                        agent_code: AGENT_CODE,
                        agent_token: AGENT_TOKEN,
                        provider_code: 'PRAGMATICPLAY'
                    })
                });

                const data = await response.json();

                if (data.status === 1) {
                    displayGames(data.games);
                    updateStats(data.games);
                } else {
                    showError('Erro na API: ' + data.msg);
                }
            } catch (error) {
                showError('Erro de conexão: ' + error.message);
            }
        }

        function displayGames(games) {
            const gamesGrid = document.getElementById('gamesGrid');
            const loading = document.getElementById('loading');

            loading.style.display = 'none';
            gamesGrid.style.display = 'grid';

            gamesGrid.innerHTML = games.map(game => `
                <div class="game-card" onclick="launchGame('${game.game_code}', '${game.game_name}')">
                    <div class="game-image">
                        🎰 ${game.game_name}
                    </div>
                    <div class="game-info">
                        <div class="game-title">${game.game_name}</div>
                        <div class="game-code">${game.game_code}</div>
                        <button class="play-button" onclick="event.stopPropagation(); launchGame('${game.game_code}', '${game.game_name}')">
                            ▶️ JOGAR AGORA
                        </button>
                    </div>
                </div>
            `).join('');
        }

        function updateStats(games) {
            document.getElementById('totalGames').textContent = games.length;
            document.getElementById('activeGames').textContent = games.filter(g => g.status === '1').length;
        }

        function showError(message) {
            document.getElementById('loading').style.display = 'none';
            document.getElementById('error').style.display = 'block';
            document.getElementById('error').innerHTML = '❌ ' + message;
        }

        async function launchGame(gameCode, gameName) {
            try {
                const userCode = 'demo_user_' + Math.random().toString(36).substr(2, 9);

                const response = await fetch(API_BASE, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        method: 'game_launch',
                        agent_code: AGENT_CODE,
                        agent_token: AGENT_TOKEN,
                        provider_code: 'PRAGMATICPLAY',
                        game_code: gameCode,
                        user_code: userCode,
                        lang: 'pt'
                    })
                });

                const data = await response.json();

                if (data.status === 1) {
                    // Abrir o jogo em uma nova janela
                    window.open(data.launch_url, '_blank', 'width=1200,height=800,scrollbars=yes,resizable=yes');
                } else {
                    alert('Erro ao lançar o jogo: ' + data.msg);
                }
            } catch (error) {
                alert('Erro de conexão: ' + error.message);
            }
        }

        // Carregar jogos quando a página carregar
        window.addEventListener('load', loadGames);
    </script>
</body>
</html>