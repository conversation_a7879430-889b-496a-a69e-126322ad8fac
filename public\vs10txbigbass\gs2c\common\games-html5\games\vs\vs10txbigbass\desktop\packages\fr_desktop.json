{"resources": [{"type": "GameObject", "id": "10d3e1ae637576a4592d770977bda54d", "data": {"root": [{"name": "fr_desktop", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 0}, "children": [{"fileID": 80647, "guid": "10d3e1ae637576a4592d770977bda54d"}, {"fileID": 80648, "guid": "10d3e1ae637576a4592d770977bda54d"}, {"fileID": 80649, "guid": "10d3e1ae637576a4592d770977bda54d"}, {"fileID": 80650, "guid": "10d3e1ae637576a4592d770977bda54d"}, {"fileID": 80651, "guid": "10d3e1ae637576a4592d770977bda54d"}, {"fileID": 80652, "guid": "10d3e1ae637576a4592d770977bda54d"}, {"fileID": 80653, "guid": "10d3e1ae637576a4592d770977bda54d"}, {"fileID": 80654, "guid": "10d3e1ae637576a4592d770977bda54d"}, {"fileID": 80655, "guid": "10d3e1ae637576a4592d770977bda54d"}, {"fileID": 80656, "guid": "10d3e1ae637576a4592d770977bda54d"}, {"fileID": 80657, "guid": "10d3e1ae637576a4592d770977bda54d"}, {"fileID": 80658, "guid": "10d3e1ae637576a4592d770977bda54d"}, {"fileID": 80659, "guid": "10d3e1ae637576a4592d770977bda54d"}, {"fileID": 80660, "guid": "10d3e1ae637576a4592d770977bda54d"}, {"fileID": 80661, "guid": "10d3e1ae637576a4592d770977bda54d"}, {"fileID": 80662, "guid": "10d3e1ae637576a4592d770977bda54d"}, {"fileID": 80663, "guid": "10d3e1ae637576a4592d770977bda54d"}, {"fileID": 80664, "guid": "10d3e1ae637576a4592d770977bda54d"}, {"fileID": 80665, "guid": "10d3e1ae637576a4592d770977bda54d"}, {"fileID": 80666, "guid": "10d3e1ae637576a4592d770977bda54d"}, {"fileID": 80667, "guid": "10d3e1ae637576a4592d770977bda54d"}, {"fileID": 80668, "guid": "10d3e1ae637576a4592d770977bda54d"}, {"fileID": 80669, "guid": "10d3e1ae637576a4592d770977bda54d"}, {"fileID": 80670, "guid": "10d3e1ae637576a4592d770977bda54d"}, {"fileID": 80671, "guid": "10d3e1ae637576a4592d770977bda54d"}, {"fileID": 80672, "guid": "10d3e1ae637576a4592d770977bda54d"}, {"fileID": 80673, "guid": "10d3e1ae637576a4592d770977bda54d"}, {"fileID": 80674, "guid": "10d3e1ae637576a4592d770977bda54d"}, {"fileID": 80675, "guid": "10d3e1ae637576a4592d770977bda54d"}, {"fileID": 80676, "guid": "10d3e1ae637576a4592d770977bda54d"}, {"fileID": 80677, "guid": "10d3e1ae637576a4592d770977bda54d"}, {"fileID": 80678, "guid": "10d3e1ae637576a4592d770977bda54d"}, {"fileID": 80679, "guid": "10d3e1ae637576a4592d770977bda54d"}, {"fileID": 80680, "guid": "10d3e1ae637576a4592d770977bda54d"}, {"fileID": 80681, "guid": "10d3e1ae637576a4592d770977bda54d"}, {"fileID": 80682, "guid": "10d3e1ae637576a4592d770977bda54d"}, {"fileID": 80683, "guid": "10d3e1ae637576a4592d770977bda54d"}, {"fileID": 80684, "guid": "10d3e1ae637576a4592d770977bda54d"}, {"fileID": 80685, "guid": "10d3e1ae637576a4592d770977bda54d"}, {"fileID": 80686, "guid": "10d3e1ae637576a4592d770977bda54d"}, {"fileID": 80687, "guid": "10d3e1ae637576a4592d770977bda54d"}, {"fileID": 80688, "guid": "10d3e1ae637576a4592d770977bda54d"}, {"fileID": 80689, "guid": "10d3e1ae637576a4592d770977bda54d"}, {"fileID": 80690, "guid": "10d3e1ae637576a4592d770977bda54d"}, {"fileID": 80691, "guid": "10d3e1ae637576a4592d770977bda54d"}, {"fileID": 80692, "guid": "10d3e1ae637576a4592d770977bda54d"}, {"fileID": 80693, "guid": "10d3e1ae637576a4592d770977bda54d"}, {"fileID": 80694, "guid": "10d3e1ae637576a4592d770977bda54d"}, {"fileID": 80695, "guid": "10d3e1ae637576a4592d770977bda54d"}, {"fileID": 80696, "guid": "10d3e1ae637576a4592d770977bda54d"}, {"fileID": 80697, "guid": "10d3e1ae637576a4592d770977bda54d"}, {"fileID": 80698, "guid": "10d3e1ae637576a4592d770977bda54d"}, {"fileID": 80699, "guid": "10d3e1ae637576a4592d770977bda54d"}, {"fileID": 80700, "guid": "10d3e1ae637576a4592d770977bda54d"}, {"fileID": 80701, "guid": "10d3e1ae637576a4592d770977bda54d"}, {"fileID": 80702, "guid": "10d3e1ae637576a4592d770977bda54d"}, {"fileID": 80703, "guid": "10d3e1ae637576a4592d770977bda54d"}, {"fileID": 80704, "guid": "10d3e1ae637576a4592d770977bda54d"}, {"fileID": 80705, "guid": "10d3e1ae637576a4592d770977bda54d"}, {"fileID": 80706, "guid": "10d3e1ae637576a4592d770977bda54d"}, {"fileID": 80707, "guid": "10d3e1ae637576a4592d770977bda54d"}, {"fileID": 80708, "guid": "10d3e1ae637576a4592d770977bda54d"}, {"fileID": 80709, "guid": "10d3e1ae637576a4592d770977bda54d"}, {"fileID": 80710, "guid": "10d3e1ae637576a4592d770977bda54d"}, {"fileID": 80711, "guid": "10d3e1ae637576a4592d770977bda54d"}, {"fileID": 80712, "guid": "10d3e1ae637576a4592d770977bda54d"}, {"fileID": 80713, "guid": "10d3e1ae637576a4592d770977bda54d"}, {"fileID": 80714, "guid": "10d3e1ae637576a4592d770977bda54d"}, {"fileID": 80715, "guid": "10d3e1ae637576a4592d770977bda54d"}, {"fileID": 80716, "guid": "10d3e1ae637576a4592d770977bda54d"}, {"fileID": 80717, "guid": "10d3e1ae637576a4592d770977bda54d"}, {"fileID": 80718, "guid": "10d3e1ae637576a4592d770977bda54d"}, {"fileID": 80719, "guid": "10d3e1ae637576a4592d770977bda54d"}, {"fileID": 80720, "guid": "10d3e1ae637576a4592d770977bda54d"}, {"fileID": 80721, "guid": "10d3e1ae637576a4592d770977bda54d"}, {"fileID": 80722, "guid": "10d3e1ae637576a4592d770977bda54d"}, {"fileID": 80723, "guid": "10d3e1ae637576a4592d770977bda54d"}, {"fileID": 80724, "guid": "10d3e1ae637576a4592d770977bda54d"}, {"fileID": 80725, "guid": "10d3e1ae637576a4592d770977bda54d"}, {"fileID": 80726, "guid": "10d3e1ae637576a4592d770977bda54d"}, {"fileID": 80727, "guid": "10d3e1ae637576a4592d770977bda54d"}, {"fileID": 80728, "guid": "10d3e1ae637576a4592d770977bda54d"}, {"fileID": 80729, "guid": "10d3e1ae637576a4592d770977bda54d"}, {"fileID": 80730, "guid": "10d3e1ae637576a4592d770977bda54d"}, {"fileID": 80731, "guid": "10d3e1ae637576a4592d770977bda54d"}, {"fileID": 80732, "guid": "10d3e1ae637576a4592d770977bda54d"}, {"fileID": 80733, "guid": "10d3e1ae637576a4592d770977bda54d"}], "s": "0"}, "fileID": 80734}, {"componentType": "ModificationsManager", "enabled": true, "serializableData": {"root": {"fileID": 0}, "EditMode": false, "Atlases": [], "Transforms": [{"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page1/Title/PaytableTitleLabel1", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page1/Rules/AllSymbolsPayLabel", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page1/ScatterHolder/SymbolScatter/Sprite", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0.52, "y": 0.52, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page1/ScatterHolder/DescriptionHolder/Label1", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -24, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page1/ScatterHolder/DescriptionHolder/Label2", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -35, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page1/WildHolder/SymbolScatter/Sprite", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0.25, "y": 0.25, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page1/WildHolder/DescriptionHolder/Label1", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -12.5, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page1/WildHolder/DescriptionHolder/Label2", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -50, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page1/WildHolder/DescriptionHolder/Label3", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -87.5, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/MoneySymbolHolder/TitleHolder/Title", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 11, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/MoneySymbolHolder/DescriptionHolder/MoneySymbolHolder/Sprite", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0.7, "y": 0.7, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/MoneySymbolHolder/DescriptionHolder/MoneySymbolRules/Label3", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FreeSpinsHolder1/TitleHolder/Title", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/RuleHolder1/Rule1", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 15, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder1/LabelHolder1/Label1", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 15, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder1/LabelHolder2/Label2", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 15, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder1/LabelHolder3/Label3", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 15, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/RuleHolder2/Rule2", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 19, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder2/LabelHolder1/Label1", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 21, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder2/LabelHolder2/Label2", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 8, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/RuleHolder3/Rule3", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 27, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder1/Label1", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 23, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder2/Label2", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder3/Label3", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -12, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder4/Label4", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -18, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder5/Label5", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -15, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder1/Rule1", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 9, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder2/Rule2", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 7.9, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder3/Rule3", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder4/Rule4", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -30, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder5/Rule5", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -22, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder6/Rule6", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -16, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder7/Rule7", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -14, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder8/Rule8", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -28, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/FreeSpinsHolder2/SpecialReelsHolder/SpecialReels", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -29, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/MaxWin/TitleHolder/Title", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/MaxWin/RuleHolder/Label1", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/CAT/TitleHolder/Title1New", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 10, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/CAT/RuleHolder1/Rule1", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -9, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/CAT/RuleHolder2/Rule2", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -9, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/CAT/RuleHolder3/Rule3", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -9, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page4/Title/PaytableTitleLabel", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page4/Volatility/VolatilityDescription", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -30, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page4/RulesTop/AllSymbolsPay", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 50, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page4/RulesTop/AllWinsMultiplied", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 21, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page4/RulesTop/AllValuesExpressed", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -45, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page4/RulesTop/OnlyTheHighestWin", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -65, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page4/RulesTop/WhenWinningOnMultiplePaylines", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -95, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page4/Lines/Sprite", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page4/RulesBottom/SpaceAndEnter", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 92, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page4/RulesBottom/RTP", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page4/RulesBottom/MalfunctionLabel", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -97, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page4/MinMaxHolder", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -185, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/MoneySymbolHolder/DescriptionHolder/PossibleValues/Label", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -8, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/MaxWin/RuleHolder/HolderLabelJackpot/Label1New", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}], "Labels": [{"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 80735, "guid": "10d3e1ae637576a4592d770977bda54d"}, "extraPayloads": [], "path": "Game/GamePivot/FSWONWindow/content/FreespinsWonWindow/LabelsAndSprites/Catches_label", "oldContent": "CATCHES", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 80736, "guid": "10d3e1ae637576a4592d770977bda54d"}, "extraPayloads": [], "path": "Game/GamePivot/FreeSpinsPurchase/BuyButtons/Options/Option_0/OptVisual/Texts/BoughtText/LandscapeText/label1", "oldContent": "ACTIVE", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 80737, "guid": "10d3e1ae637576a4592d770977bda54d"}, "extraPayloads": [], "path": "Game/GamePivot/FSWONWindow/PressAnywhere_Label/label", "oldContent": "Press anywhere to continue", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 80738, "guid": "10d3e1ae637576a4592d770977bda54d"}, "extraPayloads": [], "path": "Game/GamePivot/FSExtraWindow/content/PressAnywhere_Label/label", "oldContent": "Press anywhere to continue...", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 80739, "guid": "10d3e1ae637576a4592d770977bda54d"}, "extraPayloads": [], "path": "Game/GamePivot/FreeSpinsPurchase/FSPurchaseWindow/Content/AnimatedPivot/Texts/BuyText/label", "oldContent": "BUY FREE SPINS", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 80740, "guid": "10d3e1ae637576a4592d770977bda54d"}, "extraPayloads": [], "path": "Game/GamePivot/FsFeature/content/Feature_trail/Holder/fisherman_highlight/ScalerX10/stretcher_fs/spins", "oldContent": "spins", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 80741, "guid": "10d3e1ae637576a4592d770977bda54d"}, "extraPayloads": [], "path": "Game/Background/PaytableOnScreen/Portrait/Message1/labelMsg1", "oldContent": "AL<PERSON> SYMBOLS PAY FROM LEFT TO RIGHT. BONUS PAYS ON ANY POSITION.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 80742, "guid": "10d3e1ae637576a4592d770977bda54d"}, "extraPayloads": [], "path": "Game/GamePivot/FsFeature/content/Feature_trail/Holder/fisherman_highlight/ScalerX3/stretcher_fs/free", "oldContent": "free", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 80743, "guid": "10d3e1ae637576a4592d770977bda54d"}, "extraPayloads": [], "path": "Game/GamePivot/Reels/ThePivot/BonusMessages/Fisherman/Labels/uilabel", "oldContent": "MORE FISHERMEN!", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 80744, "guid": "10d3e1ae637576a4592d770977bda54d"}, "extraPayloads": [], "path": "Game/GamePivot/FreeSpinsPurchase/BuyButtons/Options/Option_0/OptVisual/Texts/BoughtText/LandscapeText/label0", "oldContent": "FEATURE", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 80745, "guid": "10d3e1ae637576a4592d770977bda54d"}, "extraPayloads": [], "path": "Game/GamePivot/FreeSpinsPurchase/BuyButtons/Options/Option_0/OptVisual/Texts/BuyText/PortraitText/label0", "oldContent": "BUY FREE SPINS", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 80746, "guid": "10d3e1ae637576a4592d770977bda54d"}, "extraPayloads": [], "path": "Game/GamePivot/FsFeature/content/Feature_trail/Holder/fisherman_highlight/ScalerX2/stretcher_fs/free", "oldContent": "free", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 80747, "guid": "10d3e1ae637576a4592d770977bda54d"}, "extraPayloads": [], "path": "Game/GamePivot/FSResultWindow/content/SignPivot/FreespinsCongratsWindow/Labels/Congrats_label", "oldContent": "CONGRATULATIONS!", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 80748, "guid": "10d3e1ae637576a4592d770977bda54d"}, "extraPayloads": [], "path": "Game/GamePivot/FSExtraWindow/content/SignPivot/FreespinsExtraWindow/Labels/Congrats_label", "oldContent": "CONGRATULATIONS!", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 80749, "guid": "10d3e1ae637576a4592d770977bda54d"}, "extraPayloads": [], "path": "Game/GamePivot/FSExtraWindow/content/SignPivot/FreespinsExtraWindow/Labels/FreeSpins_label ", "oldContent": "FREESPINS", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 80750, "guid": "10d3e1ae637576a4592d770977bda54d"}, "extraPayloads": [], "path": "Game/GamePivot/FSExtraWindow/content/SignPivot/FreespinsExtraWindow/Labels/AreNow_label", "oldContent": "ARE NOW", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 80751, "guid": "10d3e1ae637576a4592d770977bda54d"}, "extraPayloads": [], "path": "Game/GamePivot/FreeSpinsPurchase/BuyButtons/Options/Option_0/OptVisual/Texts/BuyText/LandscapeTest/label0", "oldContent": "BUY FREE SPINS", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 80752, "guid": "10d3e1ae637576a4592d770977bda54d"}, "extraPayloads": [], "path": "Game/GamePivot/Reels/ThePivot/BonusMessages/PlusFS/Labels/uilabel", "oldContent": "EXTRA FREE SPINS!", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 80753, "guid": "10d3e1ae637576a4592d770977bda54d"}, "extraPayloads": [], "path": "Game/GamePivot/FsFeature/content/Feature_trail/Holder/fisherman_highlight/ScalerX10/stretcher_fs/free", "oldContent": "free", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 80754, "guid": "10d3e1ae637576a4592d770977bda54d"}, "extraPayloads": [], "path": "Game/GamePivot/FSWONWindow/content/FreespinsWonWindow/LabelsAndSprites/Congratulations_label", "oldContent": "CONGRATULATIONS!", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 80755, "guid": "10d3e1ae637576a4592d770977bda54d"}, "extraPayloads": [], "path": "Game/GamePivot/Reels/ThePivot/BonusMessages/Fishes/Labels/uilabel", "oldContent": "MORE FISH!", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 80756, "guid": "10d3e1ae637576a4592d770977bda54d"}, "extraPayloads": [], "path": "Game/GamePivot/FreeSpinsPurchase/BuyButtons/Options/Option_0/OptVisual/Texts/BoughtText/PortraitText/label0", "oldContent": "FEATURE ACTIVE", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 80757, "guid": "10d3e1ae637576a4592d770977bda54d"}, "extraPayloads": [], "path": "Game/GamePivot/FSExtraWindow/content/SignPivot/FreespinsExtraWindow/Labels/Extra_label", "oldContent": "THE NEXT", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 80758, "guid": "10d3e1ae637576a4592d770977bda54d"}, "extraPayloads": [], "path": "Game/GamePivot/FSResultWindow/content/SignPivot/FreespinsCongratsWindow/Labels/YouWon_label", "oldContent": "YOU HAVE WON", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 80759, "guid": "10d3e1ae637576a4592d770977bda54d"}, "extraPayloads": [], "path": "Game/GamePivot/FsFeature/content/Feature_trail/Holder/fisherman_highlight/ScalerX3/stretcher_fs/spins", "oldContent": "spins", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 80760, "guid": "10d3e1ae637576a4592d770977bda54d"}, "extraPayloads": [], "path": "Game/Background/PaytableOnScreen/Landscape/Message1/labelMsg1", "oldContent": "AL<PERSON> SYMBOLS PAY FROM LEFT TO RIGHT. BONUS PAYS ON ANY POSITION.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 80761, "guid": "10d3e1ae637576a4592d770977bda54d"}, "extraPayloads": [], "path": "Game/GamePivot/FsFeature/content/Feature_trail/Holder/fisherman_highlight/ScalerX2/stretcher_fs/spins", "oldContent": "spins", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 80762, "guid": "10d3e1ae637576a4592d770977bda54d"}, "extraPayloads": [], "path": "Game/GamePivot/Reels/ThePivot/BonusMessages/Level2/Labels/uilabel", "oldContent": "START FROM LEVEL 2!", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 80763, "guid": "10d3e1ae637576a4592d770977bda54d"}, "extraPayloads": [], "path": "Game/GamePivot/FSWONWindow/content/FreespinsWonWindow/LabelsAndSprites/FreeSpins_label", "oldContent": "FREE SPINS!", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 80764, "guid": "10d3e1ae637576a4592d770977bda54d"}, "extraPayloads": [], "path": "Game/GamePivot/FSResultWindow/content/PressAnywhere_Label/label", "oldContent": "Press anywhere to continue...", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 80765, "guid": "10d3e1ae637576a4592d770977bda54d"}, "extraPayloads": [], "path": "Game/GamePivot/Reels/ThePivot/BonusMessages/Hooks/Labels/uilabel", "oldContent": "MORE HOOKS AND EXPLOSIONS!", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 80766, "guid": "10d3e1ae637576a4592d770977bda54d"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/CAT/RuleHolder2/Rule2", "oldContent": "When buying the FREE SPINS round, on the triggering spin 3, 4 or 5 SCATTERS can hit randomly.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 80767, "guid": "10d3e1ae637576a4592d770977bda54d"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder4/Label4", "oldContent": "- START FROM LEVEL 2 - The round starts from level 2 in the progressive feature.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 80768, "guid": "10d3e1ae637576a4592d770977bda54d"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder5/Label5", "oldContent": "- +2 SPINS - The subsequent round starts with 2 more free spins from the beginning and 2 more spins are added to every retrigger.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 80769, "guid": "10d3e1ae637576a4592d770977bda54d"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/RuleHolder1/Rule1", "oldContent": "Hit 3 or more SCATTER symbols to trigger the FREE SPINS feature.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 80770, "guid": "10d3e1ae637576a4592d770977bda54d"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/RulesTop/AllValuesExpressed", "oldContent": "All values are expressed as actual wins in coins.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 80771, "guid": "10d3e1ae637576a4592d770977bda54d"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/Volatility/VolatilityDescription", "oldContent": "High volatility games pay out less often on average but the chance to hit big wins in a short time span is higher", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 80772, "guid": "10d3e1ae637576a4592d770977bda54d"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/MoneySymbolHolder/DescriptionHolder/MoneySymbolRules/Label3", "oldContent": "The fish paying symbols are also MONEY symbols. At every spin, the fish take a random money value which can be won during the FREE SPINS feature.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 80773, "guid": "10d3e1ae637576a4592d770977bda54d"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/RulesBottom/RTP/TheoreticalRTP/Label", "oldContent": "The theoretical RTP of this game is {0}%", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 80774, "guid": "10d3e1ae637576a4592d770977bda54d"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder8/Rule8", "oldContent": "Also randomly, when there are fisherman symbols on the screen but no fish, at the end of a free spin, a bazooka animation can appear and change all the symbols from the screen, except for fisherman symbols to something else.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 80775, "guid": "10d3e1ae637576a4592d770977bda54d"}, "extraPayloads": [], "path": "Paytable/Pages/Page1/Rules/AllSymbolsPayLabel", "oldContent": "All symbols pay from left to right on adjacent reels starting from the leftmost reel.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 80776, "guid": "10d3e1ae637576a4592d770977bda54d"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder5/Rule5", "oldContent": "After the fourth level, the feature cannot be retriggered anymore.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 80777, "guid": "10d3e1ae637576a4592d770977bda54d"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/FreeSpinsHolder2/SpecialReelsHolder/SpecialReels", "oldContent": "Special reels are in play during the feature.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 80778, "guid": "10d3e1ae637576a4592d770977bda54d"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/CAT/RuleHolder1/Rule1", "oldContent": "The FREE SPINS round can be instantly triggered from the base game by buying it for 100x current total bet.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 80779, "guid": "10d3e1ae637576a4592d770977bda54d"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/RuleHolder2/Rule2", "oldContent": "In the base game whenever 2 SCATTER symbols hit without a third, there is a chance for another one to be brought onto the screen by a random feature:", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 80780, "guid": "10d3e1ae637576a4592d770977bda54d"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder1/Label1", "oldContent": "- MORE FISH - More fish symbols are present on the reel strips during the subsequent free spins round", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 80781, "guid": "10d3e1ae637576a4592d770977bda54d"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder2/Rule2", "oldContent": "All the WILD symbols that hit during the feature are collected until the end of the round.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 80782, "guid": "10d3e1ae637576a4592d770977bda54d"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/RulesTop/AllSymbolsPay", "oldContent": "All symbols pay from left to right on selected paylines.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 80783, "guid": "10d3e1ae637576a4592d770977bda54d"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder3/Label3", "oldContent": "- MORE DYNAMITES, HOOKS AND BAZOOKAS - During the round, the chance to hit dynamite, hook or bazooka spin feature is increased.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 80784, "guid": "10d3e1ae637576a4592d770977bda54d"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/RulesBottom/SpaceAndEnter", "oldContent": "SPACE and ENTER buttons on the keyboard can be used to start and stop the spin.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 80785, "guid": "10d3e1ae637576a4592d770977bda54d"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder7/Rule7", "oldContent": "Randomly, when there are fish symbols on the screen but no fisherman, at the end of a free spin, a hook will appear pulling a random reel up to bring fisherman symbols onto the screen.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 80786, "guid": "10d3e1ae637576a4592d770977bda54d"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder1/Rule1", "oldContent": "During the FREE SPINS feature each WILD symbol also collects all the values from MONEY symbols on the screen.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 80787, "guid": "10d3e1ae637576a4592d770977bda54d"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/MaxWin/TitleHolder/Title", "oldContent": "MAX WIN", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 80788, "guid": "10d3e1ae637576a4592d770977bda54d"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder1/LabelHolder2/Label2", "oldContent": "4x SCATTER awards 15 free spins", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 80789, "guid": "10d3e1ae637576a4592d770977bda54d"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/MinMaxHolder/MaxBet/MaximumText", "oldContent": "MAXIMUM BET:", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 80790, "guid": "10d3e1ae637576a4592d770977bda54d"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FreeSpinsHolder1/TitleHolder/Title", "oldContent": "FREE SPINS", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 80791, "guid": "10d3e1ae637576a4592d770977bda54d"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/MoneySymbolHolder/TitleHolder/Title", "oldContent": "MONEY SYMBOL", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 80792, "guid": "10d3e1ae637576a4592d770977bda54d"}, "extraPayloads": [], "path": "Paytable/Pages/Page1/WildHolder/DescriptionHolder/Label1", "oldContent": "This is the WILD symbol.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 80793, "guid": "10d3e1ae637576a4592d770977bda54d"}, "extraPayloads": [], "path": "Paytable/Pages/Page1/Title/PaytableTitleLabel1", "oldContent": "GAME RULES", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 80794, "guid": "10d3e1ae637576a4592d770977bda54d"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/CAT/TitleHolder/Title1New", "oldContent": "BUY FREE SPINS", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 80795, "guid": "10d3e1ae637576a4592d770977bda54d"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/MinMaxHolder/MinBet/MinimumText", "oldContent": "MINIMUM BET:", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 80796, "guid": "10d3e1ae637576a4592d770977bda54d"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/RulesBottom/MalfunctionLabel", "oldContent": "Malfunction voids all pays and plays.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 80797, "guid": "10d3e1ae637576a4592d770977bda54d"}, "extraPayloads": [], "path": "Paytable/Pages/Page1/ScatterHolder/DescriptionHolder/Label2", "oldContent": "It appears on all reels.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 80798, "guid": "10d3e1ae637576a4592d770977bda54d"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder1/LabelHolder1/Label1", "oldContent": "5x SCATTER awards 20 free spins", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 80799, "guid": "10d3e1ae637576a4592d770977bda54d"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/RulesTop/AllWinsMultiplied", "oldContent": "All wins are multiplied by bet per line.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 80800, "guid": "10d3e1ae637576a4592d770977bda54d"}, "extraPayloads": [], "path": "Paytable/Pages/Page1/ScatterHolder/DescriptionHolder/Label1", "oldContent": "This is the SCATTER symbol.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 80801, "guid": "10d3e1ae637576a4592d770977bda54d"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/RulesTop/OnlyTheHighestWin", "oldContent": "Only the highest win is paid per line.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 80802, "guid": "10d3e1ae637576a4592d770977bda54d"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder1/LabelHolder3/Label3", "oldContent": "3x SCATTER awards 10 free spins", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 80803, "guid": "10d3e1ae637576a4592d770977bda54d"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder2/LabelHolder1/Label1", "oldContent": "- Randomly, if the SCATTERS on the screen can move down one position without leaving the reel area, a respin is triggered where the reels with SCATTERS move one position down and the reels without SCATTERS respin.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 80804, "guid": "10d3e1ae637576a4592d770977bda54d"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder4/Rule4", "oldContent": "The retriggered spins are played after the previous batch of free spins ends. The multiplier applies to the retriggered spins.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 80805, "guid": "10d3e1ae637576a4592d770977bda54d"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/RulesTop/WhenWinningOnMultiplePaylines", "oldContent": "When winning on multiple paylines, all wins are added to the total win.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 80806, "guid": "10d3e1ae637576a4592d770977bda54d"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/Title/PaytableTitleLabel", "oldContent": "GAME RULES", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 80807, "guid": "10d3e1ae637576a4592d770977bda54d"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/MaxWin/RuleHolder/Label1", "oldContent": "The maximum win amount is limited to {0}x bet. If the total win of a FREE SPINS ROUND reaches {1}x the round immediately ends, win is awarded and all remaining free spins are forfeited", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 80808, "guid": "10d3e1ae637576a4592d770977bda54d"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/RulesBottom/RTP/TheoreticalRTPBONUS/Label", "oldContent": "The RTP of the game when using \"BUY FREE SPINS\" is {0}%", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 80809, "guid": "10d3e1ae637576a4592d770977bda54d"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/Volatility/VolatilityMeter/LabelHolder/VolatilityLabel", "oldContent": "VOLATILITY", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 80810, "guid": "10d3e1ae637576a4592d770977bda54d"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder6/Rule6", "oldContent": "Randomly, when there are fisherman symbols on the screen but no fish, at the end of a free spin, fish MONEY symbols can appear in random positions via the dynamite spin feature.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 80811, "guid": "10d3e1ae637576a4592d770977bda54d"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder2/LabelHolder2/Label2", "oldContent": "- Randomly, a hook can pull one of the reels up to reveal another SCATTER.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 80812, "guid": "10d3e1ae637576a4592d770977bda54d"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/RuleHolder3/Rule3", "oldContent": "Before the round starts, 0 to 5 modifiers that apply to the subsequent round are randomly selected:", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 80813, "guid": "10d3e1ae637576a4592d770977bda54d"}, "extraPayloads": [], "path": "Paytable/Pages/Page1/WildHolder/DescriptionHolder/Label2", "oldContent": "It appears on all reels during the FREE SPINS round.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 80814, "guid": "10d3e1ae637576a4592d770977bda54d"}, "extraPayloads": [], "path": "Paytable/Pages/Page1/WildHolder/DescriptionHolder/Label3", "oldContent": "Substitutes for all symbols except SCATTER.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 80815, "guid": "10d3e1ae637576a4592d770977bda54d"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder3/Rule3", "oldContent": "Every 4th WILD symbol collected retriggers the feature, awards 10 more free spins and the multiplier for MONEY symbol collection increases to 2x for the second level, 3x for the third level and 10x for the fourth level.  ", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 80816, "guid": "10d3e1ae637576a4592d770977bda54d"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder2/Label2", "oldContent": "- MORE FISHERMAN - More WILD symbols are present on the reel strips during the subsequent free spins round", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 80817, "guid": "10d3e1ae637576a4592d770977bda54d"}, "extraPayloads": [], "path": "IntroScreen/content/Labels_Holder_landscape/Label_Holder_bigger_1/Label_2 (1)", "oldContent": "BIG FREE SPINS MODIFIERS!", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 80818, "guid": "10d3e1ae637576a4592d770977bda54d"}, "extraPayloads": [], "path": "IntroScreen/content/Labels_Holder_landscape/Label_Holder_bigger_1/Label_1", "oldContent": "GO FISHIN' FOR", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 80819, "guid": "10d3e1ae637576a4592d770977bda54d"}, "extraPayloads": [], "path": "IntroScreen/content/IntroButtons/ButtonSkipIntro/content/TextHolder/Label_1", "oldContent": "DON'T SHOW NEXT TIME", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 80820, "guid": "10d3e1ae637576a4592d770977bda54d"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/MoneySymbolHolder/DescriptionHolder/PossibleValues/Label", "oldContent": "Possible values are: 2x, 5x, 10x, 15x, 20x, 25x, 50x, 100x, 200x, 500x, 1666x, 2500x or 5000x total bet.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 80821, "guid": "10d3e1ae637576a4592d770977bda54d"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/MaxWin/RuleHolder/HolderLabelJackpot/Label1New", "oldContent": "The maximum win amount is limited to {0}x bet except Jack<PERSON>. If the total win of a FREE SPINS round reaches {1}x bet the round immediately ends, win is awarded and all remaining free spins are forfeited.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}], "Spines": [], "revisionNumber": 0}, "fileID": 80822}], "fileID": 80823}, {"name": "Game/GamePivot/FSWONWindow/content/FreespinsWonWindow/LabelsAndSprites/Catches_label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 80734, "guid": "10d3e1ae637576a4592d770977bda54d"}, "children": [], "psr": "d"}, "fileID": 80647}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "992d676375ad74b4aa69318524aa049f"}, "_text": "PRISES", "fontSize": 50, "width": 338, "height": 110, "overflow": 0}, "fileID": 80735}], "fileID": 80824}, {"name": "Game/GamePivot/FreeSpinsPurchase/BuyButtons/Options/Option_0/OptVisual/Texts/BoughtText/LandscapeText/label1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 80734, "guid": "10d3e1ae637576a4592d770977bda54d"}, "children": [], "psr": "d"}, "fileID": 80648}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "1c2a90bd6795f664d812d8e24ded758b"}, "_text": "ACTIVE", "fontSize": 40, "width": 170, "height": 80, "overflow": 0}, "fileID": 80736}], "fileID": 80825}, {"name": "Game/GamePivot/FSWONWindow/PressAnywhere_Label/label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 80734, "guid": "10d3e1ae637576a4592d770977bda54d"}, "children": [], "psr": "d"}, "fileID": 80649}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "992d676375ad74b4aa69318524aa049f"}, "_text": "Appuyez n'importe où pour continuer", "fontSize": 35, "width": 1340, "height": 65, "overflow": 0}, "fileID": 80737}], "fileID": 80826}, {"name": "Game/GamePivot/FSExtraWindow/content/PressAnywhere_Label/label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 80734, "guid": "10d3e1ae637576a4592d770977bda54d"}, "children": [], "psr": "d"}, "fileID": 80650}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "992d676375ad74b4aa69318524aa049f"}, "_text": "Appuyez n'importe où pour continuer", "fontSize": 35, "width": 1340, "height": 65, "overflow": 0}, "fileID": 80738}], "fileID": 80827}, {"name": "Game/GamePivot/FreeSpinsPurchase/FSPurchaseWindow/Content/AnimatedPivot/Texts/BuyText/label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 80734, "guid": "10d3e1ae637576a4592d770977bda54d"}, "children": [], "psr": "d"}, "fileID": 80651}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "992d676375ad74b4aa69318524aa049f"}, "_text": "ACHETER DES SPINS GRATUITS", "fontSize": 69, "width": 826, "height": 69, "overflow": 0}, "fileID": 80739}], "fileID": 80828}, {"name": "Game/GamePivot/FsFeature/content/Feature_trail/Holder/fisherman_highlight/ScalerX10/stretcher_fs/spins", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 80734, "guid": "10d3e1ae637576a4592d770977bda54d"}, "children": [], "psr": "d"}, "fileID": 80652}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "8e029e0c84321184fbba558d326569b3"}, "_text": "\nspins", "fontSize": 30, "width": 64, "height": 33, "overflow": 0}, "fileID": 80740}], "fileID": 80829}, {"name": "Game/Background/PaytableOnScreen/Portrait/Message1/labelMsg1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 80734, "guid": "10d3e1ae637576a4592d770977bda54d"}, "children": [], "psr": "d"}, "fileID": 80653}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "86e7a5cc0847bd94cb892fe593749725"}, "_text": "TOUS LES SYMBOLES PAIENT DE GAUCHE À DROITE. LE BONUS PAYE À N'IMPORTE QUELLE POSITION.", "fontSize": 40, "width": 1698, "height": 40, "alignment": 2}, "fileID": 80741}], "fileID": 80830}, {"name": "Game/GamePivot/FsFeature/content/Feature_trail/Holder/fisherman_highlight/ScalerX3/stretcher_fs/free", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 80734, "guid": "10d3e1ae637576a4592d770977bda54d"}, "children": [], "psr": "d"}, "fileID": 80654}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "8e029e0c84321184fbba558d326569b3"}, "_text": "gratuit", "fontSize": 30, "width": 60, "height": 33, "overflow": 0}, "fileID": 80742}], "fileID": 80831}, {"name": "Game/GamePivot/Reels/ThePivot/BonusMessages/Fisherman/Labels/uilabel", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 80734, "guid": "10d3e1ae637576a4592d770977bda54d"}, "children": [], "psr": "d"}, "fileID": 80655}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "992d676375ad74b4aa69318524aa049f"}, "_text": "PLUS DE PÊCHEURS !", "fontSize": 256, "width": 1000, "height": 450, "overflow": 0}, "fileID": 80743}], "fileID": 80832}, {"name": "Game/GamePivot/FreeSpinsPurchase/BuyButtons/Options/Option_0/OptVisual/Texts/BoughtText/LandscapeText/label0", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 80734, "guid": "10d3e1ae637576a4592d770977bda54d"}, "children": [], "psr": "d"}, "fileID": 80656}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "1c2a90bd6795f664d812d8e24ded758b"}, "_text": "FONCTION", "fontSize": 40, "width": 170, "height": 80, "overflow": 0}, "fileID": 80744}], "fileID": 80833}, {"name": "Game/GamePivot/FreeSpinsPurchase/BuyButtons/Options/Option_0/OptVisual/Texts/BuyText/PortraitText/label0", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 80734, "guid": "10d3e1ae637576a4592d770977bda54d"}, "children": [], "psr": "d"}, "fileID": 80657}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "992d676375ad74b4aa69318524aa049f"}, "_text": "ACHETER DES SPINS GRATUITS", "fontSize": 26, "width": 270, "height": 70, "overflow": 0}, "fileID": 80745}], "fileID": 80834}, {"name": "Game/GamePivot/FsFeature/content/Feature_trail/Holder/fisherman_highlight/ScalerX2/stretcher_fs/free", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 80734, "guid": "10d3e1ae637576a4592d770977bda54d"}, "children": [], "psr": "d"}, "fileID": 80658}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "8e029e0c84321184fbba558d326569b3"}, "_text": "gratuit", "fontSize": 30, "width": 60, "height": 33, "overflow": 0}, "fileID": 80746}], "fileID": 80835}, {"name": "Game/GamePivot/FSResultWindow/content/SignPivot/FreespinsCongratsWindow/Labels/Congrats_label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 80734, "guid": "10d3e1ae637576a4592d770977bda54d"}, "children": [], "psr": "d"}, "fileID": 80659}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "992d676375ad74b4aa69318524aa049f"}, "_text": "FÉLICITATIONS!", "fontSize": 169, "width": 1000, "height": 75, "overflow": 0}, "fileID": 80747}], "fileID": 80836}, {"name": "Game/GamePivot/FSExtraWindow/content/SignPivot/FreespinsExtraWindow/Labels/Congrats_label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 80734, "guid": "10d3e1ae637576a4592d770977bda54d"}, "children": [], "psr": "d"}, "fileID": 80660}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "992d676375ad74b4aa69318524aa049f"}, "_text": "FÉLICITATIONS!", "fontSize": 169, "width": 1000, "height": 75, "overflow": 0}, "fileID": 80748}], "fileID": 80837}, {"name": "Game/GamePivot/FSExtraWindow/content/SignPivot/FreespinsExtraWindow/Labels/FreeSpins_label ", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 80734, "guid": "10d3e1ae637576a4592d770977bda54d"}, "children": [], "psr": "d"}, "fileID": 80661}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "992d676375ad74b4aa69318524aa049f"}, "_text": "SPINS GRATUITS", "fontSize": 50, "width": 1000, "height": 169, "overflow": 0}, "fileID": 80749}], "fileID": 80838}, {"name": "Game/GamePivot/FSExtraWindow/content/SignPivot/FreespinsExtraWindow/Labels/AreNow_label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 80734, "guid": "10d3e1ae637576a4592d770977bda54d"}, "children": [], "psr": "d"}, "fileID": 80662}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "992d676375ad74b4aa69318524aa049f"}, "_text": "SONT MAINTENANT", "fontSize": 50, "width": 475, "height": 78, "overflow": 0}, "fileID": 80750}], "fileID": 80839}, {"name": "Game/GamePivot/FreeSpinsPurchase/BuyButtons/Options/Option_0/OptVisual/Texts/BuyText/LandscapeTest/label0", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 80734, "guid": "10d3e1ae637576a4592d770977bda54d"}, "children": [], "psr": "d"}, "fileID": 80663}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "992d676375ad74b4aa69318524aa049f"}, "_text": "ACHETER DES SPINS GRATUITS", "fontSize": 20, "width": 270, "height": 48, "overflow": 0}, "fileID": 80751}], "fileID": 80840}, {"name": "Game/GamePivot/Reels/ThePivot/BonusMessages/PlusFS/Labels/uilabel", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 80734, "guid": "10d3e1ae637576a4592d770977bda54d"}, "children": [], "psr": "d"}, "fileID": 80664}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "992d676375ad74b4aa69318524aa049f"}, "_text": "EXTRA SPINS GRATUITS", "fontSize": 256, "width": 1200, "height": 450, "overflow": 0}, "fileID": 80752}], "fileID": 80841}, {"name": "Game/GamePivot/FsFeature/content/Feature_trail/Holder/fisherman_highlight/ScalerX10/stretcher_fs/free", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 80734, "guid": "10d3e1ae637576a4592d770977bda54d"}, "children": [], "psr": "d"}, "fileID": 80665}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "8e029e0c84321184fbba558d326569b3"}, "_text": "gratuit", "fontSize": 30, "width": 60, "height": 33, "overflow": 0}, "fileID": 80753}], "fileID": 80842}, {"name": "Game/GamePivot/FSWONWindow/content/FreespinsWonWindow/LabelsAndSprites/Congratulations_label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 80734, "guid": "10d3e1ae637576a4592d770977bda54d"}, "children": [], "psr": "d"}, "fileID": 80666}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "992d676375ad74b4aa69318524aa049f"}, "_text": "FÉLICITATIONS!", "fontSize": 169, "width": 1000, "height": 75, "overflow": 0}, "fileID": 80754}], "fileID": 80843}, {"name": "Game/GamePivot/Reels/ThePivot/BonusMessages/Fishes/Labels/uilabel", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 80734, "guid": "10d3e1ae637576a4592d770977bda54d"}, "children": [], "psr": "d"}, "fileID": 80667}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "992d676375ad74b4aa69318524aa049f"}, "_text": "PLUS DE POISSON", "fontSize": 256, "width": 1200, "height": 450, "overflow": 0}, "fileID": 80755}], "fileID": 80844}, {"name": "Game/GamePivot/FreeSpinsPurchase/BuyButtons/Options/Option_0/OptVisual/Texts/BoughtText/PortraitText/label0", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 80734, "guid": "10d3e1ae637576a4592d770977bda54d"}, "children": [], "psr": "d"}, "fileID": 80668}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "1c2a90bd6795f664d812d8e24ded758b"}, "_text": "FONCTION ACTIVE", "fontSize": 60, "width": 164, "height": 208, "overflow": 0}, "fileID": 80756}], "fileID": 80845}, {"name": "Game/GamePivot/FSExtraWindow/content/SignPivot/FreespinsExtraWindow/Labels/Extra_label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 80734, "guid": "10d3e1ae637576a4592d770977bda54d"}, "children": [], "psr": "d"}, "fileID": 80669}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "992d676375ad74b4aa69318524aa049f"}, "_text": "EXTRA", "fontSize": 75, "width": 1000, "height": 169, "overflow": 0}, "fileID": 80757}], "fileID": 80846}, {"name": "Game/GamePivot/FSResultWindow/content/SignPivot/FreespinsCongratsWindow/Labels/YouWon_label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 80734, "guid": "10d3e1ae637576a4592d770977bda54d"}, "children": [], "psr": "d"}, "fileID": 80670}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "992d676375ad74b4aa69318524aa049f"}, "_text": "VOUS AVEZ GAGNÉ", "fontSize": 110, "width": 1000, "height": 169, "overflow": 0}, "fileID": 80758}], "fileID": 80847}, {"name": "Game/GamePivot/FsFeature/content/Feature_trail/Holder/fisherman_highlight/ScalerX3/stretcher_fs/spins", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 80734, "guid": "10d3e1ae637576a4592d770977bda54d"}, "children": [], "psr": "d"}, "fileID": 80671}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "8e029e0c84321184fbba558d326569b3"}, "_text": "\nspins", "fontSize": 30, "width": 64, "height": 33, "overflow": 0}, "fileID": 80759}], "fileID": 80848}, {"name": "Game/Background/PaytableOnScreen/Landscape/Message1/labelMsg1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 80734, "guid": "10d3e1ae637576a4592d770977bda54d"}, "children": [], "psr": "d"}, "fileID": 80672}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "86e7a5cc0847bd94cb892fe593749725"}, "_text": "TOUS LES SYMBOLES PAIENT DE GAUCHE À DROITE. LE BONUS PAYE À N'IMPORTE QUELLE POSITION.", "fontSize": 30, "width": 1274, "height": 30, "alignment": 2}, "fileID": 80760}], "fileID": 80849}, {"name": "Game/GamePivot/FsFeature/content/Feature_trail/Holder/fisherman_highlight/ScalerX2/stretcher_fs/spins", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 80734, "guid": "10d3e1ae637576a4592d770977bda54d"}, "children": [], "psr": "d"}, "fileID": 80673}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "8e029e0c84321184fbba558d326569b3"}, "_text": "\nspins", "fontSize": 30, "width": 64, "height": 33, "overflow": 0}, "fileID": 80761}], "fileID": 80850}, {"name": "Game/GamePivot/Reels/ThePivot/BonusMessages/Level2/Labels/uilabel", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 80734, "guid": "10d3e1ae637576a4592d770977bda54d"}, "children": [], "psr": "d"}, "fileID": 80674}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "992d676375ad74b4aa69318524aa049f"}, "_text": "DÉPART AU NIVEAU 2 !", "fontSize": 256, "width": 1280, "height": 450, "overflow": 0}, "fileID": 80762}], "fileID": 80851}, {"name": "Game/GamePivot/FSWONWindow/content/FreespinsWonWindow/LabelsAndSprites/FreeSpins_label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 80734, "guid": "10d3e1ae637576a4592d770977bda54d"}, "children": [], "psr": "d"}, "fileID": 80675}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "992d676375ad74b4aa69318524aa049f"}, "_text": "SPINS GRATUITS", "fontSize": 80, "width": 1000, "height": 150, "overflow": 0}, "fileID": 80763}], "fileID": 80852}, {"name": "Game/GamePivot/FSResultWindow/content/PressAnywhere_Label/label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 80734, "guid": "10d3e1ae637576a4592d770977bda54d"}, "children": [], "psr": "d"}, "fileID": 80676}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "992d676375ad74b4aa69318524aa049f"}, "_text": "Appuyez n'importe où pour continuer", "fontSize": 35, "width": 1340, "height": 65, "overflow": 0}, "fileID": 80764}], "fileID": 80853}, {"name": "Game/GamePivot/Reels/ThePivot/BonusMessages/Hooks/Labels/uilabel", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 80734, "guid": "10d3e1ae637576a4592d770977bda54d"}, "children": [], "psr": "d"}, "fileID": 80677}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "992d676375ad74b4aa69318524aa049f"}, "_text": "PLUS DE HAMEÇONS ET D’EXPLOSIONS !", "fontSize": 256, "width": 1000, "height": 450, "overflow": 0}, "fileID": 80765}], "fileID": 80854}, {"name": "Paytable/Pages/Page3/CAT/RuleHolder2/Rule2", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 80734, "guid": "10d3e1ae637576a4592d770977bda54d"}, "children": [], "psr": "d"}, "fileID": 80678}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "a1bdd48586194d0458f1428fd37baeb5"}, "_text": "En achetant le partie de SPINS GRATUITS, vous pouvez obtenir au hasard 3, 4 ou 5 symboles SCATTER lors du spin déclencheur.", "fontSize": 25, "anchorY": 0, "width": 1400, "height": 75, "overflow": 0, "spacingY": 5}, "fileID": 80766}], "fileID": 80855}, {"name": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder4/Label4", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 80734, "guid": "10d3e1ae637576a4592d770977bda54d"}, "children": [], "psr": "d"}, "fileID": 80679}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "a1bdd48586194d0458f1428fd37baeb5"}, "_text": "DÉPART AU NIVEAU 2 - La partie commence au niveau 2 dans la fonction progressive.", "fontSize": 25, "anchorX": 0, "width": 1400, "height": 100, "overflow": 0}, "fileID": 80767}], "fileID": 80856}, {"name": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder5/Label5", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 80734, "guid": "10d3e1ae637576a4592d770977bda54d"}, "children": [], "psr": "d"}, "fileID": 80680}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "a1bdd48586194d0458f1428fd37baeb5"}, "_text": "- +2 SPINS - La partie suivante commence avec 2 spins gratuits supplémentaires dès le début et 2 spins supplémentaires sont ajoutés à chaque redéclenchement.", "fontSize": 25, "anchorX": 0, "width": 1347, "height": 100, "overflow": 0}, "fileID": 80768}], "fileID": 80857}, {"name": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/RuleHolder1/Rule1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 80734, "guid": "10d3e1ae637576a4592d770977bda54d"}, "children": [], "psr": "d"}, "fileID": 80681}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "a1bdd48586194d0458f1428fd37baeb5"}, "_text": "Obtenez 3 symboles SCATTER ou plus pour déclencher la fonction SPINS GRATUITS.", "fontSize": 25, "width": 1400, "height": 100, "overflow": 0}, "fileID": 80769}], "fileID": 80858}, {"name": "Paytable/Pages/Page4/RulesTop/AllValuesExpressed", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 80734, "guid": "10d3e1ae637576a4592d770977bda54d"}, "children": [], "psr": "d"}, "fileID": 80682}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "a1bdd48586194d0458f1428fd37baeb5"}, "_text": "Toutes les valeurs sont exprimées sous forme de gains réels en pièces", "fontSize": 25, "width": 1333, "height": 160, "overflow": 0}, "fileID": 80770}], "fileID": 80859}, {"name": "Paytable/Pages/Page4/Volatility/VolatilityDescription", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 80734, "guid": "10d3e1ae637576a4592d770977bda54d"}, "children": [], "psr": "d"}, "fileID": 80683}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "a1bdd48586194d0458f1428fd37baeb5"}, "_text": "Les jeux à volatilité élevée paient moins souvent en moyenne mais la chance de gagner d'énormes sommes sur une courte période est plus élevée.", "fontSize": 25, "anchorY": 0, "width": 1368, "height": 93, "overflow": 0}, "fileID": 80771}], "fileID": 80860}, {"name": "Paytable/Pages/Page2/MoneySymbolHolder/DescriptionHolder/MoneySymbolRules/Label3", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 80734, "guid": "10d3e1ae637576a4592d770977bda54d"}, "children": [], "psr": "d"}, "fileID": 80684}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "a1bdd48586194d0458f1428fd37baeb5"}, "_text": "Les symboles payants poissons sont également des symboles ARGENT. Lors de chaque spin, les poissons prennent une valeur aléatoire pouvant être remportée pendant la fonction SPINS GRATUITS.", "fontSize": 25, "anchorX": 0, "width": 935, "height": 100, "overflow": 0}, "fileID": 80772}], "fileID": 80861}, {"name": "Paytable/Pages/Page4/RulesBottom/RTP/TheoreticalRTP/Label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 80734, "guid": "10d3e1ae637576a4592d770977bda54d"}, "children": [], "psr": "d"}, "fileID": 80685}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "a1bdd48586194d0458f1428fd37baeb5"}, "_text": "Le TRJ théorique pour ce jeu est de {0}%", "fontSize": 25, "anchorY": 1, "width": 1200, "height": 75, "overflow": 0}, "fileID": 80773}], "fileID": 80862}, {"name": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder8/Rule8", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 80734, "guid": "10d3e1ae637576a4592d770977bda54d"}, "children": [], "psr": "d"}, "fileID": 80686}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "a1bdd48586194d0458f1428fd37baeb5"}, "_text": "De manière également aléatoire, lorsqu’il y a symboles PÊCHEUR à l’écran sans poisson, à la fin d'un spin gratuit, une animation de bazooka peut apparaître et transformer tous les symboles de l’écran à l’exception des symboles PÊCHEUR en d’autres.", "fontSize": 25, "width": 1400, "height": 100, "overflow": 0}, "fileID": 80774}], "fileID": 80863}, {"name": "Paytable/Pages/Page1/Rules/AllSymbolsPayLabel", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 80734, "guid": "10d3e1ae637576a4592d770977bda54d"}, "children": [], "psr": "d"}, "fileID": 80687}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "a1bdd48586194d0458f1428fd37baeb5"}, "_text": "Tous les symboles payent de la gauche vers la droite sur des rouleaux adjacents à partir du rouleau le plus à gauche.", "fontSize": 25, "width": 1000, "height": 60, "overflow": 0}, "fileID": 80775}], "fileID": 80864}, {"name": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder5/Rule5", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 80734, "guid": "10d3e1ae637576a4592d770977bda54d"}, "children": [], "psr": "d"}, "fileID": 80688}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "a1bdd48586194d0458f1428fd37baeb5"}, "_text": "Après le quatrième niveau, la fonction ne peut plus être redéclenchée.", "fontSize": 25, "width": 1400, "height": 100, "overflow": 0}, "fileID": 80776}], "fileID": 80865}, {"name": "Paytable/Pages/Page3/FreeSpinsHolder2/SpecialReelsHolder/SpecialReels", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 80734, "guid": "10d3e1ae637576a4592d770977bda54d"}, "children": [], "psr": "d"}, "fileID": 80689}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "a1bdd48586194d0458f1428fd37baeb5"}, "_text": "Des rouleaux spéciaux sont en jeu pendant la fonction", "fontSize": 25, "width": 1400, "height": 100, "overflow": 0}, "fileID": 80777}], "fileID": 80866}, {"name": "Paytable/Pages/Page3/CAT/RuleHolder1/Rule1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 80734, "guid": "10d3e1ae637576a4592d770977bda54d"}, "children": [], "psr": "d"}, "fileID": 80690}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "a1bdd48586194d0458f1428fd37baeb5"}, "_text": "La SÉQUENCE DE SPINS GRATUITS peut être déclenchée instantanément depuis le jeu de base en l’achetant pour 100x la mise totale actuelle. ", "fontSize": 25, "anchorY": 1, "width": 1400, "height": 100, "overflow": 0}, "fileID": 80778}], "fileID": 80867}, {"name": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/RuleHolder2/Rule2", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 80734, "guid": "10d3e1ae637576a4592d770977bda54d"}, "children": [], "psr": "d"}, "fileID": 80691}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "a1bdd48586194d0458f1428fd37baeb5"}, "_text": "Pendant le jeu de base, chaque fois que vous obtenez 2 symboles SCATTER sans qu'il y en ait un troisième, il y a une chance qu'un autre soit amené à l'écran par une fonction aléatoire :", "fontSize": 25, "width": 1169, "height": 100, "overflow": 0}, "fileID": 80779}], "fileID": 80868}, {"name": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder1/Label1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 80734, "guid": "10d3e1ae637576a4592d770977bda54d"}, "children": [], "psr": "d"}, "fileID": 80692}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "a1bdd48586194d0458f1428fd37baeb5"}, "_text": "- PLUS DE POISSON - Plus de symboles poisson sont présents sur les bandes de rouleau pendant la partie de spins gratuits suivante", "fontSize": 25, "anchorX": 0, "width": 1400, "height": 100, "overflow": 0}, "fileID": 80780}], "fileID": 80869}, {"name": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder2/Rule2", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 80734, "guid": "10d3e1ae637576a4592d770977bda54d"}, "children": [], "psr": "d"}, "fileID": 80693}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "a1bdd48586194d0458f1428fd37baeb5"}, "_text": "Tous les symboles WILD obtenus pendant la fonction sont collectés jusqu’à la fin de la séquence.", "fontSize": 25, "width": 1400, "height": 100, "overflow": 0}, "fileID": 80781}], "fileID": 80870}, {"name": "Paytable/Pages/Page4/RulesTop/AllSymbolsPay", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 80734, "guid": "10d3e1ae637576a4592d770977bda54d"}, "children": [], "psr": "d"}, "fileID": 80694}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "a1bdd48586194d0458f1428fd37baeb5"}, "_text": "Tous les symboles paient de gauche à droite sur les lignes de paiement sélectionnées.", "fontSize": 25, "width": 1194, "height": 160, "overflow": 0, "spacingY": 5}, "fileID": 80782}], "fileID": 80871}, {"name": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder3/Label3", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 80734, "guid": "10d3e1ae637576a4592d770977bda54d"}, "children": [], "psr": "d"}, "fileID": 80695}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "a1bdd48586194d0458f1428fd37baeb5"}, "_text": "- PLUS DE DYNAMITES, DE HAMEÇONS ET DE BAZOOKAS - Pendant la partie, les chances d’obtenir le spin dynamite, hameçon ou bazooka sont augmentées.", "fontSize": 25, "anchorX": 0, "width": 1359, "height": 100, "overflow": 0}, "fileID": 80783}], "fileID": 80872}, {"name": "Paytable/Pages/Page4/RulesBottom/SpaceAndEnter", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 80734, "guid": "10d3e1ae637576a4592d770977bda54d"}, "children": [], "psr": "d"}, "fileID": 80696}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "a1bdd48586194d0458f1428fd37baeb5"}, "_text": "Les touches ESPACE et ENTRÉE du clavier peuvent être utilisées pour lancer et arrêter la rotation des rouleaux.", "fontSize": 25, "width": 1400, "height": 60, "overflow": 0}, "fileID": 80784}], "fileID": 80873}, {"name": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder7/Rule7", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 80734, "guid": "10d3e1ae637576a4592d770977bda54d"}, "children": [], "psr": "d"}, "fileID": 80697}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "a1bdd48586194d0458f1428fd37baeb5"}, "_text": "<PERSON> hasard, lorsque des symboles poisson sont présents à l'écran mais pas de pêcheur, à la fin d'un spin gratuit, un hameçon apparaît et soulève un rouleau au hasard pour faire apparaître des symboles pêcheur à l’écran.", "fontSize": 25, "width": 1384, "height": 100, "overflow": 0}, "fileID": 80785}], "fileID": 80874}, {"name": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder1/Rule1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 80734, "guid": "10d3e1ae637576a4592d770977bda54d"}, "children": [], "psr": "d"}, "fileID": 80698}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "a1bdd48586194d0458f1428fd37baeb5"}, "_text": "Pendant la séquence de SPINS GRATUITS, chaque symbole WILD collecte également toutes les valeurs des symboles ARGENT à l’écran.", "fontSize": 25, "width": 1164, "height": 100, "overflow": 0}, "fileID": 80786}], "fileID": 80875}, {"name": "Paytable/Pages/Page3/MaxWin/TitleHolder/Title", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 80734, "guid": "10d3e1ae637576a4592d770977bda54d"}, "children": [], "psr": "d"}, "fileID": 80699}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "a1bdd48586194d0458f1428fd37baeb5"}, "_text": "GAIN MAX", "fontSize": 35, "width": 1100, "height": 100, "overflow": 0}, "fileID": 80787}], "fileID": 80876}, {"name": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder1/LabelHolder2/Label2", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 80734, "guid": "10d3e1ae637576a4592d770977bda54d"}, "children": [], "psr": "d"}, "fileID": 80700}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "a1bdd48586194d0458f1428fd37baeb5"}, "_text": "4x symboles SCATTER rapportent 15 spins gratuits.", "fontSize": 25, "width": 1400, "height": 75, "overflow": 0}, "fileID": 80788}], "fileID": 80877}, {"name": "Paytable/Pages/Page4/MinMaxHolder/MaxBet/MaximumText", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 80734, "guid": "10d3e1ae637576a4592d770977bda54d"}, "children": [], "psr": "d"}, "fileID": 80701}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "a1bdd48586194d0458f1428fd37baeb5"}, "_text": "MISE MAXIMUM :", "fontSize": 25, "anchorX": 0, "width": 208, "height": 26}, "fileID": 80789}], "fileID": 80878}, {"name": "Paytable/Pages/Page2/FreeSpinsHolder1/TitleHolder/Title", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 80734, "guid": "10d3e1ae637576a4592d770977bda54d"}, "children": [], "psr": "d"}, "fileID": 80702}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "a1bdd48586194d0458f1428fd37baeb5"}, "_text": "SPINS GRATUITS", "fontSize": 35, "width": 1150, "height": 100, "overflow": 0}, "fileID": 80790}], "fileID": 80879}, {"name": "Paytable/Pages/Page2/MoneySymbolHolder/TitleHolder/Title", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 80734, "guid": "10d3e1ae637576a4592d770977bda54d"}, "children": [], "psr": "d"}, "fileID": 80703}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "a1bdd48586194d0458f1428fd37baeb5"}, "_text": "SYMBOLE ARGENT", "fontSize": 35, "width": 1150, "height": 70, "overflow": 0}, "fileID": 80791}], "fileID": 80880}, {"name": "Paytable/Pages/Page1/WildHolder/DescriptionHolder/Label1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 80734, "guid": "10d3e1ae637576a4592d770977bda54d"}, "children": [], "psr": "d"}, "fileID": 80704}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "a1bdd48586194d0458f1428fd37baeb5"}, "_text": "Voici le symbole WILD.", "fontSize": 25, "anchorX": 0, "anchorY": 1, "width": 363, "height": 75, "overflow": 0}, "fileID": 80792}], "fileID": 80881}, {"name": "Paytable/Pages/Page1/Title/PaytableTitleLabel1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 80734, "guid": "10d3e1ae637576a4592d770977bda54d"}, "children": [], "psr": "d"}, "fileID": 80705}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "a1bdd48586194d0458f1428fd37baeb5"}, "_text": "RÈGLES DU JEU", "fontSize": 35, "width": 1150, "height": 100, "overflow": 0}, "fileID": 80793}], "fileID": 80882}, {"name": "Paytable/Pages/Page3/CAT/TitleHolder/Title1New", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 80734, "guid": "10d3e1ae637576a4592d770977bda54d"}, "children": [], "psr": "d"}, "fileID": 80706}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "a1bdd48586194d0458f1428fd37baeb5"}, "_text": "ACHETER DES SPINS GRATUITS", "fontSize": 35, "width": 1400, "height": 70, "overflow": 0}, "fileID": 80794}], "fileID": 80883}, {"name": "Paytable/Pages/Page4/MinMaxHolder/MinBet/MinimumText", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 80734, "guid": "10d3e1ae637576a4592d770977bda54d"}, "children": [], "psr": "d"}, "fileID": 80707}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "a1bdd48586194d0458f1428fd37baeb5"}, "_text": "MISE MINIMUM :", "fontSize": 25, "anchorX": 0, "width": 206, "height": 26}, "fileID": 80795}], "fileID": 80884}, {"name": "Paytable/Pages/Page4/RulesBottom/MalfunctionLabel", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 80734, "guid": "10d3e1ae637576a4592d770977bda54d"}, "children": [], "psr": "d"}, "fileID": 80708}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "a1bdd48586194d0458f1428fd37baeb5"}, "_text": "Une défaillance annule tous les gains et les parties.", "fontSize": 25, "width": 888, "height": 60, "overflow": 0}, "fileID": 80796}], "fileID": 80885}, {"name": "Paytable/Pages/Page1/ScatterHolder/DescriptionHolder/Label2", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 80734, "guid": "10d3e1ae637576a4592d770977bda54d"}, "children": [], "psr": "d"}, "fileID": 80709}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "a1bdd48586194d0458f1428fd37baeb5"}, "_text": "Il apparaît sur tous les rouleaux.", "fontSize": 25, "anchorX": 0, "anchorY": 0, "width": 484, "height": 75, "overflow": 0}, "fileID": 80797}], "fileID": 80886}, {"name": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder1/LabelHolder1/Label1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 80734, "guid": "10d3e1ae637576a4592d770977bda54d"}, "children": [], "psr": "d"}, "fileID": 80710}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "a1bdd48586194d0458f1428fd37baeb5"}, "_text": "5x symboles SCATTER rapportent 20 spins gratuits.", "fontSize": 25, "width": 1400, "height": 75, "overflow": 0}, "fileID": 80798}], "fileID": 80887}, {"name": "Paytable/Pages/Page4/RulesTop/AllWinsMultiplied", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 80734, "guid": "10d3e1ae637576a4592d770977bda54d"}, "children": [], "psr": "d"}, "fileID": 80711}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "a1bdd48586194d0458f1428fd37baeb5"}, "_text": "Tous les gains sont multipliés par la mise par ligne.", "fontSize": 25, "width": 1194, "height": 160, "overflow": 0, "spacingY": 5}, "fileID": 80799}], "fileID": 80888}, {"name": "Paytable/Pages/Page1/ScatterHolder/DescriptionHolder/Label1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 80734, "guid": "10d3e1ae637576a4592d770977bda54d"}, "children": [], "psr": "d"}, "fileID": 80712}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "a1bdd48586194d0458f1428fd37baeb5"}, "_text": "Il s’agit du symbole SCATTER. ", "fontSize": 25, "anchorX": 0, "anchorY": 1, "width": 454, "height": 75, "overflow": 0}, "fileID": 80800}], "fileID": 80889}, {"name": "Paytable/Pages/Page4/RulesTop/OnlyTheHighestWin", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 80734, "guid": "10d3e1ae637576a4592d770977bda54d"}, "children": [], "psr": "d"}, "fileID": 80713}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "a1bdd48586194d0458f1428fd37baeb5"}, "_text": "Seul le gain le plus élevé sur chaque ligne est rémunéré.", "fontSize": 25, "anchorY": 0, "width": 1400, "height": 50, "overflow": 0}, "fileID": 80801}], "fileID": 80890}, {"name": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder1/LabelHolder3/Label3", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 80734, "guid": "10d3e1ae637576a4592d770977bda54d"}, "children": [], "psr": "d"}, "fileID": 80714}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "a1bdd48586194d0458f1428fd37baeb5"}, "_text": "3x symboles SCATTER rapportent 10 spins gratuits.", "fontSize": 25, "width": 1400, "height": 75, "overflow": 0}, "fileID": 80802}], "fileID": 80891}, {"name": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder2/LabelHolder1/Label1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 80734, "guid": "10d3e1ae637576a4592d770977bda54d"}, "children": [], "psr": "d"}, "fileID": 80715}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "a1bdd48586194d0458f1428fd37baeb5"}, "_text": "- <PERSON>, si les SCATTERS à l'écran peuvent descendre d'un emplacement sans quitter la zone des rouleaux, un respin est déclenché où les rouleaux avec SCATTERS descendent d'un emplacement et les rouleaux sans SCATTERS sont relancés.", "fontSize": 25, "anchorX": 0, "width": 1367, "height": 125, "overflow": 0}, "fileID": 80803}], "fileID": 80892}, {"name": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder4/Rule4", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 80734, "guid": "10d3e1ae637576a4592d770977bda54d"}, "children": [], "psr": "d"}, "fileID": 80716}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "a1bdd48586194d0458f1428fd37baeb5"}, "_text": "Les spins redéclenchés se jouent à la fin du premier lot de spins gratuits. Le multiplicateur s’applique aux spins redéclenchés.", "fontSize": 25, "width": 920, "height": 125, "overflow": 0}, "fileID": 80804}], "fileID": 80893}, {"name": "Paytable/Pages/Page4/RulesTop/WhenWinningOnMultiplePaylines", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 80734, "guid": "10d3e1ae637576a4592d770977bda54d"}, "children": [], "psr": "d"}, "fileID": 80717}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "a1bdd48586194d0458f1428fd37baeb5"}, "_text": "En cas de gains sur des lignes de paiement multiples, tous les gains sont ajoutés aux gains totaux.", "fontSize": 25, "anchorY": 0, "width": 1400, "height": 50, "overflow": 0}, "fileID": 80805}], "fileID": 80894}, {"name": "Paytable/Pages/Page4/Title/PaytableTitleLabel", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 80734, "guid": "10d3e1ae637576a4592d770977bda54d"}, "children": [], "psr": "d"}, "fileID": 80718}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "a1bdd48586194d0458f1428fd37baeb5"}, "_text": "RÈGLES DU JEU", "fontSize": 30, "width": 1150, "height": 100, "overflow": 0}, "fileID": 80806}], "fileID": 80895}, {"name": "Paytable/Pages/Page3/MaxWin/RuleHolder/Label1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 80734, "guid": "10d3e1ae637576a4592d770977bda54d"}, "children": [], "psr": "d"}, "fileID": 80719}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "a1bdd48586194d0458f1428fd37baeb5"}, "_text": "Le montant maximum d'un gain est limité à {0}x la mise. Si le gain total d'une SÉQUENCE DE SPINS GRATUITS atteint {1}x, la séquence prend immédiatement fin, le gain est accordé et tous les spins gratuits restants sont annulés", "fontSize": 25, "anchorY": 0, "width": 1400, "height": 100, "overflow": 0}, "fileID": 80807}], "fileID": 80896}, {"name": "Paytable/Pages/Page4/RulesBottom/RTP/TheoreticalRTPBONUS/Label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 80734, "guid": "10d3e1ae637576a4592d770977bda54d"}, "children": [], "psr": "d"}, "fileID": 80720}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "a1bdd48586194d0458f1428fd37baeb5"}, "_text": "Le TRJ du jeu en utilisant « ACHETER DES SPINS GRATUITS » est de {0} %", "fontSize": 25, "anchorY": 0, "width": 1200, "height": 75, "overflow": 0}, "fileID": 80808}], "fileID": 80897}, {"name": "Paytable/Pages/Page4/Volatility/VolatilityMeter/LabelHolder/VolatilityLabel", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 80734, "guid": "10d3e1ae637576a4592d770977bda54d"}, "children": [], "psr": "d"}, "fileID": 80721}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "86e7a5cc0847bd94cb892fe593749725"}, "_text": "VOLATILITÉ", "fontSize": 22, "anchorX": 0, "width": 102, "height": 22}, "fileID": 80809}], "fileID": 80898}, {"name": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder6/Rule6", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 80734, "guid": "10d3e1ae637576a4592d770977bda54d"}, "children": [], "psr": "d"}, "fileID": 80722}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "a1bdd48586194d0458f1428fd37baeb5"}, "_text": "<PERSON>, lorsque des symboles pêcheur sont présents à l'écran mais pas de poissons, à la fin d'un spin gratuit, des symboles ARGENT poisson peuvent apparaître à des emplacements aléatoires via la fonction spin dynamite.", "fontSize": 25, "width": 1380, "height": 100, "overflow": 0}, "fileID": 80810}], "fileID": 80899}, {"name": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder2/LabelHolder2/Label2", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 80734, "guid": "10d3e1ae637576a4592d770977bda54d"}, "children": [], "psr": "d"}, "fileID": 80723}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "a1bdd48586194d0458f1428fd37baeb5"}, "_text": "- <PERSON> has<PERSON>, un hameçon peut soulever l'un des rouleaux pour révéler un autre SCATTER.", "fontSize": 25, "anchorX": 0, "width": 1400, "height": 75, "overflow": 0}, "fileID": 80811}], "fileID": 80900}, {"name": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/RuleHolder3/Rule3", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 80734, "guid": "10d3e1ae637576a4592d770977bda54d"}, "children": [], "psr": "d"}, "fileID": 80724}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "a1bdd48586194d0458f1428fd37baeb5"}, "_text": "Avant le début de la partie, de 0 à 5 modificateurs s'appliquant à la partie suivante sont choisis au hasard :", "fontSize": 25, "width": 1400, "height": 100, "overflow": 0}, "fileID": 80812}], "fileID": 80901}, {"name": "Paytable/Pages/Page1/WildHolder/DescriptionHolder/Label2", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 80734, "guid": "10d3e1ae637576a4592d770977bda54d"}, "children": [], "psr": "d"}, "fileID": 80725}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "a1bdd48586194d0458f1428fd37baeb5"}, "_text": "Il apparaît sur tous les rouleaux pendant la séquence de SPINS GRATUITS.", "fontSize": 25, "anchorX": 0, "width": 363, "height": 75, "overflow": 0}, "fileID": 80813}], "fileID": 80902}, {"name": "Paytable/Pages/Page1/WildHolder/DescriptionHolder/Label3", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 80734, "guid": "10d3e1ae637576a4592d770977bda54d"}, "children": [], "psr": "d"}, "fileID": 80726}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "a1bdd48586194d0458f1428fd37baeb5"}, "_text": "Remplace tous les symboles à l’exception du SCATTER.", "fontSize": 25, "anchorX": 0, "anchorY": 0, "width": 363, "height": 75, "overflow": 0}, "fileID": 80814}], "fileID": 80903}, {"name": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder3/Rule3", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 80734, "guid": "10d3e1ae637576a4592d770977bda54d"}, "children": [], "psr": "d"}, "fileID": 80727}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "a1bdd48586194d0458f1428fd37baeb5"}, "_text": "Tous les 4 symboles WILD, la fonction est redéclenchée et 10 spins gratuits supplémentaires sont accordés et le multiplicateur pour la collecte de symboles ARGENT augmente à 2x pour le deuxième niveau, 3x pour le troisième niveau et 10x pour le quatrième niveau.", "fontSize": 25, "width": 1327, "height": 150, "overflow": 0}, "fileID": 80815}], "fileID": 80904}, {"name": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder2/Label2", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 80734, "guid": "10d3e1ae637576a4592d770977bda54d"}, "children": [], "psr": "d"}, "fileID": 80728}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "a1bdd48586194d0458f1428fd37baeb5"}, "_text": "- PLUS DE PÊCHEUR - Plus de symboles WILD sont présents sur les bandes de rouleau pendant la partie de spins gratuits suivante", "fontSize": 25, "anchorX": 0, "width": 1400, "height": 100, "overflow": 0}, "fileID": 80816}], "fileID": 80905}, {"name": "IntroScreen/content/Labels_Holder_landscape/Label_Holder_bigger_1/Label_2 (1)", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 80734, "guid": "10d3e1ae637576a4592d770977bda54d"}, "children": [], "psr": "d"}, "fileID": 80729}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "8e029e0c84321184fbba558d326569b3"}, "_text": "GROS MODIFICATEURS DE SPINS GRATUITS !", "fontSize": 60, "width": 1000, "height": 80, "overflow": 0}, "fileID": 80817}], "fileID": 80906}, {"name": "IntroScreen/content/Labels_Holder_landscape/Label_Holder_bigger_1/Label_1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 80734, "guid": "10d3e1ae637576a4592d770977bda54d"}, "children": [], "psr": "d"}, "fileID": 80730}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "8e029e0c84321184fbba558d326569b3"}, "_text": "PARTEZ À LA PÊCHE AUX", "fontSize": 60, "width": 1000, "height": 80, "overflow": 0}, "fileID": 80818}], "fileID": 80907}, {"name": "IntroScreen/content/IntroButtons/ButtonSkipIntro/content/TextHolder/Label_1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 80734, "guid": "10d3e1ae637576a4592d770977bda54d"}, "children": [], "psr": "d"}, "fileID": 80731}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "a1bdd48586194d0458f1428fd37baeb5"}, "_text": "NE PAS MONTRER LA PROCHAINE FOIS", "fontSize": 30, "anchorX": 0, "width": 560, "height": 30}, "fileID": 80819}], "fileID": 80908}, {"name": "Paytable/Pages/Page2/MoneySymbolHolder/DescriptionHolder/PossibleValues/Label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 80734, "guid": "10d3e1ae637576a4592d770977bda54d"}, "children": [], "psr": "d"}, "fileID": 80732}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "a1bdd48586194d0458f1428fd37baeb5"}, "_text": "Les valeurs possibles sont : 2x, 5x, 10x, 15x, 20x, 25x, 50x, 100x, 200x, 500x, 1666x, 2500x ou 5000x la mise totale.", "fontSize": 25, "anchorX": 0, "width": 1049, "height": 100, "overflow": 0}, "fileID": 80820}], "fileID": 80909}, {"name": "Paytable/Pages/Page3/MaxWin/RuleHolder/HolderLabelJackpot/Label1New", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 80734, "guid": "10d3e1ae637576a4592d770977bda54d"}, "children": [], "psr": "d"}, "fileID": 80733}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "a1bdd48586194d0458f1428fd37baeb5"}, "_text": "Le montant maximum d’un gain est limité à {0}x la mise, à l’exception du Jackpot. Si le gain total de la séquence DE SPINS GRATUITS atteint {1}x la mise, la séquence prend immédiatement fin, le gain est accordé et tous les spins gratuits restants sont annulés.", "fontSize": 25, "anchorY": 0, "width": 1400, "height": 100, "overflow": 0}, "fileID": 80821}], "fileID": 80910}]}}, {"type": "Font", "id": "992d676375ad74b4aa69318524aa049f", "data": {"fontName": "f992d676375ad74b4aa69318524aa04", "path": "@font-face{font-family:'f992d676375ad74b4aa69318524aa04';src:url('data:application/x-font-woff;base64,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') format('woff')}"}}, {"type": "Font", "id": "1c2a90bd6795f664d812d8e24ded758b", "data": {"fontName": "f1c2a90bd6795f664d812d8e24ded75", "path": "@font-face{font-family:'f1c2a90bd6795f664d812d8e24ded75';src:url('data:application/x-font-woff;base64,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') format('woff')}"}}, {"type": "Font", "id": "8e029e0c84321184fbba558d326569b3", "data": {"fontName": "f8e029e0c84321184fbba558d326569", "path": "@font-face{font-family:'f8e029e0c84321184fbba558d326569';src:url('data:application/x-font-woff;base64,d09GRgABAAAAACMEAA8AAAAAQlwAAQABAAAAAAAAAAAAAAAAAAAAAAAAAABGRlRNAAAi6AAAABoAAAAcjD5j5kdERUYAACC8AAAAHgAAAB4AKQBqR1BPUwAAIPwAAAHsAAADVt0h/+xHU1VCAAAg3AAAACAAAAAgRHZMdU9TLzIAAAHMAAAARgAAAGBjkV5rY21hcAAAAxQAAACvAAABUjEboipnYXNwAAAgtAAAAAgAAAAI//8AA2dseWYAAASQAAAaJQAANYgO6aL7aGVhZAAAAVgAAAA0AAAANg5D3R1oaGVhAAABjAAAACAAAAAkB+MCz2htdHgAAAIUAAABAAAAAZCq5PzQbG9jYQAAA8QAAADKAAAAyomOfPBtYXhwAAABrAAAAB4AAAAgAKsAZG5hbWUAAB64AAABVgAAAqOP47cYcG9zdAAAIBAAAACjAAAA6gmuCg94nGNgZGBgYGRw2qopUBXPb/OVgZv5BVCE4ZrxmSgY/f/i/yymN8w3gFwOBiaQKABlwg2seJxjYGRgYPb7n8Z4ldnx/8X/h5neMABFUEAKALN8B7d4nGNgZGBgSGFIZGBlAAEmIGZkAIk5gPkMABaxARAAAHicY2BinMk4i4GVgYGpiymCgYHBG0IzxjEYMeowoAJmZE5AZFAwkFJgOMXs9z+N8SrLJ4bTQD4jSI6Ji/EVWI4JAAIhC7QAAHictY8/L0NhGMXPea9KbAYJm6S+RFezGMyW7iYfgARfwGSQmOS2Bh0Mlg4aRoPERpRwaVoMN+2VtI2rx3G/g3d4z/v8eX/nedBGcbjqy2+WUeIuSqGNiCPl4UDjMOday/mqBqyC3FfOcwQuqs89BLy7v+J6TwkrmGZXY15qyGeljPXKa3WsL2yqyzPdct69f3/XrBuIomVsc9OMurkPeuS9RrxQj2/65rFzR2Yljjsa4MkznJjxaV1XFmrmpvpibnaizNq3f8ZT6yFm+eHaDRZCC0ue6Y5Xzjc9X0M/nPGODYlbmoQV71N2/P/+aeEfa4Kh+bH5O5gqfO31C4zky5J4nGNgYGBmgGAZBkYGEPAB8hjBfBYGAyDNAYRMQLqO4QDDqf//gSwFCOv/4/+L/s+E6gIDRjYGOJcRpIeJARUAJZlZWNnYOTi5uHl4+fgFBIWERUTFxCUkpaRlZOXkFRSVlFVU1dQ1NLW0dXT19A0MjYxNTM3MLSytrG1s7ewdHJ2cXVzd3D08vbx9fP38AwKDgkNCw8IjIqOiY2Lj4hMSGagIknBLJeOUAQAIliEBAAAAAAAAAAAAAAAAPgB0AQgBdgIIAmQCggKuAtYDNAN4A6ADxAPiBB4EXgScBOQFOgWKBdgGHgZYBqQG6gceB14HjgfQB/4IQgi8CQYJUgmcCdYKHgpkCr4LGgtEC3YL1AwODHAMxA0GDUgNpA3sDjgOgg66DvgPcA+wD/AQNhB6EKQQ7BEaEUIRZBGuEfoSRBJ+EsYTDBNmE8IT7BQeFHwUthUYFWwVrhXwFkwWlBbgFyoXYhegGBgYWBioGO4ZPBlyGbwZ8hpUGsQAAHic7Xt5dFx3fe/9/e42d9+XWe7sc2eTZkazSrI0Wiw5smVbki07XuQtJPESwIlJXiCQxBwogdOEJoE2lLWlvC6naZu0nJc8SiENj6YklO01nLxDAykNCc0hpAm8Fgho3u/eOyPLTvJO+e+dd2wdH8+9ku7v+/t818/nd41BjOz9E3DgDzAdy2KY1uy0O23TMq04oAxIU3k379LoJro2dJqiqUzavSKRNOQxS2cIgA8x+HgzKcxmMwupKqnkcsVMA2zb/dGPK6xmqBLFaMz6j9ZyhubKv8olEzjMJyQp7TgNDMPQ2uAz4H3wOUzCEmhtb91gibw7sGPTdVuaMIYFOZeN6oYuKU8iE4Z4NZuN+JfwO5ypFUdX6/VIZGLtiIiWL47uGwmuvLXw3j/Cp+AT2Cp2HMNA3Wr7m0LP9/ZKiyCfdjO0t6L3MR/svNXspCXgf+w0O1SGcj2zOu0pcOFzJ3iQBOp0JyNCum41ugD8q0YOh+NFk+JwriAJOMtHklSSaI+YUQN9pCtVcaggk+KaMyIM5xUS8ExKE0kuJTBssc2V0Wc2LTDaaNX2nxLa7chvx/kwtxYGXBjOgOsEzTUiqVCM5tmiGVUA+AgtmQ30Ad7jxmN5ieFb4ENuIlKUQmINADtcjXM4gd/tfawkOJzC3xw8QvtCmc7MJ0BiFvkE9H7ZOwNexYewOQzLiR4+/vabHQ8Db6vIJQilLvBB6YJBvJi0hyJEKHp38m4mjX4T4QL+O39wRgBelIj4WCQiKYtjeojkOcNxKUObP2+IB2YcBvk2QlDEbE1gUwk2t5sAcUOFtD0a4hb3b32we9rYp3hRpNh2pN4sV8MqTzJGjWWlkqbl5s8uXp94VwhFgMUJDn94trI9CWum2MyRoFbspAtH0dZQpP+89y/wE/B/YzFsGOtiO7D9KOpUb0+WSdHQ2yvaQrBhbxd44Nx+GOYrAAy8PYAFb+dpl/IRaYNNn1/+WUnXDJol1O/mzaFMnNF43HKzKGHYsZFPiMWhCsxMlEqW0W5MdhfAieDO5LLu38hmbuViuJGiSFlVYmDf5ou7gEvi4Ug0kjZ6WC06XinzEYVINmsIueiuaVBoxRzcJelOc2l7MhEKHV//g+AOc2ppMZEIMdUa+PaQxOKmZaQI4KzfOCQGFzh0MAzHsN4PYQb+PVbARrAxhE8XNOqmnybtVrOPxQARN49QwREmbkf3Egp5Xct7EDTqO/CTq/v2CPKpkJvPVSISxOtOPFvIlkXWHtq5MLackRlxNftBxWFCnJy1xyWciFoKJA9cd93+6xeFxpRjaWbZMoe2OYJcKQkkJOpVHB+aXcqtzizP6qWY46TWzywpioXsBl4twb7k15LXVJJLKsclpQLF/Cu9r4Nj8MeYhR7UNik/EKigCA5iAdSMSEgrD5UMw7KzLIxFr2224rHlFfgC0Z6A7M7FG29aWVLhaAvHz97wxfuPH0M2gd7XwFvhi5iBYRbltjtdiB5XARshFTxzf8k2cxtPXFmp+A/8nReWlnQw1sLJs9d/4f4Tx/3cxJ4Fn4Sf9fwC3LwIDd+8Sc/KoCy1O/UgUU1rUJb6OSuB/pogEgpDHOKNSpQblbMCRaArxqb4YZwuRlKmJjkoQWlYYTMsp4dCdolIuYkCD5qELMZlPGeiLOQ0kRKnbQKiPwxdUYdRDCnznNVSo3FaI6XhYVcihASK+SQJvNwDqALvAX8FwyiuMEDRdWRiAHMG/fVAmQLNFjIdgQQssxGkHVgExIyg8RSfljmrEtXEcdNmSLAm8ryq2o4rmMR4K8aDP06YADhOJycD4rjAWaZtxXEc8KIyFo7KEN6asyxX9uvby717sR9hv4+ZGNYZNDYJBJUOrT8FXCFoZlDYURNJmBDLxUh+cqN5EdmVOAMJik9kdMPf12Pg3eDtmIrqpe+OYAf9/YF6+/qzUmDtB84FO/kazXBSYBhFBFZ78dv7J+xZZJe0ya5+wyU3d9fJS9uo/7v/Cpvwb7HU5nqGKlmwq+BrEMhffjXfL02EmpKUHdoWIznHx8mole/XJ/AhkCf8GpO0SD2v6PvM+Wj+sFIM5TbqjLcu7H2/9xIKgEcxxZsdrArwK8GFCom7GdMvicih9aAtPPrWc3F7L0dLu3ZuPbHkOLt227jIspkuxylqvpxKMays8NTjn37PrmE+Y9ui/v3f/dCeFTgC2jh+5bZdrVZ7dNfSZDfYN9X7HpiCj2GZIOf9DhRU8Y0K7W86aFX/wLUNFNmA4sgwzuKQBgQkxkZSPOCKkKklE079CHxIpjXVjZOpqdQ9kfm8vl2q2yjk8xI786pSEDNZObLsx9HPey+C5+HfYUPI72bQHvL9kQCVQZoy9AHiKBQ9aySvkIL7+bG1D0xE5wBJOw8e0lgtnEwmhlpJGVBXbD8ydTAn8Hx5eC13g5UYdaMEpTMHwjZXOj+zkKtk0k1b0YqmsTg6SuCt5ePDk832UrLs2/MfvRfA5+GXsbYXh745Xhx6joh7NcBFQw2qPA0zsO815pngU2x38sTVR3kqqcYO0QJfyeIwYuuKTmtivhViJDXi+IYS3cml8ZWUyIjt8S8c7E4SxLnTjQlITOupdqiYildJiprZAhOxcLlRCAyeqlZQ6Z5Zyu1sTCNbX+r9L/Ac/BpW9eptECwIG8vcCNSWP28M8PQ9i8eBV9PAvDmp0jTPiXpUYCrtEI2PtjIytXsimYjj5DUnLaklKwR/F91idMuGea3IJ+Mz+9zhWIxY3pfVtaIm5epEmEumrPTnPieGwoZpJ9afAzg4OrPNj6vevyAsX0BxXX0NliiQPQDpDOUb6YfWIOAl2DbBR9ipyeMnT/Fk3nTWWEkyozkAhitkYqQWNg2G4MbWbq9nJwTtBdw6e/1XD81vJYi3naqgztbVEtVmcagYIgrFWH1sJGFIXgDI/FWyTQgH8VuCnPtB72Vk2xexJFbyor7TbtS9wu61uiDyArgGGU/lqcBkLnvN4WMrOVdVS9FigaE1pTXUOOBm05wyP3/impHaM6GkWo8WaaYJbrh//87xLas7nbghx97nhJzYyS3H5+YJ/OqrV0TrzkZ1LKil/9x7EnwD5UASWeL2RyVkDLy45njTMhqu+fG190+YYZsVzS8z6XBZykC9lGdwXP3tt0b+i5WA7/TjnU4lYq6z/qJQL2yNbsFT1fKIxEx86z7ghTma4p/v/Rj8GfJNFMthFQ8By4+SYEihMsHWAzSsvrvyVIfqjye7Isl5kqw3Vg8ss8wWyRyuU8xk98SRdlPg7w6Fw3YKx2vVDu5kSFKS0+C0Apd27RhpvHX3FVkmdWrm4PTM7NbThw8Va8VUrZP/RSoeidqO55dv9n4Kvo3s8v3i95PAiMA9XgH042gwULattj8im+ABdXv14PG+ZwpF5Bm1OXTwgOeYufmrrqmNPMMkBo6ZXb0j73sm7nie2bJ1wzFv2sObdwWOCTidCksBr7q0m2j/t+5SeeNWAx5yE/3LTMzrPNDrpSCC1jEx9w276aXLvV53fYNVL2q1r1ne63vYIliFJBbxZ4oLU6lfQdoDugI6+amRhBqLlgpKslbjQ3rst5YTsiISYEcs0XLUeFxVeWuimzGkRFq8oqJ2Ruuqzxmx68Fp8CDq7Tlvhdd0d/p1Gn6xfPigwDKSpFsHz8kMJUaZsfLhAyLDSLJqHXybxJC8w4ICFQoJYkU3edSFQDjSsOnPerekCuIw3i07XIuF0B5vw+bB10APC/vZ3i83fp4Zet+ETvtj6blcSR8vlORUtSbQRuzmk0l/g5idKZTUCbS/cHcip4rxjDg2sbE/L06uh99CueTXklbTHTxwI6FoqgJaTbx5kQ9vWZHkbEekWItXhg9PzJOh0nA32h2Z2jGbrJByLlPMjIA7d48piJa2xeTIRFQprS6c2Huwrok1y1n/QWEMedKRhaTj1FEc/bL3OPwsfBCbxpY8bh7wR7rPpXwnisCfnDsb9c5PryDFEENB426/6KHfybcQTkFyoaxrgme7plYfuf7IzkVdP7y6+PsuqbeitSdPbuuMNiRl/G1fuGf3bkscG51rJguNETcnCXsnshkrbWi0IIkkIRyjcdEyMyFGMaoSAGcPzW2d7p67+YOaXq5SYq48MeIgZvCZU29Zvu/I2gfWWmhIqo0c3H0AIOJFRCWjnE9mh2+GaUEoFoY7N0K/jj+NsH8UPokmJ1TY8k2t30X8PmiphtmPY7RHqq9GoGTyyFXmqhPjuXyUFOhkhDeoJIhEp9ISpCAhLSVT7GjNZr6zh4RFh9x2GOv9MmOWUlmJ4rRd60OswEAywYtVF+DEe/O2kROfgmePHD8qBbX1Z73vghfhQyjW0t5c48Wbl9V9xIPs8k3JoRao0x5F9tzkqQBukpUjYNvC6ZOdjq7u2Mdzx4+djTdbBAtGEJ9Rqq7ryJLGlLbSMA5umd9ZKCdjTrN+fF+rcN3BnTshnc5EZ28yFQg/rKYyYBm/eoTfmpVMr8/8sPcc+C78vFfvQX2D2XW6eD9MvGzoXKCnF0jbrsVtihANy3ahRAKRC9FCws1aBk3H4tGYP8zQs9PHEO9y4mdOwM+f2XXFxFjOtYhEMmW0m2uHJiZxWKsuBEPMMdSryTPX/d4fHtmH/Pdq7xnwM4SVjbDy9CTTUz/6pQetHfxjUbRfhjJpUD5brZriqY8tS4mEE9O0BKnWr7WH49P1uupst+FDa0c+/JaHr2FBSkomndpvvO9aLqlrkfonHvihuXP38Y8E2szT4Jvw/g3fBGnqz015b4S68Kl/08MB/VhCF2ICPdp9Wo9IzNjCZEPi1oaywraFdJyk/yK4+baVSDJBEgwPPnZ7bSgmk2SukozgBL5vbE8yTAJQOZobTkZRQDP5vBI/ccafOZ8ETyJ7isi4/tIXD93IwC1gYIwYzHngHd7CCVohR1txNHuXwrzIhGi2VHp4pzfTjS60R0EI5+HaENjirzz2Fdc0XInZ9u+fquhRhxYg7g4j23AKH9bmuGBm82LkJ/DPsdHAloviwKtlYl+xgs0KCMQpv7i4/tzu/QSy7IdcdlgBoR2LR65po5B40zVXXzllqrYYLhZIgDgzTWd2GEaIjtnjoiTwKZZN5OlTuSEwYkcpWzs2N+cHyCf3719bmapCPJu1CJakcXj16pVTKJS6dUgSZTviBpy49wrC798Rb+sEk9xASUNUod1odzbf2URnAiCN/mUsJskaGoI89awcwlNdXIiLm+4wegAzDhcsj+/0QQe3/Fl3VmZV0xPMXplS8djKZzubbmz7TgC5OAIJtqmU2b4HNuzGXkJ2m5fYfcHKWEJQVK1lhSmKvGAEWvbh5lYRrWIKXPzCKtvQM7/ZexY8jJ4ZQ0wymN4CMTYY4fokAE2W7+wUFlMxN5rOhASK48vbLYrXm+GIDPW9qzF38a6TJB6LGrnR6Kv/NsehpcJJZ/1L99zTtxuG0RooRrTOhrhHBezM58zAi4b8pfxxY4ci/KZxtlwPewyaIzRjbnsu+1GJiXKJYtlpuDhCGAeDuN7kGcBy4JHwewtVHIYjkUjaiGxfrg59ymg0EANMx2oNnO2oLWIQ5pt8Q49uCTD/Se95cB18xOf4GxZ5Kr3flC9K+bwb9uBXOrav0VuI7Ybe42V5a/ZINxOJ4WzgB0btS/Tg3ffZVxwK0jsimWF1HOvP90/DO+HfYBNeTuEDZtbsghbCa7DYxZqpPwtZdX9CQZcJVOzhTZYIRmfiIgRgS5gjGWNsjKZEIRmSQoDb0gzz0B5pbJELaTkEiIwgsjOT0kyISkdg59H1d4c5MSWK1rvCbYf69P6kRCiWldD+rX4+NZVEiOkZmX/vfb93W5qxQhRhdac++sjNMYeEci7Yw097/wgx+Dg2MsAtGJ6si7TdgWQQFIkLZSwVEwXVrIuxpKxGFNsSUi0vonFJwkkWlSiFwYecPXHdETSiXXM4wIBb/nJ6UtRytGFPzt/7zrec5JSqF+zq7n2SNa1eId6614RqdP/4V+54Pm9rBZFpB7zO4zWvotjUsDzKgH4JDfi5z6fwjMfyLlZTHnvL+JgvpyiLu7bOrCiKs2uXrQocANlJlvcFlSTnCSrEtdd++vxShc+atiDc+McnPUmlXg0BeGBuZweNS7uXJqeQDT9BWAH431AOIvA67sXlfGP28pDC+43N/Y5lsUwQ8WzB4FkA5xZOn+qMmvy2of1NWxwtlxhm4i/x6NG3B9E9+4vzjZSMev/WuRvmb0h2wa23HqVLpVY7H+Dwcu92hMMjyIIxDCP9wxjPhArMuJ3NCpMHiukF/+sBA27mFYbX7VKpJkYlqJpTVX0zTpEzQxR95nWQ2hM2gWYrMotTRKhSfPDcZtTuvNIY5mUT7r0UOczH7kmIo3kg5c1zORG4VZh5Xfh8ooL3pyY0GDzERS3lbwHgByjqIg8RimdOjo5Z8Zsi+cmfxtixVYaKp8KxpEXzHwL3DKB8ZyehISjntt5w5141UQf/9YML5J7xdNnXOZ8Hj8O/9npLTvendkSJGnVEGgzdq6lo2Pc5EiocCEX/5MabWFrNQLzy1Nd7t84e239y/7DDxm0t8u328NpViztI/4Bmh2mqTjqVcn/rSwdmZ4/srI0KMElFczG1dO6a6dPJ6fktSqy6kIBDpuYWODERL8by2hziM3Lv6+Bp+HB/hgnoRnNwjlbvJ6Tncz0owngfw/ebpD5Xo0gCAEpR1Yn336HrvMCxAk9XykSI5HgqSo56ah4LbnxvGKAsJWIwBN1SCacBSU11ZREAAkCjHp1YLa3/yvVVvdmgRvwYvAS/4ekYVhqtNRjGUffpoIjSgkqBCJBMU49NLVmVnbxubo/qVNKU7SHLMkOslEnZtry8pHbnfxucGhOjHSMfu/urx/A3XQO4FYbnWcNMx9ef/+THEQZ273uQho/6uiXsL4XTRkcdtDncH3CDr2QoZaESAyABIajfVJbTKYPkb7uOT8Yj8Vw2xHAsuBkMqcKMnYyOv3lbS+DfA8oabbi2E19/Yf0HcYNLxHXryFGfM2d6L8IfobVXPM2mhWo5Aj0ON9gd3T9HQHGRACgyRGAEqe6fA3mkc7N1KGIuELG8mwWsKLIsrkaEuIYDioBU5sosTZEwxEXTCQBiuCl3Urm0xIejNnvbdVzKMW3HoUNwSUyitgTN8jAtgfPhxSNXJUAISjprx+3miL02mTKv/UWYwgGIJhNRSPPkE8Mz+d+c21HQElFN0f2NapwT1+wJ4sCPXAhyeaOIfPtU73HwCqqrrsey/MF80KT8HuClZP9sJJ+uAi8cC/Xq4rIAoBx1RCkcaR6Ip/VYOCp+epEhC1mbIw5+VM7ltoT37Tn1p3FZTTRSOJnPv/kd6fzkpPrUnzay89NA+PZTdrIQnDf8A3gGPhEoCBv0ut8tB1JdULf6hx7fE08fvzk/12q0R67W9DFFY4sFx2ASsujkXNciGapejvBw1zN/eN+OcL1ULA/xt5//4qTAC46Zj167vHLl6ilzwnUiBa2vU38F/pU3owf6Qf4Cb9g4HbiUMNw1kV1eMBQ5xNUbP9HDEg4gD/evmi39M+gKjejn9kadOI54Mfjw8RFKoYoFQYR4riLRvEa8+3b3QN5nD2SMyeVUje8EvfhXvSfAn8PnvDMgTzN3/YMfP3I6LdeL/kZ/IEuLIAMDu8AhtJxu6hwlwaK3KJtrVWih+z7Ec0cXeJuHY5RE3KiHGTS9AAAgxCVvUTD9PJjOUgJJLiNDAFj/cena5WxwboD3/gYIcN07l9M2aUZ5F/cIgmU+W+SI1kSUg5AIicwYQVL1hp4ER1+diWbyAiXJnfOHtoH5mMgPKawd7K3X+zvwAPy+V/09LdT0fIsH9ayviAZaUbvRDE7NO6gk7w32xhJodIFFO5Hwd8eyk3dUw+hbYjYkhMKMQKHtwc/094e+gMS4OXX6+fVHshQQKHyPt0OYm3Hf3nnT3mCPvXX4DXAL/hTip95Z4UDV2dBoveLqAqDV7o6ynARI5560JIanJRWHkayhw58r1QNKuZqmyPQS4oKxHWFH4DWZAf5ZXu8FuB37OKajDr1JArsggv1i+olv9U+9Gn/xO4vBuZcYYgfnXtDbR3Dy5fsDfh3k8Ucx2ddzg2Tse6SdzG1faDqSkAgRicnGuE7hH8vHY4ulXEIgcDtsyuTsZT3lsp5yWU+5rKdc1lMu6ymX9ZTLesplPeWynnJZT7msp1zWU/4/1FNeQpzqe/B/ev2nkx68UeO/+WcOVJTBaINytG9Z/wWcv97qFoWcrBvbHCMkKwQnZiMRfmPeOXrlu5YOZWibj6bTNEMpY9X0f+SKf/Cbo4rVMvPRznRF7U4dmh4MQZ+7947332hY0BuP1KFPJbCN9wL/X9Fb1nv/A/wJfNV/z86TISiP1g0onfdOoP+PhWb1tl+7BrUCJSpIgj0LblgUJ0mCVXjrzGinpJdGKYSZEIUCuLKS27EzJ9AcBLG0ZkH2HbHZczPlog25aMqi4jMz5ydnIeTjSQdNcbfNH5ETVbtiQ6CxhPf6MuJ7D2Avw59i8eCd1Y3p69J3VtHs24gjB5khr1LxlMEQFI57o08nwQMBrKDoZhVdFSlGD+H5/dXfncsuNLeMC66uZyVmIuB5j4E/guvYsNeFPbzRFqnN/5+hHcRrnvLfqfG/U/ew+g181QNB6BKkD0Jn9AIIArH/AgaRbOlhH4FSaYDA7MztFxAgb72AgBriUd0g8BC4Ff97f/9me2M8D7Y8CA3/rcNXlDhFpXIhSFg7C040wukJikpnSeTtpUpoHL8X12VFN+MiXa1RhKoQlCjJghRhmbGmhKcx3NdGvoR/1ddGtvx66shAj6mCQI75z6slxEC44QPd5tdQT+DdA4mH7is80Ofq/4w/hfrc/K/J1t9Q/vr1aPz+i7QyIbKhlf1nCT544A01tf8DOnixcQAAAHicnZA9TsNAEIXf5g/RRIiaYiQoQhPZaYJcIMVWcgCK9E6ysi1t4sh2IqXjBhwEiY470HAFSkpKap6XDQogUWBLO9+O3sybWQBdPEDh87tC4VjhBM+OGzjCm+MmztW14xa66tZxG6fq0XGH+VcqVeuYt7WtqllBcO+4Qd8Xx01M8O64hTM1c9zGhbpz3GH+CRFy9ttxygwJUlTs2MMcl4wDePAxJM2oEIyxpMqQQmhWVDwN/z4zI0ty0Ke0N81Ya7c8F1Qiyte7IkvSSnrzSxl4/lBmOxkvMyOhLiptTF9GxojVlFLoUhdbvWBlaFsLV8vYNCVFiEkbnoYRYZbIJCtTieJyExtmbuiaUGAoKXjVycbEhP0m3/cI7GZ/mwQH74J6ajd0ID/dA7vcfux9v98j1zVfA0/tQCXFOVa08vlknrU7sJ3qoszylfh9z/P/4fIBgK543gAAeJxtzec2AgAAgNGrspUZsrIqu6zsTUbKTjbn9Hrew1vR6bd7zvf7E1DzG7fkP5VqdQKCQuo1aNSkWYtWbcIi2nXo1KVbj6heffrFDBg0ZNiIuFFjxk2YlJCUMmXajFlz5i1Iy1isvpetWJW1Zt2GTVu27di1Z9+BQ0eOncg5debchbxLBUVXrt24defeg5JHZU+evXj15t2HT1++/fwBfWkVvQAAAAAB//8AAgABAAAADAAAABYAAAACAAEAAwBjAAEABAAAAAIAAAAAAAEAAAAKABwAHgABREZMVAAIAAQAAAAA//8AAAAAAAB4nH2SMUxTURSG/3Op2nQwJi0ttI0TkYooBiOlxbEBrBUMah8txrCQmJgY09nZOBlHJ2dnRkPi4M7kwPzmTg5NnJ7fPWVw6nDef85/zj3n/PddmaSCbmpV1tnuvVBeORhlmWLG3p6M3sFpGpELjnlZOPPKNb3RSJ/0WV/1Td/1Q781sZLt2bGN7IN9tC92amd2bheW2jiUQyO0Qy+8Du+nNeF0VhXzNrKJ+tgVFdlzgW0XVVIVpg6aanhBFb61bEwc2aCSxxN2LHKiClfDYu6qivCVLNUiWCVby85V52zeT81jZa/4pQXQqy67XaNbiaqxV1XA/3e5rhW8e9lfNbOf2iTbwtpM2gK75PbIJeQOiQfYkNwRmNMS2XV26YAHYE634JeJtsEd19iA38WCmnwT95bIdnz36DVR0qJnO7twNiEeEA+JqY39vfeK5qhN1QUTMPZJORG9yE+5VWp7WIHsH6Y3YEr8j1SbYAduF66H34c7BGPnguvoYlMdca+GKxv79jmftY6iOO/A/1FUv+bMRPuXTKoNP5WqD97w7Wfdb1Q2646Dn44Vxqud8z85r7IqvKkqr6POhGU2va07ukvFfXQ80EO2aKK3pba29IgpO6h4jL4neqp9PUPlc71kx4QbGGioI736B0hM+jR4nGNgYGBkAIL7104bguhrxmeiYDQAUEgHEQAA') format('woff')}"}}, {"type": "Font", "id": "86e7a5cc0847bd94cb892fe593749725", "data": {"fontName": "f86e7a5cc0847bd94cb892fe5937497", "path": "@font-face{font-family:'f86e7a5cc0847bd94cb892fe5937497';src:url('data:application/x-font-woff;base64,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') format('woff')}"}}, {"type": "Font", "id": "a1bdd48586194d0458f1428fd37baeb5", "data": {"fontName": "fa1bdd48586194d0458f1428fd37bae", "path": "@font-face{font-family:'fa1bdd48586194d0458f1428fd37bae';src:url('data:application/x-font-woff;base64,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') format('woff')}"}}]}