<?php
// Test admin login credentials
echo "Testing admin login...\n";
echo "Username: admin\n";
echo "Password: password\n";

// Test password hash
$password = 'password';
$hash = '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi';

echo "Hash: " . $hash . "\n";
echo "Password verification: " . (password_verify($password, $hash) ? 'SUCCESS' : 'FAILED') . "\n";

// Test database connection
try {
    $pdo = new PDO('mysql:host=localhost;dbname=game_platform', 'root', '');
    echo "Database connection: SUCCESS\n";
    
    // Check if admin exists
    $stmt = $pdo->prepare("SELECT * FROM admins WHERE username = ?");
    $stmt->execute(['admin']);
    $admin = $stmt->fetch();
    
    if ($admin) {
        echo "Admin user found: SUCCESS\n";
        echo "Admin ID: " . $admin['id'] . "\n";
        echo "Admin email: " . $admin['email'] . "\n";
        echo "Admin status: " . $admin['status'] . "\n";
    } else {
        echo "Admin user found: FAILED\n";
    }
    
} catch (Exception $e) {
    echo "Database connection: FAILED - " . $e->getMessage() . "\n";
}
?>
