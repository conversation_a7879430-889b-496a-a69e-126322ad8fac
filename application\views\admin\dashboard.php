<!-- Welcome Section -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-0" style="background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%); color: white;">
            <div class="card-body p-4">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h2 class="mb-2">
                            <i class="fas fa-crown me-2"></i>
                            Bem-vindo ao Painel Administrativo
                        </h2>
                        <p class="mb-0 opacity-75">
                            Gerencie sua plataforma de jogos com controle total sobre agentes, usuários e configurações.
                        </p>
                    </div>
                    <div class="col-md-4 text-end">
                        <div class="d-flex align-items-center justify-content-end">
                            <div class="me-3">
                                <small class="opacity-75">Último acesso</small><br>
                                <strong><?= date('d/m/Y H:i') ?></strong>
                            </div>
                            <div class="bg-white bg-opacity-20 rounded-circle p-3">
                                <i class="fas fa-user-shield fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card border-0 h-100" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
            <div class="card-body p-4">
                <div class="d-flex align-items-center justify-content-between">
                    <div>
                        <div class="text-uppercase fw-bold mb-2 opacity-75" style="font-size: 0.85rem;">Total de Agentes</div>
                        <div class="h3 mb-0 fw-bold"><?= number_format($total_agents ?? 0) ?></div>
                        <small class="opacity-75">
                            <i class="fas fa-arrow-up me-1"></i>
                            +12% este mês
                        </small>
                    </div>
                    <div class="bg-white bg-opacity-20 rounded-circle p-3">
                        <i class="fas fa-users fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card border-0 h-100" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); color: white;">
            <div class="card-body p-4">
                <div class="d-flex align-items-center justify-content-between">
                    <div>
                        <div class="text-uppercase fw-bold mb-2 opacity-75" style="font-size: 0.85rem;">Total de Usuários</div>
                        <div class="h3 mb-0 fw-bold"><?= number_format($total_users ?? 0) ?></div>
                        <small class="opacity-75">
                            <i class="fas fa-arrow-up me-1"></i>
                            +8% este mês
                        </small>
                    </div>
                    <div class="bg-white bg-opacity-20 rounded-circle p-3">
                        <i class="fas fa-user-friends fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card border-0 h-100" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); color: white;">
            <div class="card-body p-4">
                <div class="d-flex align-items-center justify-content-between">
                    <div>
                        <div class="text-uppercase fw-bold mb-2 opacity-75" style="font-size: 0.85rem;">Total de Jogos</div>
                        <div class="h3 mb-0 fw-bold"><?= number_format($total_games ?? 0) ?></div>
                        <small class="opacity-75">
                            <i class="fas fa-arrow-up me-1"></i>
                            +5% este mês
                        </small>
                    </div>
                    <div class="bg-white bg-opacity-20 rounded-circle p-3">
                        <i class="fas fa-gamepad fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card border-0 h-100" style="background: linear-gradient(135deg, #fa709a 0%, #fee140 100%); color: white;">
            <div class="card-body p-4">
                <div class="d-flex align-items-center justify-content-between">
                    <div>
                        <div class="text-uppercase fw-bold mb-2 opacity-75" style="font-size: 0.85rem;">Total de Transações</div>
                        <div class="h3 mb-0 fw-bold"><?= number_format($total_transactions ?? 0) ?></div>
                        <small class="opacity-75">
                            <i class="fas fa-arrow-up me-1"></i>
                            +15% este mês
                        </small>
                    </div>
                    <div class="bg-white bg-opacity-20 rounded-circle p-3">
                        <i class="fas fa-exchange-alt fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Recent Transactions -->
    <div class="col-lg-8 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-history"></i>
                    Transações Recentes
                </h5>
            </div>
            <div class="card-body">
                <?php if (!empty($recent_transactions)): ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Usuário</th>
                                    <th>Agente</th>
                                    <th>Jogo</th>
                                    <th>Aposta</th>
                                    <th>Ganho</th>
                                    <th>Data</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($recent_transactions as $transaction): ?>
                                    <tr>
                                        <td>
                                            <small class="text-muted"><?= htmlspecialchars($transaction['user_code']) ?></small>
                                        </td>
                                        <td>
                                            <small class="text-muted"><?= htmlspecialchars($transaction['agent_name'] ?? $transaction['agent_code']) ?></small>
                                        </td>
                                        <td>
                                            <small class="text-muted"><?= htmlspecialchars($transaction['game_name'] ?? $transaction['game_code']) ?></small>
                                        </td>
                                        <td>
                                            <span class="badge bg-danger">R$ <?= number_format($transaction['bet_money'], 2, ',', '.') ?></span>
                                        </td>
                                        <td>
                                            <span class="badge bg-success">R$ <?= number_format($transaction['win_money'], 2, ',', '.') ?></span>
                                        </td>
                                        <td>
                                            <small class="text-muted"><?= date('d/m/Y H:i', strtotime($transaction['created_at'])) ?></small>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <div class="text-center py-4">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <p class="text-muted">Nenhuma transação encontrada</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Top Games -->
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-trophy"></i>
                    Top Jogos
                </h5>
            </div>
            <div class="card-body">
                <?php if (!empty($top_games)): ?>
                    <?php foreach ($top_games as $index => $game): ?>
                        <div class="d-flex align-items-center mb-3">
                            <div class="flex-shrink-0">
                                <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                    <?= $index + 1 ?>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6 class="mb-0"><?= htmlspecialchars($game['game_name']) ?></h6>
                                <small class="text-muted">
                                    <?= $game['play_count'] ?? 0 ?> jogadas • 
                                    R$ <?= number_format($game['total_bet'] ?? 0, 2, ',', '.') ?>
                                </small>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="text-center py-4">
                        <i class="fas fa-gamepad fa-3x text-muted mb-3"></i>
                        <p class="text-muted">Nenhum jogo encontrado</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-transparent border-0 pb-0">
                <h5 class="mb-0 fw-bold text-dark">
                    <i class="fas fa-bolt text-warning me-2"></i>
                    Ações Rápidas
                </h5>
                <small class="text-muted">Acesso rápido às principais funcionalidades</small>
            </div>
            <div class="card-body pt-3">
                <div class="row g-3">
                    <div class="col-md-3">
                        <a href="<?= base_url('admin/agents') ?>" class="btn btn-outline-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center p-4 text-decoration-none" style="min-height: 120px; border-radius: 1rem; transition: all 0.3s ease;">
                            <i class="fas fa-user-plus fa-2x mb-2"></i>
                            <strong>Gerenciar Agentes</strong>
                            <small class="text-muted mt-1">Criar e editar agentes</small>
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="<?= base_url('admin/games') ?>" class="btn btn-outline-success w-100 h-100 d-flex flex-column align-items-center justify-content-center p-4 text-decoration-none" style="min-height: 120px; border-radius: 1rem; transition: all 0.3s ease;">
                            <i class="fas fa-gamepad fa-2x mb-2"></i>
                            <strong>Gerenciar Jogos</strong>
                            <small class="text-muted mt-1">Configurar jogos</small>
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="<?= base_url('admin/rtp_management') ?>" class="btn btn-outline-warning w-100 h-100 d-flex flex-column align-items-center justify-content-center p-4 text-decoration-none" style="min-height: 120px; border-radius: 1rem; transition: all 0.3s ease;">
                            <i class="fas fa-percentage fa-2x mb-2"></i>
                            <strong>Configurar RTP</strong>
                            <small class="text-muted mt-1">Ajustar retornos</small>
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="<?= base_url('admin/reports') ?>" class="btn btn-outline-info w-100 h-100 d-flex flex-column align-items-center justify-content-center p-4 text-decoration-none" style="min-height: 120px; border-radius: 1rem; transition: all 0.3s ease;">
                            <i class="fas fa-chart-bar fa-2x mb-2"></i>
                            <strong>Ver Relatórios</strong>
                            <small class="text-muted mt-1">Análises detalhadas</small>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- System Status -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-server"></i>
                    Status do Sistema
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-circle text-success me-2"></i>
                            <span>Servidor Online</span>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-circle text-success me-2"></i>
                            <span>Banco de Dados Conectado</span>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-circle text-success me-2"></i>
                            <span>API Funcionando</span>
                        </div>
                    </div>
                </div>
                <hr>
                <div class="row">
                    <div class="col-md-6">
                        <small class="text-muted">
                            <i class="fas fa-clock"></i>
                            Última atualização: <?= date('d/m/Y H:i:s') ?>
                        </small>
                    </div>
                    <div class="col-md-6 text-end">
                        <small class="text-muted">
                            <i class="fas fa-code-branch"></i>
                            Versão: 1.0.0
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
