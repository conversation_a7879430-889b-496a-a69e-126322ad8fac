<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">Total de Agentes</div>
                        <div class="h5 mb-0 font-weight-bold"><?= number_format($total_agents ?? 0) ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-users fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">Total de Usuários</div>
                        <div class="h5 mb-0 font-weight-bold"><?= number_format($total_users ?? 0) ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-user-friends fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">Total de Jogos</div>
                        <div class="h5 mb-0 font-weight-bold"><?= number_format($total_games ?? 0) ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-gamepad fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">Total de Transações</div>
                        <div class="h5 mb-0 font-weight-bold"><?= number_format($total_transactions ?? 0) ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-exchange-alt fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Recent Transactions -->
    <div class="col-lg-8 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-history"></i>
                    Transações Recentes
                </h5>
            </div>
            <div class="card-body">
                <?php if (!empty($recent_transactions)): ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Usuário</th>
                                    <th>Agente</th>
                                    <th>Jogo</th>
                                    <th>Aposta</th>
                                    <th>Ganho</th>
                                    <th>Data</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($recent_transactions as $transaction): ?>
                                    <tr>
                                        <td>
                                            <small class="text-muted"><?= htmlspecialchars($transaction['user_code']) ?></small>
                                        </td>
                                        <td>
                                            <small class="text-muted"><?= htmlspecialchars($transaction['agent_name'] ?? $transaction['agent_code']) ?></small>
                                        </td>
                                        <td>
                                            <small class="text-muted"><?= htmlspecialchars($transaction['game_name'] ?? $transaction['game_code']) ?></small>
                                        </td>
                                        <td>
                                            <span class="badge bg-danger">R$ <?= number_format($transaction['bet_money'], 2, ',', '.') ?></span>
                                        </td>
                                        <td>
                                            <span class="badge bg-success">R$ <?= number_format($transaction['win_money'], 2, ',', '.') ?></span>
                                        </td>
                                        <td>
                                            <small class="text-muted"><?= date('d/m/Y H:i', strtotime($transaction['created_at'])) ?></small>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <div class="text-center py-4">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <p class="text-muted">Nenhuma transação encontrada</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Top Games -->
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-trophy"></i>
                    Top Jogos
                </h5>
            </div>
            <div class="card-body">
                <?php if (!empty($top_games)): ?>
                    <?php foreach ($top_games as $index => $game): ?>
                        <div class="d-flex align-items-center mb-3">
                            <div class="flex-shrink-0">
                                <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                    <?= $index + 1 ?>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6 class="mb-0"><?= htmlspecialchars($game['game_name']) ?></h6>
                                <small class="text-muted">
                                    <?= $game['play_count'] ?? 0 ?> jogadas • 
                                    R$ <?= number_format($game['total_bet'] ?? 0, 2, ',', '.') ?>
                                </small>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="text-center py-4">
                        <i class="fas fa-gamepad fa-3x text-muted mb-3"></i>
                        <p class="text-muted">Nenhum jogo encontrado</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-bolt"></i>
                    Ações Rápidas
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <a href="<?= base_url('admin/create_agent') ?>" class="btn btn-outline-primary w-100">
                            <i class="fas fa-user-plus"></i><br>
                            <small>Criar Agente</small>
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="<?= base_url('admin/games') ?>" class="btn btn-outline-success w-100">
                            <i class="fas fa-gamepad"></i><br>
                            <small>Gerenciar Jogos</small>
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="<?= base_url('admin/rtp_management') ?>" class="btn btn-outline-warning w-100">
                            <i class="fas fa-percentage"></i><br>
                            <small>Configurar RTP</small>
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="<?= base_url('admin/reports') ?>" class="btn btn-outline-info w-100">
                            <i class="fas fa-chart-bar"></i><br>
                            <small>Ver Relatórios</small>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- System Status -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-server"></i>
                    Status do Sistema
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-circle text-success me-2"></i>
                            <span>Servidor Online</span>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-circle text-success me-2"></i>
                            <span>Banco de Dados Conectado</span>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-circle text-success me-2"></i>
                            <span>API Funcionando</span>
                        </div>
                    </div>
                </div>
                <hr>
                <div class="row">
                    <div class="col-md-6">
                        <small class="text-muted">
                            <i class="fas fa-clock"></i>
                            Última atualização: <?= date('d/m/Y H:i:s') ?>
                        </small>
                    </div>
                    <div class="col-md-6 text-end">
                        <small class="text-muted">
                            <i class="fas fa-code-branch"></i>
                            Versão: 1.0.0
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
