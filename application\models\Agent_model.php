<?php 

class Agent_model extends CI_Model {

    
    
    public function check($code, $token) {
        $this->db->where('agentCode', $code);
        $this->db->where('token', $token);
        $this->db->where('status', 1);
        $query = $this->db->get('agents');
    
        if ($query) {
            if ($query->num_rows() > 0) {
                return true;
            } else {
                return false;
            }
        } else {
            return false;
        }
    }

    public function checkZero($code, $token) {
        $this->db->where('agentCode', $code);
        $this->db->where('token', $token);
        $this->db->where('status', 1);
        $query = $this->db->get('agents');
    
        if ($query && $query->num_rows() > 0) {
            $agent = $query->row(); // Obtém a primeira linha do resultado da consulta
            if ($agent->balance <= 0) {
                return true;
            } else {
                return false;
            }
        } else {
            return false;
        }
    }

    public function subCheck($code) {
        $this->db->where('agentCode', $code);
        $this->db->where('status', 1);
        $query = $this->db->get('agents');
    
        if ($query) {
            if ($query->num_rows() > 0) {
                return true;
            } else {
                return false;
            }
        } else {
            return false;
        }
    }
    
    public function getId($code, $token) {
        $this->db->select('id'); // Seleciona apenas o campo 'id'
        $this->db->where('agentCode', $code);
        $this->db->where('token', $token);
        $query = $this->db->get('agents');
    
        if ($query) {
            if ($query->num_rows() > 0) {
                return $query->row()->id;
            } else {
                return false;
            }
        } else {
            return false;
        }
    }
    public function getType($code, $token) {
        $this->db->select('apiType'); // Seleciona apenas o campo 'id'
        $this->db->where('agentCode', $code);
        $this->db->where('token', $token);
        $query = $this->db->get('agents');
    
        if ($query) {
            if ($query->num_rows() > 0) {
                return $query->row()->apiType;
            } else {
                return false;
            }
        } else {
            return false;
        }
    }
    public function get($code) {
        $this->db->from('agents');
        $this->db->where('agentCode', $code);
        $query = $this->db->get()->result_array();
        return $query;
    }

    public function ajustBalance($agent, $balance) {
        // Verificar se o usuário existe
        $this->db->where('agentCode', $agent);
        $query = $this->db->get('agents');

        $agents = $query->row(); // Obtém a primeira linha do resultado da consulta
        if ($agents->balance <= $balance) {
            return false;
        } else {
            $newBalance = ($agents->balance - $balance);
            $data = array('balance' => $newBalance);
            $this->db->where('agentCode', $agent);
            $this->db->update('agents', $data);
            return TRUE;
        }
    }

    public function ajustBalanceAdd($agent, $balance) {
        // Verificar se o usuário existe
        $this->db->where('agentCode', $agent);
        $query = $this->db->get('agents')->result();;

        if ($query) {

            $newBalance = ($query[0]->balance + $balance);
            $data = array('balance' => $newBalance);
            $this->db->where('agentCode', $agent);
            $this->db->update('agents', $data);
            return TRUE;
        } else {
            // Usuário não encontrado
            return FALSE;
        }
    }

    public function getBalance($code){
        $this->db->where('agentCode', $code);
        $query = $this->db->get('agents')->result();

        if ($query) {
            return $query[0]->balance;
        } else {
            return false;
        }
    }

    public function getCurrency($code){
        $this->db->where('agentCode', $code);
        $query = $this->db->get('agents')->result();

        if ($query) {
            return $query[0]->currency;
        } else {
            return false;
        }
    }

    // New methods for admin panel
    public function get_agent_by_code($agentCode) {
        $this->db->where('agentCode', $agentCode);
        $query = $this->db->get('agents');
        return $query->row_array();
    }

    public function get_agent_by_id($id) {
        $this->db->where('id', $id);
        $query = $this->db->get('agents');
        return $query->row_array();
    }

    public function get_all_agents() {
        $this->db->order_by('agent_name', 'ASC');
        $query = $this->db->get('agents');
        return $query->result_array();
    }

    public function count_agents() {
        return $this->db->count_all('agents');
    }

    public function create_agent($agent_data) {
        return $this->db->insert('agents', $agent_data);
    }

    public function update_agent($id, $agent_data) {
        $this->db->where('id', $id);
        return $this->db->update('agents', $agent_data);
    }

    public function update_agent_by_code($agentCode, $agent_data) {
        $this->db->where('agentCode', $agentCode);
        return $this->db->update('agents', $agent_data);
    }

    public function verify_login($email, $password) {
        $this->db->where('email', $email);
        $this->db->where('status', 1);
        $query = $this->db->get('agents');

        if ($query->num_rows() == 1) {
            $agent = $query->row_array();
            if (password_verify($password, $agent['password'])) {
                return $agent;
            }
        }

        return false;
    }

    public function update_last_login($agent_id) {
        $this->db->where('id', $agent_id);
        $this->db->update('agents', ['last_login' => date('Y-m-d H:i:s')]);
    }

    public function get_subagents($parent_agent) {
        $this->db->where('parent_agent', $parent_agent);
        $this->db->order_by('agent_name', 'ASC');
        $query = $this->db->get('agents');
        return $query->result_array();
    }

    public function get_recent_transactions($agentCode, $limit = 10) {
        $this->db->select('th.*, g.game_name');
        $this->db->from('transaction_history th');
        $this->db->join('games g', 'th.game_code = g.game_code', 'left');
        $this->db->where('th.agent_code', $agentCode);
        $this->db->order_by('th.created_at', 'DESC');
        $this->db->limit($limit);

        return $this->db->get()->result_array();
    }

    public function get_top_games_by_agent($agentCode, $limit = 5) {
        $this->db->select('g.game_name, g.game_code, COUNT(th.id) as play_count, SUM(th.bet_money) as total_bet, SUM(th.win_money) as total_win');
        $this->db->from('games g');
        $this->db->join('transaction_history th', 'g.game_code = th.game_code');
        $this->db->where('th.agent_code', $agentCode);
        $this->db->group_by('g.game_code');
        $this->db->order_by('total_bet', 'DESC');
        $this->db->limit($limit);

        return $this->db->get()->result_array();
    }

    public function get_daily_stats($agentCode) {
        $this->db->select('DATE(created_at) as date, COUNT(*) as transactions, SUM(bet_money) as total_bet, SUM(win_money) as total_win');
        $this->db->from('transaction_history');
        $this->db->where('agent_code', $agentCode);
        $this->db->where('created_at >=', date('Y-m-d', strtotime('-30 days')));
        $this->db->group_by('DATE(created_at)');
        $this->db->order_by('date', 'DESC');

        return $this->db->get()->result_array();
    }

    public function get_monthly_stats($agentCode) {
        $this->db->select('DATE_FORMAT(created_at, "%Y-%m") as month, COUNT(*) as transactions, SUM(bet_money) as total_bet, SUM(win_money) as total_win');
        $this->db->from('transaction_history');
        $this->db->where('agent_code', $agentCode);
        $this->db->where('created_at >=', date('Y-m-d', strtotime('-12 months')));
        $this->db->group_by('DATE_FORMAT(created_at, "%Y-%m")');
        $this->db->order_by('month', 'DESC');

        return $this->db->get()->result_array();
    }

    public function get_user_stats($agentCode) {
        $this->db->select('u.userCode, u.balance, COUNT(th.id) as transactions, SUM(th.bet_money) as total_bet, SUM(th.win_money) as total_win');
        $this->db->from('users u');
        $this->db->join('transaction_history th', 'u.userCode = th.user_code', 'left');
        $this->db->where('u.agentCode', $agentCode);
        $this->db->group_by('u.userCode');
        $this->db->order_by('total_bet', 'DESC');

        return $this->db->get()->result_array();
    }

    public function get_transactions($agentCode) {
        $this->db->select('th.*, g.game_name');
        $this->db->from('transaction_history th');
        $this->db->join('games g', 'th.game_code = g.game_code', 'left');
        $this->db->where('th.agent_code', $agentCode);
        $this->db->order_by('th.created_at', 'DESC');

        return $this->db->get()->result_array();
    }

    public function get_balance_history($agentCode) {
        $this->db->select('*');
        $this->db->from('transaction_history');
        $this->db->where('agent_code', $agentCode);
        $this->db->where('transaction_type IN', ['deposit', 'withdraw']);
        $this->db->order_by('created_at', 'DESC');
        $this->db->limit(50);

        return $this->db->get()->result_array();
    }
}