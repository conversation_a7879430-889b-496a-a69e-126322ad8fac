<?php
/**
 * Script para testar todos os jogos da plataforma
 */

// Configurações
$api_url = 'http://localhost/';
$agent_code = 'admin';
$agent_token = '5f2dbdcb-a59d-42f8-9815-cb34a9723cd9';
$provider_code = 'PRAGMATICPLAY';

// Função para fazer requisições à API
function makeApiRequest($url, $data) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Content-Length: ' . strlen(json_encode($data))
    ]);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($httpCode !== 200) {
        throw new Exception("HTTP Error: $httpCode");
    }
    
    return json_decode($response, true);
}

// Cores para output
function colorOutput($text, $color = 'white') {
    $colors = [
        'red' => "\033[31m",
        'green' => "\033[32m",
        'yellow' => "\033[33m",
        'blue' => "\033[34m",
        'magenta' => "\033[35m",
        'cyan' => "\033[36m",
        'white' => "\033[37m",
        'reset' => "\033[0m"
    ];
    
    return $colors[$color] . $text . $colors['reset'];
}

echo colorOutput("🎮 TESTE COMPLETO DA PLATAFORMA DE JOGOS\n", 'cyan');
echo colorOutput("=" . str_repeat("=", 50) . "\n", 'blue');

try {
    // 1. Testar lista de jogos
    echo colorOutput("\n📋 Testando lista de jogos...\n", 'yellow');
    
    $gameListData = [
        'method' => 'game_list',
        'agent_code' => $agent_code,
        'agent_token' => $agent_token,
        'provider_code' => $provider_code
    ];
    
    $gameListResponse = makeApiRequest($api_url, $gameListData);
    
    if ($gameListResponse['status'] !== 1) {
        throw new Exception("Erro ao obter lista de jogos: " . $gameListResponse['msg']);
    }
    
    $games = $gameListResponse['games'];
    $totalGames = count($games);
    $activeGames = count(array_filter($games, function($game) {
        return $game['status'] === '1';
    }));
    
    echo colorOutput("✅ Lista de jogos obtida com sucesso!\n", 'green');
    echo colorOutput("📊 Total de jogos: $totalGames\n", 'white');
    echo colorOutput("🟢 Jogos ativos: $activeGames\n", 'green');
    
    // 2. Testar lançamento de jogos (amostra)
    echo colorOutput("\n🚀 Testando lançamento de jogos...\n", 'yellow');
    
    $testGames = array_slice($games, 0, 10); // Testar os primeiros 10 jogos
    $successfulLaunches = 0;
    $failedLaunches = 0;
    
    foreach ($testGames as $game) {
        $userCode = 'test_user_' . uniqid();
        
        $launchData = [
            'method' => 'game_launch',
            'agent_code' => $agent_code,
            'agent_token' => $agent_token,
            'provider_code' => $provider_code,
            'game_code' => $game['game_code'],
            'user_code' => $userCode,
            'lang' => 'pt'
        ];
        
        try {
            $launchResponse = makeApiRequest($api_url, $launchData);
            
            if ($launchResponse['status'] === 1) {
                echo colorOutput("✅ {$game['game_code']}: Lançamento bem-sucedido\n", 'green');
                echo colorOutput("   URL: {$launchResponse['launch_url']}\n", 'cyan');
                $successfulLaunches++;
            } else {
                echo colorOutput("❌ {$game['game_code']}: Falha no lançamento - {$launchResponse['msg']}\n", 'red');
                $failedLaunches++;
            }
        } catch (Exception $e) {
            echo colorOutput("❌ {$game['game_code']}: Erro de conexão - {$e->getMessage()}\n", 'red');
            $failedLaunches++;
        }
        
        // Pequena pausa entre os testes
        usleep(500000); // 0.5 segundos
    }
    
    // 3. Relatório final
    echo colorOutput("\n📊 RELATÓRIO FINAL\n", 'cyan');
    echo colorOutput("=" . str_repeat("=", 30) . "\n", 'blue');
    echo colorOutput("🎮 Total de jogos na plataforma: $totalGames\n", 'white');
    echo colorOutput("🟢 Jogos ativos: $activeGames\n", 'green');
    echo colorOutput("🧪 Jogos testados: " . count($testGames) . "\n", 'yellow');
    echo colorOutput("✅ Lançamentos bem-sucedidos: $successfulLaunches\n", 'green');
    echo colorOutput("❌ Lançamentos falharam: $failedLaunches\n", 'red');
    
    $successRate = round(($successfulLaunches / count($testGames)) * 100, 2);
    echo colorOutput("📈 Taxa de sucesso: $successRate%\n", 'magenta');
    
    if ($successRate >= 90) {
        echo colorOutput("\n🎉 PLATAFORMA FUNCIONANDO PERFEITAMENTE!\n", 'green');
        echo colorOutput("Todos os sistemas estão operacionais.\n", 'green');
    } elseif ($successRate >= 70) {
        echo colorOutput("\n⚠️  PLATAFORMA FUNCIONANDO COM ALGUNS PROBLEMAS\n", 'yellow');
        echo colorOutput("A maioria dos jogos está funcionando, mas há algumas falhas.\n", 'yellow');
    } else {
        echo colorOutput("\n🚨 PLATAFORMA COM PROBLEMAS CRÍTICOS\n", 'red');
        echo colorOutput("Muitos jogos não estão funcionando corretamente.\n", 'red');
    }
    
    // 4. Lista de jogos populares funcionando
    echo colorOutput("\n🌟 JOGOS POPULARES FUNCIONANDO:\n", 'cyan');
    $popularGames = [
        'vs20sugarrush' => 'Sugar Rush',
        'vs20olympgate' => 'Gates of Olympus',
        'vs20starlight' => 'Starlight Princess',
        'vs25wolfgold' => 'Wolf Gold',
        'vs20doghouse' => 'The Dog House',
        'vs10bbbonanza' => 'Big Bass Bonanza'
    ];
    
    foreach ($popularGames as $code => $name) {
        $found = array_filter($games, function($game) use ($code) {
            return $game['game_code'] === $code;
        });
        
        if (!empty($found)) {
            echo colorOutput("🎰 $name ($code) - Disponível\n", 'green');
        }
    }
    
    echo colorOutput("\n🔗 LINKS ÚTEIS:\n", 'cyan');
    echo colorOutput("📱 Interface de jogos: http://localhost/games_demo.html\n", 'blue');
    echo colorOutput("🎮 Exemplo de jogo: http://localhost/play?game=vs20sugarrush&user=demo&lang=pt&cur=R$\n", 'blue');
    echo colorOutput("📋 API de lista: http://localhost/ (POST)\n", 'blue');
    
} catch (Exception $e) {
    echo colorOutput("\n❌ ERRO CRÍTICO: " . $e->getMessage() . "\n", 'red');
    echo colorOutput("Verifique se o servidor está rodando e a configuração está correta.\n", 'yellow');
}

echo colorOutput("\n" . str_repeat("=", 60) . "\n", 'blue');
echo colorOutput("Teste concluído em " . date('Y-m-d H:i:s') . "\n", 'white');
?>
