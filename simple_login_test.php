<?php
session_start();

// Simple login test without CodeIgniter
echo "<h2>Simple Admin Login Test</h2>";

if ($_POST) {
    $username = $_POST['username'];
    $password = $_POST['password'];
    
    echo "<h3>Processing Login...</h3>";
    echo "<p>Username: " . htmlspecialchars($username) . "</p>";
    echo "<p>Password: " . htmlspecialchars($password) . "</p>";
    
    try {
        // Connect to database
        $pdo = new PDO('mysql:host=localhost;dbname=game_platform', 'root', '');
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // Check admin credentials
        $stmt = $pdo->prepare("SELECT * FROM admins WHERE username = ? AND status = 1");
        $stmt->execute([$username]);
        $admin = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($admin && password_verify($password, $admin['password'])) {
            // Login successful
            $_SESSION['admin_logged_in'] = true;
            $_SESSION['admin_id'] = $admin['id'];
            $_SESSION['admin_username'] = $admin['username'];
            $_SESSION['admin_name'] = $admin['full_name'];
            
            echo "<div style='background: #d4edda; color: #155724; padding: 15px; border: 1px solid #c3e6cb; border-radius: 5px; margin: 10px 0;'>";
            echo "<h3>✅ LOGIN SUCCESSFUL!</h3>";
            echo "<p>Welcome, " . $admin['full_name'] . "!</p>";
            echo "<p>Session data set:</p>";
            echo "<ul>";
            echo "<li>admin_logged_in: " . ($_SESSION['admin_logged_in'] ? 'true' : 'false') . "</li>";
            echo "<li>admin_id: " . $_SESSION['admin_id'] . "</li>";
            echo "<li>admin_username: " . $_SESSION['admin_username'] . "</li>";
            echo "<li>admin_name: " . $_SESSION['admin_name'] . "</li>";
            echo "</ul>";
            echo "<p><a href='admin/login' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Go to Admin Panel</a></p>";
            echo "</div>";
            
        } else {
            echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border: 1px solid #f5c6cb; border-radius: 5px; margin: 10px 0;'>";
            echo "<h3>❌ LOGIN FAILED</h3>";
            echo "<p>Invalid username or password.</p>";
            echo "</div>";
        }
        
    } catch (Exception $e) {
        echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border: 1px solid #f5c6cb; border-radius: 5px; margin: 10px 0;'>";
        echo "<h3>❌ DATABASE ERROR</h3>";
        echo "<p>" . $e->getMessage() . "</p>";
        echo "</div>";
    }
}

// Check if already logged in
if (isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in']) {
    echo "<div style='background: #d1ecf1; color: #0c5460; padding: 15px; border: 1px solid #bee5eb; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>ℹ️ ALREADY LOGGED IN</h3>";
    echo "<p>You are already logged in as: " . $_SESSION['admin_name'] . "</p>";
    echo "<p><a href='?logout=1' style='background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Logout</a></p>";
    echo "</div>";
}

// Handle logout
if (isset($_GET['logout'])) {
    session_destroy();
    echo "<div style='background: #d4edda; color: #155724; padding: 15px; border: 1px solid #c3e6cb; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>✅ LOGGED OUT</h3>";
    echo "<p>You have been logged out successfully.</p>";
    echo "</div>";
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Simple Admin Login Test</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 600px; margin: 50px auto; padding: 20px; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input[type="text"], input[type="password"] { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; }
        button { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background: #0056b3; }
    </style>
</head>
<body>
    <h2>Simple Admin Login Form</h2>
    
    <form method="post">
        <div class="form-group">
            <label for="username">Username:</label>
            <input type="text" id="username" name="username" value="admin" required>
        </div>
        
        <div class="form-group">
            <label for="password">Password:</label>
            <input type="password" id="password" name="password" value="123456" required>
        </div>
        
        <button type="submit">Login</button>
    </form>
    
    <hr>
    <h3>Instructions:</h3>
    <ol>
        <li>Use the form above to test login functionality</li>
        <li>Default credentials: <strong>admin</strong> / <strong>123456</strong></li>
        <li>If login works here, the issue is with CodeIgniter configuration</li>
        <li>If login fails here, the issue is with database or credentials</li>
    </ol>
    
    <hr>
    <p><a href="admin/login" target="_blank">Test CodeIgniter Admin Login</a></p>
</body>
</html>
