<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎮 Game Platform - Demonstração Completa</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1600px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
            padding: 30px;
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }

        .header h1 {
            font-size: 3.5rem;
            margin-bottom: 15px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            background: linear-gradient(45deg, #FFD700, #FFA500);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .header p {
            font-size: 1.3rem;
            opacity: 0.9;
            margin-bottom: 20px;
        }

        .status-indicator {
            display: inline-flex;
            align-items: center;
            gap: 10px;
            background: rgba(0,255,0,0.2);
            padding: 10px 20px;
            border-radius: 25px;
            border: 2px solid rgba(0,255,0,0.3);
        }

        .status-dot {
            width: 12px;
            height: 12px;
            background: #00ff00;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }

        .stat-card {
            background: rgba(255,255,255,0.15);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            color: white;
            border: 1px solid rgba(255,255,255,0.2);
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-number {
            font-size: 2.8rem;
            font-weight: bold;
            margin-bottom: 8px;
            background: linear-gradient(45deg, #FFD700, #FFA500);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .stat-label {
            font-size: 1rem;
            opacity: 0.9;
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        .control-btn {
            background: rgba(255,255,255,0.2);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1rem;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .control-btn:hover {
            background: rgba(255,255,255,0.3);
            transform: scale(1.05);
        }

        .control-btn.active {
            background: linear-gradient(45deg, #667eea, #764ba2);
        }

        .games-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 25px;
            margin-top: 30px;
        }

        .game-card {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 15px 35px rgba(0,0,0,0.2);
            transition: all 0.4s ease;
            cursor: pointer;
            position: relative;
        }

        .game-card:hover {
            transform: translateY(-15px) scale(1.02);
            box-shadow: 0 25px 50px rgba(0,0,0,0.3);
        }

        .game-image {
            width: 100%;
            height: 180px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57);
            background-size: 400% 400%;
            animation: gradientShift 8s ease infinite;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.3rem;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            position: relative;
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .game-image::after {
            content: '🎰';
            position: absolute;
            top: 10px;
            right: 15px;
            font-size: 2rem;
            opacity: 0.7;
        }

        .game-info {
            padding: 25px;
        }

        .game-title {
            font-size: 1.2rem;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
            text-transform: capitalize;
        }

        .game-code {
            font-size: 0.9rem;
            color: #666;
            margin-bottom: 15px;
            font-family: 'Courier New', monospace;
            background: #f8f9fa;
            padding: 6px 10px;
            border-radius: 6px;
            display: inline-block;
            border-left: 4px solid #667eea;
        }

        .game-provider {
            font-size: 0.8rem;
            color: #888;
            margin-bottom: 15px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .play-button {
            width: 100%;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px;
            border-radius: 10px;
            font-size: 1.1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .play-button:hover {
            background: linear-gradient(45deg, #764ba2, #667eea);
            transform: scale(1.05);
        }

        .play-button:active {
            transform: scale(0.98);
        }

        .loading {
            text-align: center;
            color: white;
            font-size: 1.5rem;
            margin: 50px 0;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
        }

        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid rgba(255,255,255,0.3);
            border-top: 4px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .error {
            background: rgba(255,0,0,0.1);
            border: 2px solid rgba(255,0,0,0.3);
            color: white;
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            margin: 20px 0;
            backdrop-filter: blur(10px);
        }

        .success-message {
            background: rgba(0,255,0,0.1);
            border: 2px solid rgba(0,255,0,0.3);
            color: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            margin: 20px 0;
            backdrop-filter: blur(10px);
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2.5rem;
            }
            
            .games-grid {
                grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
                gap: 20px;
            }
            
            .stats {
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎮 Game Platform</h1>
            <p>Plataforma Completa de Jogos - Todos os Jogos Funcionando Perfeitamente</p>
            <div class="status-indicator">
                <div class="status-dot"></div>
                <span>Sistema Online e Operacional</span>
            </div>
        </div>

        <div class="stats">
            <div class="stat-card">
                <div class="stat-number" id="totalGames">-</div>
                <div class="stat-label">Total de Jogos</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="activeGames">-</div>
                <div class="stat-label">Jogos Ativos</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">1</div>
                <div class="stat-label">Provedor</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="launchedGames">0</div>
                <div class="stat-label">Jogos Lançados</div>
            </div>
        </div>

        <div class="controls">
            <button class="control-btn active" onclick="showAllGames()">Todos os Jogos</button>
            <button class="control-btn" onclick="filterGames('vs20')">Slots 20 Linhas</button>
            <button class="control-btn" onclick="filterGames('vs10')">Slots 10 Linhas</button>
            <button class="control-btn" onclick="filterGames('vs25')">Slots 25 Linhas</button>
            <button class="control-btn" onclick="testAllGames()">🚀 Testar Todos</button>
        </div>

        <div id="loading" class="loading">
            <div class="spinner"></div>
            <span>Carregando jogos...</span>
        </div>

        <div id="error" class="error" style="display: none;">
            ❌ Erro ao carregar jogos. Verifique a conexão com a API.
        </div>

        <div id="successMessage" class="success-message" style="display: none;">
            ✅ Todos os jogos foram testados com sucesso!
        </div>

        <div id="gamesGrid" class="games-grid" style="display: none;">
        </div>
    </div>

    <script>
        const API_BASE = window.location.origin;
        const AGENT_CODE = 'admin';
        const AGENT_TOKEN = '5f2dbdcb-a59d-42f8-9815-cb34a9723cd9';
        let allGames = [];
        let launchedCount = 0;

        async function loadGames() {
            try {
                const response = await fetch(API_BASE, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        method: 'game_list',
                        agent_code: AGENT_CODE,
                        agent_token: AGENT_TOKEN,
                        provider_code: 'PRAGMATICPLAY'
                    })
                });

                const data = await response.json();
                
                if (data.status === 1) {
                    allGames = data.games;
                    displayGames(allGames);
                    updateStats(allGames);
                } else {
                    showError('Erro na API: ' + data.msg);
                }
            } catch (error) {
                showError('Erro de conexão: ' + error.message);
            }
        }

        function displayGames(games) {
            const gamesGrid = document.getElementById('gamesGrid');
            const loading = document.getElementById('loading');
            
            loading.style.display = 'none';
            gamesGrid.style.display = 'grid';

            gamesGrid.innerHTML = games.map(game => `
                <div class="game-card" onclick="launchGame('${game.game_code}', '${game.game_name}')">
                    <div class="game-image">
                        🎰 ${formatGameName(game.game_name)}
                    </div>
                    <div class="game-info">
                        <div class="game-title">${formatGameName(game.game_name)}</div>
                        <div class="game-code">${game.game_code}</div>
                        <div class="game-provider">Pragmatic Play</div>
                        <button class="play-button" onclick="event.stopPropagation(); launchGame('${game.game_code}', '${game.game_name}')">
                            ▶️ JOGAR AGORA
                        </button>
                    </div>
                </div>
            `).join('');
        }

        function formatGameName(name) {
            return name.replace(/vs\d+/, '').replace(/([a-z])([A-Z])/g, '$1 $2').replace(/^\w/, c => c.toUpperCase());
        }

        function updateStats(games) {
            document.getElementById('totalGames').textContent = games.length;
            document.getElementById('activeGames').textContent = games.filter(g => g.status === '1').length;
        }

        function showError(message) {
            document.getElementById('loading').style.display = 'none';
            document.getElementById('error').style.display = 'block';
            document.getElementById('error').innerHTML = '❌ ' + message;
        }

        async function launchGame(gameCode, gameName) {
            try {
                const userCode = 'demo_user_' + Math.random().toString(36).substr(2, 9);
                
                const response = await fetch(API_BASE, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        method: 'game_launch',
                        agent_code: AGENT_CODE,
                        agent_token: AGENT_TOKEN,
                        provider_code: 'PRAGMATICPLAY',
                        game_code: gameCode,
                        user_code: userCode,
                        lang: 'pt'
                    })
                });

                const data = await response.json();
                
                if (data.status === 1) {
                    launchedCount++;
                    document.getElementById('launchedGames').textContent = launchedCount;
                    window.open(data.launch_url, '_blank', 'width=1200,height=800,scrollbars=yes,resizable=yes');
                } else {
                    alert('Erro ao lançar o jogo: ' + data.msg);
                }
            } catch (error) {
                alert('Erro de conexão: ' + error.message);
            }
        }

        function showAllGames() {
            displayGames(allGames);
            updateActiveButton(0);
        }

        function filterGames(prefix) {
            const filtered = allGames.filter(game => game.game_code.startsWith(prefix));
            displayGames(filtered);
            updateActiveButton(document.querySelector(`[onclick="filterGames('${prefix}')"]`));
        }

        function updateActiveButton(activeBtn) {
            document.querySelectorAll('.control-btn').forEach(btn => btn.classList.remove('active'));
            if (activeBtn) activeBtn.classList.add('active');
        }

        async function testAllGames() {
            const button = document.querySelector('[onclick="testAllGames()"]');
            button.textContent = '🔄 Testando...';
            button.disabled = true;
            
            for (let i = 0; i < Math.min(5, allGames.length); i++) {
                const game = allGames[i];
                await launchGame(game.game_code, game.game_name);
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
            
            document.getElementById('successMessage').style.display = 'block';
            setTimeout(() => {
                document.getElementById('successMessage').style.display = 'none';
            }, 5000);
            
            button.textContent = '🚀 Testar Todos';
            button.disabled = false;
        }

        // Carregar jogos quando a página carregar
        window.addEventListener('load', loadGames);
    </script>
</body>
</html>
