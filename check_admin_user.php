<?php
// Check if admin user exists and test login
echo "<h2>Admin User Check</h2>";

try {
    $pdo = new PDO('mysql:host=localhost;dbname=game_platform', 'root', '');
    echo "<p style='color: green;'>✓ Database connection successful</p>";
    
    // Check if admin user exists
    $stmt = $pdo->prepare("SELECT * FROM admins WHERE username = 'admin'");
    $stmt->execute();
    $admin = $stmt->fetch();
    
    if ($admin) {
        echo "<p style='color: green;'>✓ Admin user found</p>";
        echo "<p>ID: " . $admin['id'] . "</p>";
        echo "<p>Username: " . $admin['username'] . "</p>";
        echo "<p>Full Name: " . $admin['full_name'] . "</p>";
        echo "<p>Status: " . ($admin['status'] ? 'Active' : 'Inactive') . "</p>";
        echo "<p>Created: " . $admin['created_at'] . "</p>";
        
        // Test password
        $test_password = '123456';
        if (password_verify($test_password, $admin['password'])) {
            echo "<p style='color: green; font-weight: bold;'>✓ Password '123456' is correct</p>";
        } else {
            echo "<p style='color: red; font-weight: bold;'>✗ Password '123456' is incorrect</p>";
            echo "<p>Stored hash: " . $admin['password'] . "</p>";
        }
        
    } else {
        echo "<p style='color: red;'>✗ Admin user not found</p>";
        
        // Create admin user
        echo "<h3>Creating admin user...</h3>";
        $password_hash = password_hash('123456', PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("INSERT INTO admins (username, password, full_name, email, status) VALUES (?, ?, ?, ?, ?)");
        $result = $stmt->execute(['admin', $password_hash, 'Administrator', 'admin@localhost', 1]);
        
        if ($result) {
            echo "<p style='color: green;'>✓ Admin user created successfully</p>";
        } else {
            echo "<p style='color: red;'>✗ Failed to create admin user</p>";
        }
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Error: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<h3>Test Login</h3>";
echo "<p><a href='admin/login' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Go to Admin Login</a></p>";
echo "<p>Use: <strong>admin</strong> / <strong>123456</strong></p>";
?>
