{"resources": [{"type": "GameObject", "id": "8565ea4dcdab9a84ab8421a2bb921184", "data": {"root": [{"name": "es_desktop", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 0}, "children": [{"fileID": 51430, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, {"fileID": 51431, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, {"fileID": 51432, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, {"fileID": 51433, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, {"fileID": 51434, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, {"fileID": 51435, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, {"fileID": 51436, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, {"fileID": 51437, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, {"fileID": 51438, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, {"fileID": 51439, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, {"fileID": 51440, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, {"fileID": 51441, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, {"fileID": 51442, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, {"fileID": 51443, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, {"fileID": 51444, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, {"fileID": 51445, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, {"fileID": 51446, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, {"fileID": 51447, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, {"fileID": 51448, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, {"fileID": 51449, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, {"fileID": 51450, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, {"fileID": 51451, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, {"fileID": 51452, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, {"fileID": 51453, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, {"fileID": 51454, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, {"fileID": 51455, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, {"fileID": 51456, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, {"fileID": 51457, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, {"fileID": 51458, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, {"fileID": 51459, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, {"fileID": 51460, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, {"fileID": 51461, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, {"fileID": 51462, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, {"fileID": 51463, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, {"fileID": 51464, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, {"fileID": 51465, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, {"fileID": 51466, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, {"fileID": 51467, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, {"fileID": 51468, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, {"fileID": 51469, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, {"fileID": 51470, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, {"fileID": 51471, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, {"fileID": 51472, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, {"fileID": 51473, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, {"fileID": 51474, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, {"fileID": 51475, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, {"fileID": 51476, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, {"fileID": 51477, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, {"fileID": 51478, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, {"fileID": 51479, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, {"fileID": 51480, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, {"fileID": 51481, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, {"fileID": 51482, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, {"fileID": 51483, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, {"fileID": 51484, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, {"fileID": 51485, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, {"fileID": 51486, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, {"fileID": 51487, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, {"fileID": 51488, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, {"fileID": 51489, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, {"fileID": 51490, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, {"fileID": 51491, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, {"fileID": 51492, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, {"fileID": 51493, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, {"fileID": 51494, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, {"fileID": 51495, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, {"fileID": 51496, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, {"fileID": 51497, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, {"fileID": 51498, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, {"fileID": 51499, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, {"fileID": 51500, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, {"fileID": 51501, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, {"fileID": 51502, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, {"fileID": 51503, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, {"fileID": 51504, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, {"fileID": 51505, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, {"fileID": 51506, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, {"fileID": 51507, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, {"fileID": 51508, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, {"fileID": 51509, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, {"fileID": 51510, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, {"fileID": 51511, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, {"fileID": 51512, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, {"fileID": 51513, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, {"fileID": 51514, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, {"fileID": 51515, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, {"fileID": 51516, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}], "s": "0"}, "fileID": 51517}, {"componentType": "ModificationsManager", "enabled": true, "serializableData": {"root": {"fileID": 0}, "EditMode": false, "Atlases": [], "Transforms": [{"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page1/Title/PaytableTitleLabel1", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page1/Rules/AllSymbolsPayLabel", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page1/ScatterHolder/SymbolScatter/Sprite", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0.52, "y": 0.52, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page1/ScatterHolder/DescriptionHolder/Label1", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -24, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page1/ScatterHolder/DescriptionHolder/Label2", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -35, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page1/WildHolder/SymbolScatter/Sprite", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0.25, "y": 0.25, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page1/WildHolder/DescriptionHolder/Label1", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -12.5, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page1/WildHolder/DescriptionHolder/Label2", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -50, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page1/WildHolder/DescriptionHolder/Label3", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -87.5, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/MoneySymbolHolder/TitleHolder/Title", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 10, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/MoneySymbolHolder/DescriptionHolder/MoneySymbolHolder/Sprite", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0.7, "y": 0.7, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/MoneySymbolHolder/DescriptionHolder/MoneySymbolRules/Label3", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FreeSpinsHolder1/TitleHolder/Title", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/RuleHolder1/Rule1", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 13, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder1/LabelHolder1/Label1", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 22, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder1/LabelHolder2/Label2", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 22, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder1/LabelHolder3/Label3", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 22, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/RuleHolder2/Rule2", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 21, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder2/LabelHolder1/Label1", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 16, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder2/LabelHolder2/Label2", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 9, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/RuleHolder3/Rule3", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 16, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder1/Label1", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 5, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder2/Label2", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -14, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder3/Label3", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -19, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder4/Label4", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -19, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder5/Label5", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -19, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder1/Rule1", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 6, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder2/Rule2", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder3/Rule3", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -9.4, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder4/Rule4", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -25.1, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder5/Rule5", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -14.1, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder6/Rule6", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -6.6, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder7/Rule7", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder8/Rule8", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -5, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/FreeSpinsHolder2/SpecialReelsHolder/SpecialReels", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -9, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/MaxWin/TitleHolder/Title", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/MaxWin/RuleHolder/Label1", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/CAT/TitleHolder/Title1New", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/CAT/RuleHolder1/Rule1", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -14, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/CAT/RuleHolder2/Rule2", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -14, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/CAT/RuleHolder3/Rule3", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page4/Title/PaytableTitleLabel", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page4/Volatility/VolatilityDescription", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -30, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page4/RulesTop/AllSymbolsPay", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 50, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page4/RulesTop/AllWinsMultiplied", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 21, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page4/RulesTop/AllValuesExpressed", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -45, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page4/RulesTop/OnlyTheHighestWin", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -65, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page4/RulesTop/WhenWinningOnMultiplePaylines", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -95, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page4/Lines/Sprite", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page4/RulesBottom/SpaceAndEnter", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 92, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page4/RulesBottom/RTP", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page4/RulesBottom/MalfunctionLabel", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -97, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page4/MinMaxHolder", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -185, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page2/MoneySymbolHolder/DescriptionHolder/PossibleValues/Label", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": -10, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}, {"nestedFieldType": "TransformChange", "serializableData": {"Target": {"fileID": 0}, "path": "Paytable/Pages/Page3/MaxWin/RuleHolder/HolderLabelJackpot/Label1New", "TransformData": [{"nestedFieldType": "TransformInfo", "serializableData": {"Position": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 0, "y": 0, "z": 0}}, "Scale": {"nestedFieldType": "UHTMath.Vector3", "serializableData": {"x": 1, "y": 1, "z": 1}}, "Rotation": 0, "isNull": false}}], "extraPayloads": [], "isSet": true, "active": true}}], "Labels": [{"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 51518, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "extraPayloads": [], "path": "Game/GamePivot/FSWONWindow/content/FreespinsWonWindow/LabelsAndSprites/Catches_label", "oldContent": "CATCHES", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 51519, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "extraPayloads": [], "path": "Game/GamePivot/FreeSpinsPurchase/BuyButtons/Options/Option_0/OptVisual/Texts/BoughtText/LandscapeText/label1", "oldContent": "ACTIVE", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 51520, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "extraPayloads": [], "path": "Game/GamePivot/FSWONWindow/PressAnywhere_Label/label", "oldContent": "Press anywhere to continue", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 51521, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "extraPayloads": [], "path": "Game/GamePivot/FSExtraWindow/content/PressAnywhere_Label/label", "oldContent": "Press anywhere to continue...", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 51522, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "extraPayloads": [], "path": "Game/GamePivot/FreeSpinsPurchase/FSPurchaseWindow/Content/AnimatedPivot/Texts/BuyText/label", "oldContent": "BUY FREE SPINS", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 51523, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "extraPayloads": [], "path": "Game/GamePivot/FsFeature/content/Feature_trail/Holder/fisherman_highlight/ScalerX10/stretcher_fs/spins", "oldContent": "spins", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 51524, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "extraPayloads": [], "path": "Game/Background/PaytableOnScreen/Portrait/Message1/labelMsg1", "oldContent": "AL<PERSON> SYMBOLS PAY FROM LEFT TO RIGHT. BONUS PAYS ON ANY POSITION.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 51525, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "extraPayloads": [], "path": "Game/GamePivot/FsFeature/content/Feature_trail/Holder/fisherman_highlight/ScalerX3/stretcher_fs/free", "oldContent": "free", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 51526, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "extraPayloads": [], "path": "Game/GamePivot/Reels/ThePivot/BonusMessages/Fisherman/Labels/uilabel", "oldContent": "MORE FISHERMEN!", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 51527, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "extraPayloads": [], "path": "Game/GamePivot/FreeSpinsPurchase/BuyButtons/Options/Option_0/OptVisual/Texts/BoughtText/LandscapeText/label0", "oldContent": "FEATURE", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 51528, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "extraPayloads": [], "path": "Game/GamePivot/FreeSpinsPurchase/BuyButtons/Options/Option_0/OptVisual/Texts/BuyText/PortraitText/label0", "oldContent": "BUY FREE SPINS", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 51529, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "extraPayloads": [], "path": "Game/GamePivot/FsFeature/content/Feature_trail/Holder/fisherman_highlight/ScalerX2/stretcher_fs/free", "oldContent": "free", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 51530, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "extraPayloads": [], "path": "Game/GamePivot/FSResultWindow/content/SignPivot/FreespinsCongratsWindow/Labels/Congrats_label", "oldContent": "CONGRATULATIONS!", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 51531, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "extraPayloads": [], "path": "Game/GamePivot/FSExtraWindow/content/SignPivot/FreespinsExtraWindow/Labels/Congrats_label", "oldContent": "CONGRATULATIONS!", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 51532, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "extraPayloads": [], "path": "Game/GamePivot/FSExtraWindow/content/SignPivot/FreespinsExtraWindow/Labels/FreeSpins_label ", "oldContent": "FREESPINS", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 51533, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "extraPayloads": [], "path": "Game/GamePivot/FSExtraWindow/content/SignPivot/FreespinsExtraWindow/Labels/AreNow_label", "oldContent": "ARE NOW", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 51534, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "extraPayloads": [], "path": "Game/GamePivot/FreeSpinsPurchase/BuyButtons/Options/Option_0/OptVisual/Texts/BuyText/LandscapeTest/label0", "oldContent": "BUY FREE SPINS", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 51535, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "extraPayloads": [], "path": "Game/GamePivot/Reels/ThePivot/BonusMessages/PlusFS/Labels/uilabel", "oldContent": "EXTRA FREE SPINS!", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 51536, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "extraPayloads": [], "path": "Game/GamePivot/FsFeature/content/Feature_trail/Holder/fisherman_highlight/ScalerX10/stretcher_fs/free", "oldContent": "free", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 51537, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "extraPayloads": [], "path": "Game/GamePivot/FSWONWindow/content/FreespinsWonWindow/LabelsAndSprites/Congratulations_label", "oldContent": "CONGRATULATIONS!", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 51538, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "extraPayloads": [], "path": "Game/GamePivot/Reels/ThePivot/BonusMessages/Fishes/Labels/uilabel", "oldContent": "MORE FISH!", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 51539, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "extraPayloads": [], "path": "Game/GamePivot/FreeSpinsPurchase/BuyButtons/Options/Option_0/OptVisual/Texts/BoughtText/PortraitText/label0", "oldContent": "FEATURE ACTIVE", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 51540, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "extraPayloads": [], "path": "Game/GamePivot/FSExtraWindow/content/SignPivot/FreespinsExtraWindow/Labels/Extra_label", "oldContent": "THE NEXT", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 51541, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "extraPayloads": [], "path": "Game/GamePivot/FSResultWindow/content/SignPivot/FreespinsCongratsWindow/Labels/YouWon_label", "oldContent": "YOU HAVE WON", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 51542, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "extraPayloads": [], "path": "Game/GamePivot/FsFeature/content/Feature_trail/Holder/fisherman_highlight/ScalerX3/stretcher_fs/spins", "oldContent": "spins", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 51543, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "extraPayloads": [], "path": "Game/Background/PaytableOnScreen/Landscape/Message1/labelMsg1", "oldContent": "AL<PERSON> SYMBOLS PAY FROM LEFT TO RIGHT. BONUS PAYS ON ANY POSITION.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 51544, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "extraPayloads": [], "path": "Game/GamePivot/FsFeature/content/Feature_trail/Holder/fisherman_highlight/ScalerX2/stretcher_fs/spins", "oldContent": "spins", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 51545, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "extraPayloads": [], "path": "Game/GamePivot/Reels/ThePivot/BonusMessages/Level2/Labels/uilabel", "oldContent": "START FROM LEVEL 2!", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 51546, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "extraPayloads": [], "path": "Game/GamePivot/FSWONWindow/content/FreespinsWonWindow/LabelsAndSprites/FreeSpins_label", "oldContent": "FREE SPINS!", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 51547, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "extraPayloads": [], "path": "Game/GamePivot/FSResultWindow/content/PressAnywhere_Label/label", "oldContent": "Press anywhere to continue...", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 51548, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "extraPayloads": [], "path": "Game/GamePivot/Reels/ThePivot/BonusMessages/Hooks/Labels/uilabel", "oldContent": "MORE HOOKS AND EXPLOSIONS!", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 51549, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/CAT/RuleHolder2/Rule2", "oldContent": "When buying the FREE SPINS round, on the triggering spin 3, 4 or 5 SCATTERS can hit randomly.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 51550, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder4/Label4", "oldContent": "- START FROM LEVEL 2 - The round starts from level 2 in the progressive feature.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 51551, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder5/Label5", "oldContent": "- +2 SPINS - The subsequent round starts with 2 more free spins from the beginning and 2 more spins are added to every retrigger.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 51552, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/RuleHolder1/Rule1", "oldContent": "Hit 3 or more SCATTER symbols to trigger the FREE SPINS feature.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 51553, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/RulesTop/AllValuesExpressed", "oldContent": "All values are expressed as actual wins in coins.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 51554, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/Volatility/VolatilityDescription", "oldContent": "High volatility games pay out less often on average but the chance to hit big wins in a short time span is higher", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 51555, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/MoneySymbolHolder/DescriptionHolder/MoneySymbolRules/Label3", "oldContent": "The fish paying symbols are also MONEY symbols. At every spin, the fish take a random money value which can be won during the FREE SPINS feature.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 51556, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/RulesBottom/RTP/TheoreticalRTP/Label", "oldContent": "The theoretical RTP of this game is {0}%", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 51557, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder8/Rule8", "oldContent": "Also randomly, when there are fisherman symbols on the screen but no fish, at the end of a free spin, a bazooka animation can appear and change all the symbols from the screen, except for fisherman symbols to something else.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 51558, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "extraPayloads": [], "path": "Paytable/Pages/Page1/Rules/AllSymbolsPayLabel", "oldContent": "All symbols pay from left to right on adjacent reels starting from the leftmost reel.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 51559, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder5/Rule5", "oldContent": "After the fourth level, the feature cannot be retriggered anymore.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 51560, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/FreeSpinsHolder2/SpecialReelsHolder/SpecialReels", "oldContent": "Special reels are in play during the feature.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 51561, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/CAT/RuleHolder1/Rule1", "oldContent": "The FREE SPINS round can be instantly triggered from the base game by buying it for 100x current total bet.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 51562, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/RuleHolder2/Rule2", "oldContent": "In the base game whenever 2 SCATTER symbols hit without a third, there is a chance for another one to be brought onto the screen by a random feature:", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 51563, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder1/Label1", "oldContent": "- MORE FISH - More fish symbols are present on the reel strips during the subsequent free spins round", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 51564, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder2/Rule2", "oldContent": "All the WILD symbols that hit during the feature are collected until the end of the round.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 51565, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/RulesTop/AllSymbolsPay", "oldContent": "All symbols pay from left to right on selected paylines.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 51566, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder3/Label3", "oldContent": "- MORE DYNAMITES, HOOKS AND BAZOOKAS - During the round, the chance to hit dynamite, hook or bazooka spin feature is increased.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 51567, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/RulesBottom/SpaceAndEnter", "oldContent": "SPACE and ENTER buttons on the keyboard can be used to start and stop the spin.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 51568, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder7/Rule7", "oldContent": "Randomly, when there are fish symbols on the screen but no fisherman, at the end of a free spin, a hook will appear pulling a random reel up to bring fisherman symbols onto the screen.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 51569, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder1/Rule1", "oldContent": "During the FREE SPINS feature each WILD symbol also collects all the values from MONEY symbols on the screen.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 51570, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/MaxWin/TitleHolder/Title", "oldContent": "MAX WIN", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 51571, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder1/LabelHolder2/Label2", "oldContent": "4x SCATTER awards 15 free spins", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 51572, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/MinMaxHolder/MaxBet/MaximumText", "oldContent": "MAXIMUM BET:", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 51573, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FreeSpinsHolder1/TitleHolder/Title", "oldContent": "FREE SPINS", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 51574, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/MoneySymbolHolder/TitleHolder/Title", "oldContent": "MONEY SYMBOL", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 51575, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "extraPayloads": [], "path": "Paytable/Pages/Page1/WildHolder/DescriptionHolder/Label1", "oldContent": "This is the WILD symbol.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 51576, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "extraPayloads": [], "path": "Paytable/Pages/Page1/Title/PaytableTitleLabel1", "oldContent": "GAME RULES", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 51577, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/CAT/TitleHolder/Title1New", "oldContent": "BUY FREE SPINS", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 51578, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/MinMaxHolder/MinBet/MinimumText", "oldContent": "MINIMUM BET:", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 51579, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/RulesBottom/MalfunctionLabel", "oldContent": "Malfunction voids all pays and plays.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 51580, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "extraPayloads": [], "path": "Paytable/Pages/Page1/ScatterHolder/DescriptionHolder/Label2", "oldContent": "It appears on all reels.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 51581, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder1/LabelHolder1/Label1", "oldContent": "5x SCATTER awards 20 free spins", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 51582, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/RulesTop/AllWinsMultiplied", "oldContent": "All wins are multiplied by bet per line.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 51583, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "extraPayloads": [], "path": "Paytable/Pages/Page1/ScatterHolder/DescriptionHolder/Label1", "oldContent": "This is the SCATTER symbol.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 51584, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/RulesTop/OnlyTheHighestWin", "oldContent": "Only the highest win is paid per line.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 51585, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder1/LabelHolder3/Label3", "oldContent": "3x SCATTER awards 10 free spins", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 51586, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder2/LabelHolder1/Label1", "oldContent": "- Randomly, if the SCATTERS on the screen can move down one position without leaving the reel area, a respin is triggered where the reels with SCATTERS move one position down and the reels without SCATTERS respin.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 51587, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder4/Rule4", "oldContent": "The retriggered spins are played after the previous batch of free spins ends. The multiplier applies to the retriggered spins.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 51588, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/RulesTop/WhenWinningOnMultiplePaylines", "oldContent": "When winning on multiple paylines, all wins are added to the total win.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 51589, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/Title/PaytableTitleLabel", "oldContent": "GAME RULES", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 51590, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/MaxWin/RuleHolder/Label1", "oldContent": "The maximum win amount is limited to {0}x bet. If the total win of a FREE SPINS ROUND reaches {1}x the round immediately ends, win is awarded and all remaining free spins are forfeited", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 51591, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/RulesBottom/RTP/TheoreticalRTPBONUS/Label", "oldContent": "The RTP of the game when using \"BUY FREE SPINS\" is {0}%", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 51592, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "extraPayloads": [], "path": "Paytable/Pages/Page4/Volatility/VolatilityMeter/LabelHolder/VolatilityLabel", "oldContent": "VOLATILITY", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 51593, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder6/Rule6", "oldContent": "Randomly, when there are fisherman symbols on the screen but no fish, at the end of a free spin, fish MONEY symbols can appear in random positions via the dynamite spin feature.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 51594, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder2/LabelHolder2/Label2", "oldContent": "- Randomly, a hook can pull one of the reels up to reveal another SCATTER.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 51595, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/RuleHolder3/Rule3", "oldContent": "Before the round starts, 0 to 5 modifiers that apply to the subsequent round are randomly selected:", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 51596, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "extraPayloads": [], "path": "Paytable/Pages/Page1/WildHolder/DescriptionHolder/Label2", "oldContent": "It appears on all reels during the FREE SPINS round.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 51597, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "extraPayloads": [], "path": "Paytable/Pages/Page1/WildHolder/DescriptionHolder/Label3", "oldContent": "Substitutes for all symbols except SCATTER.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 51598, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder3/Rule3", "oldContent": "Every 4th WILD symbol collected retriggers the feature, awards 10 more free spins and the multiplier for MONEY symbol collection increases to 2x for the second level, 3x for the third level and 10x for the fourth level.  ", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 51599, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder2/Label2", "oldContent": "- MORE FISHERMAN - More WILD symbols are present on the reel strips during the subsequent free spins round", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 51600, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "extraPayloads": [], "path": "IntroScreen/content/Labels_Holder_landscape/Label_Holder_bigger_1/Label_2 (1)", "oldContent": "BIG FREE SPINS MODIFIERS!", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 51601, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "extraPayloads": [], "path": "IntroScreen/content/Labels_Holder_landscape/Label_Holder_bigger_1/Label_1", "oldContent": "GO FISHIN' FOR", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 51602, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "extraPayloads": [], "path": "IntroScreen/content/IntroButtons/ButtonSkipIntro/content/TextHolder/Label_1", "oldContent": "DON'T SHOW NEXT TIME", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 51603, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "extraPayloads": [], "path": "Paytable/Pages/Page2/MoneySymbolHolder/DescriptionHolder/PossibleValues/Label", "oldContent": "Possible values are: 2x, 5x, 10x, 15x, 20x, 25x, 50x, 100x, 200x, 500x, 1666x, 2500x or 5000x total bet.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}, {"nestedFieldType": "LabelChange", "serializableData": {"Target": {"fileID": 0}, "newContent": {"fileID": 51604, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "extraPayloads": [], "path": "Paytable/Pages/Page3/MaxWin/RuleHolder/HolderLabelJackpot/Label1New", "oldContent": "The maximum win amount is limited to {0}x bet except Jack<PERSON>. If the total win of a FREE SPINS round reaches {1}x bet the round immediately ends, win is awarded and all remaining free spins are forfeited.", "hasDynamicContent": false, "isSet": true, "hasCustomEffects": false}}], "Spines": [], "revisionNumber": 0}, "fileID": 51605}], "fileID": 51606}, {"name": "Game/GamePivot/FSWONWindow/content/FreespinsWonWindow/LabelsAndSprites/Catches_label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 51517, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "children": [], "psr": "d"}, "fileID": 51430}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "368a94b3b6002d342afe818bbd43a900"}, "_text": "CAPTURA", "fontSize": 50, "width": 338, "height": 110, "overflow": 0}, "fileID": 51518}], "fileID": 51607}, {"name": "Game/GamePivot/FreeSpinsPurchase/BuyButtons/Options/Option_0/OptVisual/Texts/BoughtText/LandscapeText/label1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 51517, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "children": [], "psr": "d"}, "fileID": 51431}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "a5e7d3f70463741418b374e469723061"}, "_text": "ACTIVA", "fontSize": 40, "width": 170, "height": 33, "overflow": 0}, "fileID": 51519}], "fileID": 51608}, {"name": "Game/GamePivot/FSWONWindow/PressAnywhere_Label/label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 51517, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "children": [], "psr": "d"}, "fileID": 51432}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "368a94b3b6002d342afe818bbd43a900"}, "_text": "Haga clic en cualquier parte para continuar...", "fontSize": 40, "width": 1340, "height": 65, "overflow": 0}, "fileID": 51520}], "fileID": 51609}, {"name": "Game/GamePivot/FSExtraWindow/content/PressAnywhere_Label/label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 51517, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "children": [], "psr": "d"}, "fileID": 51433}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "368a94b3b6002d342afe818bbd43a900"}, "_text": "Haga clic en cualquier parte para continuar...", "fontSize": 40, "width": 1340, "height": 65, "overflow": 0}, "fileID": 51521}], "fileID": 51610}, {"name": "Game/GamePivot/FreeSpinsPurchase/FSPurchaseWindow/Content/AnimatedPivot/Texts/BuyText/label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 51517, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "children": [], "psr": "d"}, "fileID": 51434}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "368a94b3b6002d342afe818bbd43a900"}, "_text": "COMPRE TIRADAS GRATIS", "fontSize": 69, "width": 826, "height": 69, "overflow": 0}, "fileID": 51522}], "fileID": 51611}, {"name": "Game/GamePivot/FsFeature/content/Feature_trail/Holder/fisherman_highlight/ScalerX10/stretcher_fs/spins", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 51517, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "children": [], "psr": "d"}, "fileID": 51435}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "c2cc4272dc3145a49bdc18644161962b"}, "_text": "tiradas", "fontSize": 30, "width": 64, "height": 33, "overflow": 0}, "fileID": 51523}], "fileID": 51612}, {"name": "Game/Background/PaytableOnScreen/Portrait/Message1/labelMsg1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 51517, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "children": [], "psr": "d"}, "fileID": 51436}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "fdea7b1d0f2905349b6698cc6f1882ef"}, "_text": "TODOS LOS SÍMBOLOS GENERAN GANANCIAS DE IZQUIERDA A DERECHA. EL BONUS SE PAGA EN CUALQUIER POSICIÓN.", "fontSize": 40, "width": 2006, "height": 40, "alignment": 2}, "fileID": 51524}], "fileID": 51613}, {"name": "Game/GamePivot/FsFeature/content/Feature_trail/Holder/fisherman_highlight/ScalerX3/stretcher_fs/free", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 51517, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "children": [], "psr": "d"}, "fileID": 51437}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "c2cc4272dc3145a49bdc18644161962b"}, "_text": "gratis", "fontSize": 30, "width": 60, "height": 33, "overflow": 0}, "fileID": 51525}], "fileID": 51614}, {"name": "Game/GamePivot/Reels/ThePivot/BonusMessages/Fisherman/Labels/uilabel", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 51517, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "children": [], "psr": "d"}, "fileID": 51438}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "368a94b3b6002d342afe818bbd43a900"}, "_text": "¡MÁS PESCADORES!", "fontSize": 256, "width": 1000, "height": 450, "overflow": 0}, "fileID": 51526}], "fileID": 51615}, {"name": "Game/GamePivot/FreeSpinsPurchase/BuyButtons/Options/Option_0/OptVisual/Texts/BoughtText/LandscapeText/label0", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 51517, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "children": [], "psr": "d"}, "fileID": 51439}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "a5e7d3f70463741418b374e469723061"}, "_text": "FUNCIÓN", "fontSize": 40, "width": 170, "height": 30, "overflow": 0}, "fileID": 51527}], "fileID": 51616}, {"name": "Game/GamePivot/FreeSpinsPurchase/BuyButtons/Options/Option_0/OptVisual/Texts/BuyText/PortraitText/label0", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 51517, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "children": [], "psr": "d"}, "fileID": 51440}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "368a94b3b6002d342afe818bbd43a900"}, "_text": "COMPRE TIRADAS GRATIS", "fontSize": 26, "width": 270, "height": 70, "overflow": 0}, "fileID": 51528}], "fileID": 51617}, {"name": "Game/GamePivot/FsFeature/content/Feature_trail/Holder/fisherman_highlight/ScalerX2/stretcher_fs/free", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 51517, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "children": [], "psr": "d"}, "fileID": 51441}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "c2cc4272dc3145a49bdc18644161962b"}, "_text": "gratis", "fontSize": 30, "width": 60, "height": 33, "overflow": 0}, "fileID": 51529}], "fileID": 51618}, {"name": "Game/GamePivot/FSResultWindow/content/SignPivot/FreespinsCongratsWindow/Labels/Congrats_label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 51517, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "children": [], "psr": "d"}, "fileID": 51442}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "368a94b3b6002d342afe818bbd43a900"}, "_text": "ENHORABUENA!", "fontSize": 169, "width": 1000, "height": 75, "overflow": 0}, "fileID": 51530}], "fileID": 51619}, {"name": "Game/GamePivot/FSExtraWindow/content/SignPivot/FreespinsExtraWindow/Labels/Congrats_label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 51517, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "children": [], "psr": "d"}, "fileID": 51443}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "368a94b3b6002d342afe818bbd43a900"}, "_text": "ENHORABUENA!", "fontSize": 169, "width": 1000, "height": 75, "overflow": 0}, "fileID": 51531}], "fileID": 51620}, {"name": "Game/GamePivot/FSExtraWindow/content/SignPivot/FreespinsExtraWindow/Labels/FreeSpins_label ", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 51517, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "children": [], "psr": "d"}, "fileID": 51444}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "368a94b3b6002d342afe818bbd43a900"}, "_text": "TIRADAS GRATIS", "fontSize": 75, "width": 537, "height": 73, "overflow": 0}, "fileID": 51532}], "fileID": 51621}, {"name": "Game/GamePivot/FSExtraWindow/content/SignPivot/FreespinsExtraWindow/Labels/AreNow_label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 51517, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "children": [], "psr": "d"}, "fileID": 51445}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "368a94b3b6002d342afe818bbd43a900"}, "_text": "AHORA SON", "fontSize": 70, "width": 453, "height": 100, "overflow": 0}, "fileID": 51533}], "fileID": 51622}, {"name": "Game/GamePivot/FreeSpinsPurchase/BuyButtons/Options/Option_0/OptVisual/Texts/BuyText/LandscapeTest/label0", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 51517, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "children": [], "psr": "d"}, "fileID": 51446}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "368a94b3b6002d342afe818bbd43a900"}, "_text": "COMPRE TIRADAS GRATIS", "fontSize": 26, "width": 238, "height": 33, "overflow": 0}, "fileID": 51534}], "fileID": 51623}, {"name": "Game/GamePivot/Reels/ThePivot/BonusMessages/PlusFS/Labels/uilabel", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 51517, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "children": [], "psr": "d"}, "fileID": 51447}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "368a94b3b6002d342afe818bbd43a900"}, "_text": "EXTRA TIRADAS GRATIS", "fontSize": 256, "width": 1200, "height": 450, "overflow": 0}, "fileID": 51535}], "fileID": 51624}, {"name": "Game/GamePivot/FsFeature/content/Feature_trail/Holder/fisherman_highlight/ScalerX10/stretcher_fs/free", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 51517, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "children": [], "psr": "d"}, "fileID": 51448}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "c2cc4272dc3145a49bdc18644161962b"}, "_text": "gratis", "fontSize": 30, "width": 60, "height": 33, "overflow": 0}, "fileID": 51536}], "fileID": 51625}, {"name": "Game/GamePivot/FSWONWindow/content/FreespinsWonWindow/LabelsAndSprites/Congratulations_label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 51517, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "children": [], "psr": "d"}, "fileID": 51449}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "368a94b3b6002d342afe818bbd43a900"}, "_text": "ENHORABUENA!", "fontSize": 169, "width": 1000, "height": 75, "overflow": 0}, "fileID": 51537}], "fileID": 51626}, {"name": "Game/GamePivot/Reels/ThePivot/BonusMessages/Fishes/Labels/uilabel", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 51517, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "children": [], "psr": "d"}, "fileID": 51450}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "368a94b3b6002d342afe818bbd43a900"}, "_text": "¡MÁS PECES!", "fontSize": 256, "width": 1200, "height": 450, "overflow": 0}, "fileID": 51538}], "fileID": 51627}, {"name": "Game/GamePivot/FreeSpinsPurchase/BuyButtons/Options/Option_0/OptVisual/Texts/BoughtText/PortraitText/label0", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 51517, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "children": [], "psr": "d"}, "fileID": 51451}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "a5e7d3f70463741418b374e469723061"}, "_text": "FUNCIÓN ACTIVA", "fontSize": 60, "width": 164, "height": 208, "overflow": 0}, "fileID": 51539}], "fileID": 51628}, {"name": "Game/GamePivot/FSExtraWindow/content/SignPivot/FreespinsExtraWindow/Labels/Extra_label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 51517, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "children": [], "psr": "d"}, "fileID": 51452}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "368a94b3b6002d342afe818bbd43a900"}, "_text": "EXTRA", "fontSize": 75, "width": 263, "height": 169, "overflow": 0}, "fileID": 51540}], "fileID": 51629}, {"name": "Game/GamePivot/FSResultWindow/content/SignPivot/FreespinsCongratsWindow/Labels/YouWon_label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 51517, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "children": [], "psr": "d"}, "fileID": 51453}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "368a94b3b6002d342afe818bbd43a900"}, "_text": "USTED HA GANADO", "fontSize": 110, "width": 1000, "height": 169, "overflow": 0}, "fileID": 51541}], "fileID": 51630}, {"name": "Game/GamePivot/FsFeature/content/Feature_trail/Holder/fisherman_highlight/ScalerX3/stretcher_fs/spins", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 51517, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "children": [], "psr": "d"}, "fileID": 51454}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "c2cc4272dc3145a49bdc18644161962b"}, "_text": "tiradas", "fontSize": 30, "width": 64, "height": 33, "overflow": 0}, "fileID": 51542}], "fileID": 51631}, {"name": "Game/Background/PaytableOnScreen/Landscape/Message1/labelMsg1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 51517, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "children": [], "psr": "d"}, "fileID": 51455}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "fdea7b1d0f2905349b6698cc6f1882ef"}, "_text": "TODOS LOS SÍMBOLOS GENERAN GANANCIAS DE IZQUIERDA A DERECHA. EL BONUS SE PAGA EN CUALQUIER POSICIÓN.", "fontSize": 30, "width": 1504, "height": 30, "alignment": 2}, "fileID": 51543}], "fileID": 51632}, {"name": "Game/GamePivot/FsFeature/content/Feature_trail/Holder/fisherman_highlight/ScalerX2/stretcher_fs/spins", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 51517, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "children": [], "psr": "d"}, "fileID": 51456}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "c2cc4272dc3145a49bdc18644161962b"}, "_text": "tiradas", "fontSize": 30, "width": 64, "height": 33, "overflow": 0}, "fileID": 51544}], "fileID": 51633}, {"name": "Game/GamePivot/Reels/ThePivot/BonusMessages/Level2/Labels/uilabel", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 51517, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "children": [], "psr": "d"}, "fileID": 51457}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "368a94b3b6002d342afe818bbd43a900"}, "_text": "¡COMIENCE DESDE EL NIVEL 2!", "fontSize": 256, "width": 1280, "height": 450, "overflow": 0}, "fileID": 51545}], "fileID": 51634}, {"name": "Game/GamePivot/FSWONWindow/content/FreespinsWonWindow/LabelsAndSprites/FreeSpins_label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 51517, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "children": [], "psr": "d"}, "fileID": 51458}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "368a94b3b6002d342afe818bbd43a900"}, "_text": "TIRADAS GRATIS", "fontSize": 110, "width": 806, "height": 150, "overflow": 0}, "fileID": 51546}], "fileID": 51635}, {"name": "Game/GamePivot/FSResultWindow/content/PressAnywhere_Label/label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 51517, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "children": [], "psr": "d"}, "fileID": 51459}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "368a94b3b6002d342afe818bbd43a900"}, "_text": "Haga clic en cualquier parte para continuar...", "fontSize": 40, "width": 1340, "height": 65, "overflow": 0}, "fileID": 51547}], "fileID": 51636}, {"name": "Game/GamePivot/Reels/ThePivot/BonusMessages/Hooks/Labels/uilabel", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 51517, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "children": [], "psr": "d"}, "fileID": 51460}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "368a94b3b6002d342afe818bbd43a900"}, "_text": "¡MÁS ANZUELOS Y EXPLOSIONES!", "fontSize": 256, "width": 1000, "height": 450, "overflow": 0}, "fileID": 51548}], "fileID": 51637}, {"name": "Paytable/Pages/Page3/CAT/RuleHolder2/Rule2", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 51517, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "children": [], "psr": "d"}, "fileID": 51461}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "6bd0621a4f9a5ef42941f71dac538424"}, "_text": "Al comprar una ronda de TIRADAS GRATIS, en la tirada de relanzamiento pueden aparecer aleatoriamente 3, 4 o 5 SCATTERS.", "fontSize": 25, "anchorY": 0, "width": 1233, "height": 75, "overflow": 0}, "fileID": 51549}], "fileID": 51638}, {"name": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder4/Label4", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 51517, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "children": [], "psr": "d"}, "fileID": 51462}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "6bd0621a4f9a5ef42941f71dac538424"}, "_text": "COMENZAR DESDE EL NIVEL 2 - La ronda comienza desde el nivel 2 de la función progresiva.", "fontSize": 25, "anchorX": 0, "width": 1400, "height": 100, "overflow": 0}, "fileID": 51550}], "fileID": 51639}, {"name": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder5/Label5", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 51517, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "children": [], "psr": "d"}, "fileID": 51463}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "6bd0621a4f9a5ef42941f71dac538424"}, "_text": "- +2 TIRADAS - La subsiguiente ronda comienza con 2 tiradas gratis más desde el principio y 2 tiradas más se añaden en cada relanzamiento.", "fontSize": 25, "anchorX": 0, "width": 1347, "height": 100, "overflow": 0}, "fileID": 51551}], "fileID": 51640}, {"name": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/RuleHolder1/Rule1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 51517, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "children": [], "psr": "d"}, "fileID": 51464}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "6bd0621a4f9a5ef42941f71dac538424"}, "_text": "Consiga 3 o mas símbolos SCATTER para lanzar la función de TIRADAS GRATIS.", "fontSize": 25, "width": 1400, "height": 100, "overflow": 0}, "fileID": 51552}], "fileID": 51641}, {"name": "Paytable/Pages/Page4/RulesTop/AllValuesExpressed", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 51517, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "children": [], "psr": "d"}, "fileID": 51465}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "6bd0621a4f9a5ef42941f71dac538424"}, "_text": "Todos los valores se expresan como ganancias reales en monedas.", "fontSize": 25, "width": 1333, "height": 160, "overflow": 0}, "fileID": 51553}], "fileID": 51642}, {"name": "Paytable/Pages/Page4/Volatility/VolatilityDescription", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 51517, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "children": [], "psr": "d"}, "fileID": 51466}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "6bd0621a4f9a5ef42941f71dac538424"}, "_text": "Los juegos de alta volatilidad pagan con una frecuencia menor a la media pero la posibilidad de ganar grandes premios en cortos periodos de tiempo es mayor.", "fontSize": 25, "anchorY": 0, "width": 1368, "height": 93, "overflow": 0}, "fileID": 51554}], "fileID": 51643}, {"name": "Paytable/Pages/Page2/MoneySymbolHolder/DescriptionHolder/MoneySymbolRules/Label3", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 51517, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "children": [], "psr": "d"}, "fileID": 51467}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "6bd0621a4f9a5ef42941f71dac538424"}, "_text": "Los símbolos de pez pagadores también son símbolos DINERO. En cada tirada, el pez toma un valor aleatorio de dinero que puede ser ganado durante la función de TIRADAS GRATIS.", "fontSize": 25, "anchorX": 0, "width": 935, "height": 100, "overflow": 0}, "fileID": 51555}], "fileID": 51644}, {"name": "Paytable/Pages/Page4/RulesBottom/RTP/TheoreticalRTP/Label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 51517, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "children": [], "psr": "d"}, "fileID": 51468}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "6bd0621a4f9a5ef42941f71dac538424"}, "_text": "El RTP teórico de este juego es del {0}%", "fontSize": 25, "anchorY": 1, "width": 1200, "height": 75, "overflow": 0}, "fileID": 51556}], "fileID": 51645}, {"name": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder8/Rule8", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 51517, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "children": [], "psr": "d"}, "fileID": 51469}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "6bd0621a4f9a5ef42941f71dac538424"}, "_text": "Tam<PERSON><PERSON> aleatoriamente, cuando hay símbolos pescador en pantalla pero no de pez, al final de la tirada gratis, puede aparecer una animación de bazuca y cambiar todos los símbolos en pantalla, salvo los símbolos pescador, en otra cosa.", "fontSize": 25, "width": 1251, "height": 100, "overflow": 0}, "fileID": 51557}], "fileID": 51646}, {"name": "Paytable/Pages/Page1/Rules/AllSymbolsPayLabel", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 51517, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "children": [], "psr": "d"}, "fileID": 51470}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "6bd0621a4f9a5ef42941f71dac538424"}, "_text": "Todos los símbolos generan ganancias de izquierda a derecha en rodillos adyacentes, a partir del rodillo de más a la izquierda.", "fontSize": 25, "width": 1000, "height": 60, "overflow": 0}, "fileID": 51558}], "fileID": 51647}, {"name": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder5/Rule5", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 51517, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "children": [], "psr": "d"}, "fileID": 51471}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "6bd0621a4f9a5ef42941f71dac538424"}, "_text": "Tras el cuarto nivel, la función no puede relanzarse más.", "fontSize": 25, "width": 1400, "height": 100, "overflow": 0}, "fileID": 51559}], "fileID": 51648}, {"name": "Paytable/Pages/Page3/FreeSpinsHolder2/SpecialReelsHolder/SpecialReels", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 51517, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "children": [], "psr": "d"}, "fileID": 51472}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "6bd0621a4f9a5ef42941f71dac538424"}, "_text": "Se emplean rodillos especiales durante la función.", "fontSize": 25, "width": 1400, "height": 100, "overflow": 0}, "fileID": 51560}], "fileID": 51649}, {"name": "Paytable/Pages/Page3/CAT/RuleHolder1/Rule1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 51517, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "children": [], "psr": "d"}, "fileID": 51473}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "6bd0621a4f9a5ef42941f71dac538424"}, "_text": "La RONDA DE TIRADAS GRATIS puede originarse instantáneamente desde el juego base comprándolo por 100x la apuesta total actual. ", "fontSize": 25, "anchorY": 1, "width": 1400, "height": 100, "overflow": 0}, "fileID": 51561}], "fileID": 51650}, {"name": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/RuleHolder2/Rule2", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 51517, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "children": [], "psr": "d"}, "fileID": 51474}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "6bd0621a4f9a5ef42941f71dac538424"}, "_text": "En el juego base, cuando aparezcan 2 símbolos SCATTER sin un terecro, hay una posibilidad de traer uno más a la pantalla mediante una función aleatoria:", "fontSize": 25, "width": 1169, "height": 100, "overflow": 0}, "fileID": 51562}], "fileID": 51651}, {"name": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder1/Label1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 51517, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "children": [], "psr": "d"}, "fileID": 51475}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "6bd0621a4f9a5ef42941f71dac538424"}, "_text": "- MÁS PECES - Hay más símbolos pez presentes en las tiras de los rodillos durante la subsiguiente ronda de tiradas gratis", "fontSize": 25, "anchorX": 0, "width": 994, "height": 89, "overflow": 0}, "fileID": 51563}], "fileID": 51652}, {"name": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder2/Rule2", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 51517, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "children": [], "psr": "d"}, "fileID": 51476}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "6bd0621a4f9a5ef42941f71dac538424"}, "_text": "Todos los símbolos WILD que aparezcan durante la función se recopilan hasta el final de la ronda.", "fontSize": 25, "width": 1400, "height": 100, "overflow": 0}, "fileID": 51564}], "fileID": 51653}, {"name": "Paytable/Pages/Page4/RulesTop/AllSymbolsPay", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 51517, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "children": [], "psr": "d"}, "fileID": 51477}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "6bd0621a4f9a5ef42941f71dac538424"}, "_text": "Todos los símbolos generan ganancias de izquierda a derecha en las líneas de pago seleccionadas.", "fontSize": 25, "width": 1194, "height": 160, "overflow": 0, "spacingY": 5}, "fileID": 51565}], "fileID": 51654}, {"name": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder3/Label3", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 51517, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "children": [], "psr": "d"}, "fileID": 51478}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "6bd0621a4f9a5ef42941f71dac538424"}, "_text": "- MÁS DINAMITAS, ANZUELOS Y BAZUCAS - Durante la ronda, la posibilidad de conseguir la función de tirada dinamita, anzuelo o bazuca se incrementa.", "fontSize": 25, "anchorX": 0, "width": 1359, "height": 100, "overflow": 0}, "fileID": 51566}], "fileID": 51655}, {"name": "Paytable/Pages/Page4/RulesBottom/SpaceAndEnter", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 51517, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "children": [], "psr": "d"}, "fileID": 51479}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "6bd0621a4f9a5ef42941f71dac538424"}, "_text": "Los botones ESPACIO e INTRO del teclado pueden usarse para iniciar o detener el juego.", "fontSize": 25, "width": 1400, "height": 60, "overflow": 0}, "fileID": 51567}], "fileID": 51656}, {"name": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder7/Rule7", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 51517, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "children": [], "psr": "d"}, "fileID": 51480}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "6bd0621a4f9a5ef42941f71dac538424"}, "_text": "Aleatoriamente, cuando hay símbolos pez en pantalla pero no de pescador, al final de una tirada gratis aparecerá un anzuelo tirando hacia arriba de un rodillo aleatorio para traer símbolos pescador a la pantalla.", "fontSize": 25, "width": 1384, "height": 100, "overflow": 0}, "fileID": 51568}], "fileID": 51657}, {"name": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder1/Rule1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 51517, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "children": [], "psr": "d"}, "fileID": 51481}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "6bd0621a4f9a5ef42941f71dac538424"}, "_text": "Durante la función de TIRADAS GRATIS cada símbolo WILD también recopila todos los valores de los símbolos DINERO en pantalla.", "fontSize": 25, "width": 1183, "height": 100, "overflow": 0}, "fileID": 51569}], "fileID": 51658}, {"name": "Paytable/Pages/Page3/MaxWin/TitleHolder/Title", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 51517, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "children": [], "psr": "d"}, "fileID": 51482}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "6bd0621a4f9a5ef42941f71dac538424"}, "_text": "PREMIO MAX", "fontSize": 35, "width": 1100, "height": 100, "overflow": 0}, "fileID": 51570}], "fileID": 51659}, {"name": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder1/LabelHolder2/Label2", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 51517, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "children": [], "psr": "d"}, "fileID": 51483}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "6bd0621a4f9a5ef42941f71dac538424"}, "_text": "4x símbolos SCATTER otorgan 15 tiradas gratis.", "fontSize": 25, "width": 1400, "height": 75, "overflow": 0}, "fileID": 51571}], "fileID": 51660}, {"name": "Paytable/Pages/Page4/MinMaxHolder/MaxBet/MaximumText", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 51517, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "children": [], "psr": "d"}, "fileID": 51484}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "6bd0621a4f9a5ef42941f71dac538424"}, "_text": "APUESTA MÁXIMA:", "fontSize": 25, "anchorX": 0, "width": 224, "height": 26}, "fileID": 51572}], "fileID": 51661}, {"name": "Paytable/Pages/Page2/FreeSpinsHolder1/TitleHolder/Title", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 51517, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "children": [], "psr": "d"}, "fileID": 51485}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "6bd0621a4f9a5ef42941f71dac538424"}, "_text": "TIRADAS GRATIS", "fontSize": 35, "width": 1150, "height": 100, "overflow": 0}, "fileID": 51573}], "fileID": 51662}, {"name": "Paytable/Pages/Page2/MoneySymbolHolder/TitleHolder/Title", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 51517, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "children": [], "psr": "d"}, "fileID": 51486}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "6bd0621a4f9a5ef42941f71dac538424"}, "_text": "SÍMBOLO DINERO", "fontSize": 35, "width": 1150, "height": 70, "overflow": 0}, "fileID": 51574}], "fileID": 51663}, {"name": "Paytable/Pages/Page1/WildHolder/DescriptionHolder/Label1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 51517, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "children": [], "psr": "d"}, "fileID": 51487}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "6bd0621a4f9a5ef42941f71dac538424"}, "_text": "Este es el símbolo WILD.", "fontSize": 25, "anchorX": 0, "anchorY": 1, "width": 363, "height": 75, "overflow": 0}, "fileID": 51575}], "fileID": 51664}, {"name": "Paytable/Pages/Page1/Title/PaytableTitleLabel1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 51517, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "children": [], "psr": "d"}, "fileID": 51488}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "6bd0621a4f9a5ef42941f71dac538424"}, "_text": "REGLAS DEL JUEGO", "fontSize": 35, "width": 1150, "height": 100, "overflow": 0}, "fileID": 51576}], "fileID": 51665}, {"name": "Paytable/Pages/Page3/CAT/TitleHolder/Title1New", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 51517, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "children": [], "psr": "d"}, "fileID": 51489}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "6bd0621a4f9a5ef42941f71dac538424"}, "_text": "COMPRE TIRADAS GRATIS", "fontSize": 35, "width": 1400, "height": 70, "overflow": 0}, "fileID": 51577}], "fileID": 51666}, {"name": "Paytable/Pages/Page4/MinMaxHolder/MinBet/MinimumText", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 51517, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "children": [], "psr": "d"}, "fileID": 51490}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "6bd0621a4f9a5ef42941f71dac538424"}, "_text": "APUESTA MÍNIMA:", "fontSize": 25, "anchorX": 0, "width": 220, "height": 26}, "fileID": 51578}], "fileID": 51667}, {"name": "Paytable/Pages/Page4/RulesBottom/MalfunctionLabel", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 51517, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "children": [], "psr": "d"}, "fileID": 51491}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "6bd0621a4f9a5ef42941f71dac538424"}, "_text": "En caso de funcionamiento erróneo, se anularán todos los pagos y las jugadas.", "fontSize": 25, "width": 992, "height": 60, "overflow": 0}, "fileID": 51579}], "fileID": 51668}, {"name": "Paytable/Pages/Page1/ScatterHolder/DescriptionHolder/Label2", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 51517, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "children": [], "psr": "d"}, "fileID": 51492}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "6bd0621a4f9a5ef42941f71dac538424"}, "_text": "Aparece en todos los rodillos.", "fontSize": 25, "anchorX": 0, "anchorY": 0, "width": 357, "height": 75, "overflow": 0}, "fileID": 51580}], "fileID": 51669}, {"name": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder1/LabelHolder1/Label1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 51517, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "children": [], "psr": "d"}, "fileID": 51493}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "6bd0621a4f9a5ef42941f71dac538424"}, "_text": "5x símbolos SCATTER otorgan 20 tiradas gratis.", "fontSize": 25, "width": 1400, "height": 75, "overflow": 0}, "fileID": 51581}], "fileID": 51670}, {"name": "Paytable/Pages/Page4/RulesTop/AllWinsMultiplied", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 51517, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "children": [], "psr": "d"}, "fileID": 51494}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "6bd0621a4f9a5ef42941f71dac538424"}, "_text": "Todas las ganancias se multiplican por la apuesta por línea.", "fontSize": 25, "width": 1194, "height": 160, "overflow": 0, "spacingY": 5}, "fileID": 51582}], "fileID": 51671}, {"name": "Paytable/Pages/Page1/ScatterHolder/DescriptionHolder/Label1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 51517, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "children": [], "psr": "d"}, "fileID": 51495}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "6bd0621a4f9a5ef42941f71dac538424"}, "_text": "Este es el símbolo SCATTER. ", "fontSize": 25, "anchorX": 0, "anchorY": 1, "width": 357, "height": 75, "overflow": 0}, "fileID": 51583}], "fileID": 51672}, {"name": "Paytable/Pages/Page4/RulesTop/OnlyTheHighestWin", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 51517, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "children": [], "psr": "d"}, "fileID": 51496}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "6bd0621a4f9a5ef42941f71dac538424"}, "_text": "Sólo se paga la ganancia más alta por línea. ", "fontSize": 25, "anchorY": 0, "width": 1400, "height": 50, "overflow": 0}, "fileID": 51584}], "fileID": 51673}, {"name": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder1/LabelHolder3/Label3", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 51517, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "children": [], "psr": "d"}, "fileID": 51497}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "6bd0621a4f9a5ef42941f71dac538424"}, "_text": "3x símbolos SCATTER otorgan 10 tiradas gratis.", "fontSize": 25, "width": 1400, "height": 75, "overflow": 0}, "fileID": 51585}], "fileID": 51674}, {"name": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder2/LabelHolder1/Label1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 51517, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "children": [], "psr": "d"}, "fileID": 51498}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "6bd0621a4f9a5ef42941f71dac538424"}, "_text": "- Aleatoriamente, si los SCATTERS en pantalla pueden moverse una posición hacia abajo sin salir del área de los rodillos, se origina un respin en el que los rodillos con SCATTERS se mueven una posición hacia abajo y los rodillos son SCATTERS hacen respin.", "fontSize": 25, "anchorX": 0, "width": 1400, "height": 125, "overflow": 0}, "fileID": 51586}], "fileID": 51675}, {"name": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder4/Rule4", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 51517, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "children": [], "psr": "d"}, "fileID": 51499}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "6bd0621a4f9a5ef42941f71dac538424"}, "_text": "Las tiradas relanzadas se juegan después de que la tanda previa de tiradas gratis finalice. El multiplicador se aplica a las tiradas relanzadas.", "fontSize": 25, "width": 1400, "height": 125, "overflow": 0}, "fileID": 51587}], "fileID": 51676}, {"name": "Paytable/Pages/Page4/RulesTop/WhenWinningOnMultiplePaylines", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 51517, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "children": [], "psr": "d"}, "fileID": 51500}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "6bd0621a4f9a5ef42941f71dac538424"}, "_text": "<PERSON>uando ganes en múltiples líneas de premio, todas las ganancias se añaden a la ganancia total.", "fontSize": 25, "anchorY": 0, "width": 1400, "height": 50, "overflow": 0}, "fileID": 51588}], "fileID": 51677}, {"name": "Paytable/Pages/Page4/Title/PaytableTitleLabel", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 51517, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "children": [], "psr": "d"}, "fileID": 51501}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "6bd0621a4f9a5ef42941f71dac538424"}, "_text": "REGLAS DEL JUEGO", "fontSize": 30, "width": 1150, "height": 100, "overflow": 0}, "fileID": 51589}], "fileID": 51678}, {"name": "Paytable/Pages/Page3/MaxWin/RuleHolder/Label1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 51517, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "children": [], "psr": "d"}, "fileID": 51502}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "6bd0621a4f9a5ef42941f71dac538424"}, "_text": "El importe máximo de premio está limitado a {0}x la apuesta. Si el premio total de una RONDA DE TIRADAS GRATIS alcanza {1}x, la ronda termina inmediatamente, se otorga el premio y las restantes tiradas gratis se pierden.", "fontSize": 25, "anchorY": 0, "width": 1400, "height": 100, "overflow": 0}, "fileID": 51590}], "fileID": 51679}, {"name": "Paytable/Pages/Page4/RulesBottom/RTP/TheoreticalRTPBONUS/Label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 51517, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "children": [], "psr": "d"}, "fileID": 51503}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "6bd0621a4f9a5ef42941f71dac538424"}, "_text": "El RTP del juego al usar \"COMPRAR TIRADAS GRATIS\" es del {0}%", "fontSize": 25, "anchorY": 0, "width": 1200, "height": 75, "overflow": 0}, "fileID": 51591}], "fileID": 51680}, {"name": "Paytable/Pages/Page4/Volatility/VolatilityMeter/LabelHolder/VolatilityLabel", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 51517, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "children": [], "psr": "d"}, "fileID": 51504}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "fdea7b1d0f2905349b6698cc6f1882ef"}, "_text": "VOLATILIDAD", "fontSize": 22, "anchorX": 0, "width": 122, "height": 22}, "fileID": 51592}], "fileID": 51681}, {"name": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder6/Rule6", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 51517, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "children": [], "psr": "d"}, "fileID": 51505}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "6bd0621a4f9a5ef42941f71dac538424"}, "_text": "Aleatoriamente, cuando hay símbolos de pescador en pantalla pero no de pez, al final de una tirada gratis pueden aparecer símbolos pez DINERO en posiciones aleatorias mediante la función de tirada dinamita.", "fontSize": 25, "width": 1271, "height": 100, "overflow": 0}, "fileID": 51593}], "fileID": 51682}, {"name": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder2/LabelHolder2/Label2", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 51517, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "children": [], "psr": "d"}, "fileID": 51506}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "6bd0621a4f9a5ef42941f71dac538424"}, "_text": "- Aleatoriamente, un anzuelo puede tirar hacia arriba de uno de los rodillos para revelar otro SCATTER.", "fontSize": 25, "anchorX": 0, "width": 1400, "height": 75, "overflow": 0}, "fileID": 51594}], "fileID": 51683}, {"name": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/RuleHolder3/Rule3", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 51517, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "children": [], "psr": "d"}, "fileID": 51507}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "6bd0621a4f9a5ef42941f71dac538424"}, "_text": "Antes de que comience la ronda, se seleccionan aleatoriamente de 0 a 5 modificadores que se aplican en la ronda subsiguiente:", "fontSize": 25, "width": 1217, "height": 100, "overflow": 0}, "fileID": 51595}], "fileID": 51684}, {"name": "Paytable/Pages/Page1/WildHolder/DescriptionHolder/Label2", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 51517, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "children": [], "psr": "d"}, "fileID": 51508}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "6bd0621a4f9a5ef42941f71dac538424"}, "_text": "Aparece en todos los rodillos durante la ronda de TIRADAS GRATIS.", "fontSize": 25, "anchorX": 0, "width": 322, "height": 75, "overflow": 0}, "fileID": 51596}], "fileID": 51685}, {"name": "Paytable/Pages/Page1/WildHolder/DescriptionHolder/Label3", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 51517, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "children": [], "psr": "d"}, "fileID": 51509}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "6bd0621a4f9a5ef42941f71dac538424"}, "_text": "Sustituye a todos los símbolos excepto al SCATTER.", "fontSize": 25, "anchorX": 0, "anchorY": 0, "width": 396, "height": 75, "overflow": 0}, "fileID": 51597}], "fileID": 51686}, {"name": "Paytable/Pages/Page3/FreeSpinsHolder2/RuleHolder3/Rule3", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 51517, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "children": [], "psr": "d"}, "fileID": 51510}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "6bd0621a4f9a5ef42941f71dac538424"}, "_text": "Cada 4º símbolo WILD recopilado relanza la función, otorga 10 tiradas gratis más y el multiplicador para la recopilacicón de símbolos DINERO se incrementa en 2x para el segundo nivel, 3x para el tercer nivel y 10x para el cuarto nivel.", "fontSize": 25, "width": 1249, "height": 85, "overflow": 0}, "fileID": 51598}], "fileID": 51687}, {"name": "Paytable/Pages/Page2/FreeSpinsHolder1/RulesHolder/ListHolder3/LabelHolder2/Label2", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 51517, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "children": [], "psr": "d"}, "fileID": 51511}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "6bd0621a4f9a5ef42941f71dac538424"}, "_text": "- MÁS PESCADORES - Hay más símbolos WILD presentes en las tiras de los rodillos durante la subsiguiente ronda de tiradas gratis", "fontSize": 25, "anchorX": 0, "width": 1400, "height": 100, "overflow": 0}, "fileID": 51599}], "fileID": 51688}, {"name": "IntroScreen/content/Labels_Holder_landscape/Label_Holder_bigger_1/Label_2 (1)", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 51517, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "children": [], "psr": "d"}, "fileID": 51512}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "c2cc4272dc3145a49bdc18644161962b"}, "_text": "GRANDES MODIFICADORES DE TIRADAS GRATIS!", "fontSize": 60, "width": 1000, "height": 80, "overflow": 0}, "fileID": 51600}], "fileID": 51689}, {"name": "IntroScreen/content/Labels_Holder_landscape/Label_Holder_bigger_1/Label_1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 51517, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "children": [], "psr": "d"}, "fileID": 51513}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "c2cc4272dc3145a49bdc18644161962b"}, "_text": "¡SALGA A PESCAR", "fontSize": 60, "width": 1000, "height": 80, "overflow": 0}, "fileID": 51601}], "fileID": 51690}, {"name": "IntroScreen/content/IntroButtons/ButtonSkipIntro/content/TextHolder/Label_1", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 51517, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "children": [], "psr": "d"}, "fileID": 51514}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "6bd0621a4f9a5ef42941f71dac538424"}, "_text": "NO VOLVER A MOSTRAR", "fontSize": 30, "anchorX": 0, "width": 348, "height": 30}, "fileID": 51602}], "fileID": 51691}, {"name": "Paytable/Pages/Page2/MoneySymbolHolder/DescriptionHolder/PossibleValues/Label", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 51517, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "children": [], "psr": "d"}, "fileID": 51515}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "6bd0621a4f9a5ef42941f71dac538424"}, "_text": "Los posibles valores son: 2x, 5x, 10x, 15x, 20x, 25x, 50x, 100x, 200x, 500x, 1666x, 2500x o 5000x la apuesta total.", "fontSize": 25, "anchorX": 0, "width": 1052, "height": 100, "overflow": 0}, "fileID": 51603}], "fileID": 51692}, {"name": "Paytable/Pages/Page3/MaxWin/RuleHolder/HolderLabelJackpot/Label1New", "activeSelf": true, "layer": 0, "components": [{"componentType": "Transform", "enabled": true, "serializableData": {"parent": {"fileID": 51517, "guid": "8565ea4dcdab9a84ab8421a2bb921184"}, "children": [], "psr": "d"}, "fileID": 51516}, {"componentType": "UILabel", "enabled": true, "serializableData": {"fontName": {"fileID": 12800000, "guid": "6bd0621a4f9a5ef42941f71dac538424"}, "_text": "El importe máximo de premio está limitado a {0}x la apuesta excepto en el Jackpot. Si el premio total de una ronda de TIRADAS GRATIS alcanza {1}x la apuesta, la ronda termina inmediatamente, se otorga el premio y todas las tiradas gratis restantes se confiscan.", "fontSize": 25, "anchorY": 0, "width": 1400, "height": 100, "overflow": 0}, "fileID": 51604}], "fileID": 51693}]}}, {"type": "Font", "id": "368a94b3b6002d342afe818bbd43a900", "data": {"fontName": "f368a94b3b6002d342afe818bbd43a9", "path": "@font-face{font-family:'f368a94b3b6002d342afe818bbd43a9';src:url('data:application/x-font-woff;base64,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') format('woff')}"}}, {"type": "Font", "id": "a5e7d3f70463741418b374e469723061", "data": {"fontName": "fa5e7d3f70463741418b374e4697230", "path": "@font-face{font-family:'fa5e7d3f70463741418b374e4697230';src:url('data:application/x-font-woff;base64,d09GRgABAAAAAExQABIAAAAAbAAABQALAAAAAAAAAAAAAAAAAAAAAAAAAABGRlRNAABMNAAAABwAAAAcW0iPTEdERUYAAEk8AAAAIwAAACQAqwDLR1BPUwAASeQAAAJPAAADpMzM4HtHU1VCAABJYAAAAIIAAADWnrKyyk9TLzIAAAIMAAAAUwAAAGB1OJtxY21hcAAAA7QAAACwAAABUi/NJn5jdnQgAAAOTAAABEQAAAYg1ZW3qWZwZ20AAARkAAAETwAAB2THcSxGZ2FzcAAASSwAAAAQAAAAEAAdACFnbHlmAAATiAAAMuEAAEaMXum6gGhlYWQAAAGUAAAANgAAADbgJCumaGhlYQAAAcwAAAAgAAAAJA2QBEZobXR4AAACYAAAAVMAAAHoo2MU72xvY2EAABKQAAAA9gAAAPZ8BGuYbWF4cAAAAewAAAAgAAAAIAR8AjluYW1lAABGbAAAAY0AAAKmM1cqSXBvc3QAAEf8AAABLwAAAefCqR5bcHJlcAAACLQAAAWYAAAIVTLosMcAAQAAAAUcKaGUs5xfDzz1AB8IAAAAAACmksb6AAAAANTe/SD+9/7RBoQHlQAAAAgAAgAAAAAAAHicY2BkYOAQ+hfAwMDW8u/7v+9sLQxAERRQBQCasAbjAAEAAAB6AEQABQArAAMAAgAQAC8AVgAAA5EBmAACAAF4nGNgZl7LOIGBmYGDZQGLJQMQQGhmBYY0pk0crEzcHGzMnEysTCxAKWYGJODr7+fP4MCgoCTHFvxfmjGcQ4hxA1CYESTHqs8WDKQUGJgA7M0JsgB4nI1QPUsDQRSc2313CKYQm3QWulGToDEEA4HzKwgGhSh4ASGFWoaIaCexEMQupaBNsBTURoKFhaWNlYWt+isuChauk5xYuzDM7L55O2/XQ7TUNeDUyEmUVAfG1TCyjbzbJLdgnEMYdYyAMGoOQ7KIeWeDuspaA0anEJNllHSMLMjLLHxtqVdQlArPh6mXIu1cROAdC3/IIfByzBqwodQRyC7rOwj0OpHhvsb9PvPLxCUGeW+g+xG4D6xViE3W5ZdXySPI6ltmbqEsUzb0TgD9SjzZUE8gporsT/XY6Hf66jZUN4D0cfZxztuBT/YlDl+tQWSUOsu/6WLGfumXSHtp5mUI+mSMnKD/jf2PSKk9eDKJoj6yoVtlzwHzz5jzzFz+sUrjqst8e6E7t3uHac5w7uD7U7WQdj7oayJBnVCniKs2620UFHh+39PJ//p+AOVLW7EAeJxjYGBgZoBgGQZGBhDwAfIYwXwWBgMgzQGETEC6juGyktz//0CWAoT1//H//nuuUF1gwMjGAOcygvQwMaACoCQzCysbOwcnFzcPLx+/gKCQsIiomLiEpJS0jKycvIKikrKKqpq6hqaWto6unr6BoZGxiamZuYWllbWNrZ29g6OTs4urm7uHp5e3j6+ff0BgUHBIaFh4RGRUdExsXHxCIgOpIJlkHcggCUMEAHVRH4t4nIVUz1MjRRTunoTdBJJlgAUjs2qPvYlKElFXS4pCaDM/lpCyioRQ1YMeZiCxAidOW7WWh9yW6uD/4MlzD3gIe+If8H/w4NE97hlf9yQRrNq1a368972v+71+800zd7+9t+06tW/Z1uY3G2tff/nki88/W/20WimvfPLxR6XiY/qhTT54/71H1vK7hXeWFh8uzM+Zsw/yuZnpbOb+val0ysCo4lE/JLIUynSJbm9XlU8jAKJbQCgJQP5djiShppG7TAbMH//DZAmTTZjYJBtoo1ohHiXyD5eSIT5ocrB/cWlA5Cttf6ftdEk7eXBsG2YQr9BzicQh8aT/rCe80IX14plphzrd6WoFxdMzYM6AJX16GmN/E2vD8L312ECZPFQld6jryTp1VQkyVfSijtxtcs+1bDuoViR2juihRLQmZ8uaghydRt5z5H2dhhyr7aABiSvX4nxoosOwnOvQTvQDl6koUDnmyvIpdeXTn/4qVCtD/Fuby6wzxKjNr9DOTT+u9103UNnmHf7iNt1KCa9wTJQrxAsif23y21FbPYMAFq1WGi1uQ9XUOydqGy2udwCL4sIqFKkwtc1kw13qKSQ8ITJLa7QnTkL4WMtCotZz+2J5h13d/Il2PCLanNpyy6JB5D6KHyLRen5ZZ6R+N1KtxOZc0un4wezIyOVvG91JTFuariyoetxqrCqidZCIJEcEKuFUGsU19eiuIXG0BjQYAYaOHkP/QmGuqw8xVTQpEa8RCIG++vsuEo2Qe0XzNVKmD8IUwqfEF6GIhjf9Q0pMKuJcTpx6oE20y2HW8OblwJL+eSDNsIfXR9LwWw250PyeQ0E+6UWAwLVFbShsLhhzdt8URiAXEA3seVOurFQrX1Urz2SDnppENrxx4mB9FT6mbSv9DIYMHYIj+02e+AQdWheIrZYDaYQqcj2OLO6rSH8cmUwPKfwovyOMEFqUmdLkmjWXFrzeusRLbwl3kzj8mB6J01NFsctLkRhYpVCcB+6be9lovLWX8klZN0AuODxlGUFiGVZKaQCa56m7A8fH4ESJ9gSEmYYXEYMuGNQ9VzJx1QmAvR71jqP/YwnhdajXiTq1JIEjWVu/UPsAftEifDgWucEIGhEgktaR0A2U7GAqaNVRtdForNgJEo4QAGAaXPXRC8qCv9wmsPI+TyQxvLleU3dgQRquIm2lmQQauXeI1sgOYCSlqLzdtUlKZ2zu0UbzgI88OY8auNGGTcNZV4spPmvGDJ/tHfArEyFy1uYXBjacsBbEjyHGrwhCTKOGQhWoHKIctVILnIzmW1cMob6OpjWg/SM40DSWGWMYHQ2NBDOTRCWdiCEDIukkwsbsNGCZBOtrTI8YGU6bs+kplmFZljPyhhVjBV0A8hKUm8XoMofz2IphVkvDQ9yPs8xKGH1gsKTCs/1/U+8f8Mscgmn6CYlqaoDaCz1oIlea70i2y38OeiIEtf8DcaIaNgB4nG1WXUxURxSeObt47yLKj4BbYRkQg8C6sEV+pCDsLqyx3aZSAQMVESsb1NBAA6Y1bYU2hVZt6026GuNDbWvS2PbB66yaK6xK25cm/ZEa9cHadB/60if71Adf6Dd3V2uim3zzzTnfmXPmzJ17s0E363Fksq0OF+tlJkbOSjH6gQAwDkwDBpAENEeWpCURdNEfbCvdY730O/gu+K7tL3LoyKUhVw5GPzANLAJORwaUZVAExlpgDJgCnHRbrlgSFt2SWYp+kJqi7+UyRTflckW/yUxFN6RL0a9SV/SL5Mt6Lfo5teAntSDxsBn6TnKCuCAdSryutmfR1ZSVUNYV+pLOymrhv0Zn2TiwADh44eWoEOwOv2PRi1CPWRS+lC/MyTqs2yU/ahYW75UD60B/yZgQV+koHWF1TPAeejuwelj4RwOjXaOO0ViB+DTmF03f9tQLNkdjSD92keam6kWCTtMpVs4EnZGxfKT9TMaQnUdldCOsIzKqrFUyqrSX4tFK0RXMpPfYEDAOJAEnTdOUrBX+oIemWADoAhzMn7ZMYAFwPvIofegxZZGmLmmLI8kRsvhtOVKAWgOqIYsGZawEG8iPo4Vt12gHOw7cABy0I5Az2yguA6Wzgdnp2YVZp0XbA7lwzMYaBduyhTGWl6sHLCq+6Ha5XcY1fpNpzOC77TEjUKgZf2vG15pxWDN2a8ZOzejTjB0aGgxQPRvGobTTJsW8jJrlsPgmkTaPUgPME5ZNr1tUDxqf53mM01fUKKNiEbeymVUiwwvUxKLg5xEbFRkJJIbJ99rmLbU0Kk5b9qKTFqpExZGU9W7KOmTZkbtSkd3Iayeg96nO5s/JBz8u17OyRJwK5qbdrVTDQuAW8tncSn7b30K1CN9jUQ1oyLIXDyKpz1abWaHO8IQaqUkWrm6HHzO2mrXZ3Er11BAvbHNlz1EDohskbW4PrsV8DCA2ja1xFcOi9BzbzHbDPg6cAc4D14EMlo1RAISd5tvVaskv8wtUNcxYAaopbiUf1cTz21xjwTw0MwWcB+4DTiSpQZIatg0wsfl/0EhGIFu1xZtzgmvgKgW6AANwsvbHUiRtzw3MlgBSdViMNsrNrj/nIXFk90keFsEVtnUd431gCXCgqg9VfZfIwpvx2jxVsR/h2xIf0MWARVVSF2/MUyXLhzMsZ3B3qTM+UymSc9SBw+mIwzWTSAXwe+TBxRmxQHUiZlGJPCneStH8PHlZBZJsk9Gq1KtXhxdsJe6YARALYJwGFoEk4OR5MqojMiSjFalXVn0lgnEsjybIwx6g4AeopIspFIw/EC6mSvGwifvuwStYgqPYQNXq+0FeqsaWmnDyVbSe6fBUglVTJVRsR3jSXE3FcUTm4LCK8SiKsbUFjJzWU4W9oJgqAl5drNPX6qV6iV6sr9HdeqGer+fpOfpKPUvP1HV9me7USUeh0k08Yi7sZZFXS81/u8stnvnyK2ZGeYibeREW6Qm5zVWOCEW6Q+Ymb8TS2XazyRsxXV07+y5w/kk/vCZ9aHHW02fxZ5RrpsjM6+i7wjjfMPNxUZr7+70eczjS3We+6ek369TE8PSzQu+TPzf3Mi+/gjaKw+/s73RPTHq93Auf+h0cnBjk6UAl2D9IE498iJgcTLE9S0fa9lOqPfabeMLDI12HEniKp/D5ErzhsiaSmpjQlBTphmLYiqEUI6kZKcXNw/u7Q1jad0Fnof6OgRTHaXkmzmWoqKw/VJgz3mYfUkuZ+3DRnJPxc2y5t9/MKg+ZKwAl+YK+oJKczJZWwp2dltyHW8qK5vi5tJQDdy4emN2B3cRBtP9/OxZ9Ed5nDoUP7DH3hA8MmbHw/tIhfJDGyjvtmMmHh+tVi/A/ILzPohNYEjg2ZJaWd5qacpxMO9aWdz7l5CYenrPb+x+F8Q8qeJyVVG1MlWUYvu77ed/DmWB+YEWmoehRFzVAnWThtxkmSumxabnUaiJuOTXAT3QG5pSpiYjlRx8W9IOzkD+hJkWSYxRJSTYNa+n8KElxSiqMztN1jnPZD926rz3v+z6f73Vf930/nmmAZxriQs1NQAxgf2X7PfS24+wJjofNjLWXPAtxV3PP330O0IH3mr1t8uGtdhfrj1EYg+m2CReRgWb4kYLFSLVVcOCTIilBhBTBiyiMQ51MQpvAHLbVSGX/FezEFbRKd7PXlmEz10XhmCRyzyZ+9cJofCvjJdc0QNj3yf2SxP5KDMAM7kqQBVJkK22TbSWPPmTyFGZjNU+AzOZY6J/p5DNHRmgPncdeT8TBh4nIQREOoBpHcFyekPmSKa/LQlkv5ZqkARNpDjtd7TfcPwppWIt8lCIgXukiPSRZ0unTdnshzCiRp0+hF5lYgmzsQANO4Dra6GOk9JNHyfVVWc31JzRdM3S52eCU2zJba6/BkO0QDMNwvICXuHcZ/1OIbdiOXdiNElSgFnWox3c88ziacB5X6FWk3Bdm0YtnPyvTZA6Z58gqeUs+kDo5qyVukWerhc20ufQ/kgwfoC590BdJGIyRVDwVk8jYj+nEDLyMeZjPaGUR2ViONWSxjtiKYnrzDrELe8NsDuBzHMJXqEEj+fyEM2jBVfwlrnSSGOkpA+hrmiySJbJOCqSQPr8vH0m57JdDUiU3pEN7aqzG61h9Rp/TmbpCj+jP2mQcM8ykmQKzwwTMKXPZtDrqJDjLnBo32h3nifBsC3YLNtjO9nFbTOXKbR2V8yIavRnJftSwP+M5CPFhJScTUxjx5+mfH3MZlTeIrDDWYjO2MNv24GP6U4JPiX2oRBU9+ppKH8VJnKLKl5mP14hW3EQ7FVcx0ltipa/EMaI+Gcx8GS5jiAmMwGTmzgJZLCuIN5k/BaHckJ1SQlSH0Sg/Sou0y98arcM0WZ/WCVQgXV/UBdQgTwu1RMu0gjp4TawZanLMKrPHfGKqzJfmtGl3Ojk+Z5CT6ix18pxCp96F28Wd677mZri73bPuJbfDk++pjOgaMStiV0SzNyZUb6yi/1ozmmU82625VFuJgfYkOtnrmgQ/6y7Rfh/uTQmPt6EHomyrvC370UHPh7Ke1iPJVrDCO1AsxxjlXJmIIsnFUs1DCzWYL/UIymcygjp0YcQztS8Omiys1kjUSm9swG8SJYNYG/PkPebXFmaRT2ZolqbIVH1X65Cs6QiYUrTqCpQKcEMfwzn12wvuJF2kMbgqG2WjYfARINZgFuMcYBz3oRtG8eQ/5Ukc5d9GI58Z6MdUJ0EuUv9HkIyRmqIPame5KWfU56y8113n8bNW+PRkYwjv3djgH8Gaf2c58hBm8g7+JdTjHRB6ng422Qn24eBB7o018bbx9mqLO/f+H/Pe8W3PyRdOMWKcWnQPM2uwacEf/gGSA2mLAAAAAAAAAAAAAAAuAGgBIAGyAlwC8gMWA04DhAPYBAgEMgROBGYEkATaBQwFbgXoBi4GmAcCB0AHsggcCEIIfAjSCPoJUAmyCl4LKAuGC94MGAxSDIQM7A0gDToNaA3EDeIOTA7ADwgPWA/CEBQQuhDuESQRkBJIEvQTdhOyE94UBBQuFIQUnhS+FSwVhhXgFjQWjhbYF0oXhhe0F/AYUhhsGP4ZQBmGGd4aNBpqGu4bLhtuHAIc+B2UHhweZh7EHuAfPh90H4of/CAgIFAgjCCwIO4hQiF4IcQiFCI0Ipgi4iLsIvYjACMKIxQjHiMoIzIjPCNGAAB4nLW7eWAUVbo3fM6pravXqt67s3Y6KyFpsicaSLHJKlvYAgSDLGFJgAASFiFhDYooSNjcRVwARzFsAWQGHXTGq6i4r4CaUe9cWxknw4ikK+9zqjoBHN/vft8fXzrdXVVdVeec37P9nuecQgQNRoiwfDlikICyFQs5y27hjQzCBDFcK5nSIgiolUxtYSYYpCvhklBYtpeUoLKyyI85vTATYLAckAnbT/1rP/ywupEvv9ab/fOv/1BfxgMQQcnsUHIe7s0hE+p9HLOCkeV51IqrFa+BINNcIz/XwM1l0VxsvM/wNHsfHmeWrkTUsNSmv1BZaaTU7inJ6eVgCgIuJk/7rCjAudnPw0eIHYrlX35Rf9I/EfxBmwixF7l9KAYloA5lgc1jlQS7xyrz8V6LzMd5LU4GI9FFWCQ6GQMSJYFDosybYoySYPZaJMHhsToZt8fqIj6vxUX8MUYXiY0xOpmEGKPME5frJSfjdDoZXpZfkgSnJAkCzzMMEdFfjTGtWDpi8f7V6mnFUxW3S5Jlp9NltFosRqN4RVjPfy2tl1tJlSK5UEyM1+u54mLWk6+d610ncB+UCAi3h6X2Uom+2sPtEX0TYNCOXAE06Duk7TZx2ZmrpLNN2V76ZfvNH0AWcOQx9J3nCjIBV4AJOug7WJDnyADJOZJx/tjTY/H0MafHHHi7DFvUd8b8sVzdM+JPow+cL1Mv43NleEYfNgU3qGvo+4/qe6fV9fhu+j6Ns9X3cRbgjVFh5zS2B38B5aGtitEfE8M5EMYJ3Ck8BoVQAh7T4sjKbsXPtYRczlbcX3E4QmxP1m12mLNDIUdWz55uh7OVTDvmcrndSTH4JP4Cbrr9aFK1tKMA0PzimH+voTqjLl+Hhn6AAgIAER0SDR34o6DBf1gK01+1U0A/AwX5qcEk3uX0uD3xxOXkBVewIBvLoEVOd15uIfyclk0K8guLCvJc8djlZK6pNbHZKSmpKUcfWnj7u++e3Pszbvb1ykhPXXBiw7Rz506eOsmu7D+vX0nJHamp8yunH2365Erc7kelT7F15PSnRhYVbrpr4JLa11cdfdu7Zo33zcM+qpMcSu78iqvmF4GNuVAQFaD+aK1iBRvDjYRxEsKUrMhtJXcqpgyCzysrMkyJK0S6b7cjhvGtsDMkDnNBJgv1z2olDS29n+5/mjSgFNyMBiIODgSfjqPHLeMG6NZDP+wl9hKZfqCyMIUpEi4Lt9MvqjZNq87m9BJxUMSy04qDSdm4QM7vg/Ny4zGnIybr+Pxvv7MjI00PkZXqKxlNCxo3hnLuq150f69OZImLkazxfhvzExxvaKLHF98f0o7brHExVn7R1QX8NvWc+vfa2tG318zEwWWLB49cmKXEJ/ZRZ98Wm3irOlT975r5o0fUzMDJ9YuHjILfEhLL1NkDYxNKKaYMYHqBfYSbgbwoCfVATytOwnGYeD0exhsMIi6YzLLJya3Yo+R5GCccJcFk4sXIY/GJGfGr7ALLGDPeEExwzOVLDnjYIMe6YvBaZnZP11ugeAcUMdBqm5vaahyXSdUuciXsk660yeCNwAFqTtBbFi4rLQvLnpJDpP+YiSeQr/MMLq5osmZnctQwrWfPClawU6ukb4BCyoIjkMYHk1KpztGPvFy3yyk4cGGXpjrSeE032cZF96ubc15+7tAfFy58ou2vR95Z/YBaOyA9OZD0zLphoewcUq3+NFj9edLdf396/c7Q4rP73/6vR/Y1qCo+UDE6eEvPrKmRPp8NGAndBBsFP4zHa344tYXFmgO2EcTNZdBc5j4yjh9X4aWD1DxuuKTb3WqOtsvH0vuMBQdbCfdxodQToL3HW5BkbMXHD5vsRrjpgCN8qzTBDerXDkYJ+gYDdiVZMShLPAYn1K1GpLI8x2PPmdhw6OmC/APP8+XvW/PqO/68oMj+ft/V55hb32pUkOZbwJeTrK72XFp7Zr09s9ae1MqHPTe256Aq6qJKm4kLcvvQ9rJxMLnh+QP5+c8caqjo5fDmlPPlSuNbHa+dW933fXvRAqZ3fZ4V2opnp5MAtGVFNYokiBxjMBMLNjFGwcobTuLjiMUWxQ0/sxxhGF5AVrNoZAwmfi1mWjFzGBnWmk5hBlmgz9MPW62SjXqhcGkboFqq6Y1MLdJTQoOaFtW0wIZdRR7BI6QJaUVpRZ4CXNWc1Vw5Y+b0OVN3ZO7hy5/MW9pn/dANG4beU7I070mKiYIHsyxnhIgaehkRcgcAJZI7FMnEoaW8iYj8ccawzXRcDLVFStvAO4YgYNBYSgOCnCdDIJCDckDB1e14jrqrXd3D7MWz1d3t6kN4loZ5Zw7Ow72RkWLO4+IW7nMgAJMPi0YOviuPGOaK40xgCWGKebsmY4BbKCgsCso4r3DD2FLDANx726IHZ8lLkkrgRvSexEyKmCBYbaxiQcxmIBd4LceQcSzENPDbWiepeQCXKIqEmaD639ijyx+Pw0PhOocioi3EiO7Trgmjsoh+Oh6nvoOHqsfgJDjf3PkVE6/52VzFCwr6le5eEUMwcxRt4vBRcpkFM/aCLbd7w9BsqCuMUn8YwEy8mqi+jb/RHZTGJwZ3fsViuKcT/EyG4v3Ujp+zY2+txCTU8pakarwjObbaVBeETmn90tSwrpI6TgIaT7ocJ/mN48Tpq1vmbzuzqNftf3tqw3uzU9QaX3ZWXExWViwbirS1Pveg0v/+Rw+SgosXNlZufK7Xmr4DV3X8fcstA+q1cULHjoOumlDuaSR2XkYCPoJEfFoRTSwxm0zIQGggxfjelyyGEPSLqiLEyfZwsQ4bWAUYSHIROa7W2Effl45TdvPlV//AzHh5eNrpsrNaXAcn+z6M24/mKqnIJ/mIv5phuAQrtlr9Yq2TYRh/LfF7zFWWQxZisRAet+LtishXe3bEEmtdDBUt+BTadCV8oBCwx7KIDHvQB0EK40NE85uHeUYinuIKOAqwBQqQXfeFcTR89yGahwT5vdjRhDfvyMrOzlJ3xs4/tPIf/96sPo+d6g/fkY04b3y/voPnPXTrpkd3j/jp+Gb1v1SRjqG08xLzL34x6oXmKPkI+S0f+9I+9tf6fHJSbSYj1woWn89iRtlOi5SKq1K3pj6Reij1TCqXmpoWX53trM5uJX2OMTtyzWl1Odpw2sKSNoK6Us2YKahhbUyRb6VvfbAPg4jvDpq9cUAXd28MgKdRhwRb3WMrzEuEofFM2ZRJEz/Zf+bzgUA1MkPxPcYs3TogPpjSI0Gt+eA7HHh7KLMjYYva8cqHC9ac+q5RvTqnZPDuhZXjY8j/uIeOnDSusKRyw73fP/X1G5uWU30F3WD+CbphBE80vEUkllOgCQwy4NeP80beYjQIptNwhAPt2IcIDhy2GQ5YpW+lKx1gg3JJSVQqJ5Cl81JxBQqFwD4jpZp15lHPQY3OITNM4+LFao2aiCvXtQDrv6D+oH6D47CTTbp2gbwUGan5735gOyL0JRXdoRidTm9ysjexluNaOy8pNs6LaxHrrbVaUlOZFAxIK5bYanFHenKiC19ygVMF7F3VKXvtdWkAfORvAHqXPkVRLw5R6HXUoVs0mMou3e4o9loc7YOLZAo5hKGAgzmpmqY2P3swUPPK5kd+nIGb3X3jUxiS2jM21aQulPlyFZjTglPqle+ef/KeIbWffD1x5qPrU3vux30n90tKG8T0ivoDO+hUCjDe0hPIiJ9T4vHHGbUWSy+uNonpVeux4GqUV43sOwqMWdWxQF3bI5S7Azft0pbwt22UndLYpMVHnVoBerrPkAskqvfxGN/sM+xqDZeZkpDOJyybuOBAtr/utXVPvVvnkFe8f++Bz6aqNamZPdJSMv3F7NN1t6anlk5W1b59lz33wJoDz2wZMWn/SWy+hAN/fmHC/L9GFoxXeleon8/rl7cw6k8YCeTEo9GKhDFhQTJkADuWncEyLNUfUCz8xWEwfwi7gZcMXEj6lg5DCke15SU2sbiiEun23XUMS5pRy+BXAzIjqao6HyeodYDy49esXC8c1Hgc1REW/IwPpQEzHqSkIas1uTYzMzcOfEwumKg1JKMCT7V/b0E1s6NIrg7sDYHDLaRhP6x9yNQSNc9L+1Ti06j/dSukVEv71M2tywB1TB037THDKqqGf3HiyHuTs+qHDfv+O2x6q796NC4jPTYxNT3WkJqRHkzrkRnP7Ex+QP3n2ZP1i17+fPsnfZMfXX7+/IW31y3BwTEFJSNWDikuGh95u7L3rXe8N6e09zxNZy6Bziz6HZ3hQF8Y0BtLT2N1/I4C0JzUaiTTdIfqCwyv9CadcQQoldKITZeL0VxkPJFdkq740aHIuuoMxs08VRkuflkFqEzMwtfX7X13kV1a+d69Bz+9AzcXxOoqg1/QVQaTm1RG/ddX6kVNZci2Wb16V+AUXWWQVjfAy9l4rW4Qr9jwFoYWDhAozH28VivQUrNwKBwN1PS9HIJ1jvoOG68ex0MiPfWwreGTE72XFeVTfIqPiZ9b4JZWYB5HLUbzXCML3OOY1QoEYIJNL0SUtmm3l9q7WriBh9CGEnUuEm0s4UZGQnW+Lx7AMlwu6HzjEY7leGjgbsXEEAhshOM5lof9Ywg74VwwAQtSEIOrW7hE/k/aDgJRYVwMdlGsSImQejAsrW0sNBA8QdBihZacyiXIF/KHwxG/N+z3SfSrKTvTQNMDFhJ3Q5R5QKJlwSzT0Yd5NfICGYMH4G1v4gfVX376UcOnL74MuN6pYZ2uODFaSphfWF5YCt3EC0UBUcBpkzrbu6KxKQdQPfrqy7xK79vRh72z/d72/z/GDhsLDfr49bHrxYr/V2N3YBeWs7DWSTIm8gKXqy58CxyFQRs6jB1yDtbK34UCYD0jFb+n3mZzOjmMOcfZpC3Jxvh6jkmyb3KluFrJFMXqdCLsn2kMp6GZeFyqTsp0Hi7pSULkR7Al+C/RFDOgGw/1vGBOacCNAzdajw8cF/5erSEDvjnU8MeF+bnP1W0frDbhhUTJjfGlZPvVJnbIh2e3j3rgmZdeb9p39+IX8Okpox/Dloreo+/v2KNVwbSaVOdRXoYxFKB+qFGxpJdk5hcobtd7mb4l9lYyU/FkcktSz5csyTQvSWCUJTl8AZ/fuyAdnG1pC15fcBKXonQy47joWu/eJB4KnobMfwAqxBhl4z5HbIdix/UHT3ElHC0ftUXaokW6MDUQrWxCxaExFlq1gwxT8yESCiYiWUKB3MKiPJ7t2mHtLvglKY1CAjmqHcKqp0h3Kzo1I+z+BjaldMsc9dxV9aK6CS/Asfd11L23Vm34i3pQLcDP41Xf4MRX1APPr4kdMH3gjiPXjgxekimpPUb17zt97PC+K9ZzsR3f+8W0xV/vQp34VrVOPaz+SW2aWvnMAXwOL8dLCfui+pfv1HPq40v65X3/6q94w+DBiyKzpq6dhXtvnbRA3U9x7QRtZmMhbgnIjMcoc/oaJxgX0tqljXcjCSfwCUKM6DJlCT3EZNNqYbPwiHBQsNGf6Y8KpFIj+ZFCFVogLEKrhKWG5eaThhPiUdMx8xn8Dv+OcBl38lfMXlHkTPQ+rGgwGE1ms9Hc2nlZsRgkA0OI0cxynJGjR+KIZFSMnGAwb1bAGgQOE8JxvAEJRJAQnHFMEIw8WM9J0oR40qTYErmPyEcWo9U9v5/RaLX4qKZmtn3b2x+OeEFLYc9bXAz5ol7/06pa/gj9LsbFxU1gP2ebpNKzdNtK6w3UnoYdso2aeBxhYhR4zgg8uxT+K/CwQ2NHTWxhElngXnB+BV5UWYcqIU4fIuUTFQnOZ8APCAYRBmixWjBNVCswLRgyAQf8SAQm8x/qaNcunzr8I7w2MVGRaMKAg+qXwP5+wg7crPb6mMZzaq/pIBMZxaAgGnjUwHkNBkcr6a/YY2PtcfWywV6PCNfg9SY2mFvJkGMp9riZcmUyjXXaW0+iqM36SkJYU1lHgKb1Tl7QSRIOBjBle4UFwKAYWWfVY/G+v5xbXbHuwdnD9qmqDz8dY+uTFJvVtyKnJ1/e5/R79y+MD+x5bOe+x27h+qi7QYuG95Yij6q3Mrl37prdU48JgzsvsJlgp8mon+LCOFGSsiQ3V5/ImOvdooQbZEQ7nCo2+CtTKk+QIcibOUJqvz08Aszrdj1UQ8eLo5Q0IN9I3zRCer2YpvE7F5upTo7Jzo+NzQnF4acH/bPlxOUR3vr37zn583BmG/av7D10ZURaOnjwhjfLrr332Mbwj/jTF1/cW7n142+6fWMQsLYgNwoodlu9xWAFcO0NwnKvbaal0nNTVuoIJMYTl5aYAn6al8vGY/GtOOfjhbEZs3buioTx05N7WHP58uLGR7+NvPHZ9vIA1+fax0P2krvm94nTMYI2mWwt9yw7ASxxoJJYJWBBMDEbEBE3mAyi29FfFC2g+sstjGGmqdKsSbVd87qZXWymTuuP3P0aS/6ofocF9SouiKQCQ3wZTP8t9RH15e4246BNkbID2qZFa1PU2jRwNHcQ8U4Ta5gpVhr15troHMV/NDSWvKF+gVm1Q53c1Uhk4nXZ+0H2aWjyKSRjETnwQSUGYxdXH2Rc5nqf6HZnSHOw3W6wi7jBkUZVIUNsiK9M/w9VCHtpciK1+aADtEwY0vT5SviGknSXUgQLoFuaq71eKwC98KuTvVlpsbE9sgPjcFj9d1rmuoq5J6q8ueGXT7QPZTq3b76t9O6Of9cOHFF35krHw0uGDave/eyz+Kvv33tx9/m/RDHTfKMJ5Smx4gaIzkAYiIE3CMREjGQmXm4RuZn8B+Yuw9Pq6NS3dFE2PdnLk9lYdfJqdTJ+ejU1efJcZCJfHjlN+nfrA76qzR+5KYUgBm45nkk+YOGW38Ito9zsKgX86h/gPHqNHZR3AmzbUPCosZ432MAt/f0oiw3WmZhiKYPSgroUQ0Kh9QVUNhPLbp34kgnG0Zvvez9Vzso3gO95/Z+bF2biGKanMoVdHB33eC3/bVTMImswII4AWyDGVtK3BRHmFOmHEhEBlTGBBa0xGwCESpMGwrd63oIgl5Ha/b6wvsvQ3ei2Eba9NAGllSOZVkqHHcodNVExQ87EYGhdNGJiAI9r0LwugwOMBqKDLfsHNqi3H6AwngL4Ujo2kWORobC1mdzVhSO5qtVo/YqFbGANHPOugGeylTw14m+7oNDy76sYRQBPOs8GV2jXctVa7XKrEsMzT7AvcoSYTUajxcIzjMEqEo4YDARo3vAWRgB7Ga4YeWL9xPiJxSICIiOgBzwZcUxEyyXxA5v0Lbwg/ehNKxrf6qi0WCUjhDAIHV6prRT+Qa9LITBBnGiyQiQqxdGyAdN56SU+sZimgzQe0R4zOgpuTwzmqtWt29VrD6tbScAbc0sVVQp24LVTbM9rH/DlD76uziDt3XrFIs3mjypFjzNbDcQA6acoiDxhMeEMZaSXQK6RdoGcIkcEcpDsE8hKskQgm8l6gTxMmgUCvzwENxHIbiVppIgXsg0iaWQfEMm77EWRXGY7RXKJ/UkkiLWJRGwlIw6j5SYAaUQLR90ITb8WVWp/AIi+UQnRmA4bwjFQrEoq//hREw+RrtEbYPQMHX0FDFwbtUNmkfrA5+oTuOoHzX4uRRJB7GnkUxTNfS6wbvA7dGYoWTH56iUmsZ4Tgw001hxNiWsw0th4gxvHN1QWNfbq+k2ZwO1Z/vY9a99Z7e36Vif70gJBT49ML3m8/c3nJo078NdfsatrC3XWleWtipi39Bv+gB5TmIuaLnnRMCVgrzebrTYIKlaDzZXganA94Op0cRdduMH1uOuii3G1YsfhBvDGrWToMb/NPNNa6dM9Sl24sm5R1AuH9RiuE20w5YBmzYV63H7u+ROVAeXgvXXUOOyWW7Ly+fLCWdt2b3n4/uGRJRCtZ+CSSRvKc7vwKmU9gFccykSjlRjCAuH3p/MBwC0uvR7xBmz1X8hsQHPcp8CTZCEeTzuSAiD21CY4rrSFaWQoDZeWhTW6Be/rFYycXpAv0QB9I7q0jgDEI+83KHvUVxdPDTzwdr+0FWfu2vJlbaBH/XQmbYj6QxfWZ4O3TWbv//wz/K+n/7BnRFXz449Gfv7kocnewlj28A2Id8VxqutOFIv6v2RwU7aUZLdbXYC7w2qw2nwv+shFH/7Jh30nLdzyeLd9prMyTse5NAI4lyLghnolskSP9Dpfgu5rNETWIr6Odw/c4/0NU+9dNd6lLs+YQEEfVFLEl2effuf0WkvC7Aln1XHEv7ZPJJUSpbR5Fbf3ovaY03mB+SfgnoNeVuIJYxJMsVzisNiBiR8wHwufxYoyM5YdxzOm1OTkVnKvUmgxOS0WU3JqfBZscRaTZEhODWQovlGo0bfV94TvkI/3+ZwcQvFcfRYTX+8UGzDGpwitHAbIPYooSWh53kI/9tNjPmQg97RkzPGdIM+gXOkKJDXhSumKVoOq7CKMWqmZMkZ9fq5rg07IgY+izmhR3U1zwqzH7bpeq+2aE07TJ4WjoZh5V10eH0xNc7vuxL4hQ0Z//9D+n4bipkCPHhlp6Y8+Oqv/HR/s3PfdMHbOsrHZfcpGZ2Tc8uy8eT/+Yd+CxtePzX1tcn6/vg/07Fk9e+oza04+P23uk6901dv2aXmLoiRjfoPAGsjMBRy2cQncHdyfuIvcTxyfyC3gaP6Ad4o8cAmsRZq7K0DEXhA4eIOQPmcS1EI1s+9XdfJVSmp+/Qdn0/JOjEaAb5G1+bMkRXRZ5HpedM7E1K94rMDM3NSvtFO/opXMuqYsfF1l6wKZlb1LX1m37cJ8J37aE0z30HjbuuPR0VUPHcR25o/X+s29te9yFpIxrOVipZq/3nsC8Z1naIQRKR8gEqbZkZ0QnoGUlSYZIs8BHwEH1++w+BHPwLdiZj8y8ib3jH48bzLqiVB7dJpGiy/aLGpv7I3arJ4YQfgpKaY+WIKcBzIYHgIw35XzdCU3lhuTG2M0s4GQFLA77DGYLf0osuaf+OXbA4HkOJ2iRKarufh9PI8ucMDU6fDrYVwO7FRCPJvAEoMpBhfZ7sIrhE0MD4ke4XietcuywyZZrbLZZLIYRdFkkk1yK8AgElYRiJtg/lTn38HWrZ1fKl5LkDXJjOCwGAgRDJBvER50AJn6WsgeJMObBbfQF9qcBlsG2BLxNMXoZF0AEMu6nDcDJIXDdFQ05aehCTYhxfPAC9G3FJ2F7gKxK1fU80VciSr/n/Hz/CY5tEmy3eF0ObuRJIACwBmD7Y6Am37y609Ffh5c5JsS+dfLeE9mT08lPiamZAfHaCnjXfjzNyMrAeY3Ixfxs+TFQWpOZA8ZRnL1uE+Lwu00f8Qmxd2AFxoJyzGQy3IGm9ViNkucIlFYA5IRc6womwgjGhKQZCaSKUEmCUQykARGEgk+2XkGer6nBXLyU4CoBJyv72HmIwkCfd8jho84SMnJyMPyR5IJvhWz+SO75HDP7CdJDrsOr7cjLMF/qf4VLg4XRwmAxnpo0k15TxeoGpbDDvnKhx2aMXrSxCNsEpPEv9x5AQnwNnReaJELrXTXAm9z5wWQUPSvG2jX7wFt74LZEUhleAGycQaIlNvjYPZvv6CuuO0WKTZdnbV7N95lCvjlSQBrfsfyn/HkPc+rvzBN5K3+j9RPxyd0+2Q+1Hjxm8qQVAM2IqMgGpDZj2IEn6HMXGUW54ibRGIQ04R0Q4ZYLA4R/yoKhBY9BJ4H9ogNgsjxhDEgo5EHgmnALN9XBOfMISN122QXiI8ju44AP2NJK5lyNIFJZAgTqtRWoLRJ1Gl3xdsWo8TrfBJpbErDVP8FmjkBsmM8dC3Fqm5F1X8UU4XWzktaBaOuEi0StewiYPfYHcyHkM6tUD/B6yabqKpd+3fkGBnKimraPiDiK7r8rlPzUQOV1EXkbqCIvCCcwsSJMcHCBpHwZANnIJBaYnanCayYp7m81A5cXwbripSURUq7F9DQ6UpwlYECOr3hVF/Ht3T8ggeoX1Mn3FGgvvw/bHLUD1NuH6f5+1zFj8lFJEgsc5Hj7eAGwSVeIUyroBXNSyOlHV1LviKlZfqsuCso57kCMombPFmFOH312uNXo3nU9fnubMWLGOYrgp0Esh0EqB8llzl8FN3NRsnjTXPdjgAOmHEIf6O+Q6e6o2stcPimPtq1PkrRPjKklRfC4u/3Ma8gT6aZbPLkyfiNq+y0q1f/oPWPyWfOa/NK66J1Zjz25joz7Nv1QnNXBZ1gRE7ivQjjvS1cIrDxWUcV5jsukYet4wr6LhE8gaAZqP+GwjJ8hcM0JdHXxtlL6FC1UjN3Q6k5DwdM2IyXXcTz1bbXX4PeJXR8hf9b9VD76ERsfecswNL/El0ikw8OEc/m0JOs9DepLbpCQQB+aGbXXVv1kLZWAnFfEif/A1yTQtfDWFoaGYiuzS1kPfiWUXS+jKUdbYvA9X7gwJANBPGMQ2/yP6j/oLyvsPN75jVuEOSjPdBkJcMiWdLMPm8AwiRvNsZN4YW0KZIpMRkln0km5ln+QLNxWk/imJqpFfsjpZQTzB8FWUcyWfBtTEvyRocUqaiU9DpUGTTsyZO1AKnNpGt0BmJ7gVbXBSpYWATptE6GHFYcZT/4JXXDoI8Onnu3h1qfctfouS9NPfmX2ITkDGfjtISY9FQPk1Xx0LON295Xj0dmLR00qP6N2h01g0cUls8uK1ig/mlQ1aTq0tAMnad/z/yPxmmT0IAWWScBPjnBgVkn8k5hBYcxYYrVFIOSVhqnJdtbSZ8jqNlRFYSBtVGV1UYB/9rsXrG2bkFfSOHSJ/ZS0wSw/JtXq32Ru3Jw+QrFkrZ1+qCVeX51I7H5U1PcvpRUYC7qmltK8qdO2h458OADc/r0mUEDfXVZ0YLI2XmlfZYifb2ALo8kNOwEBKAaxevG2M1NSRDMU9wmSZKRPC1ZXOmfqi/3KAX8Z46aeBIiywIkSiACshGBCGR9hppSUun3aj4BK3PTmjrmNXWdPxj0Jgcz4vBs7Ko7tvHgX8sHXHj8sUtDmVv2jCrJnRC5f9CgodWPPf74s+Puev9VvHz/800b39JyB9pnmq85UAD1aWEku1aKZuKJ3eZAnik2gRjjp/Am4gg042nBmJXGqUka7YuU/g7CunJ0d83t8d2MMHNRrbml8bap96al3Tdt2Ko8X0qyz5uU5qeBvaq0dPra7aR2T+O0wkK8LHL/kNysEZGlo/NKFnT183PANh6loT6Kx/ivtCNgMhhLwSl+gQM9T1gZH29oJWMU0TQtI5WsdE1N1xOc8A014brwj1pl9YaSKrm5dJbStXqE+TzyL39KdgJuSFj04vrGs7U9pn60+cCXFeo8d1IPJ2lUv5qUP3D6i3euXL9z2NA1DzfjdS+8MW/Nh6+kT6suzVs4jeoDZWHDNGzHKFaDiTMdQdZe1kYrY8WtmFVcDiQaOc4o4pBQJhBht0tsthpbyRNHzXc679ytVwTbS2lNsLI0ugTn9rAeDrW1cdDLPJfG3wvyaLpJhq3KOlCl1pSX2wc1NvHl5zyFPd679me293vrJg5MRlF5xzAXAMcEyG0rWyQpnsrbKvVwxCcEp8QICXyPKY6TeA8y4twj5im8LYFWR83xtlmJzWhaKG2lBzo45ogoeqZmUzeizzK30cnUSIdK9eEGddDATU0LODWwu1WjUJsRYm7WjAuOJe88+srfWbxJyY7PX1xWuyV/0+bhq3Li4lzJwTh3MN3LhTrIO8ubBq07M7Fw/IxQr4kPrMdfn34GTFF9YnJuyVi1X0WBUqfVYiFO/6j5jhClLpcVN3UaVnAadqPR2CxOcyM7+ApXtHRJKRlV4+tOAkfTXto/8BJf5C8fOHdHQK3xpKf41RrwBc29etVsOdVRyzyzpN+AmR2304Cqz2l/H10L30tJII8xkNT0YqvYRnYrC8EICA9vFKdhrpnwVXq4pjXOukp9rSMwAVmbMKzCT6hVfPn776tf/HqeC2lzVb923obbtXtLqFhJ/r17m4+YrKatNjuQ/mZiq5K1BtqkDqr8N7ahlUMLaH6G22lbttvXrHlksF1v8Nq3J9ZN7/VBYivbG3VheVHjffcpQZYTERbA7hijEfEchGEjLY6CA3vVDIdCcH4rWa+YzKwIvI9r5qv0yqg2oVuZGypdJf3Nq6/aIs6c/pkVVKu1/aOsaGQSWY/G5yid00K0t0xbg0xTtOWjJh6l9VGOj9ZGHV21UeaFfR+qK1erNbh5NReK/NTxNekX+SO454l4PeoaQ1QutMaMiJGbhptJFa0xt12vMX+vVt1QY4ZruO/BToLo6EuYUk0lFXKwoCN2CuuYIoqsEOQDHo/NiFLwmRSMUqSUUSlbU86kcJBc6379GO9ByIs9p8C725CXLFDMAVuzdVoKDjQnVUUnrMK0WKolpXkxkPJqV9iaFWsreVgxexoTPaM8BM5XkuDA4UZvsycU8Xf4I6WVfqnDH9YvjEJWplkjrVh4qCYLVJM9v6PO0eVMdMgfC6G15Xsr8xpHVDelqss8PTJ96jJHZkwqqy7jQquHDds5bNiMbU91jGamHRg96mDH48ywP4/IuLWqYzYXiurGD4CRE5XodhaDsZOamZMHQ0PIhDVjw9eNrT26lLBUcxSh7n7+1uK+EXo2lM9en6rW2DMy3WoNF1oxZMidO17omMrsuqd04M6OBs0sonHhM60PSXQOyztFEhIgXiWtxNOSY1eapt68svJ/W1fJfJa89MSyjSere456d++mP89NU2vcyclebzDZw1Sqpw4/u23grfX79+OxJ95dPXrtvRmzCgpmRfZPLckfr3OW/szfoS80lvYD36pxlgQp3oIdHspY+PgpFpPdinBgpQixVMaASxLFhSqCrM+43+hAu6GJMhb5PxjLN0Lh2tun3pOUuWHqsMYCtYZY7RnpHm96moML3TXotpnrH4ucP7pn8m0D1Xe5H9TVK/NLF0c+W1ZaNDeKXX/mi2h/y7pifzykBHYHjfy0vxD5rQHHLALO/3rwpw7/PzoLnftN8P+ti/9Crbl1+pC5m3sl3zN1eGOBIMQEk7zelCB49l+dOH181ozH95EhTRumDxhYp+6a2KewKvLSvJJbp3fZI3kT+mpCRVTXLrVgE6QDbYqREUw8VTa64m/cMfyq2GysopNSf/thFa110WmpsO7bhcIiuUvzv5AmPrtgtpdq1gunTGpW6bxXIu26TmHUA3TqCWgrBy1TbO+6LmYRCBsmJsgks8DPnzrCTekp+FvJ3iPxU5ymQCsZ3yIdzGklpYfzvLMMp8h4lE5upVVGWjBuD+ss+zTygx8IaFzPQI0+5hghOQa/3xutSIEVh0FHIzm9UB2+oc4YjyFd/7/VGXGUmBMXs0Z9yNUjKUket/5QUdHY1zY/9PkYPCsmPTPJ7fmgZcTYNzasPzHdxTQNnpKTldW7ZEDf8cOHb9v7dEXVvmeUsfOUzIF3eL1LZzy18PFdY/rPnzpDw4EHzBdyl8C2+iiJjPwYQfZe9kY7YzccMRqdhoM2EiKE7HZDQHPYgLe0yHeCmbdra4gq2+iUU6gymoNSngL9dum2B3ylAP9cWxsz/al7l05yT5jAXTr/XuSWdfEfecrnvU72vnc+uv7ze+ZvIBQXKlRiGYmRCbFCyHFZeBkUk3GBUnqszaYqt66UGiEtLaMVXBRNXORAUdfcaHdRkvmbOlPosXLU5GUlVrDAHlky6N/5+f37K3MXH2QaOpY/fEvpU8yqaM1jM7QvonYlcRSuwgtxI2YNtJokVrELRDKKvUMk2hZhqe0EafJMRAlQ4RjMieJ2PTklkARvj+attPrkgl+JEUEYYXgsIpYwl7TQOfHwTwzV44lKQqKRMzln9guZykwNJibBRDfugM1OE7QeLXFW1mX6w5XhSnD5+gMl0UpISXG0yEAf7oIvvezpKZbpR92iRdHKEy1B/K9VTkeho7CI2Xw68o838Zw6S4LdTdECLlY8Fz/2HZ2Ewp2/QqycBjjZ8W4lvQovMJBG/ICBKFjHjDWZjRaz2QoZu0DrnbIk2c1aFc5CWMUAPktiBWy0UQRjaLGAEMkqCDaDwSzabBJrtZpZG0tIKx6lGCWbU5JsVlYQ6O5gs9UJd7YDJamR7PCL3QjEo8ZsdNJ1NqzEGJDdbrNajSKPaanUiO2I2CTByhrh5u8iPAqdQZeBGLSSSYpZuijiO8R3xE6REU+TSZBXm0kFkkiFYnewTpDGSCcOORc4Ccs6HV0S8IYjfl+4sk6CtzZloAlDvkEe8EFrCp6Sm6Vy89f10rT27v7qqsN2l7Loc0HRbxBgXV23CN2/W2h1dMtSr/8xATeINOCwO7hpT6pDkkNSXJY65CCewo7lx+KRYxxDqHz/ibe80KJe4kL5kd6DyYsREQ/HB7vrq/eCrG3oR8X0DnNGOGN6x8YKJhDeUWJjJGLRFVwRLRjkjWw2k5EnDAPYiycBTUwqWiyJpK8RDIMuK3cCV6bbJri5U4mzCVuZrUBit3KSRXbm9gvJuEzGCTK2WGRJg7yusjKztA3+vFryAa/u4k0X2lp5FVPkKnUqplhZhgO7EWh13Wqx0emc63XssxU5vbpAdPwniNL1EipJ4wVaQbV77EXM7Cf/rNbfEp8eUl944QU8ZjR/SzzQz42RYZg//cPH5ChZoc4twuUrtJjS2d55G7MTcJPRuSOAItH0XyaSgGTZZuUNRxTBCqpJbSCFMckSMTxjNZmsVQQTBMNHyGaVbIrtRflP8jtyp2ywnSSPIpk8ojgSJcmaiELWXqjMylqdM/sjayiUWUkn3/0//FBZZy+ppDFGatNUqcwf7pp+Lo4+VXa96nzcWGhKsiSZk/D/h8qyldGzifw0WsRndi6JSRm/57l/TS30fYkXrFfAVShtz9TkfeAISCvZ8R1rmIauZ1BOa751muIQRU4A+9zOA3PkBcyDpbbicYdFngGjH6c4OCMRQH6YWQhX7jZxdxrvPEHWa2tm2mhyrK2UBf50e6S0THsKIWppYFT66n6srXsIyOS0+hHuERmJc9QfuZD6WQSrV7CbsWGjVsNUz5N3gf8noVGK146R3Z2A7wIa67voN100Sn7fJpPJ2IoZRbS65ydY57NsQisWWtC4II16mjK2RxeKhrTF5F2hKKeXo4AWy/qQW2naLliJAGHd0f1YW342Tq6d76vavblgWwnhHQOnVowWbOKAFflPvDAidhTxX8YNF/Y/NT/np9IJl7euKy3cuUX9skfm5SHPnHhr0R2aXe5X/43XajlOwjHCYBp0WjE5grqKiKU0PJbSiTpHnhzc/+mnfPkv+24a8wjFIychmbEkwZiN3os+yXhR9Po2ifqQLa758Zb5DBOvDbki+YYh0yewosvnr48Y05pgNNMssvfBRdmEZGOI/NpaPBg+ebd2vpcOeXMZ4X3DRo8f4DVLvVcVwJATRtYSP2784gAM+fItFRhtWx0qrmnAFbnJP8GYP5g/maVjLiELmF/ZVGRHPRSP/XmTeN6GzpuW8q/ZlpLnDWih0zDBQakWCrWH28Nt+mNskMQlpQG1EugSQA+lBEXMr4+tHn9gWlbtge33j9ten5s9eznjWDcmtBe7Gm8rKahBnZ36Ogthhz0J5SIkC2gG+hTtR3bFSFBKiiTlkdREVFaAMwtAAgXoKrea7YN8KAXOHq7EWP2piM0IpSKrn03sKX8YejjO82HGw0HTh3GtZKliYT4MJrYLC/N7tjsn5EntesE9rM2uwr9maEjWn/eLpgTRjCC6xpZWVKg25UafAxXokyH6g6CFRWTLbYfunvbMsjufHTty99zivXPvf+Oltbff9/zA7VVrHv9D7bi1bVkbKmY2bxtXdt9gJmN/zYQz9648uWD6qSV3vfX0o++NVfdtX3Lyvm2vLnli3rxta+55eNYnmr7t4r4kB7XaddoJxNDaNV0b1QxZBSKjjpL1GOt6pz0VqlWvRRx0kIPq8TcP0fo1tml+Uf1351dE1OYgyhTb9UeatWfuWsnow+hLDp8io5ECrGDeS2xitGD/uw/gBTERI5seIitveP4uk6hkMb8Y4osP8kMR+RyXOczZgKZ/jtzQypeoLK8sD4cq80J5dHEMH0z0YSmQ63al3ZS9kMX3bVHP44RdD6qSy+mwenxWNuXJjdiHM3HO5md6D/PFDFEXD0pMKtNzCDMt8vEzkBHFHAPfJgjEeBKaJLRBWlvHodzXow/TpYKNFBZBsoicK+OOV/EzOg7gN2blzg/OgPukMFYyC/rvRSHFhp3E4uXNFidvsRCDTMfg1W8JY9BuCqOQztKXXuyxg0Jo1cxg18PCvA2TUZEP/zW5MMH7zcZ7739q0rr+zIFH1uCxD+bdmpQ/PmVX48rqEY/um0jbBuyWQ9s9UJ4imd1uf0BMyrAzCPmT4lJbyectNiPfqrUOzQOGedK5c+dCZ2lP8rRnaKA5ba4gz9X1HFtqWoH2fXOniDMn/8FdB9aoNn+clFM9qU+mX1YX7T24mb0sl9+9vWHN3qKGfH/SyIL+iewGeWCorzdUqiyaNmNtvSZjD2DUpGFtgZ7KDGcxYCDJPLawFoPByJ+CPpqREdACnGjn8mTaW/iELmtSAD8MaAUKYrDMkLLBgzuR+smnI1fzMyKf7tm5cwfJjHyKR6mHAJMCwGQVtBWP+iox2GC47MEeRpbjLlkvW4k1zs/Z4GUi8VQ2wGq6ZVMSCsl5mp7RZivzohJyAQBx+D9gCia5wFjGqu/nzz557469Tb9KcX5TRsYvBn5G066SH1bsWr/20ay8GfkZkw6MGY5bAIMS6Nc9ICsfCqISxeMzmy8HcZBPSLhkv2wn9lhkMNgYD+1VANmua4zepes9ojov0E65u3tFM8xEFwN9gkN5uQV4iyM2UQhOmlG3efe+e7wbN+CsjszMjCxuzbDk4F8GLJvf/5GmTT/V/2nOHT+r7WPGPDRpfJc9fAi48ch/hOEwy1ChcIjXhQLSOHeOAgIJR7AAf9ik/rrjPD/jZGQGOfSM9pwXxX0djM+OYlEaKlecXhOKNcHLkCYzzOU0nJZkMWg5hdM72IBMpgS/n3fZ6ICToZUbBkxln3lWH7EcFYx8XV8TZf3Z+kCiy5nWPWhH1xZ4gkBR5sYn1U+Tc0fgOBz4JQ3+2KyMrBgre9k1q+6eVViafmdcRsPnX89bNmhQcf/h6sdjxsycmJaj6WpG1KaonDIVi99nindwsb6odL7UpPP5jdLJywOLop4JVKOoICqSqIDA/ScWOKBXusrgFCqZpMlRyWzYqL6vSYa5DJJ5vf+y2gGaZM7MmfozNkclQ2NbZuc/yGJmll1AsxDq+Kd2zAy+uJWZAMequ4+lAKmYpZ03+/oxuHa5dmxO9zG65KNJu3Zu97ECOG+Vdmxe97ESOHaPdm3NTe1+qJ1Xe9O167Tz5ncfy+hud4F+7P8A6+Y30AAAAHicdVHBSgMxFJxorQjqWTy9ox4s3YKHHtVLCwpSpUchuOl26XazZFPBgx/iF4gXb978Do8i+AX+gbNpBBHdkGTeZOa9lyyAbTxBYflZvEes0FZXEa9gXS0iXsWWuo+4Rc1zxGvYVK8Rt8l/UqlaG4zegqvBiprziFewrUzEq9hVdxG3qHmIeA076iXiNvkPPELQQxcJh+ASUxjuZ+y75PS4RRWYE0aOuFk1+TwoOjw5QsEhGJHL6PeoQ2S4G6pvuKZBOcScGTSuqRGqG53m9CFnSt2cu8OMnMWE6wXPDD1NVyX1TU3BMetpqpq+9jAgW9Cd8yyjZp/sKX1NTTxKr5skcjk1cmZL628rIyfWVdZpn9uyI0dFIaM8m/paRqY27sakHRnOK33tJa9Fi3c6NXPtZmIncuFNNTVlbUs5LvTMyN7AFmleZvW+nPqUBX/eEcs8CI+RYRHadgxNtii0w68HOeBM0OfofVvlQJJ+v/dP2nF43zr+C8Ehb9z8R4yNq3k7Oewkyd/eLzSUe5gAAAB4nG3NZ05CYRCF4XdA2qUpFuy9d0AQ7L333pWYS0kMGEBUluHK3JEaLt8/J5nkycnJDCbK8xMiwX9T+lvBhJkqLFixYceBhhMXbjx4qaYGH7XUUU8DfhppopkWWmmjnQ466aKbHnrpo58BBhlimBFGGWOcCSYJECTEFGEiTBMlxgyzzDHPAossscwKq6yxzgabbLHNDrvssc8BhxxxzAmnnHHOBZdccc0Nt9xxzwOPPBHnmy8xiVmqxCJWsYldHKKJU1ziFo94pVpqxCe1Uif10iB+W/Ll8zUVi2j5l3g+NZ7IxZ8dJT2XLcuezegGCu9GohVSOd3IHInsW66idLHSy6c/jF5eL+oZg3o6mSoYxUxaHSy/DQQmFQIKQYWQwpRCWCGiMK0QVYj9Au0DXJsAAAEAAwAIAAoAFQAF//8AD3icY2BkYGDgAWIZIGYCQgaGZAZGhlSGSiDJAhZjAAAWKAF3AHicdYyxCoNAEETfeUHkSpMqpBCxzFeksgwkbQoRkyIictj49WbuIljJsbsz92YXAzhuPLHt7HtOH999KfpmGrhyEGVZCKk9bd6+acmkbHAkoo6cM+WfU5DGWa2+XuddlfJQN8pb9UtURG51yXDUH1IJnR6MzDEf9jYe/GvzP5YJEsEAAHicdZI9aFNhFIbfexOtxJ8kJbWaUiLSFK94FZrS0JhIkVCTThI0JU0q8ac6tAYaBP/AzcXFQSdnpw4dMnXI5qTYRQfBTB0KDpmcHMTP534moIPDe8/57jnve97vR46kiC7rqkK3H7c3lLjXXlvXqY2bD1o6pzBVGaOg63+5s77WbulQkFmE5bJyAoQitnNSRS0xoaqmWnqkF/qoT+o7p507zhOn63x1frgRd0FFt+I23Wfua3fb7bof3G/wHE0pw3fcfOGbNH3UE+YzWU8hsncao5IkumR9sn1mJuTruE5aVlJpxXQElSiIg1EQqPoga15qHr0cehdZ581zFYgLoA5WQdM6yKJ8UFHzSnEwhfI8Cjl5yhOPMiHFBJ8JPuqeplH0NaEZ4iyYM3sop1FOoZjSLf7FYGRhZK2faTrOMmWGOAvmzHem3sffFv528JfC31NUJtQwuyjs6YCiv34qDkbNXVztyjedQNdUFTMP8VodVLapvMVzBs8+njMqoF9EexGUQA3XK6ABRlDowO7A3oG9D/u9ZQ2rXapdqltU+4NqmmqK84+qxK5Kg1P2OJ2oqdBfob9K/xvrpQgWwRVur0RcQq9CvMa6Sh7+5zwbaIXw6A88ZqyLTVQ3VWbyMqiBFesiHZwMJxUHNfvH4y59e4M9OD04WThZKgUqCbTL1Bv2zo+xGs4OdjTyF8uD5dmzqlvNCC992FumdxnUUPnj0qcjzeuZ1GGN8SbHdYJ3mWRHZ6if14XgDXIrOW43j5NLaFxHowa/Dn9VN34DxqbZOgAAAAABAAAAAN/WyzEAAAAAppLG+gAAAADU3v0g') format('woff')}"}}, {"type": "Font", "id": "c2cc4272dc3145a49bdc18644161962b", "data": {"fontName": "fc2cc4272dc3145a49bdc1864416196", "path": "@font-face{font-family:'fc2cc4272dc3145a49bdc1864416196';src:url('data:application/x-font-woff;base64,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') format('woff')}"}}, {"type": "Font", "id": "fdea7b1d0f2905349b6698cc6f1882ef", "data": {"fontName": "ffdea7b1d0f2905349b6698cc6f1882", "path": "@font-face{font-family:'ffdea7b1d0f2905349b6698cc6f1882';src:url('data:application/x-font-woff;base64,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') format('woff')}"}}, {"type": "Font", "id": "6bd0621a4f9a5ef42941f71dac538424", "data": {"fontName": "f6bd0621a4f9a5ef42941f71dac5384", "path": "@font-face{font-family:'f6bd0621a4f9a5ef42941f71dac5384';src:url('data:application/x-font-woff;base64,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') format('woff')}"}}]}