<?php
// Simple test to check if admin session is working
session_start();

echo "<h2>🔍 Simple Admin Session Test</h2>";

// Check if we have session data
if (isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in']) {
    echo "<div style='background: #d4edda; color: #155724; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h3>✅ ADMIN IS LOGGED IN!</h3>";
    echo "<p><strong>Admin ID:</strong> " . ($_SESSION['admin_id'] ?? 'Not set') . "</p>";
    echo "<p><strong>Username:</strong> " . ($_SESSION['admin_username'] ?? 'Not set') . "</p>";
    echo "<p><strong>Full Name:</strong> " . ($_SESSION['admin_name'] ?? 'Not set') . "</p>";
    echo "<p><strong>Session ID:</strong> " . session_id() . "</p>";
    echo "</div>";
    
    echo "<h3>🎯 Test Admin Dashboard Access</h3>";
    echo "<p><a href='http://localhost/admin' style='background: #28a745; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-size: 18px;'>Go to Admin Dashboard</a></p>";
    
} else {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h3>❌ ADMIN IS NOT LOGGED IN!</h3>";
    echo "<p>You need to login first.</p>";
    echo "</div>";
    
    echo "<h3>🔑 Login Test</h3>";
    echo "<p><a href='http://localhost/admin/login' style='background: #007bff; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-size: 18px;'>Go to Admin Login</a></p>";
}

// Show all session data
echo "<h3>📊 All Session Data</h3>";
if (!empty($_SESSION)) {
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<pre>" . print_r($_SESSION, true) . "</pre>";
    echo "</div>";
} else {
    echo "<p style='color: #6c757d;'>No session data found.</p>";
}

// Show session configuration
echo "<h3>⚙️ Session Configuration</h3>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<p><strong>Session ID:</strong> " . session_id() . "</p>";
echo "<p><strong>Session Name:</strong> " . session_name() . "</p>";
echo "<p><strong>Session Save Path:</strong> " . session_save_path() . "</p>";
echo "<p><strong>Session Cookie Params:</strong></p>";
$params = session_get_cookie_params();
echo "<ul>";
foreach ($params as $key => $value) {
    echo "<li><strong>$key:</strong> " . (is_bool($value) ? ($value ? 'true' : 'false') : $value) . "</li>";
}
echo "</ul>";
echo "</div>";

// Quick actions
echo "<h3>🚀 Quick Actions</h3>";
echo "<div style='margin: 20px 0;'>";
echo "<a href='http://localhost/admin/login' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>Admin Login</a>";
echo "<a href='http://localhost/admin' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>Admin Dashboard</a>";
echo "<a href='test_admin_session_simple.php' style='background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>Refresh</a>";

if (isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in']) {
    echo "<a href='?clear_session=1' style='background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Clear Session</a>";
}
echo "</div>";

// Handle clear session
if (isset($_GET['clear_session'])) {
    session_destroy();
    echo "<script>window.location.href = 'test_admin_session_simple.php';</script>";
}

echo "<hr>";
echo "<p style='color: #6c757d; font-size: 14px;'>This page checks if the admin session is working correctly.</p>";
?>
