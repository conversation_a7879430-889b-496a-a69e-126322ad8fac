USE game_platform;

-- Create admins table
CREATE TABLE IF NOT EXISTS admins (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    full_name VA<PERSON>HA<PERSON>(100) NOT NULL,
    status TINYINT(1) DEFAULT 1,
    last_login TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Create agent_game_config table
CREATE TABLE IF NOT EXISTS agent_game_config (
    id INT AUTO_INCREMENT PRIMARY KEY,
    agentCode VARCHAR(50) NOT NULL,
    game_code VARCHAR(50) NOT NULL,
    custom_rtp DECIMAL(5,4) NULL,
    custom_min_bet DECIMAL(10,2) NULL,
    custom_max_bet DECIMAL(10,2) NULL,
    status TINYINT(1) DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_agent_game (agentCode, game_code)
);

-- Create system_config table
CREATE TABLE IF NOT EXISTS system_config (
    id INT AUTO_INCREMENT PRIMARY KEY,
    config_key VARCHAR(100) UNIQUE NOT NULL,
    config_value TEXT NOT NULL,
    description TEXT NULL,
    updated_by VARCHAR(50) NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Insert default admin
INSERT IGNORE INTO admins (username, password, email, full_name) VALUES 
('admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '<EMAIL>', 'Administrador Principal');

-- Insert system configuration
INSERT IGNORE INTO system_config (config_key, config_value, description) VALUES
('platform_name', 'Game Platform', 'Nome da plataforma'),
('default_currency', 'BRL', 'Moeda padrão do sistema'),
('default_rtp', '0.9600', 'RTP padrão para novos jogos'),
('max_bet_limit', '1000.00', 'Limite máximo de aposta'),
('min_bet_limit', '0.10', 'Limite mínimo de aposta'),
('maintenance_mode', '0', 'Modo de manutenção (0=desabilitado, 1=habilitado)'),
('api_rate_limit', '1000', 'Limite de requisições por hora por agente');
