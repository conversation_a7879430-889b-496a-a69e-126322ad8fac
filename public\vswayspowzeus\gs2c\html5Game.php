<!doctype html>
<html>
    <head>
        <title>Power of Merlin Megaways</title>
        <meta name="google" content="nopagereadaloud"/>
        <title>UHT</title>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, minimal-ui">
        <style>
            body, html {
                margin: 0;
                padding: 0;
                color: #000;
                background: #000
            }

            #Mobile body {
                border: solid #000;
                border-width: 0 1px
            }

            html#Mobile.iOS.InFrame {
                position: fixed;
                height: 100%;
                width: 100%
            }

            #Desktop, #Desktop body {
                width: 100%;
                height: 100%;
                overflow: hidden
            }

            .message-box {
                display: none !important
            }

            #pauseindicator, #wheelofpatience, .scale-holder, .logotype, .logotype-wheel {
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                position: absolute
            }

            .message-box {
                display: none !important
            }

            #wheelofpatience, .logotype-wheel, .pause-wheel {
                background: url(data:image/gif;base64,R0lGODlhZABkALMAAAAAAFkzEZhYH/KMMDQeChoPBbNnJMx2KQ0HAwUDAX9JGgAAAAAAAAAAAAAAAAAAACH/C05FVFNDQVBFMi4wAwEAAAAh/wtYTVAgRGF0YVhNUDw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNi1jMTExIDc5LjE1ODMyNSwgMjAxNS8wOS8xMC0wMToxMDoyMCAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wTU09Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9tbS8iIHhtbG5zOnN0UmVmPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvc1R5cGUvUmVzb3VyY2VSZWYjIiB4bWxuczp4bXA9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC8iIHhtcE1NOk9yaWdpbmFsRG9jdW1lbnRJRD0ieG1wLmRpZDphMzdhMzFhZS0zOGNkLWNlNDItOGEzNy0wNGM3YTI3YzFjZDYiIHhtcE1NOkRvY3VtZW50SUQ9InhtcC5kaWQ6NTEzQzk2QzRBMzNFMTFFNTlEQjlGN0E4ODA2OTk5OUMiIHhtcE1NOkluc3RhbmNlSUQ9InhtcC5paWQ6NTEzQzk2QzNBMzNFMTFFNTlEQjlGN0E4ODA2OTk5OUMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIDIwMTUgKFdpbmRvd3MpIj4gPHhtcE1NOkRlcml2ZWRGcm9tIHN0UmVmOmluc3RhbmNlSUQ9InhtcC5paWQ6MmMyZThmMmQtOGU4MC0xYzQyLTgwNmEtNjljNTUyZmMwODczIiBzdFJlZjpkb2N1bWVudElEPSJ4bXAuZGlkOmEzN2EzMWFlLTM4Y2QtY2U0Mi04YTM3LTA0YzdhMjdjMWNkNiIvPiA8L3JkZjpEZXNjcmlwdGlvbj4gPC9yZGY6UkRGPiA8L3g6eG1wbWV0YT4gPD94cGFja2V0IGVuZD0iciI/PgH//v38+/r5+Pf29fTz8vHw7+7t7Ovq6ejn5uXk4+Lh4N/e3dzb2tnY19bV1NPS0dDPzs3My8rJyMfGxcTDwsHAv769vLu6ubi3trW0s7KxsK+urayrqqmop6alpKOioaCfnp2cm5qZmJeWlZSTkpGQj46NjIuKiYiHhoWEg4KBgH9+fXx7enl4d3Z1dHNycXBvbm1sa2ppaGdmZWRjYmFgX15dXFtaWVhXVlVUU1JRUE9OTUxLSklIR0ZFRENCQUA/Pj08Ozo5ODc2NTQzMjEwLy4tLCsqKSgnJiUkIyIhIB8eHRwbGhkYFxYVFBMSERAPDg0MCwoJCAcGBQQDAgEAACH5BAkKAAAALAAAAABkAGQAAAT/EMhJq704yyLGOIQmjmRpnpunDgHqvrDbrV4S33gO0Gqo/8ARz9MKGo+TIQvJtCAUh4PAV1IWTYls04Q40K7CIViULdu2IwXvUKiKS2YzWuTlCdy8cSZenmvqPFQaViR8fX4YMzwGJIQjhlqIFwRKSyKOGpCRkhaKNAcIl2+ZmpwYBZUKonlkkKYZakNsg6MYmmevToA0dxmYF6W5GAGVghW/FbfCGQZKjBjIFMHLk5V6EtESytSJSqAX2dvcFwW7K6oWSsbarn4FBaEnsUNtFZ4quBPiTRxRBwr1SHRRgo4Cqi/A2mFBwDCfBgQG/PkL4DADsSG9KnDwYGAdO0Mn/xiKZEgigESJBq5daGYnhsJHI0dW7HTypICAGSgFcgkSZsyYIwTUrAlQxEUVBWHw8fkTqNGhQylqICDAwBQdckQ03ToCItSaHccl29p0phMoX23iXJaAbNkTVNOeVBAvV1u3Mc2OCBBRbhQDdTndxSsSxxO/UZIiGkxYL4p+cp9xItzwCIG+X181bpKAL9SMkvA6/nG4pkc0jGVyghxF5ZzUo/kFIBD4VRYEscXq3s27t+/fwIO/JkC8uPHjxnMLn2SgufPn0KFf3d32nfXr2LHjBnA5uvfvYcUmyE6+/LsECsCrf654GQLz8K9DXE9fMrf4+N/VXw+aWn786e33Xf97wvwXHwJVCShdbWwZWJ4NTyQooAB08faeg/ItpxRlqmno4YcghijiiCSeUEAACsxGTXUM+oFAAALEGKNraBRgHDycUCXjjqdtgQByBBSg3A0FKLDjkTQyAWRxawVx4pFQEojGksa1eENnUGaZJBJU3jikCAQYmSWUTc5hY5dMfmlBkWNm2eMWCaB5XJldwdjmkUUJM56cVcJ1J55vSrInn0HmxsGfMkq124+EBiqBmH8GYKU2KKZIp4nFTboBoZdOgGiee1SqwKiOThXAqad2CsCgXaoKAKRRlirqqKNqqsGJqKJqKwDvtbrXmIqKUCStxJY6Sa6oGssokK5GuGP6AK5OMGux5CTbIgHIohrtpsjl1hmt20oQJrHENolrrgxim62KWJxZKGrkEqtHZ9mu8+K60KLQlpowjBsvqBOci+yx+PLr4r+jXiNwrsDgy25v0xLb4sKonuLwrqYM++9pFJ+aE77GShJxwhl07Nq964Y7h7/xXmqyqesSYPAR6CFM48sZoFyvWCyTqynOJV/MzRMIBwq0LeruTM3Io452tMX4qmwE0RuP8DQGSSMb8hEak7slAFdfoPPAwlBNbrRhE6z10vF+DTa+cMRMjdkpKpd2tbnKPLS6epdwt9jJlfj2uoITCXfhMPyNeCZyL/4CxRg7jvdskTcRAQAh+QQJCgAAACwAAAAAZABkAAAE/xDISau9OMsizjCEJo5kaZ6bN6xDgL5w/AoseyRyru+AWg8hnnA4+rFcxKRyYlwhl1AKQmEwCIKlZss0rSoQ0RLCcCiXn8UmWlPwDQ6F8EhhroNJ2nXGYDTIRWR1ZQpZaiQEWnF/GIGCB1gaeSMJbjWQixQdjgcCeIYiAVoDd5gVBJtnaUZ6FQiinaUXmo4GOJGfGTSJsRdtqKwUkhmIWoS8F3SoihjCjKK2x62NgsbMuBahzdEVAaiPGdoUlFoH27moftarGAqil+YTp7/qP6wFr/AZybSkwdcTuposy1fB16ZqFCqteEesCcIoCAr0I9FNmYWANqBN4NPkhpwCVP+8DBSRYFqdhwDa1LPQcN2JBDA1akAgoIpNAwFkDkMFq6AmEBey9XkZMybFmzevlJh1MobQH+8wFJ2q00JIpDYFjOy1KSqJe0Z6kqRadA5WrF9AUcvx9M3EC2Spjghw9mxODQQUCFDg9US3FWk1xCU7qWZdpEoJihssl0SXw0gVbN3GuLEJDpCRBngbq7JlFAQMZ7bCeZHnsjkS0B2NM9ppmENAjhaL6fWS0JmPnY6iWnRk3YwXIViNtG+YuLxk2wT2h3C0AgQIlC5lVLH169iza9/OvXu+BNHDix8/vqr3QwLSq1/Pvr1x1wjiy59Pf76t0O3z60+suL7///EBoNf/fgSqhxJlACZoX4EMpmedghDG12CBB0YTIYQBTLgfc7xcCOGAGq4XGEEJePgfDsOBOKECm2FXoon2nRfDa1PJaOONOOao4448ooAAAQEEMBl18Zknh2oKJJkkh2FEVMCTCBh5m5JUSqbbk1hCuUgBAVRJJZNLZCmmRBB16eWXx4w5ppQwgHeml2AqoeaYUQqR15teDtnknHTqwCWeXr4XBZ9qTjeTmYAqKSQ8ThKaJZumJFqloKY16iiZX0mqJAGQdnZplhRp2mIG4AW5qA4REYBpBpYSaqgEkp5KqqmmUpoBdOLpOUGrhc4BKKVA0hpkpxf8SN6rAJTo6lxvcuqY+bCm6soGeaqWkACfyCL5JbITQBttL8E6awGux3KhprVAsihtQd4G+RaXtIpLAbnkvdRonce1G6QFqkH7DnjUVosCsTsE6+277YIbMMGxDNduVPBCC1fAAmdnMLRVRSwsBvSOx20sGgvrVcimDkNxdvoyR/K+GBhL7bqlrByknjJPu7Bi/XprXM2kUgzzHxcLayjPt1LM8BIO6ywC0Sa/nE/QtLLJdMsUf7xEztCuOzXHAf8cpr5fJTyCy/Vuk7SwMG/NNbXwKB22t+iS5zXSGJegNtXlfRcuwXe3jOvc2vXdo91iDx6D4IaT5HbiMIQsL+MvQBfk49FEAAAh+QQJCgAAACwAAAAAZABkAAAE/xDISau9OMuizhGEJo5kaZ6b4a0B6r6w262ekcR4rgMq7YW7oHDkYw2PSErR00o6K4mAQKAAlpaHJik6VSCepYTAQCZbiUutCGEYuAeHAngUKNu/JKxaI3i/DXMiY3ZkCldpJAR+fnKBGIOEBo0ieiRti25njhQKkWQCeYgiAZhveJsVBJ5maEV7FggHpQOgqBedngI3GpUaCrNxthcFqwavFb0YBbMDhsIXdauTF8kXfaUHp88UYqvOGNWpzMfbAKqrmkqiFgmypYDlj6u11OsVpLPp8Smr5OESsWbR22cBVyQB2ib8A/AL2zSCFNj0u9DDRzpFs8gdQVAgIZ1iD/8ZFrFh4RKmA7uecJgiIIDHDN08vSLm4xXGUvoyqILjhQQCBSxZBkgp4lykbxQ4qAABbRY8EsQWZaMTNGiVEgbtID2BD1PICwiuYdp6q2pVBV8rEIuUcwSCUmSbMjswAqjZqi5HEdJIoqsbAy8pEHDHbMAIAnfvDtVAAOjVHDsP9MwQtfDfEQnsJrbadttPy346T0AgZfPZtML8guZrYaXpoHnLDQb9RgBqxppfTxbWkPYB0SK4vJ7COsnN1UEKlDYdd47JwruDNH79jLDT25A3NwfzXCpwHaTvfj+i+k3xIa6njEci1g3CbQUIEAi8CbEBtBDz69/Pv7///wACmED/fPIVaOCB8hVAVIAoNKbAgxBGKKGE622SwIUYZqjhhhtM6OGHD2Jny4YklnghAAGAqGKE5zli4osbrijjg/rBaOOFM8pY440wppjjhy0GwiOMP/3oIX3PDFkiQD4aqUBs+ympIYNUVmnllVhmqeWWWyJAQAAEiDgHhtsk8GUAaIJZTgIItNmmMGemmaaYR7DpppsLOqGcnHwGKcSdgCKQ559x9pnmM4EGOigOZhraZ4VBJJqoEHs6yiedQ9gp6Z2LmlCppXJC+uemir7gJah9YpqEpqTiecKnqAagKhitBloCrKAS0GmZtd7ZV6xgImnml2HqwGZHuwLAKqnJohgr/3aNyjlrBRwVYG1HmPVKB6hiFppmsxgMeO214NZ6mKO6+uTotACNO24YmzYbBZ/zmeDtnGARqGC+7lqLJBSTbhGnqrim6ZGXB+ZZbb+zsurIvWjqg2CxUDDsb4AFo3nwxPos3C+4SVraMcfKWPyvbJYOijCCGHjsLsiokLZuyySXbHF/EKtJ88QwWYytfhnLmsHKB2rg8rv5Rfuo0TWH6/PJjgSNJNEGruEzzE7IbKhoVBc4wtME5Zwu0zxbbTHWdVoqYtfyQWVyPFrT61PTGojLMNyOQs32eEdbu8+9fNMtAsNQP3FqmmO7JXjd/UI0YIIn7H3CsT9zCYDkluOAeRXmMGzO+QsTs/s5QAiiPXpEBe67TwQAIfkECQoAAAAsAAAAAGQAZAAABP8QyEmrvThLpIxRhCaOZGmem+CtIeq+MNqtaxLfeA6otNfqwKCoxxIajxSiD8m0IAIKgaJwUhp+o0RAIAggmqVElMvFDpXmDEJwaB8MVLBoSyZ/SdY0RuF2C+QiY3VdJXkkBH19cYAXg3WLGoYjbIlteowAgoMKeGgjAZVud5gVBY5kl0meGggGoQecpBd0jgo2kasZfK+QshOmp4S4RKkABa8HAb4YtI69FZIYlJUGt8sVYsHKGdEWiK/b197BAs8T3RQJrqF/4nvBsRfoE6CvxdfAp5fzAK2v8e5mkRulitiFXaEIBqywRlujXL+QhQODoIDCT+SezehhbcK0RNX/5DxRQNKLiWynJqbokeaYvRMEKCm4eIEDyZsgOmqImbJmlA966lVqR8LUgKNHD9CsQACn03sTNJFRaeJbJXNOFCDdOgAgM6dOA2ClkK8OVAwIQnlldoDr1gOfwII1uXMQ1RNC2xhYOoGAAbdc4YpoKhcsAZ2loAQ4O5jNB77GBAB2ayALlMKGF2LjM9nt2ZGYw46VBapz4LsZClwOfTMAYlJ+TXMt94Iw65uQmwSQvfXKjQS2WaMGQ4B30uEoQLNeJlk2rNc3VC/39dc0bSbBwzLv7FvkapyMhex2m0yW9JvhjWjdOhMfAQK55fCcorm+/fv48+vfz59/ggIABijg/4ACQtdfUQEkqOCCDDY42jJGGTcbFao1aOGFYtlXgISdVYjhhwva1xyHbnUB4onIyUJiZyieaN+KkxHQIobpgVEdjEgpNeOF8WFSHI5IhYCAjDsmCB9+4+GY4oEZAFfkYUxGKeWUVFZp5ZU5DPneg1ieRKSCNQqRwJhj+uIhg1wiQeaaBgoxJI3LsMkmRV9eGGYOcsp5hJMn3olDnnkGUUCdH6ZpBKCA4vBmi4buiaieL/CJ4pHuPBroCWeCSECjclgK6QhPMNrmNZ6uecikkP33HqW/IeDqqACUCisAhDbIKgbArbplq66+mkWpI9QKZpqD6vrerBck0Ouys3oarO6dzRq7ao/JLrvsSYlmQeitIhQrrTn/AYjAa9Yye8Kl2q7KqZbSctsPgYgpW66rKMwJSLu7WkCgRTXNO25/3kp7UbgEVuuvf/humuy+WPlLr34BG/sawQPi6jCypLD7La4Mq+FwfhHr2mTHFvuLMSAaG8sXxQJqIO+89yU8GssBinBxfSGv2ibNAIrwcrma5drugzxz6TC1TeR8rM8ku3yzOEJvzPS+JBztTsoiZ9G0zQdDjW+PRYfRtTghGxp2CfOevKe0yJ5Nws8PB0TxuVtn0ava+bndJQx67+1C336jsC/SgTsBb+ExEPxvQBEAACH5BAkKAAAALAAAAABkAGQAAAT/EMhJq704SxSEUIQmjmRpnpvirSHqvjDarWsS33gOqLTX6sCgqMcSGo8Uog/JtCQIAUWgcFIKfqNEVIpolrSKcBg7VJIzCIFhbRBQvSKCeN4lWc8YBZutgIvmcwEldyQFe3tvfheAc4kahCNqh2uOihRbjIKRZiMEk2x1lhUFjGJ4FZAZCZKTfaIXmIw2j5waAZ+UrxcIpWNlRKcShriauhZyvaEXqYu4ArPGFQm9UrTAGZ64wcbIpZVJtcvO0ba9xRbMl7gG3+QTpL2n6Rvrru4YsYDQ4NcWt7jK7lXgZW5RuHfrzjVBgGAfiW6MAgKY0cPhDmcWjXAIwJGARFXU/87w6nFm2KdtxyQF+OiEo8uOGS9ADLRrBgiZuOyNIHWgZ08DLClAefmyHaxSCk1km2R0YACfUA/oxEe0atN33mAg+JQUloGoUA2MqErWYxxAXU38YyMgqAQCAsBGFRuHbFkRBVyifKhHwEoNPOVGFZDFrt2rxrQIlrsXwBPDZN2K8rQYbFoMeSETJRBTUYG4laEqQIxZM9HOXp6G9nnlxmPTARobIbD652UUG00bU1D7r5DMkI2BXjy6yVC7whe3hvOaKOkjtMEaCIBa42ZyvEVLZoKAQIHtXuR8eC6wvPnz6NOrX89ePYIC8OPLnw8ffHvAsA+n/3xggP//AAbo3/8BbgAAXH5WnVdAfwI22OABBRyH4HXmCeDghQL6NSFy5jGI4YcHbMhheR5+iKGEIsZ2ngEmfmhAcyJydh4BLZ7oGIr5yYjeUzUGGOJ9McAIm45AFmnkkUgmqeSSRyQQn31MZCabEU4SYKWV5B0RAIAGTKlDhFeGCWUQW2aYZQzdhanmmUCUGKAC1QUJpppqGgPibTDMSWeYbOrQYpdApLknnWMCoUCNbd0g6KBrRoOAmxjiiUGVjBLqTho9HuDlopVaWSgTnvQIFAmcVtonHDy2eMB2ejJaQHXvwRcnCQnUKsI0NU4lU6dm4TXfpxTUKmycn5lIlwadPhfrfK4Na2vzJyxeeGwGrV75Kq301QeDs8OWUGaDujpB57UlLPvrBQkw1NCk3D5L6qE+gkfpdyc4mS25waqrLrrtuluIhf6x48e99Fqgr7oZ9esvqd4pYu58FqV7MEP89nsfwR9JPDG7Fq/3sHyTTkwxx9yuZ2+2LGl8sCoKq0dwUyrrq0HL6H0cX2cx78tyx+ZhPLPIkik8qx82y/qzyFnw7M7J9AUNdNJKO0pwFk9DXfI9Pt9a9a00R8M0s1QjTWvUutw7a84jW+3sPR/bhzaUZL/ydZZv17t2eTKbULfdCy+5N5M4/A14kFsPjubEQxuuysaKB45weREAACH5BAkKAAAALAAAAABkAGQAAAT/EMhJq704SxSUCoUmjmRpnpunKgTqvrDbrV4S33gO0Gqo/8ARz9MKGo+TIQvJtCQIgaivpCySnp0AollKRL8gU7WrEJgFiik3AwVHt6QxKXA+K9Yi99cqkosKdXVqeBV6X4MYfhplgWaIhBKGX3FDfBgEjWdwkBUFkmF9lSIJjI0BnJefNouiGnSZAo+oHJKWFooWgLCnqKmSmxe4hbAKq71On7YUwhO6mcrHEp6SsjutwcTRrp8ZzACYsNXa04bQzAiwArzabKrB1xSvmcDsFrTl7zyIzqaECQjGSrQxRA/ADBoBJZQKVGwNgoEECmbwUsseD1v8AkG7xChAQgxY/9wU+IiBnJ4LHDyAqgCu0Z0SBRQYmDlTgMROtcTFw/eiZSCdErzQHGpgXbtkNyncAwMUA7p+IzARHSog6qc9JCkMjLKRhE80SaUJmDp1hMmrOgu0aSqCQJkPYRHIJEv0pYaQV7myhSSU7tSuE/DmDRCx3gSpfokCrvBwMFZ2MRPX3Uvh7FUCWdcEkEyVspOtl48R4FxzcYnGeU0bmSvZoxG1eY+NTZymieU9x1hPFaD6huBDosmqy3wEtRRtm4dqiYagwMh6bj94Nky9uvXr2LNr314dgffv4MOD547jIYHz6NOrVx/2WOQD8OPLnw/fQG3z6/Prb8+pgAH6AAZogP9z+hW43nRMKBDggvSxYOCD6Fn3H4MUwgfhhdZVqKFaFxaIIBICaMigAE90qB9xqBAg4oItJMChiec9d91oK8pXFHkxlAhjYTj26OOPQAYp5JBI/OMdimtM05sRzTnnHH9MBDDAlAMYsGQOTTrpJJJHEEDllwpAiYOLWpYpZhAGfPnlAa4VmWWZWh5zgJpqHnAlCW/C6eSZQMxJp5qx/JCnnlvm9uefYfpGKKF8AoFAmofWadQJgy4qI3MKREqnlSeQaamWjTIBiKaAQunppwWEuoaKpFJpAH+VwgnQKN9xmcd/nE4UgJ+kTnoBqpdmIJ6tGAigpl1OGUtqVX9Yaqr/eKpK4CWdi42mqQEjxHomtLO+oGydJex6KLIZyEost906kcC6GEBKp68gKVinmE2mewW6JK2rL0mZHnrmqFMOiAe6Eu27by6RkpstAR+iYCS0IBnM7i2RNuwQt1lJPDFjkWKr3cPiTaQxBlIeeufAGIsscQa8gosdwXeNfEmk8GoDcnjEaUzct3RGWxy6o8hcUqTMGnbzeEGvvEjF1OE7gs4iINCx0UA/LXQGJf9ZMyROW620CC1/6XE0R3/XxdVsRFpPylegnQHPVB6wNsRnf/3Hn0VrE7IJUIc7L3VGEhuU2xpYe0CiRAZGeOIu9M24b4s/znfkktetb+U5HGxYAQQAIfkECQoAAAAsAAAAAGQAZAAABP8QyEmrvThLRIIvWiiOZGluXhqAZ+u+Z6d6CWzfODCnbO7/od0HSCxShCuj0pIgOAkIE7InanqgS5JVRQ0Ku5lEQEEmR7Mh2aw2mpLGZXIAnRYS3t9RIR4/0y9IAX4abiJwfApgfxJqM3eGeWmIZYOLEwWBihWFYZNylhiNKgRsGZyhngqVoCingJEYCKmPrBaiKqtHsICppbUUWzu0rzuaAHuew78UmEi5Eq5HnnPLoUjKusWonsbLCJkY0QAJqdTV1na+E+KHiM/nAN/XxDNgyJPYRgkI6iLNQqtuBejXjk8/IgkKPClwEEMwRxbkcbFwD1G+DJjkNKzQ5MnCEv//IEZUo4jAtBLfBKhUqcqfx5fvKgi8KMLkpG4TxKzcKcAcxpcvC8RsVe+FLEQ+M5jkuVOBS6BBN0oIGYDmCJtxpB5TwJTpU6hBQ3BwgvMqnIEaUnbl6bQmWKhDWelcy9RqToVvo8JjxJUuT7sVxub1WDbLHr88BbnAO/iJViUEEDctHIbxYMpACkhmCViL5be1AmxGC0QwaFZ96SZR8vllaLoKOt9ICLdW5K6yfSCwjJmI6J2Ka+1meK6DgtV7kytfzry58+fQn+9DQL269ev8ottIWKC79+/gwT8GJUuAgfPo06s/L6Al9/Dw44//g8D8+vv3BQyPzz+8cgX4Bbhe/yL9FeidcvYJqKABBjao3IIQ7tcgfHEtAiCEASrw3oThzUcHARgGeMc+HH6X3XIghqhebtpx1BhUHrYo44w01mjjjThqsE8CMRbBAXJyXdfjDwEcYOQBAvSGw3TYDYkDAUdG2RIaTGJHXS0CRBmlAaTpY+V1tWgppgEsavEldrUYIKaYifhQ5ZnUOXmDAmuuOSUMcDb5CwJq1qklly+8maecOcjip5gCsCgonIRmluWhUla46JeNGgEipEcaEFee1lXBY6WMmJeojkViekBSF3B6oo6f8jjnALDCimoFe2AqgAiDjtBqqzAQEOuvgGl26K0hTOrhrp/CIMCvsRrwRvufYs4aEZolIJvsBVDCmuQFyzIrqxalblnhONX1aK2rFgTgLRgKeAvrAeNudeS2VJ7bTwLuEsuMu7C2hVJ3i9iLLb8XtOvuAUpaYu9B6rqb6gH86svcwuEQDAi/A5T5h8AVO4yBAfw6OzHHF3s8ML/SLkNyyd5q0K238CpHsSkWY1AAxO76C8/KLDMbgsHuJlzEzDSbHAvO3kr8C9FFtxwExhoDwXPHTocAsrsiq3xuGzVr4OvB52zNtdEug12N2IZ0rcHN3uq89K4lNFx12r9qmhy6caudxqN34ii3zzne8Pevgd/w9dyFt4Bv0onbMPgAQjc+QbYD0FtNBAAh+QQJCgAAACwAAAAAZABkAAAE/xDISau9OMtEQiCFJo5kaZ5b53kh6r4wqq5eEt94DtA0ov9AEm/VChqPkiELybwUCCCfSRkojjgegq1J4kC/UqHSqkkMw9zM8/vdjqjkzIyWFrHZcQycVKCi6xV3bH96Y2KGgE6CUHkWe3ZUAW6JFAiLUISOiBlmVASUGGuLb5sYczyZoF6LjROPGAiRn6Chl1oarxeRkrQYq4KtO6UVfbm9xLapwkOtu8catrO6wxOnNMHHlpeNxhuyzyKigpOu1ADWK8rgAL93ed2xnonkfLb06PS79EYJCAX/CPbJ2WYByzULxcypCaBAwS0+/yIWUFdBG7ALlrLkQefBRKyGIP95ifAnUSLFCeK+YFuo5CQ7AiBjOhxBsmTEgDQXubzQiYc0aDKD0rRp82TKldCGCETJMKjMoUSLjnwyMUfCh7CaOo0ZAGJUm0uPcdjqFCmAml9vhqUEk2zQn1fSSgXXx63MADsryi35rK3dhlVwoE2bF0iBvyDhxui312wQrW6x/kiwtxdkp3ibDOZL6zJXxzc2F1iLxK9MxVwY33x2WTKlfoWRFCMQe53t27hz697Nu7fvCQmCCx9OfPhvHLARKF/OvDlz0qA+CphOvbr16QoyJ3fOnTt0QAgUXB9PXkH38+dzByDP/jpe9PCX525Pv3r8+/Pr199+3/n3OuLpx57/Amf1151uBAjI3iz89fdfIgkqaB1qx5XQTjRsPFjhhhx26OGHIIaIgnC2aQNaEMVpCEQABrRogAIn5pCiijoQ4OKNmdUxI4m0CHDjjQKIhMSOxtHy45ECUIgckTyC4uORP8IIBJNFdgYllELCQGWVtCDw5JVAKinCllz2EguYSJ5IZnC5HYZmlDutSSMXCb7pogCkrTmSAgMMcEBXOMAkwEycsGinAYCWQSVNB/TpaKIvKHDApJNCipECdhI4JpElCODopzFKQAClpK5UwJdQaqrojCUQ8OmnlpogKamTCtAqqjh2UdwJBrz66AU21pqHALRSKiYFZiAZZ5MlBOCr/6OKOfvpAXHMWqwB/7nZopSpNfqsAQV5+6mtxBRLaaxT1Rbps30q5qqvB1xgbbGh1lEAuwOQW4G0vmJkgLkHqLqbp+y+w64eAB9wbC/vPiswBfy+msG/5uqbW6/PHiBQxJ/KkTC6rOEL8g4HZzAvqQaoe0QC4r4KbgYcO6pBAR/jxie7YsbcpwgBJFyvYfharEvJGSBAcbEP90LwsxTpPMAbCS+smchvEK0BseYmzRa7Glf9LAmjmvvyMfc+K7XTJZw86djHYPwp27hYPbO5IwNC87Rmod0srSmXiOmLeel967Yq4ya4iDccjjgMDU+7OHIt9yn04y5wTC3lgfZ6gAUAP/8QAQAh+QQJCgAAACwAAAAAZABkAAAE/xDISau9OMtUCCmIJo5kaZ6bpxIh6r4w2q1ebN+4RKtt7v+inacHLBoBQtZxeUEUQIlTkijieApRZunJxZqmpQQhQA4otSJn9/kVUjPjMpmAFq25bwx4hJDLs3UXd1yAGntBfmV5gQBqgySHGQWJZYWMExyDICORFwmUc5cYjnecboiglqIbmpuGpxl9oAWro62qFJ0VcYkEuLUArYs6sIKgAbTATbdwxRa8fnTKGK3JF7oSk6DDypmDi9ifoNLTtpp6zhPQfr/lAN53ebqylNbuy5qqMzTX44EIABG0o6bpjRgab+glsjdCm5mBFAJKhBixoKcZrio4jGYCwboAFP8TSBxJMds3GBvlcMP0MVSakTBJwCMEQxzHhsfI8IEJkyKpjC9S+rKTs8wIkTx7VnEiEEcHM0NjtaREImnSkpfEFJXDMJZVnveybeV6AunXieVSFj2DwuxZgMqmzrrh9itWIGqPdY3xFu4quXKi/qgLdtVWtkYIS6xVdG9innd/5EW2imTcaJEHJ8hs5CnisKBDix5NurTp06hT+9jMurXr15zR9FFAu7bt27YDtIDNm3doBLiDC6ctsLdx16ADDF+e+7jzzaCZS6f93Hn06cyrH0+OfXiAd9phhy7QXXiy8MhFEyh/m5xqE1aSJPHyvr79+/jz698vSIEBAe7V4tH/B8AAN8CBByoQlwAMCqCAY0sEgOCEA3z3V4MY6lYHAQdQOKEBtSiA4YgB4mWAhxQeUMuILD5YhIEoUgjiKiKyOCJlOUgYo4cl1hGAjTaCZAMBJ+5IoYIC1ggkhgr02JAARqbo5D8/LjmiiyboGCWCFrpDnpU3rqTDlggqIKYo64HZoAIUFRmlARA2osABBxgw5RciNqnBJ2oyeKcEWx7wJwJuHtjlCwEYoKiihzZRpZVIatDhjo1ioICHB5x5zaKcxpmNkjZGmsGlKApwZgExVlqCf5wqKqohoGbIx6QIwmlCoROqSoAAdWJZAaut2hnGo2ueCVyHBqgKx44lBkDn+7O2VpBosP/dNcmanuKQAK0yWpCAAc8++2ow1DKKAlOBaMnjM+FCe8G01GpaGbcTjguAs+3S2US5Bth7D5QoZvpuvvq+y2+2f1GKAb75ZiBAuQKMhiuCB/zCcLtw8PunKOpSOOXF4WoAbKsCxJYYvQhGnAHIz2pQAL/K0rhjnCwXvPLBXqKcoAg1q7jnw9T6ewnAmK7UMycaT4OAwjwT7LMIQAcr9IYxztg0wSS8TK3KwKCK4sb3Or3q1uVMzPXVDVdFbcyBFMCtAZoeXQKRnJbsDnCMyiv33DUKud/e/CEqduAwcJiv1YS3BW67UyeuAcjROu7CrnT6qkwEACH5BAkKAAAALAAAAABkAGQAAAT/EMhJq704y4RKQYkmjmRpnpunfmjrvm23enBt39KshnjvjzoP70cs5oJDo5LCQYBOwUISuFuanNhnKTrNJAjgcNdqaWYRJu42HC6QRWfs+KIeIdjs+RsQl5PqInhsaHsXfVh/SECCYoUXZnF6E4AYX4wEbo6Gh4QalHSXBJKFnKOfFZaMmZqPnCKnFAWhrBqlnooYd5edtGW2GLASoau9m4cZwbK7xV6/FrCpgsTMFpy8sbgVyoyjzM7YOl26qtQikGeGQaCXewnu7ijfKTPXfKH1nnkl7/wn51mtqjy7NK0WgQAIEYoawa9htwmHHhq69PBLwosBCDB02JDEPy0t/6K1+XMQY0KN5jg6ZHhGIjBBo+6YxIgypcp+5vzYGCfF4EyTNTXc5FiuTIGfM/E9GrqyKABZSE0WtMn0HTWZUS8udFEVJ62jWU8qNdHVKisEYRVOfVGWVsmsPYlUdZuVwFgcQ+n+tNuO6NeZmFh1LPZWoUunRLbdRcy4sePHkCNLnvzDYtqThylPCDCgs+fPoEMHaIz1slgABEKrXt056FXTPxEcYE378wHEhWGfrM27M2LdP3vz/g3cpADhrAUQL36xAPLVa/8yR+imwPHnnQVE7wW2+HbNlaKIFwK+vPnz6NOrXw9MgQABrs+C+a4EgQLQCggr2K8gAP0fAcwmmv9b/BUYQGYuEGBAcrQEUOCD8flgXW0G0PLghf7JpYCAtCnHyoUgBrCYCQEKFyEZDoJ44VYvKIjcaLTYpyKIJ75yXW8G1PgGWjNi+N9mHNZ2AIzlHNXjgweSkFpvByiAoBUEHGlgNzd2uJ19BhwA3w0H9bfdF1Lup6MEQaqWIxxZHqDmkDUE8N6bY/J4JJEZlGkbnRlsuOaaI2JAwJuA0mfkjHhecJ9qCvRZwJ57jpmBm4C+l5+SMzqKwIKfaWeCAIyuWShqChggQKIXQBrplh5FeWGf9qmJagkEdLpmQQoaYKuo9ZgaqZNXpNhfnzUkkGanHjJx67GTxnLqm5/WwsLfGwHKegCtx95aLAW6RgrsWdI2eUGt1VZYzbLvNVuOnp0agA+41fpJrqaNxSpts+wemye5ySLGqawGzFHvrRn8Sa6jjsgra43/2qpBtm/yWpSw0l5rQcLiAvOuuZpEK+t2FIvA8JvbKrGotPl+G67CQrm3LMZ7oMuouoGcXHHA7/5ohGzzjtDxCCqfyjKUESsp8x/4MjNypwTvPMLHJbOy755N+zk0CQisTE0Bwx4As9AnmyBww0/+gFa5LilN6ahJrmc2ey2szfYJCUv8NlknRz0319aGfDcFUdpKKjURAAAh+QQJCgAAACwAAAAAZABkAAAE/xDISau9OM+EetJgKI5kOXUo8pls65pc2r10bUsyut58D+Yen3BIAaqIyEtiuRsZm6FUssRklp6kgnYLnSqrViewi0FstwgvCFwV58iWxPms1rDD0bFofobX73gaWD98W3V2gCKDGoVaaYcYgEt5b4SNfpCSlDKYco2PkJGagnoZZo2ha6NlpUqNBaCponeklRivBbKqiay2FqeFmLoAq7+tFa+xw1+8xr5Fr8shkmSLG8l1BQTK08UbQBfAfGoFBgPnArkk1OGcca/CFWYEBAXxAAgH5/sDCtwZ3r4F+fVpnTZ6COMp4McwwIiATi6JkIOwYr0Q5hjyM0BgIhsahf/+WThosSJGjRrTdWvzQpyWKCVjhliIUqMCYYFeeIK1hmRMhOoE6avJ8IBDaSN/xhQpLyPRjR2R4lO6tASBoU/3CWCqZh7VivZaBMjK7wDXJD6/bqOBQADZcwpkIVCL8CwJAk6JHpCV9mfQHgGw1pSlNiyRBDRR7k3Vt6JdGwXcajwaam7Mv2rw8qPM2KLhVAQCBMCcCtg9qahTq17NurXr17oShBZNu7Zt0QROw74Q+K1Go6tl3x5+OzcAAr6JRpUqnLhz2rnzJt+Yevbz68inK059vbtowdrPLZbqvbvk8Fqrl3deD/z0A6SHFVhPPFdk91kPqFQ9n37t+LtNhMv/J7oFaOCBCCao4IIMZlCAAgIosFxs2jw2BAIKHKChhnHpYp1oFt6QQAAGbGjihIf0Z5txXhAggIkwCiDLcyj68CCMOMqYSncAthQYjjh2GIp3a91AIpBA1phZeSy64CKSQHKWCX09MpIhlDFW6UVz5WlZwY9YbsgRagjQ16QGV4W5YQAFkkNfPC+qqUCVZQpggABKmmCdlh/SiFGYeP5gpwGEGiClCQEooKiieZ7QJ3F/ImmoCCQWWmiIyCyqqZcqOjcTkgE8Vo6lhTYKQqKaKnroLexFUaKJc5KgAKmlXkAAhAKEyluqi5pKAZe13VMmoYHeRWuhpLkowLIRcnMr9q+K6jZXbZi2kMCgtAq5AbPcHoohtBLCMIMalR6bLLfMajsBqtBWewgCxxK6qrLo6lgBYuCuKtWsx25la73LYvAstF4yFu+k/wKcAbup6rsMtqTaawG96DoIbrjVHdwoxdxqwHCqbSJxbbzqVsAxs4Jc7Osh5dJa5ckBo3mxu0KMeqzDEsAssRIX41wHv7QKIIzOIQzMa8FIwBvvykSH8PGiPk9hc8QiNA3Cg+AuM7WlBVt9atbLAF1oyQIDvLMp0K4sNcT+Vm32XQ2HLESZqurmdQjzKRDAmQve3WALfv+tJ8BkC74OwFEbjia6/ihOw63L6ipNBAAh+QQFCgAAACwAAAAAZABkAAAE/xDISau9OM/Ek/5gKI4k1XVlqq7qebJwLEsuOt94WHN571c7z29oQSgOAwOBFCwlEAghMYRADq6DwKg5gnoR05ACSy6IuNSvN/yxkgeC8+6sXrMzb/IShP7UvVJ3FQZ5Vwc6c3x/UIIYBIVXWh99GYtRjRiEkGAalBdPi5gYBZADCpOJGpaBohQCpWYZnhWWnK1FpXGyqZ+rtxgBpXufvEWWvxlub4cYszTHyI6lkhbOAL7RmaW2JsUb0NkXpJCn1d4S2GEFBNxisOY1F7VsBZpwsVSl5UAuvYuskxAUKNCukrIBBxQABAbJQDMb1cApGkgRn4YxhQ5Qa1NIVwpQdf+6VKxY0IK9QkpAPMozLMWfhUVGygTxqtQVARYvYMTisUXISTKDgghm01AAmBIIGDhgYOMKkEifBZVZktbBUgdahrs2NWjUCQhqFr2Z85fArjK/VlA6FouCqncSoKWq9kKAqw3hTjk7l2LdSjuL7mskt+9AvSQKiMXaii9axCoI4MXCDFPfSz8S3G3YGO1fGEYKaY07FXIOtlicNnJM8BeBAAHKYoK6tbbt27hz697NO1sCAsCDCx8u/HPvCgGWHljOvLlz5iltyyVOnXgBD5Kfa9+etfbv6uCHJxDAvXzzntHWhV9PoID598trs58P37zDrfPZH6m/fXD6/OHVw9//cwbIdgsCAFYHRgEKKMefAQoYaFaCw5l2nDy1hHLhhhx26OGHIIaYWAAKKDDabIcdmJwBLDb1SwGwwcYOYQEI0OKNJ6oT444EGLcCATbeeKN/d7y2444SysCgkEyid8eRULbXg2ZMVkkkG0ZCyaOFKgVZpZA5TpGlljz6WIFiX1apWlxjkhljkoClyWSEyPzmZpRcAqCUnDcKECYmCN55ZI8h7Mkni0fdto6gMRL6QYOHvqUKiQKYqKSMcMLIaGwgHIrTJAoIIKqof6o06CRtalkqAF42uSoANY46ap4UaHokZIG6ueqKTCYKgmKyjvrqBanCJsKiWsJ5TasQKjtB/6jBkioOiQoEUJKbzgIgV5R1IRCrpSMAGa2oZRFQ4rmSruXmZ7kGMOMUCUAbLZHxoluiat6SOew3ZqoQ67hlMWhvicS6SasgYY0rwJrmDnylZvrq9m+0JTU8sCPY4gbsuDlabG8Gd+Imb7BXJuVwyQDYCmW2RSr8qSMna1AsbP36EK/Ca07gMbqTRhzOxMEmufO5H8xsbTYbR5uzzjF3oms2QI+qUNFNa6DykSz7kDDHKlWtQcgvKowyBUMT/KubyCQtq7Nlj002mdGMLOrSa3nd86lpjzx1oXZbDWXNOHhbraN8OzyijICH07aIMSzOOAsCX/z4Uw7TPTnV9h58+QgEMN6reQ4RAAA7) 50% 50% no-repeat
            }

            #wheelofpatience, .logotype-wheel {
                z-index: 0;
                background-size: 0;
                background-position: 50% 50%
            }

            #Desktop #wheelofpatience, #Desktop .logotype-wheel {
                background-size: 3% auto
            }

            @media  all and (orientation: portrait) {
                #Mobile #wheelofpatience, #Mobile .logotype-wheel {
                    background-size: auto 4%
                }
            }

            @media  all and (orientation: landscape) {
                #Mobile #wheelofpatience, #Mobile .logotype-wheel {
                    background-size: 4% auto
                }
            }

            .scale-holder, .scale-holder * {
                margin: 0;
                padding: 0
            }

            .scale-holder {
                z-index: 2;
                -webkit-touch-callout: none;
                -webkit-user-select: none;
                -khtml-user-select: none;
                -moz-user-select: none;
                -ms-user-select: none;
                user-select: none
            }

            .scale-root {
                position: absolute;
                top: 0;
                left: 0;
                height: 999px;
                -moz-transform-origin: 0 0;
                -o-transform-origin: 0 0;
                -webkit-transform-origin: 0 0;
                -ms-transform-origin: 0 0;
                transform-origin: 0 0
            }

            #pauseindicator {
                z-index: 5
            }

            canvas.paused {
                opacity: .25
            }

            .pause-content {
                position: absolute;
                z-index: 6;
                width: 375px;
                height: 30px;
                top: 50%;
                left: 50%;
                margin-top: -15px;
                margin-left: -187.5px;
                overflow: visible
            }

            .pause-wheel {
                width: 30px;
                height: 30px;
                background-size: auto 100%;
                margin: 0 auto
            }

            .progress-bar, .progress-value {
                height: 9px;
                border-radius: 9px
            }

            .progress-bar {
                position: relative;
                top: 30px;
                background: #505050
            }

            .progress-value {
                width: 0;
                background: #e38a21
            }

            #PauseRoot, #pauseindicator, #progressbar {
                display: none
            }

            .logoOn canvas {
                opacity: 0
            }

            .logoOn .logotype {
                z-index: 100;
                background: 50% 50% no-repeat
            }

            .logoOn .logotype-wheel {
                display: inline;
                z-index: 101;
                background-image: url(data:image/gif;base64,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);
                background-position: 50% 90%
            }

            #DeferredLoadingText {
                position: relative;
                top: 60px;
                font-family: Tahoma,sans-serif;
                font-size: 18px;
                color: #fff;
                text-align: center
            }
        </style>
        <script>
            if (window.location.href.indexOf("replayGame.do") != -1)
                document.title = "Pragmatic Replay";
            var gaQueue = []
              , ga = function() {
                gaQueue.push(arguments)
            };
            var ga4Queue = []
              , gtag = function() {
                ga4Queue.push(arguments)
            };

            var URLGameSymbol = "_unknown_game_symbol_from_url_";
            var LoadingStep = 0;

            var UHT_SEND_ERRORS = true;
            var UHT_HAD_ERRORS = false;

            window.onerror = function(messageOrEvent, source, lineno, colno) {

                if (!UHT_SEND_ERRORS)
                    return;

                UHT_HAD_ERRORS = true;

                var args = null;

                if (messageOrEvent instanceof Event)
                    args = [messageOrEvent["message"], messageOrEvent["fileName"], messageOrEvent["lineNumber"], messageOrEvent["columnNumber"]];
                else
                    args = [messageOrEvent, source, lineno, colno];

                args[1] = String(args[1]).split("?").shift().split("/").pop();

                var msg = args[0] + " at " + args[1] + ":" + args[2] + ":" + args[3];
                ga('BehaviourTracker.send', 'event', "uht_errors", msg, URLGameSymbol, 1);

                window.onerror = null;
            }
            ;

            window.onbeforeunload = function() {
                var step = LoadingStep.toString() + (LoadingStep + 1).toString();
                var lastStep = LoadingStep.toString();

                if (LoadingStep == 4) {
                    step = "PLAYING";
                    lastStep = "PLAYING"
                }

                ga('LoadingTracker.send', 'event', "uht_loading", "_CLOSED_error_" + step, URLGameSymbol, UHT_HAD_ERRORS ? 1 : 0);

                if (LoadingStep > 1)
                    globalTracking.StopTimerAndSend("uht_loading", "_CLOSED_at_" + lastStep, "LoadingTracker");
                else if (GA_timer_load_start != undefined)
                    ga('LoadingTracker.send', 'timer', "uht_loading", "_CLOSED_at_1", URLGameSymbol, new Date().getTime() - GA_timer_load_start);

                UHT_SEND_ERRORS = false;

                if (SendTrackingIfQueued != undefined) {
                    SendTrackingIfQueued();
                    SendTrackingIfQueued();
                    SendTrackingIfQueued();
                    SendTrackingIfQueued();
                }

                return;
            }

            var game_symbol_from_url = (function() {
                var params = [];
                var urlSplitted = location.href.split("?");
                if (urlSplitted.length > 1) {
                    var paramsSplitted = urlSplitted[1].split("&");
                    for (var i = 0; i < paramsSplitted.length; ++i) {
                        var paramSplitted = paramsSplitted[i].split("=");
                        params[paramSplitted[0]] = (paramSplitted.length > 1) ? paramSplitted[1] : null;
                    }
                }
                return params["symbol"];
            }
            )();

            var game_symbol_from_url_value = game_symbol_from_url;

            if (game_symbol_from_url_value != undefined)
                URLGameSymbol = game_symbol_from_url_value;

            var gaMapping = {};
            function ga4_init(ua_id, ua_params, ga4_id) {
                ga('create', ua_id, ua_params);
                var sampleRate = ua_params.sampleRate;
                if (ua_params.name != "WasabiTracker")
                    sampleRate /= 10;
                if (Math.random() <= (sampleRate / 100)) {
                    gtag('config', ga4_id, {
                        send_page_view: false
                    });
                    gaMapping[ua_params.name] = ga4_id;
                }
            }

            ga4_init('UA-83294317-2', {
                'siteSpeedSampleRate': 10,
                'sampleRate': 5,
                name: "RatingTracker"
            }, 'G-X4NZ7202MD');
            ga4_init('UA-83294317-3', {
                'siteSpeedSampleRate': 10,
                'sampleRate': 1,
                name: "LoadingTracker"
            }, 'G-18F57V2EP0');
            ga4_init('UA-83294317-4', {
                'siteSpeedSampleRate': 10,
                'sampleRate': 1,
                name: "SpinTracker"
            }, 'G-R0S1TGV01J');
            ga4_init('UA-83294317-5', {
                'siteSpeedSampleRate': 10,
                'sampleRate': 100,
                name: "ServerErrorsTracker"
            }, 'G-6G1F81S55L');
            ga4_init('UA-83294317-6', {
                'siteSpeedSampleRate': 10,
                'sampleRate': 5,
                name: "BehaviourTracker"
            }, 'G-ZLLL6ZSBLR');

            ga('LoadingTracker.send', 'event', "uht_loading", "_0_game_icon_clicked", URLGameSymbol, 1);
            window["gtag"]("event", "_0_game_icon_clicked", {
                'send_to': window['gaMapping']['LoadingTracker'],
                'event_category': "uht_loading",
                'event_label': URLGameSymbol,
                'value': 1
            });

            function sendGAQueued() {
                var item = gaQueue.shift();
                if (item != undefined)
                    ga.apply(window, item);

                if (gaQueue.length > 0)
                    setTimeout(sendGAQueued, 1500);
            }

            !function(r, d) {
                function i(i) {
                    for (var e = {}, o = 0; o < i.length; o++)
                        e[i[o].toUpperCase()] = i[o];
                    return e
                }
                function n(i, e) {
                    return typeof i == w && -1 !== I(e).indexOf(I(i))
                }
                function t(i, e) {
                    if (typeof i == w)
                        return i = i.replace(/^\s\s*/, "").replace(/\s\s*$/, ""),
                        typeof e == b ? i : i.substring(0, 255)
                }
                function s(i, e) {
                    for (var o, a, r, n, t, s = 0; s < e.length && !n; ) {
                        for (var b = e[s], w = e[s + 1], l = o = 0; l < b.length && !n; )
                            if (n = b[l++].exec(i))
                                for (a = 0; a < w.length; a++)
                                    t = n[++o],
                                    typeof (r = w[a]) == c && 0 < r.length ? 2 === r.length ? typeof r[1] == u ? this[r[0]] = r[1].call(this, t) : this[r[0]] = r[1] : 3 === r.length ? typeof r[1] != u || r[1].exec && r[1].test ? this[r[0]] = t ? t.replace(r[1], r[2]) : d : this[r[0]] = t ? r[1].call(this, t, r[2]) : d : 4 === r.length && (this[r[0]] = t ? r[3].call(this, t.replace(r[1], r[2])) : d) : this[r] = t || d;
                        s += 2
                    }
                }
                function e(i, e) {
                    for (var o in e)
                        if (typeof e[o] == c && 0 < e[o].length)
                            for (var a = 0; a < e[o].length; a++) {
                                if (n(e[o][a], i))
                                    return "?" === o ? d : o
                            }
                        else if (n(e[o], i))
                            return "?" === o ? d : o;
                    return i
                }
                var u = "function"
                  , b = "undefined"
                  , c = "object"
                  , w = "string"
                  , l = "model"
                  , p = "name"
                  , m = "type"
                  , f = "vendor"
                  , h = "version"
                  , g = "architecture"
                  , o = "console"
                  , a = "mobile"
                  , v = "tablet"
                  , x = "smarttv"
                  , k = "wearable"
                  , y = "embedded"
                  , _ = "Amazon"
                  , S = "Apple"
                  , T = "ASUS"
                  , q = "BlackBerry"
                  , z = "Browser"
                  , N = "Chrome"
                  , A = "Firefox"
                  , C = "Google"
                  , E = "Huawei"
                  , O = "LG"
                  , U = "Microsoft"
                  , j = "Motorola"
                  , R = "Opera"
                  , M = "Samsung"
                  , P = "Sony"
                  , V = "Xiaomi"
                  , B = "Zebra"
                  , D = "Facebook"
                  , I = function(i) {
                    return i.toLowerCase()
                }
                  , W = {
                    ME: "4.90",
                    "NT 3.11": "NT3.51",
                    "NT 4.0": "NT4.0",
                    2E3: "NT 5.0",
                    XP: ["NT 5.1", "NT 5.2"],
                    Vista: "NT 6.0",
                    7: "NT 6.1",
                    8: "NT 6.2",
                    "8.1": "NT 6.3",
                    10: ["NT 6.4", "NT 10.0"],
                    RT: "ARM"
                }
                  , F = {
                    browser: [[/\b(?:crmo|crios)\/([\w\.]+)/i], [h, [p, "Chrome"]], [/edg(?:e|ios|a)?\/([\w\.]+)/i], [h, [p, "Edge"]], [/(opera mini)\/([-\w\.]+)/i, /(opera [mobiletab]{3,6})\b.+version\/([-\w\.]+)/i, /(opera)(?:.+version\/|[\/ ]+)([\w\.]+)/i], [p, h], [/opios[\/ ]+([\w\.]+)/i], [h, [p, R + " Mini"]], [/\bopr\/([\w\.]+)/i], [h, [p, R]], [/(kindle)\/([\w\.]+)/i, /(lunascape|maxthon|netfront|jasmine|blazer)[\/ ]?([\w\.]*)/i, /(avant |iemobile|slim)(?:browser)?[\/ ]?([\w\.]*)/i, /(ba?idubrowser)[\/ ]?([\w\.]+)/i, /(?:ms|\()(ie) ([\w\.]+)/i, /(flock|rockmelt|midori|epiphany|silk|skyfire|ovibrowser|bolt|iron|vivaldi|iridium|phantomjs|bowser|quark|qupzilla|falkon|rekonq|puffin|brave|whale|qqbrowserlite|qq)\/([-\w\.]+)/i, /(weibo)__([\d\.]+)/i], [p, h], [/(?:\buc? ?browser|(?:juc.+)ucweb)[\/ ]?([\w\.]+)/i], [h, [p, "UC" + z]], [/\bqbcore\/([\w\.]+)/i], [h, [p, "WeChat(Win) Desktop"]], [/micromessenger\/([\w\.]+)/i], [h, [p, "WeChat"]], [/konqueror\/([\w\.]+)/i], [h, [p, "Konqueror"]], [/trident.+rv[: ]([\w\.]{1,9})\b.+like gecko/i], [h, [p, "IE"]], [/yabrowser\/([\w\.]+)/i], [h, [p, "Yandex"]], [/(avast|avg)\/([\w\.]+)/i], [[p, /(.+)/, "$1 Secure " + z], h], [/\bfocus\/([\w\.]+)/i], [h, [p, A + " Focus"]], [/\bopt\/([\w\.]+)/i], [h, [p, R + " Touch"]], [/coc_coc\w+\/([\w\.]+)/i], [h, [p, "Coc Coc"]], [/dolfin\/([\w\.]+)/i], [h, [p, "Dolphin"]], [/coast\/([\w\.]+)/i], [h, [p, R + " Coast"]], [/miuibrowser\/([\w\.]+)/i], [h, [p, "MIUI " + z]], [/fxios\/([-\w\.]+)/i], [h, [p, A]], [/\bqihu|(qi?ho?o?|360)browser/i], [[p, "360 " + z]], [/(oculus|samsung|sailfish)browser\/([\w\.]+)/i], [[p, /(.+)/, "$1 " + z], h], [/(comodo_dragon)\/([\w\.]+)/i], [[p, /_/g, " "], h], [/(electron)\/([\w\.]+) safari/i, /(tesla)(?: qtcarbrowser|\/(20\d\d\.[-\w\.]+))/i, /m?(qqbrowser|baiduboxapp|2345Explorer)[\/ ]?([\w\.]+)/i], [p, h], [/(metasr)[\/ ]?([\w\.]+)/i, /(lbbrowser)/i], [p], [/((?:fban\/fbios|fb_iab\/fb4a)(?!.+fbav)|;fbav\/([\w\.]+);)/i], [[p, D], h], [/safari (line)\/([\w\.]+)/i, /\b(line)\/([\w\.]+)\/iab/i, /(chromium|instagram)[\/ ]([-\w\.]+)/i], [p, h], [/\bgsa\/([\w\.]+) .*safari\//i], [h, [p, "GSA"]], [/headlesschrome(?:\/([\w\.]+)| )/i], [h, [p, N + " Headless"]], [/ wv\).+(chrome)\/([\w\.]+)/i], [[p, N + " WebView"], h], [/droid.+ version\/([\w\.]+)\b.+(?:mobile safari|safari)/i], [h, [p, "Android " + z]], [/(chrome|omniweb|arora|[tizenoka]{5} ?browser)\/v?([\w\.]+)/i], [p, h], [/version\/([\w\.]+) .*mobile\/\w+ (safari)/i], [h, [p, "Mobile Safari"]], [/version\/([\w\.]+) .*(mobile ?safari|safari)/i], [h, p], [/webkit.+?(mobile ?safari|safari)(\/[\w\.]+)/i], [p, [h, e, {
                        "1.0": "/8",
                        "1.2": "/1",
                        "1.3": "/3",
                        "2.0": "/412",
                        "2.0.2": "/416",
                        "2.0.3": "/417",
                        "2.0.4": "/419",
                        "?": "/"
                    }]], [/(webkit|khtml)\/([\w\.]+)/i], [p, h], [/(navigator|netscape\d?)\/([-\w\.]+)/i], [[p, "Netscape"], h], [/mobile vr; rv:([\w\.]+)\).+firefox/i], [h, [p, A + " Reality"]], [/ekiohf.+(flow)\/([\w\.]+)/i, /(swiftfox)/i, /(icedragon|iceweasel|camino|chimera|fennec|maemo browser|minimo|conkeror|klar)[\/ ]?([\w\.\+]+)/i, /(seamonkey|k-meleon|icecat|iceape|firebird|phoenix|palemoon|basilisk|waterfox)\/([-\w\.]+)$/i, /(firefox)\/([\w\.]+)/i, /(mozilla)\/([\w\.]+) .+rv\:.+gecko\/\d+/i, /(polaris|lynx|dillo|icab|doris|amaya|w3m|netsurf|sleipnir|obigo|mosaic|(?:go|ice|up)[\. ]?browser)[-\/ ]?v?([\w\.]+)/i, /(links) \(([\w\.]+)/i], [p, h]],
                    cpu: [[/(?:(amd|x(?:(?:86|64)[-_])?|wow|win)64)[;\)]/i], [[g, "amd64"]], [/(ia32(?=;))/i], [[g, I]], [/((?:i[346]|x)86)[;\)]/i], [[g, "ia32"]], [/\b(aarch64|arm(v?8e?l?|_?64))\b/i], [[g, "arm64"]], [/\b(arm(?:v[67])?ht?n?[fl]p?)\b/i], [[g, "armhf"]], [/windows (ce|mobile); ppc;/i], [[g, "arm"]], [/((?:ppc|powerpc)(?:64)?)(?: mac|;|\))/i], [[g, /ower/, "", I]], [/(sun4\w)[;\)]/i], [[g, "sparc"]], [/((?:avr32|ia64(?=;))|68k(?=\))|\barm(?=v(?:[1-7]|[5-7]1)l?|;|eabi)|(?=atmel )avr|(?:irix|mips|sparc)(?:64)?\b|pa-risc)/i], [[g, I]]],
                    device: [[/\b(sch-i[89]0\d|shw-m380s|sm-[pt]\w{2,4}|gt-[pn]\d{2,4}|sgh-t8[56]9|nexus 10)/i], [l, [f, M], [m, v]], [/\b((?:s[cgp]h|gt|sm)-\w+|galaxy nexus)/i, /samsung[- ]([-\w]+)/i, /sec-(sgh\w+)/i], [l, [f, M], [m, a]], [/\((ip(?:hone|od)[\w ]*);/i], [l, [f, S], [m, a]], [/\((ipad);[-\w\),; ]+apple/i, /applecoremedia\/[\w\.]+ \((ipad)/i, /\b(ipad)\d\d?,\d\d?[;\]].+ios/i], [l, [f, S], [m, v]], [/\b((?:ag[rs][23]?|bah2?|sht?|btv)-a?[lw]\d{2})\b(?!.+d\/s)/i], [l, [f, E], [m, v]], [/(?:huawei|honor)([-\w ]+)[;\)]/i, /\b(nexus 6p|\w{2,4}-[atu]?[ln][01259x][012359][an]?)\b(?!.+d\/s)/i], [l, [f, E], [m, a]], [/\b(poco[\w ]+)(?: bui|\))/i, /\b; (\w+) build\/hm\1/i, /\b(hm[-_ ]?note?[_ ]?(?:\d\w)?) bui/i, /\b(redmi[\-_ ]?(?:note|k)?[\w_ ]+)(?: bui|\))/i, /\b(mi[-_ ]?(?:a\d|one|one[_ ]plus|note lte|max)?[_ ]?(?:\d?\w?)[_ ]?(?:plus|se|lite)?)(?: bui|\))/i], [[l, /_/g, " "], [f, V], [m, a]], [/\b(mi[-_ ]?(?:pad)(?:[\w_ ]+))(?: bui|\))/i], [[l, /_/g, " "], [f, V], [m, v]], [/; (\w+) bui.+ oppo/i, /\b(cph[12]\d{3}|p(?:af|c[al]|d\w|e[ar])[mt]\d0|x9007|a101op)\b/i], [l, [f, "OPPO"], [m, a]], [/vivo (\w+)(?: bui|\))/i, /\b(v[12]\d{3}\w?[at])(?: bui|;)/i], [l, [f, "Vivo"], [m, a]], [/\b(rmx[12]\d{3})(?: bui|;|\))/i], [l, [f, "Realme"], [m, a]], [/\b(milestone|droid(?:[2-4x]| (?:bionic|x2|pro|razr))?:?( 4g)?)\b[\w ]+build\//i, /\bmot(?:orola)?[- ](\w*)/i, /((?:moto[\w\(\) ]+|xt\d{3,4}|nexus 6)(?= bui|\)))/i], [l, [f, j], [m, a]], [/\b(mz60\d|xoom[2 ]{0,2}) build\//i], [l, [f, j], [m, v]], [/((?=lg)?[vl]k\-?\d{3}) bui| 3\.[-\w; ]{10}lg?-([06cv9]{3,4})/i], [l, [f, O], [m, v]], [/(lm(?:-?f100[nv]?|-[\w\.]+)(?= bui|\))|nexus [45])/i, /\blg[-e;\/ ]+((?!browser|netcast|android tv)\w+)/i, /\blg-?([\d\w]+) bui/i], [l, [f, O], [m, a]], [/(ideatab[-\w ]+)/i, /lenovo ?(s[56]000[-\w]+|tab(?:[\w ]+)|yt[-\d\w]{6}|tb[-\d\w]{6})/i], [l, [f, "Lenovo"], [m, v]], [/(?:maemo|nokia).*(n900|lumia \d+)/i, /nokia[-_ ]?([-\w\.]*)/i], [[l, /_/g, " "], [f, "Nokia"], [m, a]], [/(pixel c)\b/i], [l, [f, C], [m, v]], [/droid.+; (pixel[\daxl ]{0,6})(?: bui|\))/i], [l, [f, C], [m, a]], [/droid.+ ([c-g]\d{4}|so[-gl]\w+|xq-a\w[4-7][12])(?= bui|\).+chrome\/(?![1-6]{0,1}\d\.))/i], [l, [f, P], [m, a]], [/sony tablet [ps]/i, /\b(?:sony)?sgp\w+(?: bui|\))/i], [[l, "Xperia Tablet"], [f, P], [m, v]], [/ (kb2005|in20[12]5|be20[12][59])\b/i, /(?:one)?(?:plus)? (a\d0\d\d)(?: b|\))/i], [l, [f, "OnePlus"], [m, a]], [/(alexa)webm/i, /(kf[a-z]{2}wi)( bui|\))/i, /(kf[a-z]+)( bui|\)).+silk\//i], [l, [f, _], [m, v]], [/((?:sd|kf)[0349hijorstuw]+)( bui|\)).+silk\//i], [[l, /(.+)/g, "Fire Phone $1"], [f, _], [m, a]], [/(playbook);[-\w\),; ]+(rim)/i], [l, f, [m, v]], [/\b((?:bb[a-f]|st[hv])100-\d)/i, /\(bb10; (\w+)/i], [l, [f, q], [m, a]], [/(?:\b|asus_)(transfo[prime ]{4,10} \w+|eeepc|slider \w+|nexus 7|padfone|p00[cj])/i], [l, [f, T], [m, v]], [/ (z[bes]6[027][012][km][ls]|zenfone \d\w?)\b/i], [l, [f, T], [m, a]], [/(nexus 9)/i], [l, [f, "HTC"], [m, v]], [/(htc)[-;_ ]{1,2}([\w ]+(?=\)| bui)|\w+)/i, /(zte)[- ]([\w ]+?)(?: bui|\/|\))/i, /(alcatel|geeksphone|nexian|panasonic|sony)[-_ ]?([-\w]*)/i], [f, [l, /_/g, " "], [m, a]], [/droid.+; ([ab][1-7]-?[0178a]\d\d?)/i], [l, [f, "Acer"], [m, v]], [/droid.+; (m[1-5] note) bui/i, /\bmz-([-\w]{2,})/i], [l, [f, "Meizu"], [m, a]], [/\b(sh-?[altvz]?\d\d[a-ekm]?)/i], [l, [f, "Sharp"], [m, a]], [/(blackberry|benq|palm(?=\-)|sonyericsson|acer|asus|dell|meizu|motorola|polytron)[-_ ]?([-\w]*)/i, /(hp) ([\w ]+\w)/i, /(asus)-?(\w+)/i, /(microsoft); (lumia[\w ]+)/i, /(lenovo)[-_ ]?([-\w]+)/i, /(jolla)/i, /(oppo) ?([\w ]+) bui/i], [f, l, [m, a]], [/(archos) (gamepad2?)/i, /(hp).+(touchpad(?!.+tablet)|tablet)/i, /(kindle)\/([\w\.]+)/i, /(nook)[\w ]+build\/(\w+)/i, /(dell) (strea[kpr\d ]*[\dko])/i, /(le[- ]+pan)[- ]+(\w{1,9}) bui/i, /(trinity)[- ]*(t\d{3}) bui/i, /(gigaset)[- ]+(q\w{1,9}) bui/i, /(vodafone) ([\w ]+)(?:\)| bui)/i], [f, l, [m, v]], [/(surface duo)/i], [l, [f, U], [m, v]], [/droid [\d\.]+; (fp\du?)(?: b|\))/i], [l, [f, "Fairphone"], [m, a]], [/(u304aa)/i], [l, [f, "AT&T"], [m, a]], [/\bsie-(\w*)/i], [l, [f, "Siemens"], [m, a]], [/\b(rct\w+) b/i], [l, [f, "RCA"], [m, v]], [/\b(venue[\d ]{2,7}) b/i], [l, [f, "Dell"], [m, v]], [/\b(q(?:mv|ta)\w+) b/i], [l, [f, "Verizon"], [m, v]], [/\b(?:barnes[& ]+noble |bn[rt])([\w\+ ]*) b/i], [l, [f, "Barnes & Noble"], [m, v]], [/\b(tm\d{3}\w+) b/i], [l, [f, "NuVision"], [m, v]], [/\b(k88) b/i], [l, [f, "ZTE"], [m, v]], [/\b(nx\d{3}j) b/i], [l, [f, "ZTE"], [m, a]], [/\b(gen\d{3}) b.+49h/i], [l, [f, "Swiss"], [m, a]], [/\b(zur\d{3}) b/i], [l, [f, "Swiss"], [m, v]], [/\b((zeki)?tb.*\b) b/i], [l, [f, "Zeki"], [m, v]], [/\b([yr]\d{2}) b/i, /\b(dragon[- ]+touch |dt)(\w{5}) b/i], [[f, "Dragon Touch"], l, [m, v]], [/\b(ns-?\w{0,9}) b/i], [l, [f, "Insignia"], [m, v]], [/\b((nxa|next)-?\w{0,9}) b/i], [l, [f, "NextBook"], [m, v]], [/\b(xtreme\_)?(v(1[045]|2[015]|[3469]0|7[05])) b/i], [[f, "Voice"], l, [m, a]], [/\b(lvtel\-)?(v1[12]) b/i], [[f, "LvTel"], l, [m, a]], [/\b(ph-1) /i], [l, [f, "Essential"], [m, a]], [/\b(v(100md|700na|7011|917g).*\b) b/i], [l, [f, "Envizen"], [m, v]], [/\b(trio[-\w\. ]+) b/i], [l, [f, "MachSpeed"], [m, v]], [/\btu_(1491) b/i], [l, [f, "Rotor"], [m, v]], [/(shield[\w ]+) b/i], [l, [f, "Nvidia"], [m, v]], [/(sprint) (\w+)/i], [f, l, [m, a]], [/(kin\.[onetw]{3})/i], [[l, /\./g, " "], [f, U], [m, a]], [/droid.+; (cc6666?|et5[16]|mc[239][23]x?|vc8[03]x?)\)/i], [l, [f, B], [m, v]], [/droid.+; (ec30|ps20|tc[2-8]\d[kx])\)/i], [l, [f, B], [m, a]], [/(ouya)/i, /(nintendo) ([wids3utch]+)/i], [f, l, [m, o]], [/droid.+; (shield) bui/i], [l, [f, "Nvidia"], [m, o]], [/(playstation [345portablevi]+)/i], [l, [f, P], [m, o]], [/\b(xbox(?: one)?(?!; xbox))[\); ]/i], [l, [f, U], [m, o]], [/smart-tv.+(samsung)/i], [f, [m, x]], [/hbbtv.+maple;(\d+)/i], [[l, /^/, "SmartTV"], [f, M], [m, x]], [/(nux; netcast.+smarttv|lg (netcast\.tv-201\d|android tv))/i], [[f, O], [m, x]], [/(apple) ?tv/i], [f, [l, S + " TV"], [m, x]], [/crkey/i], [[l, N + "cast"], [f, C], [m, x]], [/droid.+aft(\w)( bui|\))/i], [l, [f, _], [m, x]], [/\(dtv[\);].+(aquos)/i], [l, [f, "Sharp"], [m, x]], [/\b(roku)[\dx]*[\)\/]((?:dvp-)?[\d\.]*)/i, /hbbtv\/\d+\.\d+\.\d+ +\([\w ]*; *(\w[^;]*);([^;]*)/i], [[f, t], [l, t], [m, x]], [/\b(android tv|smart[- ]?tv|opera tv|tv; rv:)\b/i], [[m, x]], [/((pebble))app/i], [f, l, [m, k]], [/droid.+; (glass) \d/i], [l, [f, C], [m, k]], [/droid.+; (wt63?0{2,3})\)/i], [l, [f, B], [m, k]], [/(quest( 2)?)/i], [l, [f, D], [m, k]], [/(tesla)(?: qtcarbrowser|\/[-\w\.]+)/i], [f, [m, y]], [/droid .+?; ([^;]+?)(?: bui|\) applew).+? mobile safari/i], [l, [m, a]], [/droid .+?; ([^;]+?)(?: bui|\) applew).+?(?! mobile) safari/i], [l, [m, v]], [/\b((tablet|tab)[;\/]|focus\/\d(?!.+mobile))/i], [[m, v]], [/(phone|mobile(?:[;\/]| safari)|pda(?=.+windows ce))/i], [[m, a]], [/(android[-\w\. ]{0,9});.+buil/i], [l, [f, "Generic"]]],
                    engine: [[/windows.+ edge\/([\w\.]+)/i], [h, [p, "EdgeHTML"]], [/webkit\/537\.36.+chrome\/(?!27)([\w\.]+)/i], [h, [p, "Blink"]], [/(presto)\/([\w\.]+)/i, /(webkit|trident|netfront|netsurf|amaya|lynx|w3m|goanna)\/([\w\.]+)/i, /ekioh(flow)\/([\w\.]+)/i, /(khtml|tasman|links)[\/ ]\(?([\w\.]+)/i, /(icab)[\/ ]([23]\.[\d\.]+)/i], [p, h], [/rv\:([\w\.]{1,9})\b.+(gecko)/i], [h, p]],
                    os: [[/microsoft (windows) (vista|xp)/i], [p, h], [/(windows) nt 6\.2; (arm)/i, /(windows (?:phone(?: os)?|mobile))[\/ ]?([\d\.\w ]*)/i, /(windows)[\/ ]?([ntce\d\. ]+\w)(?!.+xbox)/i], [p, [h, e, W]], [/(win(?=3|9|n)|win 9x )([nt\d\.]+)/i], [[p, "Windows"], [h, e, W]], [/ip[honead]{2,4}\b(?:.*os ([\w]+) like mac|; opera)/i, /cfnetwork\/.+darwin/i], [[h, /_/g, "."], [p, "iOS"]], [/(mac os x) ?([\w\. ]*)/i, /(macintosh|mac_powerpc\b)(?!.+haiku)/i], [[p, "Mac OS"], [h, /_/g, "."]], [/droid ([\w\.]+)\b.+(android[- ]x86)/i], [h, p], [/(android|webos|qnx|bada|rim tablet os|maemo|meego|sailfish)[-\/ ]?([\w\.]*)/i, /(blackberry)\w*\/([\w\.]*)/i, /(tizen|kaios)[\/ ]([\w\.]+)/i, /\((series40);/i], [p, h], [/\(bb(10);/i], [h, [p, q]], [/(?:symbian ?os|symbos|s60(?=;)|series60)[-\/ ]?([\w\.]*)/i], [h, [p, "Symbian"]], [/mozilla\/[\d\.]+ \((?:mobile|tablet|tv|mobile; [\w ]+); rv:.+ gecko\/([\w\.]+)/i], [h, [p, A + " OS"]], [/web0s;.+rt(tv)/i, /\b(?:hp)?wos(?:browser)?\/([\w\.]+)/i], [h, [p, "webOS"]], [/crkey\/([\d\.]+)/i], [h, [p, N + "cast"]], [/(cros) [\w]+ ([\w\.]+\w)/i], [[p, "Chromium OS"], h], [/(nintendo|playstation) ([wids345portablevuch]+)/i, /(xbox); +xbox ([^\);]+)/i, /\b(joli|palm)\b ?(?:os)?\/?([\w\.]*)/i, /(mint)[\/\(\) ]?(\w*)/i, /(mageia|vectorlinux)[; ]/i, /([kxln]?ubuntu|debian|suse|opensuse|gentoo|arch(?= linux)|slackware|fedora|mandriva|centos|pclinuxos|red ?hat|zenwalk|linpus|raspbian|plan 9|minix|risc os|contiki|deepin|manjaro|elementary os|sabayon|linspire)(?: gnu\/linux)?(?: enterprise)?(?:[- ]linux)?(?:-gnu)?[-\/ ]?(?!chrom|package)([-\w\.]*)/i, /(hurd|linux) ?([\w\.]*)/i, /(gnu) ?([\w\.]*)/i, /\b([-frentopcghs]{0,5}bsd|dragonfly)[\/ ]?(?!amd|[ix346]{1,2}86)([\w\.]*)/i, /(haiku) (\w+)/i], [p, h], [/(sunos) ?([\w\.\d]*)/i], [[p, "Solaris"], h], [/((?:open)?solaris)[-\/ ]?([\w\.]*)/i, /(aix) ((\d)(?=\.|\)| )[\w\.])*/i, /\b(beos|os\/2|amigaos|morphos|openvms|fuchsia|hp-ux)/i, /(unix) ?([\w\.]*)/i], [p, h]]
                }
                  , G = function(i, e) {
                    if (typeof i == c && (e = i,
                    i = d),
                    !(this instanceof G))
                        return (new G(i,e)).getResult();
                    var o = i || (typeof r != b && r.navigator && r.navigator.userAgent ? r.navigator.userAgent : "")
                      , a = e ? function(i, e) {
                        var o, a = {};
                        for (o in i)
                            e[o] && e[o].length % 2 == 0 ? a[o] = e[o].concat(i[o]) : a[o] = i[o];
                        return a
                    }(F, e) : F;
                    return this.getBrowser = function() {
                        var i, e = {};
                        return e[p] = d,
                        e[h] = d,
                        s.call(e, o, a.browser),
                        e.major = typeof (i = e.version) == w ? i.replace(/[^\d\.]/g, "").split(".")[0] : d,
                        e
                    }
                    ,
                    this.getCPU = function() {
                        var i = {};
                        return i[g] = d,
                        s.call(i, o, a.cpu),
                        i
                    }
                    ,
                    this.getDevice = function() {
                        var i = {};
                        return i[f] = d,
                        i[l] = d,
                        i[m] = d,
                        s.call(i, o, a.device),
                        i
                    }
                    ,
                    this.getEngine = function() {
                        var i = {};
                        return i[p] = d,
                        i[h] = d,
                        s.call(i, o, a.engine),
                        i
                    }
                    ,
                    this.getOS = function() {
                        var i = {};
                        return i[p] = d,
                        i[h] = d,
                        s.call(i, o, a.os),
                        i
                    }
                    ,
                    this.getResult = function() {
                        return {
                            ua: this.getUA(),
                            browser: this.getBrowser(),
                            engine: this.getEngine(),
                            os: this.getOS(),
                            device: this.getDevice(),
                            cpu: this.getCPU()
                        }
                    }
                    ,
                    this.getUA = function() {
                        return o
                    }
                    ,
                    this.setUA = function(i) {
                        return o = typeof i == w && 255 < i.length ? t(i, 255) : i,
                        this
                    }
                    ,
                    this.setUA(o),
                    this
                };
                G.VERSION = "0.7.31",
                G.BROWSER = i([p, h, "major"]),
                G.CPU = i([g]),
                G.DEVICE = i([l, f, m, o, a, x, v, k, y]),
                G.ENGINE = G.OS = i([p, h]),
                typeof exports != b ? (typeof module != b && module.exports && (exports = module.exports = G),
                exports.UAParser2 = G) : typeof define == u && define.amd ? define(function() {
                    return G
                }) : typeof r != b && (r.UAParser2 = G);
                var L, Z = typeof r != b && (r.jQuery || r.Zepto);
                Z && !Z.ua && (L = new G,
                Z.ua = L.getResult(),
                Z.ua.get = function() {
                    return L.getUA()
                }
                ,
                Z.ua.set = function(i) {
                    L.setUA(i);
                    var e, o = L.getResult();
                    for (e in o)
                        Z.ua[e] = o[e]
                }
                )
            }("object" == typeof window ? window : this);
            var goog = {
                require: function() {},
                provide: function() {}
            };
            var UHT_ALL = false;
            var UHT_CONFIG = {
                GAME_URL: "",
                GAME_URL_ALTERNATIVE: "",
                LANGUAGE: "en",
                SYMBOL: "symbol",
                MINI_MODE: false,
                LOBBY_LAUNCHED: false
            };
            var UHT_DEVICE_TYPE = {
                MOBILE: false,
                DESKTOP: false
            };
            var UHT_FRAME = false;
            var UHT_LOW_END_DEVICE = false;
            var currentDatapathRetries = 0;
            var retriesBeforeAlternativeDatapath = 5;
            var LowEndDeviceIdentifiers = ["S III", "GT-I9300", "iPhone 5", "iPhone 5C", "iPhone 5S", "iPhone 6", "iPhone 6 Plus"];
            var UHTConsole = {};
            var UHT_UA_INFO = (new UAParser2).getResult();
            window.console = window.console || function() {
                var c = {};
                c.log = c.warn = c.debug = c.info = c.error = c.time = c.dir = c.profile = c.clear = c.exception = c.trace = c.assert = function() {}
                ;
                return c
            }();
            UHTConsole.Message = function(type, args) {
                this.type = type;
                this.args = args
            }
            ;
            UHTConsole.allowToWrite = false;
            UHTConsole.methods = ["log", "info", "warn", "error"];
            UHTConsole.source = {};
            UHTConsole.replacement = {};
            UHTConsole.messages = [];
            UHTConsole.wasAllowedToWrite = false;
            UHTConsole.redirectOutput = false;
            UHTConsole.logFilename = null;
            UHTConsole.GetReplacement = function(methodIdx) {
                return function() {
                    var stringARGS = [];
                    for (var i = 0; i < arguments.length; i++)
                        if (arguments[i] != null)
                            stringARGS.push(arguments[i].toString());
                    if (UHTConsole.redirectOutput) {
                        var args = [];
                        args.push(["g", UHT_CONFIG.SYMBOL].join("="));
                        args.push(["f", UHTConsole.logFilename].join("="));
                        args.push(["d", (new Date).getTime()].join("="));
                        args.push([UHTConsole.methods[methodIdx], stringARGS.join(",")].join("="));
                        (new Image).src = "http://localhost/console.php?" + args.join("&")
                    } else
                        UHTConsole.messages.push(new UHTConsole.Message(UHTConsole.methods[methodIdx],stringARGS));
                    if (UHTConsole.messages.length > 512)
                        UHTConsole.messages.splice(0, 128)
                }
            }
            ;
            UHTConsole.AllowToWrite = function(allowToWrite) {
                //allowToWrite = true;
                if (UHTConsole.redirectOutput) {
                    UHTConsole.wasAllowedToWrite = allowToWrite;
                    return
                }
                for (var i = 0; i < UHTConsole.methods.length; ++i) {
                    var name = UHTConsole.methods[i];
                    if (UHTConsole.source[name] == null)
                        UHTConsole.source[name] = console[name];
                    if (!allowToWrite)
                        if (UHTConsole.replacement[name] == null)
                            UHTConsole.replacement[name] = UHTConsole.GetReplacement(i);
                    console[name] = allowToWrite ? UHTConsole.source[name] : UHTConsole.replacement[name]
                }
                if (allowToWrite && !UHTConsole.allowToWrite) {
                    for (var i = 0; i < UHTConsole.messages.length; ++i)
                        console[UHTConsole.messages[i].type](UHTConsole.messages[i].args[0]);
                    UHTConsole.messages = []
                }
                UHTConsole.allowToWrite = allowToWrite
            }
            ;
            UHTConsole.RedirectOutput = function(redirectOutput) {
                if (UHTConsole.redirectOutput == Boolean(redirectOutput))
                    return;
                if (redirectOutput) {
                    if (UHTConsole.logFilename == null)
                        UHTConsole.logFilename = UHTConsole.FormatDate(new Date);
                    UHTConsole.wasAllowedToWrite = UHTConsole.allowToWrite;
                    UHTConsole.AllowToWrite(false);
                    UHTConsole.redirectOutput = redirectOutput;
                    for (var i = 0; i < UHTConsole.messages.length; ++i)
                        console[UHTConsole.messages[i].type](UHTConsole.messages[i].args[0]);
                    UHTConsole.messages = []
                } else {
                    UHTConsole.redirectOutput = redirectOutput;
                    UHTConsole.AllowToWrite(UHTConsole.wasAllowedToWrite)
                }
            }
            ;
            UHTConsole.FormatDate = function(d) {
                var date = d.toJSON().split("T")[0];
                var time = d.toTimeString().split(" ")[0].replace(/:/g, "-");
                return [date, time].join("_")
            }
            ;
            var Loader = {};
            Loader.WURFLProcessed = false;
            Loader.statisticsURL = null;
            Loader.statistics = null;
            Loader.LoadScript = function(url, loadCallback, errorCallback) {
                var script = document.createElement("script");
                script.src = url;
                if (loadCallback != undefined)
                    script.onload = loadCallback;
                if (errorCallback != undefined) {
                    script.onabort = errorCallback;
                    script.onerror = errorCallback
                }
                document.getElementsByTagName("HEAD")[0].appendChild(script);
                return script
            }
            ;
            Loader.LoadWURFL = function() {
                //var wurflURL = location.protocol + "//device.pragmaticplay.net/wurfl.js";
                //if (location.hostname.indexOf("ppgames.net") != -1)
                //    wurflURL = location.protocol + "//device.ppgames.net/wurfl.js";
                //Loader.LoadScript(wurflURL, Loader.WURFLLoadHandler, Loader.WURFLErrorHandler);
                //setTimeout(Loader.WURFLErrorHandler, 2E3)

                var isMobile = /(Android|iPad|iPhone|mobile)/g.test(navigator.userAgent);
                if (isMobile) {
                    Loader.LoadScript("https://contents-kr1.static-ppgames.net/device.pragmaticplay.net/mobile/wurfl.js", Loader.WURFLLoadHandler, Loader.WURFLErrorHandler);
                } else {
                    Loader.LoadScript("https://contents-kr1.static-ppgames.net/device.pragmaticplay.net/desktop/wurfl.js", Loader.WURFLLoadHandler, Loader.WURFLErrorHandler);
                }
                setTimeout(Loader.WURFLErrorHandler, 5E3)
            }
            ;
            Loader.WURFLLoadHandler = function() {
                if (Loader.WURFLProcessed)
                    return;
                Loader.WURFLProcessed = true;
                var WURFL = window["WURFL"] || null;
                if (WURFL == null) {
                    setTimeout(Loader.WURFLLoadHandler, 10);
                    return
                }
                if (WURFL.complete_device_name != undefined)
                    for (var id in LowEndDeviceIdentifiers)
                        if (WURFL.complete_device_name.indexOf(LowEndDeviceIdentifiers[id]) >= 0) {
                            UHT_LOW_END_DEVICE = true;
                            break
                        }
                console.log("WURFL loaded");
                UHT_DEVICE_TYPE = {
                    MOBILE: WURFL.is_mobile,
                    DESKTOP: !WURFL.is_mobile
                };
                Loader.SetExtraInfo();
                Loader.SendStatistics(JSON.stringify(WURFL))
            }
            ;
            Loader.WURFLErrorHandler = function() {
                if (Loader.WURFLProcessed)
                    return;
                Loader.WURFLProcessed = true;
                console.log("WURFL not loaded use UAParser2");
                var device = UHT_UA_INFO.device;
                var mobile = device.type == "mobile" || device.type == "tablet";
                UHT_DEVICE_TYPE = {
                    MOBILE: mobile,
                    DESKTOP: !mobile
                };
                Loader.SetExtraInfo();
                Loader.SendStatistics(JSON.stringify({}))
            }
            ;
            Loader.SetExtraInfo = function() {
                var inFrame = false;
                try {
                    inFrame = window.top != window
                } catch (e) {
                    inFrame = true
                }
                UHT_FRAME = inFrame;
                var os = UHT_UA_INFO.os.name;
                var device = UHT_UA_INFO.device.model;
                if (device != undefined)
                    for (var id in LowEndDeviceIdentifiers)
                        if (device.indexOf(LowEndDeviceIdentifiers[id]) >= 0) {
                            UHT_LOW_END_DEVICE = true;
                            break
                        }
                var classNames = [document.documentElement.className || "", os, device, String(UHT_UA_INFO.browser.name).replace(/\s/g, ""), UHT_CONFIG.MINI_MODE ? "MiniMode" : "StandardMode"];
                classNames.push(inFrame ? "InFrame" : "MainWindow");
                document.documentElement.className = classNames.join(" ");
                document.documentElement.id = UHT_DEVICE_TYPE.MOBILE ? "Mobile" : "Desktop"
            }
            ;
            var PLATFORM_APPENDED = false;
            Loader.LoadGame = function() {
                if (!Loader.WURFLProcessed) {
                    setTimeout(Loader.LoadGame, 50);
                    return
                }
                if (UHT_ALL && !PLATFORM_APPENDED) {
                    UHT_CONFIG.GAME_URL += (UHT_CONFIG.MINI_MODE ? "mini" : UHT_DEVICE_TYPE.MOBILE ? "mobile" : "desktop") + "/";
                    UHT_CONFIG.GAME_URL_ALTERNATIVE += (UHT_CONFIG.MINI_MODE ? "mini" : UHT_DEVICE_TYPE.MOBILE ? "mobile" : "desktop") + "/";
                    PLATFORM_APPENDED = true
                }
                var script = Loader.LoadScript(UHT_CONFIG.GAME_URL + "bootstrap.js" + "?key=" + "1.0.4", Loader.LoadGameCallback, function() {
                    document.getElementsByTagName("HEAD")[0].removeChild(script);
                    currentDatapathRetries++;
                    if (currentDatapathRetries == retriesBeforeAlternativeDatapath) {
                        UHT_CONFIG.GAME_URL = UHT_CONFIG.GAME_URL_ALTERNATIVE;
                        window["ga"]("LoadingTracker.send", "event", "uht_loading", "_USED_ALTERNATIVE_DATA_PATH", window["URLGameSymbol"], 1)
                    }
                    setTimeout(Loader.LoadGame, 250)
                })
            }
            ;
            Loader.LoadGameCallback = function() {
                delete window.Loader;
                window.onload(null)
            }
            ;
            Loader.Listener = function(json) {
                console.info("Loader::Receive " + json);
                var params = JSON.parse(json);
                if (params["common"] == "EVT_GET_CONFIGURATION") {
                    delete window.sendToGame;
                    var args = params["args"];
                    if (typeof args["config"] == "string")
                        args["config"] = JSON.parse(args["config"]);
                    UHT_CONFIG.GAME_URL = args["config"]["datapath"];
                    UHT_CONFIG.GAME_URL_ALTERNATIVE = args["config"]["datapath_alternative"];
                    if (UHT_CONFIG.GAME_URL_ALTERNATIVE == undefined)
                        UHT_CONFIG.GAME_URL_ALTERNATIVE = args["config"]["datapath"];
                    UHT_CONFIG.STYLENAME = args["config"]["styleName"];
                    UHT_CONFIG.LANGUAGE = args["config"]["lang"];
                    var tmp = UHT_CONFIG.GAME_URL.split("/");
                    var pathParts = [];
                    for (var i = 0; i < tmp.length; ++i)
                        if (tmp[i].length > 0)
                            pathParts.push(tmp[i]);
                    var symbol = pathParts[pathParts.length - 1];
                    UHT_CONFIG.SYMBOL = symbol;
                    UHT_CONFIG.MINI_MODE = args["config"]["minimode"] == "1";
                    UHT_CONFIG.LOBBY_LAUNCHED = args["config"]["lobbyLaunched"] == true;
                    if (args["config"]["brandRequirements"] != null && args["config"]["brandRequirements"].indexOf("FORCEMOBILE") != -1) {
                        UHT_DEVICE_TYPE.MOBILE = true;
                        UHT_DEVICE_TYPE.DESKTOP = false;
                        UHT_CONFIG.MINI_MODE = false
                    }
                    var statURL = args["config"]["statisticsURL"];
                    if (statURL != undefined) {
                        Loader.statisticsURL = statURL + (/\?/.test(statURL) ? "&" : "?") + "mgckey=" + args["config"]["mgckey"];
                        if (Loader.statistics != null)
                            Loader.SendStatistics(Loader.statistics)
                    }
                    Loader.LoadGame();
                    UHTLogotype.LoadLogoInfo(args["config"]["styleName"])
                }
            }
            ;
            var GA_timer_load_start = (new Date).getTime();
            Loader.Start = function() {
                UHTConsole.AllowToWrite(false);
                var sendToAdapter = null;
                try {
                    sendToAdapter = window.parent["sendToAdapter"] || null
                } catch (e) {}
                if (sendToAdapter == null)
                    sendToAdapter = window["sendToAdapter"] || null;
                var online = sendToAdapter != null;
                console.info("Loader::loaded - online = " + String(online));
                if (online) {
                    window.sendToGame = Loader.Listener;
                    sendToAdapter(JSON.stringify({
                        common: "EVT_GET_CONFIGURATION",
                        type: "html5"
                    }))
                } else
                    Loader.LoadGame()
            }
            ;
            Loader.SendStatistics = function(params) {
                if (Loader.statisticsURL == null) {
                    Loader.statistics = params;
                    return
                }
                var xhr = new XMLHttpRequest;
                xhr.open("POST", Loader.statisticsURL + "&channel=" + (UHT_CONFIG.MINI_MODE ? "mini" : "") + (UHT_DEVICE_TYPE.MOBILE ? "mobile" : "desktop") + (UHT_CONFIG.LOBBY_LAUNCHED ? "_mini_lobby" : ""), true);
                xhr.setRequestHeader("Content-type", "application/json");
                xhr.send(params)
            }
            ;
            if (location.href.indexOf("WURFL_NOT_ALLOWED") > -1)
                Loader.WURFLErrorHandler();
            else
                setTimeout(Loader.LoadWURFL, 0);
            window.onload = Loader.Start;
            var UHTLogoIsVisible = true;
            var UHTLogotype = {};
            UHTLogotype.name = null;
            UHTLogotype.path = null;
            UHTLogotype.data = null;
            UHTLogotype.logoEl = null;
            UHTLogotype.logoImg = null;
            UHTLogotype.timer = -1;
            UHTLogotype.duration = 2E3;
            UHTLogotype.gameLoadingStarted = false;
            UHTLogotype.hideLogoTimeout = null;
            UHTLogotype.LoadLogoInfo = function(name) {
                if (UHT_CONFIG.GAME_URL.length == 0 || UHTLogotype == null)
                    return;
                var split = UHT_CONFIG.GAME_URL.split("/");
                split.splice(split.indexOf(UHT_CONFIG.SYMBOL) - 2);
                UHTLogotype.name = name;
              UHTLogotype.path = split.join("/") + "/public/vswayspowzeus/gs2c/common/v5/js/";
                var script = Loader.LoadScript(UHTLogotype.path + "logo_info.js", UHTLogotype.OnLogoInfoLoaded, function() {
                    document.getElementsByTagName("HEAD")[0].removeChild(script);
                    currentDatapathRetries++;
                    if (currentDatapathRetries == retriesBeforeAlternativeDatapath) {
                        UHT_CONFIG.GAME_URL = UHT_CONFIG.GAME_URL_ALTERNATIVE;
                        PLATFORM_APPENDED = false
                    }
                    setTimeout(UHTLogotype.LoadLogoInfo.bind(null, UHT_CONFIG.STYLENAME), 250)
                })
            }
            ;
            UHTLogotype.OnLogoInfoLoaded = function() {
                if (UHTLogotype == null)
                    return;
                var info = window["UHTLogotypeInfo"] || null;
                if (info != null)
                    UHTLogotype.data = info[UHTLogotype.name] || null;
                if (UHTLogotype.data != null) {
                    UHTLogotype.logoImg = new Image;
                    UHTLogotype.logoImg.onload = UHTLogotype.OnLogoLoaded;
                    UHTLogotype.logoImg.src = UHTLogotype.path + UHTLogotype.data["src"]
                } else {
                    UHTLogotype.UpdateStyle("logoOff", "logoOn");
                    UHTLogoIsVisible = false
                }
            }
            ;
            UHTLogotype.OnLogoLoaded = function() {
                var wheel = document.createElement("div");
                wheel.className = "logotype-wheel";
                var el = document.createElement("div");
                el.className = "logotype";
                el.style.backgroundColor = UHTLogotype.data["bg"];
                el.style.backgroundImage = "url('" + UHTLogotype.logoImg.src + "')";
                el.appendChild(wheel);
                document.body.appendChild(el);
                UHTLogotype.logoEl = el;
                UHTLogotype.UpdateStyle("logoOn", "logoOff");
                UHTLogotype.timer = (new Date).getTime();
                UHTLogoIsVisible = true;
                UHTLogotype.HandleResize();
                window.addEventListener("resize", UHTLogotype.HandleResize, false);
                if (UHTLogotype.gameLoadingStarted)
                    UHTLogotype.DelayHideLogo(UHTLogotype.duration)
            }
            ;
            UHTLogotype.HandleResize = function() {
                if (UHTLogotype.data == null)
                    return;
                var w = UHTLogotype.logoImg.width;
                var h = UHTLogotype.logoImg.height;
                var sw = "auto";
                var sh = "auto";
                var r1 = window.innerWidth / window.innerHeight;
                var r2 = w / h;
                if (UHTLogotype.data["fit"] == "shrink")
                    if (r2 < r1)
                        sh = "100%";
                    else
                        sw = "100%";
                else if (r2 < r1)
                    sw = "100%";
                else
                    sh = "100%";
                UHTLogotype.logoEl.style.backgroundSize = [sw, sh].join(" ")
            }
            ;
            UHTLogotype.GameLoadingStarted = function() {
                UHTLogotype.gameLoadingStarted = true;
                if (UHTLogotype.data == null) {
                    UHTLogotype.HideLogo();
                    return
                }
                if (UHTLogotype.timer > 0) {
                    var dt = UHTLogotype.duration - ((new Date).getTime() - UHTLogotype.timer);
                    if (dt <= 0)
                        UHTLogotype.HideLogo();
                    else
                        UHTLogotype.DelayHideLogo(dt)
                }
            }
            ;
            UHTLogotype.DelayHideLogo = function(delay) {
                if (UHTLogotype.hideLogoTimeout != null)
                    clearTimeout(UHTLogotype.hideLogoTimeout);
                UHTLogotype.hideLogoTimeout = setTimeout(UHTLogotype.HideLogo, delay)
            }
            ;
            UHTLogotype.HideLogo = function() {
                if (UHTLogotype.logoEl != null) {
                    document.body.removeChild(UHTLogotype.logoEl);
                    window.removeEventListener("resize", UHTLogotype.HandleResize, false)
                }
                UHTLogotype.UpdateStyle("logoOff", "logoOn");
                UHTLogotype = null;
                UHTLogoIsVisible = false
            }
            ;
            UHTLogotype.UpdateStyle = function(add, remove) {
                var split = (document.documentElement.className || "").split(" ");
                var cls = [];
                for (var i = 0; i < split.length; ++i)
                    if (split[i].length > 0 && split[i] != remove)
                        cls.push(split[i]);
                cls.push(add);
                document.documentElement.className = cls.join(" ")
            }
            ;
            UHT_ALL = true;
        </script>
        <script>
            var scaleLoadingBar = function() {
                if (document.getElementById("ScaleRootLoading").style.display == "none")
                    document.getElementById("ScaleRootLoading").style.display = "block;"
                var scaleRootHeight = 999;
                var zoom = 1;

                if (window.innerWidth > window.innerHeight) {
                    scaleRootHeight = 999;
                    zoom = window.innerHeight / 999;
                    if (zoom > 1.05)
                        zoom = 1.05;
                } else {
                    scaleRootHeight = 666;
                    zoom = window.innerWidth / 666;
                    if (zoom > 1.05)
                        zoom = 1.05;
                }

                var styles = ["-moz-transform: scale(VAL)", "-webkit-transform: scale(VAL)", "-ms-transform: scale(VAL)", "-o-transform: scale(VAL)", "transform: scale(VAL)"];

                if (UHT_DEVICE_TYPE.DESKTOP)
                    zoom = zoom > 1 ? 1 : zoom;

                for (var i = 0; i < styles.length; ++i) {
                    styles[i] = styles[i].replace("VAL", zoom);
                }

                styles.push("width:" + (window.innerWidth / zoom) + 'px');
                styles.push("margin-top:" + ((window.innerHeight - 999 * zoom) / 2) + 'px');

                document.getElementById("ScaleRootLoading").setAttribute("style", styles.join(";"));
            }
        </script>
        <style>
            #pplogo {
                top: 35%;
                margin: 0 auto;
                position: relative;
                width: 356px;
                height: 212px;
                background-size: contain;
                background-image: url('data:image/png;base64,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');
            }

            #ScaleRootLoading {
                z-index: 2;
                margin: auto;
            }

            .meter {
                height: 6px;
                position: relative;
                background: rgba(37, 37, 37, 1.00);
                border-radius: 6px;
                position: absolute;
                z-index: 6;
                width: 356px;
                top: 50%;
                left: 50%;
                margin-top: 60px;
                margin-left: -178px;
                overflow: visible;
            }

            .meter > span {
                display: block;
                height: 6px;
                border-top-right-radius: 6px;
                border-bottom-right-radius: 6px;
                border-top-left-radius: 6px;
                border-bottom-left-radius: 6px;
                background-image: linear-gradient( rgb(255, 212, 160) 10%, rgb(255, 164, 57), rgb(241, 151, 44), rgb(221, 132, 28), rgb(201, 115, 12), rgb(197, 116, 21) );
                box-shadow: 0px 0px 8px 8px rgba(255, 178, 86, 0.11);
                position: relative;
                overflow: hidden;
            }

            .loading-holder {
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                position: absolute;
                margin: 0;
                padding: 0;
            }

            .loading-root {
                position: absolute;
                top: 0;
                left: 0;
                height: 999px;
                -moz-transform-origin: 0 0;
                -o-transform-origin: 0 0;
                -webkit-transform-origin: 0 0;
                -ms-transform-origin: 0 0;
                transform-origin: 0 0;
            }
        </style>
       <script type="text/javascript" src="/public/vswayspowzeus/gs2c/common/js/html5-script-external.js"></script>
        <script type="text/javascript">
            Html5GameManager.init({
                contextPath: "/",
                cashierUrl: "",
                lobbyUrl: "js://window.close();",
                mobileCashierUrl: "",
                mobileLobbyUrl: "",
                gameConfig: '{"lobbyLaunched":false,"jurisdiction":"99","openHistoryInWindow":false,"RELOAD_JACKPOT":"\/gs2c\/jackpot\/reload.do","styleName":"golden","SETTINGS":"\/games\/SweetKingdomPM\/saveSettings.do","openHistoryInTab":false,"replaySystemUrl":"https:\/\/replay.pragmaticplay.eu.com","multiProductMiniLobby":false,"ingameLobbyApiURL":"\/gs2c\/minilobby\/games.json","environmentId":419,"historyType":"internal","vendor":"T","currency":"R$","lang":"en","datapath":"https://api.mmopragmatic.site/public/vswayspowzeus/gs2c/common/v5/games-html5/games/vs/vswayspowzeus/","amountType":"COIN","LOGOUT":"\/gs2c\/logout.do","REGULATION":"\/gs2c\/regulation\/process.do?symbol\\u003dvswaysmegahays","datapath_alternative":"https://api.mmopragmatic.site/public/vswayspowzeus/gs2c/common/v5/games-html5/games/vs/vswayspowzeus/","replaySystemContextPath":"\/ReplayService","sessionKey":["VC8hg9RNXC7HWDlDYQl7HtAGMLsPlAwoVA4ytczIT5TSynxyBzU1xjiit5G2TRqF3J7kVcb3Xs8dfBpKWIvlOw\\u003d\\u003d","r48\/4M0kDw0ZUpNowc\/CqA\\u003d\\u003d","nfsH8UsO1bqZi\/ohjw21\/vhe8of3PaMILcnMvetLLvBmQECrM3wWurrX2dPybcdhcgTMhK8rFVZVtvdxv+XFI7AvU0qXZlu7UDFXDKnWVs+7AScd0ZDYepj1I98l6RoI68veAS\/jr1jQmuGIUg\/RwTEaqWpNiBkRsC2MyW+Hqa1m\/\/VVDNJYrBwvtdoJdFX+MTEg3LriN0lxIaNG4iIWJYElwIEpcW\/Qg0uQF1DPYf93jWfV+\/0I8+p0GZO6\/Nrg+lcr5GpWury7dqlR46MtDg\\u003d\\u003d"],"showRealCash":"1","statisticsURL":"\/gs2c\/stats.do","accountType":"R","clock":"0","mgckey":"<?php echo $_GET['user']; ?>","gameService":"https://api.mmopragmatic.site/public/vswayspowzeus/gs2c/v3/gameService","gameVerificationURL":"\/gs2c\/session\/verify\/vswayspowzeus","RELOAD_BALANCE":"\/gs2c\/SweetKingdomPM\/reloadBalance.do","currencyOriginal":"USD","extend_events":"1","sessionTimeout":"30","CLOSE_GAME":"\/gs2c\/closeGame.do?symbol\\u003dvswayspowzeus","region":"Asia","sessionKeyV2":["jbiB0Ul8cCVfgJtaiVvXtOCEyAISsPWXfaiMQ7hA7YA8lNcmcKbtIG4rCzqPj3QQ5Yviw+EyCZcnhAMA80Ao6GDfIvLQN+vE4exoUdB\/ojDyT7OFeZhD8FGMit2Uoz3yhPf0PfZtFl3Y6rsbtwQNDke8jtPhWG6hHV0S5g5QOfc\\u003d","Ve+\/3hr3rhy+2g1zcD2DJt19d0lZWrPDqVUjwDgKjgvxE687aZVY8Ca4+4bAhiVhyO2VwJsTr7J3mn7P+RiRu\/qxh3SX342OrQabjlIjBSAl13h\/ik\/mcWvxP1BQM0as1Mq+ttaCGdej0DK3nB9fAOkchUqkIy6MqPWu\/cFrtng\\u003d"],"HISTORY":"\/gs2c\/lastGameHistory.do?symbol\\u003dvswayspowzeus\\u0026mgckey\\u003dAUTHTOKEN@UnlhM2Zyam1QcitrNzgxNTNWSTJ4UT09~stylename@golden~SESSION@db375e96-102f-4b36-abe0-f9ef4a784819~SN@3f756467"}',
                mgckey:"<?php echo $_GET['user']; ?>",
                jurisdictionMsg: "",
                extendSessionUrl: "",
                extendSessionInterval: null
            });
        </script>
    </head>
    <body class="CLIENT EXTERNAL HTML5">
        <div class="pageOverlap"></div>
        <div class="message-box browser-unsupported-message">
            <div class="message-title" style="color: #fff;">You are using an unsupported browser.</div>
            <div class="message-text" style="color: #fff;">Please use Google Chrome.</div>
        </div>
        <div class="scale-holder" id="PauseRoot">
            <div class="scale-root" id="ScaleRoot">
                <div id="pauseindicator">
                    <div class="pause-content">
                        <div class="pause-wheel"></div>
                        <div id="progressbar" class="progress-bar">
                            <div class="progress-value" id="progressvalue"></div>
                        </div>
                        <div id="DeferredLoadingText"></div>
                    </div>
                </div>
            </div>
        </div>
        <div id="wheelofpatience"></div><div id="ScaleRootLoading" style="display: none;"></div>
        <script>
            if(document.getElementById("#PauseRoot") !=undefined)
            {
                window.addEventListener("DOMContentLoaded", scaleLoadingBar, false);
                window.addEventListener('resize', scaleLoadingBar, false);
            }
        </script>
    </body>
</html>