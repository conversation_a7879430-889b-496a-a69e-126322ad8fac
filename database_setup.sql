-- Game Platform Database Setup
-- Create database
CREATE DATABASE IF NOT EXISTS `game_platform` CHARACTER SET utf8 COLLATE utf8_general_ci;
USE `game_platform`;

-- Agents table
CREATE TABLE IF NOT EXISTS `agents` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `agentCode` varchar(50) NOT NULL,
  `agentToken` varchar(255) NOT NULL,
  `balance` decimal(15,2) DEFAULT 0.00,
  `currency` varchar(10) DEFAULT 'BRL',
  `status` tinyint(1) DEFAULT 1,
  `type` tinyint(1) DEFAULT 1,
  `rtpgeral` decimal(5,4) DEFAULT 0.9500,
  `createdAt` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updatedAt` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `agentCode` (`agentCode`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- Users table
CREATE TABLE IF NOT EXISTS `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `agentCode` varchar(50) NOT NULL,
  `userCode` varchar(50) NOT NULL,
  `aasUserCode` varchar(100) NOT NULL,
  `balance` decimal(15,2) DEFAULT 0.00,
  `status` tinyint(1) DEFAULT 1,
  `apiType` tinyint(1) DEFAULT 1,
  `createdAt` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updatedAt` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `aasUserCode` (`aasUserCode`),
  KEY `agentCode` (`agentCode`),
  KEY `userCode` (`userCode`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- Providers table
CREATE TABLE IF NOT EXISTS `providers` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `code` varchar(50) NOT NULL,
  `name` varchar(100) NOT NULL,
  `type` varchar(20) DEFAULT 'slot',
  `status` tinyint(1) DEFAULT 1,
  `createdAt` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `code` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- Games table
CREATE TABLE IF NOT EXISTS `games` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `game_code` varchar(50) NOT NULL,
  `game_name` varchar(100) NOT NULL,
  `provider` varchar(50) NOT NULL,
  `banner` varchar(255) DEFAULT NULL,
  `status` tinyint(1) DEFAULT 1,
  `rtp` decimal(5,4) DEFAULT 0.9600,
  `lines` int(11) DEFAULT 20,
  `min_bet` decimal(10,2) DEFAULT 0.01,
  `max_bet` decimal(10,2) DEFAULT 100.00,
  `bonus_buy_multiplier` int(11) DEFAULT 100,
  `free_spins_multiplier` int(11) DEFAULT 500,
  `createdAt` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `game_code` (`game_code`),
  KEY `provider` (`provider`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- Game sessions table
CREATE TABLE IF NOT EXISTS `game_session` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `code` varchar(50) NOT NULL,
  `key` varchar(255) NOT NULL,
  `user` varchar(100) NOT NULL,
  `createdAt` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `user` (`user`),
  KEY `code` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- Game responses table
CREATE TABLE IF NOT EXISTS `game_responses` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `action` varchar(50) NOT NULL,
  `response` text,
  `game_code` varchar(50) NOT NULL,
  `index` varchar(50) DEFAULT NULL,
  `createdAt` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `game_code` (`game_code`),
  KEY `action` (`action`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- Transaction history table
CREATE TABLE IF NOT EXISTS `transaction_history` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_code` varchar(50) NOT NULL,
  `agent_code` varchar(50) NOT NULL,
  `user_balance` decimal(15,2) NOT NULL,
  `user_after_balance` decimal(15,2) NOT NULL,
  `provider_code` varchar(50) NOT NULL,
  `currency` varchar(10) NOT NULL,
  `game_code` varchar(50) NOT NULL,
  `bet_money` decimal(15,2) NOT NULL,
  `win_money` decimal(15,2) NOT NULL,
  `txn_id` varchar(50) NOT NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `user_code` (`user_code`),
  KEY `agent_code` (`agent_code`),
  KEY `game_code` (`game_code`),
  KEY `txn_id` (`txn_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- Insert default provider
INSERT INTO `providers` (`code`, `name`, `type`, `status`) VALUES
('PRAGMATICPLAY', 'Pragmatic Play', 'slot', 1);

-- Insert default agent
INSERT INTO `agents` (`agentCode`, `agentToken`, `balance`, `currency`, `status`, `type`, `rtpgeral`) VALUES
('DEMO001', 'demo_token_123456789', 10000.00, 'BRL', 1, 1, 0.9500);

-- Insert games data
INSERT INTO `games` (`game_code`, `game_name`, `provider`, `banner`, `status`, `rtp`, `lines`, `min_bet`, `max_bet`, `bonus_buy_multiplier`, `free_spins_multiplier`) VALUES
('vs10bbbonanza', 'Big Bass Bonanza', 'PRAGMATICPLAY', 'vs10bbbonanza.png', 1, 0.9600, 10, 0.10, 250.00, 100, 500),
('vs10bbfmission', 'Big Bass Floats My Boat', 'PRAGMATICPLAY', 'vs10bbfmission.png', 1, 0.9600, 10, 0.10, 250.00, 100, 500),
('vs10bbkir', 'Big Bass Keeping it Reel', 'PRAGMATICPLAY', 'vs10bbkir.png', 1, 0.9600, 10, 0.10, 250.00, 100, 500),
('vs10bblpop', 'Big Bass Lake\'s Pot of Gold', 'PRAGMATICPLAY', 'vs10bblpop.png', 1, 0.9600, 10, 0.10, 250.00, 100, 500),
('vs10bxmasbnza', 'Big Bass Christmas Bonanza', 'PRAGMATICPLAY', 'vs10bxmasbnza.png', 1, 0.9600, 10, 0.10, 250.00, 100, 500),
('vs10cowgold', 'Cowboy Gold', 'PRAGMATICPLAY', 'vs10cowgold.png', 1, 0.9600, 10, 0.10, 125.00, 100, 500),
('vs10eyestorm', 'Eye of the Storm', 'PRAGMATICPLAY', 'vs10eyestorm.png', 1, 0.9600, 10, 0.10, 125.00, 100, 500),
('vs10fruity2', 'Fruity Treats', 'PRAGMATICPLAY', 'vs10fruity2.png', 1, 0.9600, 10, 0.10, 125.00, 100, 500),
('vs10madame', 'Madame Destiny', 'PRAGMATICPLAY', 'vs10madame.png', 1, 0.9600, 10, 0.10, 125.00, 100, 500),
('vs10returndead', 'Return of the Dead', 'PRAGMATICPLAY', 'vs10returndead.png', 1, 0.9600, 10, 0.10, 125.00, 100, 500),
('vs10txbigbass', 'Big Bass Bonanza Megaways', 'PRAGMATICPLAY', 'vs10txbigbass.png', 1, 0.9600, 10, 0.10, 250.00, 100, 500),
('vs10vampwolf', 'Vampire vs Wolves', 'PRAGMATICPLAY', 'vs10vampwolf.png', 1, 0.9600, 10, 0.10, 125.00, 100, 500),
('vs12bbb', 'Big Bass Bonanza', 'PRAGMATICPLAY', 'vs12bbb.png', 1, 0.9600, 12, 0.12, 300.00, 100, 500),
('vs12bbbxmas', 'Big Bass Christmas Bonanza', 'PRAGMATICPLAY', 'vs12bbbxmas.png', 1, 0.9600, 12, 0.12, 300.00, 100, 500),
('vs15godsofwar', 'Gods of War', 'PRAGMATICPLAY', 'vs15godsofwar.png', 1, 0.9600, 15, 0.15, 375.00, 100, 200),
('vs20amuleteg', 'Amulet of Egypt', 'PRAGMATICPLAY', 'vs20amuleteg.png', 1, 0.9600, 20, 0.20, 100.00, 100, 500),
('vs20bchprty', 'Beach Party Hot', 'PRAGMATICPLAY', 'vs20bchprty.png', 1, 0.9600, 20, 0.20, 100.00, 100, 500),
('vs20bonzgold', 'Bonanza Gold', 'PRAGMATICPLAY', 'vs20bonzgold.png', 1, 0.9600, 20, 0.20, 100.00, 100, 500),
('vs20candvil', 'Candy Village', 'PRAGMATICPLAY', 'vs20candvil.png', 1, 0.9600, 20, 0.20, 100.00, 100, 500),
('vs20cleocatra', 'Cleocatra', 'PRAGMATICPLAY', 'vs20cleocatra.png', 1, 0.9600, 20, 0.20, 100.00, 100, 500),
('vs20clustcol', 'Cluster Tumble', 'PRAGMATICPLAY', 'vs20clustcol.png', 1, 0.9600, 20, 0.20, 100.00, 100, 500),
('vs20daydead', 'Day of Dead', 'PRAGMATICPLAY', 'vs20daydead.png', 1, 0.9600, 20, 0.20, 100.00, 100, 500),
('vs20doghouse', 'The Dog House', 'PRAGMATICPLAY', 'vs20doghouse.png', 1, 0.9600, 20, 0.20, 100.00, 100, 500),
('vs20egypt', 'Ancient Egypt Classic', 'PRAGMATICPLAY', 'vs20egypt.png', 1, 0.9600, 20, 0.20, 100.00, 100, 500),
('vs20fparty2', 'Fruit Party 2', 'PRAGMATICPLAY', 'vs20fparty2.png', 1, 0.9600, 20, 0.20, 100.00, 100, 500),
('vs20fruitsw', 'Fruit Rainbow', 'PRAGMATICPLAY', 'vs20fruitsw.png', 1, 0.9600, 20, 0.20, 100.00, 100, 500),
('vs20fruitswx', 'Fruit Party', 'PRAGMATICPLAY', 'vs20fruitswx.png', 1, 0.9600, 20, 0.20, 100.00, 100, 500),
('vs20goldfever', 'Gold Rush', 'PRAGMATICPLAY', 'vs20goldfever.png', 1, 0.9600, 20, 0.20, 100.00, 100, 500),
('vs20heartcleo', 'Heart of Cleopatra', 'PRAGMATICPLAY', 'vs20heartcleo.png', 1, 0.9600, 20, 0.20, 100.00, 100, 500),
('vs20midas2', 'Hand of Midas', 'PRAGMATICPLAY', 'vs20midas2.png', 1, 0.9600, 20, 0.20, 100.00, 100, 500),
('vs20muertos', 'Muertos Multiplier Megaways', 'PRAGMATICPLAY', 'vs20muertos.png', 1, 0.9600, 20, 0.20, 100.00, 100, 500),
('vs20olympgate', 'Gates of Olympus', 'PRAGMATICPLAY', 'vs20olympgate.png', 1, 0.9600, 20, 0.20, 125.00, 100, 500),
('vs20olympx', 'Gates of Olympus 1000', 'PRAGMATICPLAY', 'vs20olympx.png', 1, 0.9600, 20, 0.20, 125.00, 100, 500),
('vs20pbonanza', 'Pirate Gold Deluxe', 'PRAGMATICPLAY', 'vs20pbonanza.png', 1, 0.9600, 20, 0.20, 100.00, 100, 500),
('vs20pistols', 'Pistoleras', 'PRAGMATICPLAY', 'vs20pistols.png', 1, 0.9600, 20, 0.20, 100.00, 133, 500),
('vs20sbxmas', 'Sweet Bonanza Xmas', 'PRAGMATICPLAY', 'vs20sbxmas.png', 1, 0.9600, 20, 0.20, 125.00, 100, 500),
('vs20schristmas', 'Sweet Christmas', 'PRAGMATICPLAY', 'vs20schristmas.png', 1, 0.9600, 20, 0.20, 100.00, 100, 500),
('vs20starlight', 'Starlight Princess', 'PRAGMATICPLAY', 'vs20starlight.png', 1, 0.9600, 20, 0.20, 125.00, 100, 500),
('vs20sugarnudge', 'Sugar Rush', 'PRAGMATICPLAY', 'vs20sugarnudge.png', 1, 0.9600, 20, 0.20, 125.00, 100, 500),
('vs20sugarrush', 'Sugar Rush', 'PRAGMATICPLAY', 'vs20sugarrush.png', 1, 0.9600, 20, 0.20, 125.00, 100, 500),
('vs20sugarrushx', 'Sugar Rush Xmas', 'PRAGMATICPLAY', 'vs20sugarrushx.png', 1, 0.9600, 20, 0.20, 125.00, 100, 500),
('vs20tweethouse', 'Tweet House', 'PRAGMATICPLAY', 'vs20tweethouse.png', 1, 0.9600, 20, 0.20, 100.00, 100, 500),
('vs20wildparty', 'Wild Beach Party', 'PRAGMATICPLAY', 'vs20wildparty.png', 1, 0.9600, 20, 0.20, 100.00, 100, 500),
('vs20wildpix', 'Wild Pixies', 'PRAGMATICPLAY', 'vs20wildpix.png', 1, 0.9600, 20, 0.20, 100.00, 100, 500),
('vs25bullfiesta', 'Bull Fiesta', 'PRAGMATICPLAY', 'vs25bullfiesta.png', 1, 0.9600, 25, 0.25, 125.00, 100, 500),
('vs25chilli', 'Chilli Heat', 'PRAGMATICPLAY', 'vs25chilli.png', 1, 0.9600, 25, 0.25, 125.00, 100, 500),
('vs25copsrobbers', 'Cash Patrol', 'PRAGMATICPLAY', 'vs25copsrobbers.png', 1, 0.9600, 25, 0.25, 125.00, 100, 500),
('vs25mustang', 'Mustang Gold', 'PRAGMATICPLAY', 'vs25mustang.png', 1, 0.9600, 25, 0.25, 125.00, 100, 500),
('vs25wolfgold', 'Wolf Gold', 'PRAGMATICPLAY', 'vs25wolfgold.png', 1, 0.9600, 25, 0.25, 125.00, 100, 500),
('vs40pirate', 'Pirate Gold', 'PRAGMATICPLAY', 'vs40pirate.png', 1, 0.9600, 40, 0.20, 100.00, 100, 500),
('vs40samurai3', 'Samurai Ken', 'PRAGMATICPLAY', 'vs40samurai3.png', 1, 0.9600, 40, 0.20, 100.00, 100, 500),
('vs40spartaking', 'Spartan King', 'PRAGMATICPLAY', 'vs40spartaking.png', 1, 0.9600, 40, 0.20, 100.00, 100, 500),
('vs40wildwest', 'Wild West Gold', 'PRAGMATICPLAY', 'vs40wildwest.png', 1, 0.9600, 40, 0.20, 100.00, 100, 500),
('vs50juicyfr', 'Juicy Fruits', 'PRAGMATICPLAY', 'vs50juicyfr.png', 1, 0.9600, 50, 0.25, 125.00, 100, 500),
('vs50safariking', 'Safari King', 'PRAGMATICPLAY', 'vs50safariking.png', 1, 0.9600, 50, 0.25, 125.00, 100, 500),
('vs5luckytig', 'Lucky Tiger', 'PRAGMATICPLAY', 'vs5luckytig.png', 1, 0.9600, 5, 0.05, 62.50, 100, 500),
('vs5strh', 'Striking Hot 5', 'PRAGMATICPLAY', 'vs5strh.png', 1, 0.9600, 5, 0.05, 62.50, 100, 500),
('vs5super7', 'Super 7s', 'PRAGMATICPLAY', 'vs5super7.png', 1, 0.9600, 5, 0.05, 62.50, 100, 500),
('vswayshammthor', 'Hammer of Thor', 'PRAGMATICPLAY', 'vswayshammthor.png', 1, 0.9600, 20, 0.20, 100.00, 150, 500),
('vswaysmadame', 'Madame Destiny Megaways', 'PRAGMATICPLAY', 'vswaysmadame.png', 1, 0.9600, 20, 0.20, 100.00, 150, 500),
('vswayspowzeus', 'Power of Thor Megaways', 'PRAGMATICPLAY', 'vswayspowzeus.png', 1, 0.9600, 20, 0.20, 100.00, 150, 500);
