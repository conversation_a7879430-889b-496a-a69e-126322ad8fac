<?php 

class User_model extends CI_Model {

    public function get($user_code, $agent) {
        $this->db->from('users');
        $this->db->where('agentCode', $agent);
        $this->db->where('userCode', $user_code);
        $query = $this->db->get()->result_array();
        return $query;
    }

    public function getUserAndAgType($user_code, $agent) {
        $this->db->from('users');
        $this->db->where('apiType', 1);
        $this->db->where('agentCode', $agent);
        $this->db->where('userCode', $user_code);
        $query = $this->db->get()->result_array();
        return $query;
    }


    public function getByID($user_code, $agent) {
        $this->db->from('users');
        $this->db->where('agentCode', $agent);
        $this->db->where('userCode', $user_code);
        $query = $this->db->get()->result_array();
        return $query;
    }

    public function getReturnID($user_code, $agent) {
        $this->db->select('id');
        $this->db->from('users');
        $this->db->where('agentCode', $agent);
        $this->db->where('userCode', $user_code);
        $query = $this->db->get()->row(); // Use row() para obter apenas uma linha como objeto
        return $query->id; // Acesse diretamente o ID
    }

    public function getByInternalID($user) {
        $this->db->where('aasUserCode', $user);
        $query = $this->db->get('users')->result();
        return $query;
    }

    public function createExt($id) {
        $this->spinshield->createUser($id);
        //$this->fiverscan->createUser($id);
        return true;
    }
    
    public function create($insertData) {
        $this->db->insert('users', $insertData);
        $id = $this->db->insert_id();
        return $id;
    }

    public function getAllUserAndBalance($code){

        $this->db->where('agentCode',$code);
        $this->db->select('userCode as user_code, balance');
        $this->db->from('users');
        $query = $this->db->get()->result_array();
        return $query;
    }

    public function getAllUser($code){

        $this->db->where('agentCode',$code);
        $this->db->select('userCode as user_code, status, totalDebit, totalCredit, balance');
        $this->db->from('users');
        $query = $this->db->get()->result_array();
        return $query;
    }

    public function getBalance($user_code, $agent) {

        $this->db->where('agentCode', $agent);
        $this->db->where('userCode', $user_code);
        $user = $this->db->get('users')->result();

        $this->db->where('aasUserCode', $user[0]->aasUserCode);
        $curbalance = $this->db->get('users')->result();
        return $curbalance[0]->balance;
    }


    public function updateBalance($user_code, $new_balance) {
        $this->db->where('aasUserCode', $user_code);
        $query = $this->db->get('users');

        if ($query->num_rows() > 0) {
            $data = array('balance' => $new_balance);
            $this->db->where('aasUserCode', $user_code);
            $this->db->update('users', $data);
            return TRUE;
        } else {
            return FALSE;
        }
    }

    // New methods for admin panel
    public function get_user_by_code($userCode) {
        $this->db->where('userCode', $userCode);
        $query = $this->db->get('users');
        return $query->row_array();
    }

    public function get_user_by_id($id) {
        $this->db->where('id', $id);
        $query = $this->db->get('users');
        return $query->row_array();
    }

    public function get_user_by_id_and_agent($id, $agentCode) {
        $this->db->where('id', $id);
        $this->db->where('agentCode', $agentCode);
        $query = $this->db->get('users');
        return $query->row_array();
    }

    public function get_all_users() {
        $this->db->order_by('userCode', 'ASC');
        $query = $this->db->get('users');
        return $query->result_array();
    }

    public function get_all_users_with_agents() {
        $this->db->select('u.*, a.agent_name');
        $this->db->from('users u');
        $this->db->join('agents a', 'u.agentCode = a.agentCode');
        $this->db->order_by('u.userCode', 'ASC');
        $query = $this->db->get();
        return $query->result_array();
    }

    public function get_users_by_agent($agentCode) {
        $this->db->where('agentCode', $agentCode);
        $this->db->order_by('userCode', 'ASC');
        $query = $this->db->get('users');
        return $query->result_array();
    }

    public function count_users() {
        return $this->db->count_all('users');
    }

    public function count_users_by_agent($agentCode) {
        $this->db->where('agentCode', $agentCode);
        return $this->db->count_all_results('users');
    }

    public function get_total_balance_by_agent($agentCode) {
        $this->db->select('SUM(balance) as total_balance');
        $this->db->where('agentCode', $agentCode);
        $query = $this->db->get('users');
        $result = $query->row_array();
        return $result['total_balance'] ?: 0;
    }

    public function create_user($user_data) {
        return $this->db->insert('users', $user_data);
    }

    public function update_user($id, $user_data) {
        $this->db->where('id', $id);
        return $this->db->update('users', $user_data);
    }

    public function update_user_by_code($userCode, $user_data) {
        $this->db->where('userCode', $userCode);
        return $this->db->update('users', $user_data);
    }

    public function get_user_transactions($userCode, $limit = 100) {
        $this->db->select('th.*, g.game_name');
        $this->db->from('transaction_history th');
        $this->db->join('games g', 'th.game_code = g.game_code', 'left');
        $this->db->where('th.user_code', $userCode);
        $this->db->order_by('th.created_at', 'DESC');
        $this->db->limit($limit);

        return $this->db->get()->result_array();
    }

    public function get_user_stats($userCode) {
        $this->db->select('COUNT(*) as total_transactions, SUM(bet_money) as total_bet, SUM(win_money) as total_win');
        $this->db->from('transaction_history');
        $this->db->where('user_code', $userCode);
        $query = $this->db->get();
        return $query->row_array();
    }
}